package com.dfl.smartindicator;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *   Created by zhangpan on 2020-01-20.
 *   Description:
 * </pre>
 */
public class MyViewPagerAdapter extends RecyclerView.Adapter<MyPagerViewHolder> {
  private final List<Integer> mDrawableList = new ArrayList<>();

  public MyViewPagerAdapter(List<Integer> drawableList) {
    mDrawableList.addAll(drawableList);
  }

  @NonNull
  @Override
  public MyPagerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    View view =
        LayoutInflater.from(parent.getContext()).inflate(R.layout.item_slide_mode, parent, false);
    return new MyPagerViewHolder(view);
  }

  @Override
  public void onBindViewHolder(@NonNull MyPagerViewHolder holder, int position) {
    holder.bind(mDrawableList.get(position), position);
  }

  @Override
  public int getItemCount() {
    return mDrawableList.size();
  }
}
