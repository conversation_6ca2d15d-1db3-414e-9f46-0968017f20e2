<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.zhpan.bannerview.BannerViewPager
        android:id="@+id/view_pager2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />


    <!--    <com.zhpan.viewpagerindicator.smartscene.SmartSceneIndicatorView-->
    <!--        android:id="@+id/smartSceneIndicator"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--       app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        android:background="@android:color/white" />   -->


<!--        <View-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="0dp"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="@id/twoScrollIndicatorView"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            android:background="@android:color/white"-->
<!--            />-->

<!--    <com.dfl.smartindicator.shape.TwoIndicatorView-->
<!--        android:id="@+id/twoScrollIndicatorView"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintBottom_toTopOf="@id/multiIndicatorView"-->
<!--        android:layout_marginBottom="10dp"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        />-->

<!--        <com.dfl.smartindicator.shape.MultiIndicatorView-->
<!--            android:id="@+id/multiIndicatorView"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            />-->


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="vertical"
        >

        <Button
            android:id="@+id/bt1"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="1页"
            />
        <Button
            android:id="@+id/bt2"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="2页"
            android:layout_marginTop="10dp"
            />

        <Button
            android:id="@+id/bt3"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="3页"
            android:layout_marginTop="10dp"
            />

        <Button
            android:id="@+id/bt4"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="4页"
            android:layout_marginTop="10dp"
            />

        <Button
            android:id="@+id/bt5"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="5页"
            android:layout_marginTop="10dp"
            />

        <Button
            android:id="@+id/bt6"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="6页"
            android:layout_marginTop="10dp"
            />

        <Button
            android:id="@+id/bt20"
            android:layout_width="200dp"
            android:layout_height="50dp"
            android:text="20页"
            android:layout_marginTop="10dp"
            />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>