/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 * <p>
 * * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 * * All Rights Reserved.
 * *
 * * Redistribution and use in source and binary forms, with or without
 * * modification, are NOT permitted except as agreed by
 * * iAuto (Shanghai) Co., Ltd.
 * *
 * * Unless required by applicable law or agreed to in writing, software
 * * distributed under the License is distributed on an "AS IS" BASIS,
 * * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 * @file IVIInf_notifyDistanceToPositionWithType_param_t.java
 * @brief
 */

package ics;

import java.io.Serializable;

import ics.com.serialization.common.Structure;
import ics.com.runtime.utils.FieldOrderAnnotation;

public class IVIInf_notifyDistanceToPositionWithType_param_t implements Structure, Serializable {
    @FieldOrderAnnotation(order = 1)
    public Double latitude;
    @FieldOrderAnnotation(order = 2)
    public Double longitude;
    @FieldOrderAnnotation(order = 3)
    public Integer distance;// uint32_t data: please convert by function: Integer.toUnsignedInt or Integer
    // .toUnsignedLong
    @FieldOrderAnnotation(order = 4)
    public Byte type;// uint8_t data: please convert by function: Byte.toUnsignedInt or Byte.toUnsignedLong

    public IVIInf_notifyDistanceToPositionWithType_param_t() {
        this.latitude = new Double((double) 0.0);
        this.longitude = new Double((double) 0.0);
        this.distance = new Integer(0);
        this.type = new Byte((byte) 0);
    }

    public IVIInf_notifyDistanceToPositionWithType_param_t(Double latitude, Double longitude, Integer distance,
                                                           Byte type) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.distance = distance;
        this.type = type;
    }

}
