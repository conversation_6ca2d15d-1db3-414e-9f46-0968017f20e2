/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 * <p>
 * * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 * * All Rights Reserved.
 * *
 * * Redistribution and use in source and binary forms, with or without
 * * modification, are NOT permitted except as agreed by
 * * iAuto (Shanghai) Co., Ltd.
 * *
 * * Unless required by applicable law or agreed to in writing, software
 * * distributed under the License is distributed on an "AS IS" BASIS,
 * * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 * @file requestNaviWithRoutePoint.java
 * @brief requestNaviWithRoutePoint.java
 */

package ara.ivi.navigation.ics.navigation.v1.skeleton;

import ara.ivi.navigation.ics.navigation.v1.TransformPropsObj;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonRequestCallback;
import ics.com.runtime.ErrorCode;
import ics.com.runtime.SessionTag;
import ics.com.runtime.Skeleton;
import ics.com.runtime.utils.FieldOrderAnnotation;
import ics.com.serialization.common.Wrapper;

public class requestNaviWithRoutePoint {
    private ComSkeletonInstance instance;
    private String name;
    private int idx;

    public requestNaviWithRoutePoint(String name, int idx, ComSkeletonInstance ins) {
        this.name = name;
        this.idx = idx;
        this.instance = ins;
    }

    public void RegMethodProcess(requestNaviWithRoutePointHandle callback) {
        this.instance.setMethodRequestCallback(this.idx, new ComSkeletonRequestCallback() {
            @Override
            public void onCall(byte[] payload, SessionTag tag) {
                requestNaviWithRoutePointInput input = (requestNaviWithRoutePointInput) Skeleton.executeRRMethodAsyncRequest(idx, instance, payload, TransformPropsObj.instance().kDefaultSomeipTransformationProps, requestNaviWithRoutePointInput.class);
                callback.OnCall(input.type, input.routePoint);
            }

            @Override
            public void onError(ErrorCode errorCode, SessionTag tag) {
                //error handle
                callback.OnError(errorCode);
            }
        });
    }

    public interface requestNaviWithRoutePointHandle {
        public void OnCall(Byte type, String routePoint);

        public void OnError(ErrorCode errorCode);
    }

    public static class requestNaviWithRoutePointInput implements Wrapper {
        @FieldOrderAnnotation(order = 1)
        public Byte type;
        @FieldOrderAnnotation(order = 2)
        public String routePoint;

        public requestNaviWithRoutePointInput(Byte type, String routePoint) {
            this.type = type;
            this.routePoint = routePoint;
        }
    }
}

