/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file MsgCenterSkeleton.java
 * @brief MsgCenterSkeleton.java
 *
 *
 */

package ara.ivi.msgcenter.ics.msgcenter.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class MsgCenterSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.msgcenter.ics.msgcenter.v1.skeleton.showMessage showMessage;

    public MsgCenterSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("msgcenter_1_0", instanceId), loop);
        this.showMessage = new showMessage("showMessage", this.instance.getMethodIndex("showMessage"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

