/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file MediaSkeleton.java
 * @brief MediaSkeleton.java
 *
 *
 */

package ara.ivi.media.ics.media.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class MediaSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.media.ics.media.v1.skeleton.playMusic playMusic;
    public ara.ivi.media.ics.media.v1.skeleton.switchPlayMode switchPlayMode;
    public ara.ivi.media.ics.media.v1.skeleton.setMediaStatus setMediaStatus;
    public ara.ivi.media.ics.media.v1.skeleton.switchMediaSource switchMediaSource;
    public ara.ivi.media.ics.media.v1.skeleton.closeMedia closeMedia;

    public MediaSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("media_1_0", instanceId), loop);
        this.playMusic = new playMusic("playMusic", this.instance.getMethodIndex("playMusic"), this.instance);
        this.switchPlayMode = new switchPlayMode("switchPlayMode", this.instance.getMethodIndex("switchPlayMode"), this.instance);
        this.setMediaStatus = new setMediaStatus("setMediaStatus", this.instance.getMethodIndex("setMediaStatus"), this.instance);
        this.switchMediaSource = new switchMediaSource("switchMediaSource", this.instance.getMethodIndex("switchMediaSource"), this.instance);
        this.closeMedia = new closeMedia("closeMedia", this.instance.getMethodIndex("closeMedia"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

