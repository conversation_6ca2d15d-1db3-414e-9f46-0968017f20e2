/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file QQMusicSkeleton.java
 * @brief QQMusicSkeleton.java
 *
 *
 */

package ara.ivi.qqmusic.ics.qqmusic.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class QQMusicSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecificMoodPlaylists playSpecificMoodPlaylists;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecificEraPlaylists playSpecificEraPlaylists;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playGenreSongList playGenreSongList;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSingerSong playSpecifiedSingerSong;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSong playSpecifiedSong;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSongList playSpecifiedSongList;
    public ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.play play;

    public QQMusicSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("qqmusic_1_0", instanceId), loop);
        this.playSpecificMoodPlaylists = new playSpecificMoodPlaylists("playSpecificMoodPlaylists", this.instance.getMethodIndex("playSpecificMoodPlaylists"), this.instance);
        this.playSpecificEraPlaylists = new playSpecificEraPlaylists("playSpecificEraPlaylists", this.instance.getMethodIndex("playSpecificEraPlaylists"), this.instance);
        this.playGenreSongList = new playGenreSongList("playGenreSongList", this.instance.getMethodIndex("playGenreSongList"), this.instance);
        this.playSpecifiedSingerSong = new playSpecifiedSingerSong("playSpecifiedSingerSong", this.instance.getMethodIndex("playSpecifiedSingerSong"), this.instance);
        this.playSpecifiedSong = new playSpecifiedSong("playSpecifiedSong", this.instance.getMethodIndex("playSpecifiedSong"), this.instance);
        this.playSpecifiedSongList = new playSpecifiedSongList("playSpecifiedSongList", this.instance.getMethodIndex("playSpecifiedSongList"), this.instance);
        this.play = new play("play", this.instance.getMethodIndex("play"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

