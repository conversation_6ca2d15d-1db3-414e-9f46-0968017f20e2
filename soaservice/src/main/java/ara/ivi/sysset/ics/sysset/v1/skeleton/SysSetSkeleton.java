/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file SysSetSkeleton.java
 * @brief SysSetSkeleton.java
 *
 *
 */

package ara.ivi.sysset.ics.sysset.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class SysSetSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.notifyDayStatus notifyDayStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getBluetoothConnectionStatus getBluetoothConnectionStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getBluetoothSwitchStatus getBluetoothSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getHotspotConnectionStatus getHotspotConnectionStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getHotspotSwitchStatus getHotspotSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getWLANConnectionStatus getWLANConnectionStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getWLANSwitchStatus getWLANSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getMobileNetworkSwitchStatus getMobileNetworkSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.getMobileNetworkConnectionStatus getMobileNetworkConnectionStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setBluetoothSwitchStatus setBluetoothSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setDayAndNightMode setDayAndNightMode;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setHotspotSwitchStatus setHotspotSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setScreenBrightness setScreenBrightness;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setThemeWallpaper setThemeWallpaper;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setVolume setVolume;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setWLANSwitchStatus setWLANSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setMobileNetworkSwitchStatus setMobileNetworkSwitchStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setScreenText setScreenText;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setMuteStatus setMuteStatus;
    public ara.ivi.sysset.ics.sysset.v1.skeleton.setOutCarVolume setOutCarVolume;

    public SysSetSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("sysset_1_0", instanceId), loop);
        this.notifyDayStatus = new notifyDayStatus("notifyDayStatus", this.instance.getEventIndex("notifyDayStatus"), this.instance);
        this.getBluetoothConnectionStatus = new getBluetoothConnectionStatus("getBluetoothConnectionStatus", this.instance.getMethodIndex("getBluetoothConnectionStatus"), this.instance);
        this.getBluetoothSwitchStatus = new getBluetoothSwitchStatus("getBluetoothSwitchStatus", this.instance.getMethodIndex("getBluetoothSwitchStatus"), this.instance);
        this.getHotspotConnectionStatus = new getHotspotConnectionStatus("getHotspotConnectionStatus", this.instance.getMethodIndex("getHotspotConnectionStatus"), this.instance);
        this.getHotspotSwitchStatus = new getHotspotSwitchStatus("getHotspotSwitchStatus", this.instance.getMethodIndex("getHotspotSwitchStatus"), this.instance);
        this.getWLANConnectionStatus = new getWLANConnectionStatus("getWLANConnectionStatus", this.instance.getMethodIndex("getWLANConnectionStatus"), this.instance);
        this.getWLANSwitchStatus = new getWLANSwitchStatus("getWLANSwitchStatus", this.instance.getMethodIndex("getWLANSwitchStatus"), this.instance);
        this.getMobileNetworkSwitchStatus = new getMobileNetworkSwitchStatus("getMobileNetworkSwitchStatus", this.instance.getMethodIndex("getMobileNetworkSwitchStatus"), this.instance);
        this.getMobileNetworkConnectionStatus = new getMobileNetworkConnectionStatus("getMobileNetworkConnectionStatus", this.instance.getMethodIndex("getMobileNetworkConnectionStatus"), this.instance);
        this.setBluetoothSwitchStatus = new setBluetoothSwitchStatus("setBluetoothSwitchStatus", this.instance.getMethodIndex("setBluetoothSwitchStatus"), this.instance);
        this.setDayAndNightMode = new setDayAndNightMode("setDayAndNightMode", this.instance.getMethodIndex("setDayAndNightMode"), this.instance);
        this.setHotspotSwitchStatus = new setHotspotSwitchStatus("setHotspotSwitchStatus", this.instance.getMethodIndex("setHotspotSwitchStatus"), this.instance);
        this.setScreenBrightness = new setScreenBrightness("setScreenBrightness", this.instance.getMethodIndex("setScreenBrightness"), this.instance);
        this.setThemeWallpaper = new setThemeWallpaper("setThemeWallpaper", this.instance.getMethodIndex("setThemeWallpaper"), this.instance);
        this.setVolume = new setVolume("setVolume", this.instance.getMethodIndex("setVolume"), this.instance);
        this.setWLANSwitchStatus = new setWLANSwitchStatus("setWLANSwitchStatus", this.instance.getMethodIndex("setWLANSwitchStatus"), this.instance);
        this.setMobileNetworkSwitchStatus = new setMobileNetworkSwitchStatus("setMobileNetworkSwitchStatus", this.instance.getMethodIndex("setMobileNetworkSwitchStatus"), this.instance);
        this.setScreenText = new setScreenText("setScreenText", this.instance.getMethodIndex("setScreenText"), this.instance);
        this.setMuteStatus = new setMuteStatus("setMuteStatus", this.instance.getMethodIndex("setMuteStatus"), this.instance);
        this.setOutCarVolume = new setOutCarVolume("setOutCarVolume", this.instance.getMethodIndex("setOutCarVolume"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

