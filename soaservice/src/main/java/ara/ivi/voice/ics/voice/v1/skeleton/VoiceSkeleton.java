/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file VoiceSkeleton.java
 * @brief VoiceSkeleton.java
 *
 *
 */

package ara.ivi.voice.ics.voice.v1.skeleton;
import android.os.Looper;
import ics.com.runtime.ComSkeletonInstance;
import ics.com.runtime.ComSkeletonStateChangedCallback;
import ics.com.runtime.InstanceHandle;

public class VoiceSkeleton {
    private ComSkeletonInstance instance;
    public ara.ivi.voice.ics.voice.v1.skeleton.setTTSBroadcastContent setTTSBroadcastContent;
    public ara.ivi.voice.ics.voice.v1.skeleton.setOuterSpeaker setOuterSpeaker;

    public VoiceSkeleton(String instanceId, Looper loop) {
        this.instance = new ComSkeletonInstance(new InstanceHandle("voice_1_0", instanceId), loop);
        this.setTTSBroadcastContent = new setTTSBroadcastContent("setTTSBroadcastContent", this.instance.getMethodIndex("setTTSBroadcastContent"), this.instance);
        this.setOuterSpeaker = new setOuterSpeaker("setOuterSpeaker", this.instance.getMethodIndex("setOuterSpeaker"), this.instance);
    }

    // API OfferService
    public void OfferService() {
        this.instance.start();
    }

    // API StopOfferService
    public void StopOfferService() {
        this.instance.stop();
    }

    // API SetStateChangedCallback
    public void SetStateChangedCallback(ComSkeletonStateChangedCallback callback) {
        this.instance.setStateChangedCallback(callback);
    }
}

