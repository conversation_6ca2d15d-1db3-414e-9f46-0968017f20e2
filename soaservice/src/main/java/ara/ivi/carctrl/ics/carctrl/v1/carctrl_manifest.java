/**
 * * --------------------------------------------------------------------
 * * |                                                                  |
 * * |     _         _    _ _______ ____         _____ ____  __  __     |
 * * |    (_)   /\  | |  | |__   __/ __ \       / ____/ __ \|  \/  |    |
 * * |     _   /  \ | |  | |  | | | |  | |     | |   | |  | | \  / |    |
 * * |    | | / /\ \| |  | |  | | | |  | |     | |   | |  | | |\/| |    |
 * * |    | |/ ____ \ |__| |  | | | |__| |  _  | |___| |__| | |  | |    |
 * * |    |_/_/    \_\____/   |_|  \____/  (_)  \_____\____/|_|  |_|    |
 * * |                                                                  |
 * * --------------------------------------------------------------------
 *
 *  * Copyright @ 2020 iAuto (Shanghai) Co., Ltd.
 *  * All Rights Reserved.
 *  *
 *  * Redistribution and use in source and binary forms, with or without
 *  * modification, are NOT permitted except as agreed by
 *  * iAuto (Shanghai) Co., Ltd.
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *
 *
 * @file carctrl_manifest.cpp
 * @brief carctrl_manifest.cpp
 *
 *
 */

package ara.ivi.carctrl.ics.carctrl.v1;
import ics.com.runtime.ComTransportManager;
import ics.com.runtime.ComTransportManifest;

public class carctrl_manifest {
    public carctrl_manifest(){}

    public static void OnInit(){
        ComTransportManifest manifest = new ComTransportManifest();
        manifest.serviceInfo = "carctrl_1_0";
        manifest.e2eManifest = "";
        manifest.permissionManifest = "";
        manifest.events = new String[]{};
        manifest.fields = new String[]{};
        manifest.methods = new String[]{"setAirPurifierSwitchStatus","setAromatherapyMachineDispersingMode","setAromatherapyMachineSwitchStatus","setAromatherapyMachineWorkingTime","setAromatherapyMachineConcentrationStatus","setDrivingMode","setEnergyRecoveryMode","setChildSeatVentilationStatus","setChildSeatHeatingStatus","setSeatMemoryMode","setAtmosphereLampColorTheme","setAtmosphereLightStatus","setAtmosphereLightMode","setAtmosphereLightBrightness","setAtmosphereLightColor","setAtmosphereLightSceneLightingEffectStatus","setRearFogLampStatus","setSayHiHeadlightSignalType","setAtmosphereLightSOASignalType","getInCarAirQualityStatus","getChildSeatStatus","setInsideLampTheme","setSmartAromatherapyMachineSwitchStatus","setSmartAromatherapyMachineConcentration","setSmartAromatherapyMachineDispersingMode","setSmartAromatherapyMachineFlavor","setSmartAtmosphereLightSwitchStatus","setSmartAtmosphereLightBrightness","setSmartAtmosphereLightMode","setSmartAtmosphereLightColor","setheadlightSOASignalType","setStarRingLightStatus","setInsideLampCustomized","setWirelessCharger","setAiBackSeats","setHudStatus","setHudMode","setHudColorTheme"};
        manifest.bindingName = "isomeip";
        manifest.deploymentManifest ="{\n" +
        "    \"services\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"short_name\" : \"carctrl_1_0\",\n" +
        "            \"service\" : 165,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"minor_version\" : 0,\n" +
        "            \"events\":\n" +
        "            [\n" +
        "            ],\n" +
        "            \"fields\":\n" +
        "            [\n" +
        "            ],\n" +
        "            \"methods\":\n" +
        "            [\n" +
        "                {\n" +
        "                    \"short_name\": \"setAirPurifierSwitchStatus\",\n" +
        "                    \"method\": 8193,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAromatherapyMachineDispersingMode\",\n" +
        "                    \"method\": 8194,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAromatherapyMachineSwitchStatus\",\n" +
        "                    \"method\": 8195,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAromatherapyMachineWorkingTime\",\n" +
        "                    \"method\": 8196,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAromatherapyMachineConcentrationStatus\",\n" +
        "                    \"method\": 8197,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setDrivingMode\",\n" +
        "                    \"method\": 8198,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setEnergyRecoveryMode\",\n" +
        "                    \"method\": 8199,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setChildSeatVentilationStatus\",\n" +
        "                    \"method\": 8209,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setChildSeatHeatingStatus\",\n" +
        "                    \"method\": 8210,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSeatMemoryMode\",\n" +
        "                    \"method\": 8228,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLampColorTheme\",\n" +
        "                    \"method\": 8229,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightStatus\",\n" +
        "                    \"method\": 8230,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightMode\",\n" +
        "                    \"method\": 8231,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightBrightness\",\n" +
        "                    \"method\": 8232,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightColor\",\n" +
        "                    \"method\": 8233,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightSceneLightingEffectStatus\",\n" +
        "                    \"method\": 8240,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setRearFogLampStatus\",\n" +
        "                    \"method\": 8241,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSayHiHeadlightSignalType\",\n" +
        "                    \"method\": 8242,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAtmosphereLightSOASignalType\",\n" +
        "                    \"method\": 8245,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getInCarAirQualityStatus\",\n" +
        "                    \"method\": 1,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"getChildSeatStatus\",\n" +
        "                    \"method\": 2,\n" +
        "                    \"fire_and_forget\" : false,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setInsideLampTheme\",\n" +
        "                    \"method\": 8247,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAromatherapyMachineSwitchStatus\",\n" +
        "                    \"method\": 8248,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAromatherapyMachineConcentration\",\n" +
        "                    \"method\": 8249,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAromatherapyMachineDispersingMode\",\n" +
        "                    \"method\": 8256,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAromatherapyMachineFlavor\",\n" +
        "                    \"method\": 8257,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAtmosphereLightSwitchStatus\",\n" +
        "                    \"method\": 8258,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAtmosphereLightBrightness\",\n" +
        "                    \"method\": 8259,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAtmosphereLightMode\",\n" +
        "                    \"method\": 8260,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setSmartAtmosphereLightColor\",\n" +
        "                    \"method\": 8261,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setheadlightSOASignalType\",\n" +
        "                    \"method\": 8246,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setStarRingLightStatus\",\n" +
        "                    \"method\": 8262,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setInsideLampCustomized\",\n" +
        "                    \"method\": 8263,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setWirelessCharger\",\n" +
        "                    \"method\": 8264,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setAiBackSeats\",\n" +
        "                    \"method\": 8265,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setHudStatus\",\n" +
        "                    \"method\": 8272,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setHudMode\",\n" +
        "                    \"method\": 8273,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                },\n" +
        "                {\n" +
        "                    \"short_name\": \"setHudColorTheme\",\n" +
        "                    \"method\": 8274,\n" +
        "                    \"fire_and_forget\" : true,\n" +
        "                    \"communication_type\" : \"TCP\"\n" +
        "                }\n" +
        "            ],\n" +
        "            \"eventgroups\":\n" +
        "            [\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"provided_instances\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"service\" : 165,\n" +
        "            \"major_version\" : 1,\n" +
        "            \"short_name\" : \"CarCtrl_1_SomeipProvided\",\n" +
        "            \"instance\" : 1,\n" +
        "            \"udp_port\" : 0,\n" +
        "            \"tcp_port\" : 30501,\n" +
        "            \"tcp_reuse\" : true,\n" +
        "            \"udp_reuse\" : false,\n" +
        "            \"method_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"event_attribute\" :\n" +
        "            [\n" +
        "            ],\n" +
        "            \"offer_time_reference\" : \"CarCtrl_SomeipServerServiceInstanceConfig\",\n" +
        "            \"ethernet_reference\" : \"IVIunicastEndpoint\",\n" +
        "            \"tcp_tls_flag\" : false,\n" +
        "            \"udp_tls_flag\" : false,\n" +
        "            \"provided_eventgroups\" :\n" +
        "            [\n" +
        "            ]\n" +
        "        }\n" +
        "    ],\n" +
        "    \"required_instances\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_offer_times\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"sd_time_name\" : \"CarCtrl_SomeipServerServiceInstanceConfig\",\n" +
        "            \"initial_delay_min\" : 10,\n" +
        "            \"initial_delay_max\" : 50,\n" +
        "            \"repetitions_base_delay\" : 100,\n" +
        "            \"repetitions_max\" : 5,\n" +
        "            \"ttl\" : 3,\n" +
        "            \"cyclic_offer_delay\" : 2000,\n" +
        "            \"request_response_delay_min\" : 10,\n" +
        "            \"request_response_delay_max\" : 20\n" +
        "        }\n" +
        "    ],\n" +
        "    \"sd_client_find_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_client_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"sd_server_subscribe_times\" :\n" +
        "    [\n" +
        "    ],\n" +
        "    \"network_endpoints\" :\n" +
        "    [\n" +
        "        {\n" +
        "            \"network_id\" : \"IVIunicastEndpoint\",\n" +
        "            \"ip_addr\" : \"************\",\n" +
        "            \"ip_addr_type\" : 4,\n" +
        "            \"subnet_mask\" : \"*************\",\n" +
        "            \"ttl\" : 3,\n" +
        "            \"multicast_ip_addr\" : \"**********\",\n" +
        "            \"multicast_port\" : 30490\n" +
        "        }\n" +
        "      ]\n" +
        "  }";

        ComTransportManager.instance().loadManifest("carctrl_1_0",manifest);
    }
}
