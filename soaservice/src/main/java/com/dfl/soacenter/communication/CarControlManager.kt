package com.dfl.soacenter.communication

import ara.ivi.carctrl.ics.Status
import ara.ivi.carctrl.ics.SwStatus
import ara.ivi.carctrl.ics.ThemeId
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.AIOTInfoBean
import com.dfl.soacenter.entity.AIOTListBean
import com.dfl.soacenter.entity.CarControlChildSeatStatus
import com.dfl.soacenter.entity.CarControlSetStatus
import com.dfl.soacenter.entity.CarControlSetStatus4SayHi
import com.dfl.soacenter.entity.CarInSoaSet
import com.dfl.soacenter.entity.CarOutSoaSet
import com.dfl.soacenter.entity.ChildSeatInfo
import com.dfl.soacenter.entity.ReqActionAIOTBean
import com.dfl.soacenter.entity.ReqActionAIOTBean2
import com.dfl.soacenter.entity.ReqChildSeatSetStatus
import com.dfl.soacenter.entity.ReqColorTheme
import com.dfl.soacenter.entity.ReqInsideLampTheme
import com.dfl.soacenter.entity.ReqLightBrightness
import com.dfl.soacenter.entity.ReqLightColor
import com.dfl.soacenter.entity.ReqLightEffect
import com.dfl.soacenter.entity.ReqLightStatus
import com.dfl.soacenter.entity.ReqNeckPillowAndLumbarActionBeanswitch
import com.dfl.soacenter.entity.ReqOpenPage
import com.dfl.soacenter.entity.ReqReadLampCustomSet
import com.dfl.soacenter.entity.ReqRefrigeratorActionBean
import com.dfl.soacenter.entity.ReqSeatMemoryMode
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResActionAIOTBean2
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.google.gson.Gson
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/1/19.
 *Describer: 车控 应用间通信管理
 */
object CarControlManager : BaseSoaManager() {

    private object ProtocolType {
        /**获取当前已激活的AIOT设备列表*/
        const val FIND_AIOT_LIST = 1002

        /**AIOT设备动作执行*/
        const val AIOT_ACTION = 1004

        /**获取AIOT信息 */
        const val GET_AIOT_INFO = 1005

        /**设置驾驶模式*/
        const val SET_DRIVING_MODE = 1043

        /**儿童座椅加热*/
        const val SET_CHILD_HEAT = 1063

        /**儿童座椅状态*/
        const val CHILD_SEAT_STATUS = 1077

        /**设置氛围灯颜色主题*/
        const val SET_COLOR_THEME = 1022

        /**设置氛围灯开关*/
        const val SET_LIGHT_STATUS = 40001

        /**设置氛围灯模式*/
        const val SET_LIGHT_MODE = 1070

        /**设置氛围灯亮度*/
        const val SET_LIGHT_BRIGHTNESS = 1071

        /**设置氛围灯颜色*/
        const val SET_LIGHT_COLOR = 1072

        /**设置灯效场景*/
        const val SET_LIGHTING_EFFECT = 1073

        /**设置雾灯*/
        const val SET_REARFOG_LAMP = 1039

        /**触发打招呼灯语（大灯）*/
        const val SAY_HI_HEAD_LIGHT = 1053

        /**车内SOA*/
        const val SET_CAR_IN_SOA = 1057

        /**车外SOA*/
        const val SET_CAR_OUT_SOA = 70004

        /**能量回收*/
        const val SET_ENERGY_RECOVERY_MODE = 1068

        /**设置按摩颈枕 温度*/
        const val SET_NECK_PILLOW = 1064

        /**设置按摩腰靠 温度*/
        const val SET_LUMBAR = 1065

        /**设置座椅记忆*/
        const val SET_SEAT_MEMORY_MODE = 1011

        /**设置冰箱*/
        const val SET_REFRIGERATOR = 1067

        /**打开车控页面*/
        const val OPEN_PAGE = 1001

        /**AIOT的所有设置*/
        const val AIOT_SET = 10018

        /**室内灯开关*/
        const val READ_LAMP_SWITCH = 90006

        /**室内灯主题*/
        const val READ_LAMP_MODE = 1082

        /**室内灯自定义*/
        const val READ_LAMP_CUSTOM_SET = 1112

        /**星环灯设置*/
        const val STAR_RING_LAMP_SWITCH = 1111

        /**无线充电开关设置 */
        const val SET_WIRELESS_CHARGER = 1041
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("CarControlManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_CAR_CONTROL)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_LIMITED_RECONNECT).setThreadPool(1, 1).build()
    }

    /**获取当前已激活的AIOT设备列表*/
    suspend fun findAIOTList(): AIOTListBean? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.FIND_AIOT_LIST,
                    data = null,
                    messageType = SoaConstants.MESSAGE_TYPE_DISPATCH
                )
            )
            val method = "findAIOTList"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<AIOTListBean>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, AIOTListBean::class.java
                    )
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })
        }
    }

    /**设置冰箱*/
    fun actionRefrigerator(req: ReqRefrigeratorActionBean) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_REFRIGERATOR, data = req
            )
        )
        val method = "actionRefrigerator"
        actionToSendMessage(json, method)
    }

    /**按摩颈枕温度执行*/
    fun actionNeckPillow(req: ReqNeckPillowAndLumbarActionBeanswitch) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_NECK_PILLOW, data = req
            )
        )
        val method = "actionNeckPillow"
        actionToSendMessage(json, method)
    }

    /**按摩靠枕温度执行*/
    fun actionLumbar(req: ReqNeckPillowAndLumbarActionBeanswitch) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LUMBAR, data = req
            )
        )
        val method = "actionLumbar"
        actionToSendMessage(json, method)
    }

    /**设置座椅记忆*/
    fun setSeatMemoryMode(req: ReqSeatMemoryMode) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_SEAT_MEMORY_MODE, data = req
            )
        )
        val method = "setSeatMemoryMode"
        actionToSendMessage(json, method)
    }

    /**AIOT设备动作执行*/
    fun actionAIOT2(req: ReqActionAIOTBean2) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.AIOT_SET, data = req
            )
        )
        val method = "actionAIOT2"
        actionToSendMessage(json, method)
    }

    /**查询 AIOT设备 */
    suspend fun getActionAIOT2(req: ReqActionAIOTBean2): ResActionAIOTBean2? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.AIOT_SET, data = req
                )
            )
            val method = "getActionAIOT2"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<ResActionAIOTBean2>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, ResActionAIOTBean2::class.java
                    )
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })
        }
    }


    /**AIOT设备动作执行*/
    fun actionAIOT(req: ReqActionAIOTBean) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.AIOT_ACTION, data = req
            )
        )
        val method = "actionAIOT"
        actionToSendMessage(json, method)
    }

    /**获取设备的信息*/
    suspend fun getAIOTInfo(req: AIOTInfoBean): AIOTInfoBean? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.GET_AIOT_INFO, data = req
                )
            )
            val method = "getAIOTInfo"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<AIOTInfoBean>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, AIOTInfoBean::class.java
                    )
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })
        }
    }

    /**设置驾驶模式*/
    fun setDrivingMode(req: CarControlSetStatus) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_DRIVING_MODE, data = req
            )
        )
        val method = "setDrivingMode"
        actionToSendMessage(json, method)
    }

    /**儿童座椅状态*/
    fun setChildStatus(operationType: Int, swStatus: SwStatus) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_CHILD_HEAT, data = ReqChildSeatSetStatus(operationType, swStatus.value)
            )
        )
        val method = "setChildStatus"
        actionToSendMessage(json, method)
    }

    /**儿童座椅检测儿童已离座、已入座*/
    suspend fun getChildSeatStatus(): ChildSeatInfo? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.CHILD_SEAT_STATUS, data = CarControlChildSeatStatus()
                )
            )
            val method = "getChildSeatStatus"

            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<ChildSeatInfo>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, ChildSeatInfo::class.java
                    )
                )
                continuation.resume(res.data)
            }, {
                continuation.resume(null)
            })

        }
    }


    /**设置氛围灯颜色主题*/
    fun setAtmosphereLampColorTheme(themeId: ThemeId) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_COLOR_THEME, data = ReqColorTheme(themeId.value - 1)
            )
        )
        val method = "setAtmosphereLampColorTheme"
        actionToSendMessage(json, method)
    }

    /**设置氛围灯开关*/
    fun setAtmosphereLightStatus(status: Status) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LIGHT_STATUS, data = ReqLightStatus(if (status == Status.ON) 1 else 2)
            )
        )
        val method = "setAtmosphereLightStatus"
        actionToSendMessage(json, method)
    }

    //设置氛围灯模式
    fun setAtmosphereLightMode(value: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LIGHT_MODE, data = CarControlSetStatus(value = value)
            )
        )
        val method = "setAtmosphereLightMode"
        actionToSendMessage(json, method)
    }

    /**设置氛围灯亮度 brightness:0-7*/
    fun setAtmosphereLightBrightness(brightness: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LIGHT_BRIGHTNESS, data = ReqLightBrightness(brightness.toInt())
            )
        )
        val method = "setAtmosphereLightBrightness"
        actionToSendMessage(json, method)
    }

    /**设置氛围灯颜色*/
    fun setAtmosphereLightColor(color: Long) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LIGHT_COLOR, data = ReqLightColor(color.toInt())
            )
        )
        val method = "setAtmosphereLightColor"
        actionToSendMessage(json, method)
    }

    /**设置场景灯效*/
    fun setAtmosphereLightSceneLightingEffectStatus(status: Int, value: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_LIGHTING_EFFECT, data = ReqLightEffect(status = status, value = value)
            )
        )
        val method = "setAtmosphereLightSceneLightingEffectStatus"
        actionToSendMessage(json, method)
    }

    /**设置雾灯*/
    fun setRearFogLampStatus(status: Status) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_REARFOG_LAMP, data = CarControlSetStatus(1, status.value)
            )
        )
        val method = "setRearFogLampStatus"
        actionToSendMessage(json, method)
    }

    /**触发打招呼灯语（大灯）*/
    fun setSayHiHeadlightSignalType(type: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SAY_HI_HEAD_LIGHT,
                data = CarControlSetStatus4SayHi(operationType = 1, value = type, type = 0)
            )
        )
        val method = "setSayHiHeadlightSignalType"
        actionToSendMessage(json, method)
    }

    /**播放车内SOA*/
    fun setCarInSoa(status: Int, type: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_CAR_IN_SOA, data = CarInSoaSet(status, type)
            )
        )
        val method = "setCarInSoa"
        actionToSendMessage(json, method)
    }

    /**播放车外SOA*/
    fun setCarOutSoa(status: Int, type: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_CAR_OUT_SOA, data = CarOutSoaSet(status, type)
            )
        )
        val method = "setCarOutSoa"
        actionToSendMessage(json, method)
    }

    /**设置室内灯主题*/
    fun setInsideLampTheme(theme: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.READ_LAMP_MODE, data = ReqInsideLampTheme(value = theme)
            )
        )
        val method = "setInsideLampTheme"
        actionToSendMessage(json, method)
    }

    /**星环灯设置*/
    fun setStarRingLightStatus(status: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.STAR_RING_LAMP_SWITCH, data = ReqInsideLampTheme(value = status)
            )
        )
        val method = "setStarRingLightStatus"
        actionToSendMessage(json, method)
    }

    /**设置室内灯自定义*/
    fun setReadLampCustomSet(
        brightness: Int, // 亮度brightness：[1,100]
        temp: Int, //色温temp：[1,100]
        zone: Int //0 ALL 1 前左  2 前右  3 后左  4 后右
    ) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.READ_LAMP_CUSTOM_SET,
                data = ReqReadLampCustomSet(brightness = brightness, temp = temp, zone = zone)
            )
        )
        val method = "setReadLampCustomSet"
        actionToSendMessage(json, method)
    }

    /**能量回收机制*/
    fun setEnergyRecoveryMode(mode: Byte) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_ENERGY_RECOVERY_MODE, data = CarControlSetStatus(1, mode.toInt())
            )
        )
        val method = "setEnergyRecoveryMode"
        actionToSendMessage(json, method)
    }

    /**
     * 打开车控页面
     * @param pageId 指定页面id
     * @param showLauncher 1显示0不显示launcher
     * @param selectId 自定义按键弹窗0可用
     */
    fun openPage(pageId: Int, showLauncher: Int = 1, selectId: Int = 0) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.OPEN_PAGE, data = ReqOpenPage(pageId)
            )
        )
        val method = "openPage"
        actionToSendMessage(json, method)
    }

    /**设置无线充电开关*/
    fun setWirelessCharger(status: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.SET_WIRELESS_CHARGER, data = CarControlSetStatus(1, status)
            )
        )
        val method = "setWirelessCharger"
        actionToSendMessage(json, method)
    }
}