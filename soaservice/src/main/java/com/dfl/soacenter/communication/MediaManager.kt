package com.dfl.soacenter.communication

import ara.ivi.media.ics.MusicPlayMode
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.IVIPlayMode
import com.dfl.soacenter.entity.IVIPlayTypeReq
import com.dfl.soacenter.entity.IVIPlayTypeRes
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/1/30.
 *Describer: 中间件 应用间通信管理
 */
object MediaManager : BaseSoaManager() {

    private object ProtocolType {
        const val IVI_PLAY = 20000
        const val IVI_PAUSE = 20001
        const val IVI_LAST = 20002
        const val IVI_NEXT = 20003
        const val IVI_PLAY_MODE = 20010
        const val IVI_GET_TYPE = 20006
        const val IVI_SWITCH_TYPE = 20007
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("IVIInfoManager")
        mClient = ConnectionClient.Builder(
            CommonUtils.getApp(), "com.dfl.mozart" //  ServiceConstant.Package.PACKAGE_mozart //
        ).setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT).setThreadPool(1, 1).build()
    }

    private suspend fun getMediaType(): IVIPlayTypeRes? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.IVI_GET_TYPE, data = IVIPlayTypeReq(type = 0)
                )
            )
            val method = "getMediaType"
            queryToSendMessage(json, method, { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<IVIPlayTypeRes>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, IVIPlayTypeRes::class.java
                    )
                )
                val bean = res.data
                if (bean?.isSuccess() == true) {
                    continuation.resume(bean)
                } else {
                    continuation.resume(null)
                }
            }, {
                continuation.resume(null)
            })
        }
    }

    /**切换内部音频
     * 1:QQ音乐
     * 2:蓝牙音乐
     * 3:USB音乐
     * 4:音乐MV
     * 5:KTV
     * 6:USB视频
     * 7:CarLink
     * 8:HiCar
     * 9:小程序
     * 10:CarPlay
     * 11:云听
     * 12:酷狗音乐
     * 13:酷我
     * 14:喜马拉雅
     * 15:AI智教
     * 16:腾讯视频
     * 17:Bilibili
     * 18:先锋云游戏
     * */
    fun switchMediaType(type: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.IVI_SWITCH_TYPE, data = IVIPlayTypeReq(type = 0)
            )
        )
        val method = "switchMediaType"
        actionToSendMessage(json, method)
    }

    /**播放 暂停*/
    @JvmStatic
    fun setMediaStatus(type: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            val value = async {
                getMediaType()
            }.await()

            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = if (type == 0) ProtocolType.IVI_PAUSE else ProtocolType.IVI_PLAY,
                    data = IVIPlayTypeReq(type = value?.type)
                )
            )
            val method = "setMediaStatus"
            actionToSendMessage(json, method)
        }

    }

    /**上一首 下一首*/
    fun switchMediaSource(status: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            val value = async {
                getMediaType()
            }.await()

            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = if (status == 1) ProtocolType.IVI_LAST else ProtocolType.IVI_NEXT,
                    data = IVIPlayTypeReq(type = value?.type)
                )
            )
            val method = "switchMediaSource"
            actionToSendMessage(json, method)
        }

    }

    /**播放模式*/
    private fun switchPlayMode(mode: Int) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.IVI_PLAY_MODE, data = IVIPlayMode(
                    mode
                )
            )
        )
        val method = "switchPlayMode"
        actionToSendMessage(json, method)
    }

    /**播放模式*/
    fun switchPlayMode(mode: MusicPlayMode) {
        switchPlayMode(
            when (mode) {
                MusicPlayMode.LIST_CYCLE -> 1
                MusicPlayMode.RANDOM_HEAD -> 3
                MusicPlayMode.SINGLE_CYCLE -> 2
            }
        )
    }
}