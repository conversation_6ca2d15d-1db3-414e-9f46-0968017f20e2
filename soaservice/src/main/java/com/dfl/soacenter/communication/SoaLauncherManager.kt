package com.dfl.soacenter.communication

import android.content.Intent
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.api.base.Constants
import com.dfl.api.da.setting.ISetting
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.ReqOpenPage
import com.dfl.soacenter.entity.RequestSoaBaseBean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/26
 * desc : soa进程打开应用
 * version: 1.0
 */
object SoaLauncherManager : BaseSoaManager() {
    object ProtocolType {

        /**
         * 打开屏保模式
         */
        const val SHOW_SCREENSAVER = 4002

        /**
         * 关闭屏保模式
         */
        const val CLOSE_SCREENSAVER = 4004

    }

    private const val PACKAGE_NAME = "com.dfl.systemui"

    /**
     * 打开应用滚轮值
     * 车主指南:1 , 蓝牙电话:2 ,   天气:3,  消息中心:4, 能源中心:5,  小尼空间:6, 车辆控制:7 ,空调:8,  导航:9 ,
     * 在线音乐（QQ音乐）:10,   酷狗唱唱:11,  蓝牙音乐:12,  USB音视频:13,   系统设置:14, 声音空间:15,  应用中心:16,
     * 爱奇艺:17,  优酷:18,  哔哩哔哩:19,  云听:20,  喜马拉雅:21,  宝宝巴士:22,  口袋故事 :23 , 凤凰FM:24,   乐听头条:25
     */
    private val appValue2PackageNameMap: Map<Int, String> = mapOf(
        13 to CommonUtils.getString(R.string.scene_text_app_open_package_name_1),
        6 to CommonUtils.getString(R.string.scene_text_app_open_package_name_2),
        3 to CommonUtils.getString(R.string.scene_text_app_open_package_name_3),
        7 to CommonUtils.getString(R.string.scene_text_app_open_package_name_4),
        8 to CommonUtils.getString(R.string.scene_text_app_open_package_name_5),
        5 to CommonUtils.getString(R.string.scene_text_app_open_package_name_6),
        2 to CommonUtils.getString(R.string.scene_text_app_open_package_name_7),
        14 to CommonUtils.getString(R.string.scene_text_app_open_package_name_8),
        1 to CommonUtils.getString(R.string.scene_text_app_open_package_name_9),
        16 to CommonUtils.getString(R.string.scene_text_app_open_package_name_10),
        4 to CommonUtils.getString(R.string.scene_text_app_open_package_name_11),
        15 to CommonUtils.getString(R.string.scene_text_app_open_package_name_12),
    )

    /**customapi da系统类 用于实现熄屏*/
    private var mISetting: ISetting? = null
    private val mPackageManager = CommonUtils.getApp().packageManager
    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("SoaLauncherManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), PACKAGE_NAME)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
        CoroutineScope(Dispatchers.IO).launch {
            initCustomApi()
        }
    }

    private suspend fun initCustomApi(): Boolean = initCustomApi(
        Constants.DFL_DA_SETTING_SETTING_SERVICE,
        mISetting,
        { if (it is ISetting) it else null },
        { mISetting = it }
    )

    /**
     * 打开应用,如果已经启动apk，则直接将apk从后台调到前台运行，如果未启动apk，则重新启动
     * 打开空调需要车控应用间通讯
     * @param appValue 传入的应用InputArgValue,找不到默认打开车主指南
     * @return 打开是否成功,查不到packageName或对应的Intent.ACTION_MAIN返回false
     */
    fun openAPP(appValue: Int): Boolean {
        //打开空调
        if (appValue == 8) {
            CarControlManager.openPage(ReqOpenPage.AIR_CONDITION)
            return true
        }
        //获取应用inputArg包名对应
        var packageName = appValue2PackageNameMap[appValue] ?: ""
        //打开蓝牙电话
        if (appValue == 2) {
            packageName = if (ConfigManager.isHase()) {
                "com.hsae.bluetoothdial"
            } else {
                "com.dfl.btphone"
            }
        }
        //根据包名查询启动页intent,首先查询Intent.CATEGORY_INFO,其次查询Intent.CATEGORY_LAUNCHER
        val intent = mPackageManager.getLaunchIntentForPackage(packageName)
        if (packageName.isEmpty() || intent == null) {
            CommonLogUtils.logE(TAG, "打开失败 appValue:$appValue,packageName:$packageName,intent:$intent")
            return false
        } else {
            //添加冷启动flags
            intent.flags = Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED or Intent.FLAG_ACTIVITY_NEW_TASK
            CommonLogUtils.logI(TAG, "打开成功 appValue:$appValue,packageName:$packageName,intent:$intent")
            CommonUtils.getApp().startActivity(intent)
        }
        return true
    }

    /**
     * 显示屏保
     * @param status 打开 : 0,关闭 : 1
     */
    fun setScreenStatus(status: Int) {
        val protocolId = if (status == 0) {
            ProtocolType.SHOW_SCREENSAVER
        } else {
            ProtocolType.CLOSE_SCREENSAVER
        }
        val bean = RequestSoaBaseBean(protocolId = protocolId, data = null)
        queryToSendMessage(GsonUtils.toJson(bean), "setScreenStatus") {}
    }

    /**
     * 实现熄屏
     * @param type 0睡眠 1中控 2副驾
     * @param status 0熄屏 1亮屏
     */
    fun setIVIScreenOnOffStatus(type: Int, status: Int) {
        CustomApiUtils.requestCustomApi({
            if (initCustomApi()) {
                CommonLogUtils.logI(TAG, "setIVIScreenOnOffStatus ISetting=${mISetting}")
                mISetting?.setIVIScreenOnOffStatus(type, status)
            }
        }, { e ->
            e.printStackTrace()
            CommonLogUtils.logE(TAG, "setIVIScreenOnOffStatus error,  $e")
        })

    }
}