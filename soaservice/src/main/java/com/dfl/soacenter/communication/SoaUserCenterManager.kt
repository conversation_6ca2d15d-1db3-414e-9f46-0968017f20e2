package com.dfl.soacenter.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.UserCenterRequestAddSchedule
import com.dfl.soacenter.entity.UserCenterRequestSchedule
import com.dfl.soacenter.entity.UserCenterResponse
import com.dfl.soacenter.entity.UserCenterResponseSchedule
import com.google.gson.JsonParseException
import org.json.JSONException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/02/21
 * desc : soa进程中与个人中心app应用间通讯
 * version: 1.0
 */
object SoaUserCenterManager : BaseSoaManager() {

    private object ProtocolType {
        /**
         * 查询时间段行程
         */
        const val QUERY_SCHEDULE = 1027


        const val ADD_SCHEDULE = 1031
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("SoaUserCenterManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_USER_CENTER)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    fun addSchedule() {
        val bean = RequestSoaBaseBean<UserCenterRequestAddSchedule>(
            protocolId = ProtocolType.ADD_SCHEDULE,
            data = UserCenterRequestAddSchedule()
        )
        val bean2 = RequestSoaBaseBean(
            protocolId = ProtocolType.ADD_SCHEDULE,
            data = UserCenterRequestAddSchedule(
                departureTime = "2024-02-21 18:03:59",
                arriveTime = "2024-02-21 19:30:00",
                note = "幼儿园接孩子"
            )
        )
        queryToSendMessage(GsonUtils.toJson(bean), "addSchedule") {}
        queryToSendMessage(GsonUtils.toJson(bean2), "addSchedule") {}
    }

    suspend fun querySchedule(): UserCenterResponseSchedule? {
        //播报开始时间
        val startTime = System.currentTimeMillis()
        //当天的23:59:59
        val endTime = Calendar.getInstance(Locale.CHINA)
        endTime.set(Calendar.HOUR_OF_DAY, 23)
        endTime.set(Calendar.MINUTE, 59)
        endTime.set(Calendar.SECOND, 59)
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA)
        val reqData = UserCenterRequestSchedule(dateFormat.format(Date(startTime)), dateFormat.format(endTime.time))
        val bean = RequestSoaBaseBean(protocolId = ProtocolType.QUERY_SCHEDULE, data = reqData)
        return suspendCoroutine {
            queryToSendMessage(GsonUtils.toJson(bean), "querySchedule") { json ->
                try {
                    val res = GsonUtils.fromJson<UserCenterResponse<UserCenterResponseSchedule>>(
                        json,
                        GsonUtils.type(UserCenterResponse::class.java, UserCenterResponseSchedule::class.java)
                    )
                    if (res.result == UserCenterResponse.SUCCESS) {
                        it.resume(res.data)
                    } else {
                        it.resume(null)
                    }
                } catch (e: Exception) {
                    if (e is JsonParseException || e is JSONException) {
                        CommonLogUtils.logE(TAG, "querySchedule json转换失败,json=$json,e=$e")
                    }
                    it.resume(null)
                    e.printStackTrace()
                }
            }

        }

    }

}