package com.dfl.soacenter.entity

import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.soacenter.SoaConstants

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/01/22
 * desc : 系统设置通信请求响应
 * version: 1.0
 */

/**
 * @property themeType 0 自动 1 白天 2 黑夜
 * @constructor 设置日夜模式请求data
 */
data class ThemeDataBean(
    val themeType: Int = 0
) {
    companion object {
        const val AUTO = 0
        const val DAY = 1
        const val NIGHT = 2
    }
}

/**
 * @property type 1通话 2语音 3导航 4多媒体 5蓝牙音乐
 * @property value 范围:0-40
 * @constructor 设置音量
 */
data class VolumeDataBean(
    val type: Int = VOLUME_CALL,
    val value: Int = 0
) {
    companion object {
        /**
         * 通话音量
         */
        const val VOLUME_CALL = 1

        /**
         * 语音音量
         */
        const val VOLUME_ASSISTANT = 2

        /**
         * 导航音量
         */
        const val VOLUME_NAVI = 3

        /**
         * 多媒体音量
         */
        const val VOLUME_MEDIA = 4

        /**
         * 蓝牙音量
         */
        const val VOLUME_BLUETOOTH = 5

        /**
         * 车外音量
         */
        const val VOLUME_CAR_OUT = 6
    }
}

/**
 * @property value 特殊值 0关,未连接 1开,连接
 * @constructor 设置禁音,中控屏亮度,壁纸,蓝牙,热点,wifi,移动网络
 */
data class ValueDataBean(
    val value: Int = 0
) {
    companion object {
        const val OFF_OR_UNCONNECTED = 0
    }
}


data class SystemSettingResponseBean(
    /**具体的协议ID*/
    val protocolId: Int,
    /**发送方标志 本应用包名*/
    val requestAuthor: String = GeneralInfoUtils.getRequestAuthor(),
    /**请求响应码*/
    val requestCode: String = GeneralInfoUtils.getRequestCode(),
    /**消息类型 : request：请求的消息 response：响应的消息 dispatch：主动透出的消息*/
    val messageType: String = SoaConstants.MESSAGE_TYPE_REQUEST,
    /**请求的应用版本*/
    val versionName: String = GeneralInfoUtils.getVersionName(),
    /**状态码*/
    val statusCode: String = STATUS_CODE_FAIL,
    /**响应体*/
    val data: ValueDataBean? = null
) {
    companion object {
        /**
         * 响应成功
         */
        const val STATUS_CODE_SUCCESS: String = "10000"

        /**
         * 响应失败
         */
        const val STATUS_CODE_FAIL: String = "10001"
    }
}