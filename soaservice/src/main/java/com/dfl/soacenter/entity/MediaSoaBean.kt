package com.dfl.soacenter.entity

/**
 *Created by 钟文祥 on 2024/1/15.
 *Describer:
 */
class MediaSoaBean {}

/**请求指定歌手*/
data class MediaSingerBean(
    val singer: String? //歌手id
)


/**请求指定歌曲*/
data class MediaSongNameBean(
    val name: String //歌曲id
)

/**请求歌单实体*/
data class MediaSongTypeBean(
    val songType: Int
    //歌单类型（1:流行 2:中国风 3:民谣 4:摇滚 5:轻音乐 6:电子 7:爵士 8:乡村 9:快乐
    // 10:伤感 11:安静 12:励志 13:治愈 14:思念 15:甜蜜 16:寂寞 17:宣泄 18:00年代
    // 19:90年代 20:80年代 21:70年代 101:最近播放(歌曲歌单)
    // 102:我喜欢(歌曲) 103:下载(歌曲) 104:每日推荐 105:新歌速递 106:热门儿歌）
)

/**搜索关键字*/
data class SearchKeyBean(
    var key: String?
)

/**歌和歌手的实体*/
data class SongSingerInfo(
    var song_id: Long? = 0, //歌id
    var song_name: String? = "",//歌名称
    var singer_id: Long?,//歌手id
    var singer_name: String?,//歌手名称
    var singer_pic: String? //歌手图片
)

/**歌手实体*/
data class SingerInfo( //需要转为SongSingerInfo
    var id: Long?, var name: String?, var singerPic: String?
)

/**qq音乐的播放模式*/
data class QQPlayMode(
    var type: Int //播放模式0：顺序，1：单曲，2：随机
)

/**中间件 播放模式*/
data class IVIPlayMode(
    var type: Int //播放模式 1：列表循环 2：单曲循环 3：随机播放 4顺序播放
)

/**中间件 通用请求类*/
data class IVIPlayTypeReq(
    var type: Int? = 0 //获取type是 type表示选择音频数据，0只需要音频数据 1都需要（音频、视频），未设置默认返回音频数据

    //控制动作时 1:QQ音乐 2:蓝牙音乐 /3:USB音乐 4:音乐MV 5:KTV 6:USB视频
    //7:CarLink 8:HiCar 9:小程序 10:CarPlay 11:云听 12:酷狗音乐 13:酷我
    //14:喜马拉雅 15:AI智教 16:腾讯视频 17:Bilibili 18:先锋云游戏
)

data class IVIPlayTypeRes(
    var resultCode: Int, //10000:已执行   10001:未找到资源
    var type: Int,
    //控制动作时 1:QQ音乐 2:蓝牙音乐 /3:USB音乐 4:音乐MV 5:KTV 6:USB视频
    //7:CarLink 8:HiCar 9:小程序 10:CarPlay 11:云听 12:酷狗音乐 13:酷我
    //14:喜马拉雅 15:AI智教 16:腾讯视频 17:Bilibili 18:先锋云游戏
    var packageName: String, //包名
//    var tag: String, //服务端tag
    var page: String, //页面全类名
    var appName: String, //应用名称
    var appLogo: String //应用logo
) {
    fun isSuccess(): Boolean {
        return resultCode == 10000
    }
}