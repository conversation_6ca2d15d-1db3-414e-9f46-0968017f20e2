package com.dfl.soacenter.soaimpl

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.annotation.RequiresApi
import ara.ivi.oms.ics.oms.v1.skeleton.OMSSkeleton
import ara.ivi.oms.ics.oms.v1.skeleton.checkExactPassengerGender
import ara.ivi.oms.ics.oms.v1.skeleton.checkExactSeatPassengerBehavior
import ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistChild
import ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistPassenger
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.OMSManager
import com.dfl.soacenter.entity.OmsPositionSendCCM
import ics.OMS_notifyExactSeatPassengerBehavior_param_t
import ics.OMS_notifyIfExactPassengerGender_param_t
import ics.OMS_notifyIfExactSeatExistChild_param_t
import ics.OMS_notifyIfExactSeatExistPassenger_param_t
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 *Created by 钟文祥 on 2024/12/30.
 *Describer: oms 的 soa
 */
class OMSSWCImpl(instanceId: String, loop: Looper) : OMSSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("OMSSWCImpl")
    }


    //发送消息给ccm的队列，要求每隔一秒才执行
    private var notifyHandler: Handler? = null

    /** 发送场景给ccm执行*/
    fun notifyToCCM(item: OmsPositionSendCCM) {
        if (notifyHandler == null) {
            notifyHandler = object : Handler(Looper.getMainLooper()) {
                @RequiresApi(Build.VERSION_CODES.TIRAMISU)
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        1 -> {
                            val value = msg.data.getSerializable(
                                "data"
                            ) as OMS_notifyIfExactSeatExistChild_param_t

                            notifyIfExactSeatExistChild.Send(value)
                            Thread.sleep(1000)  //ccm处理时间是几十ms级别的，1s比较稳妥
                        }

                        2 -> {
                            val value = msg.data.getSerializable(
                                "data"
                            ) as OMS_notifyIfExactSeatExistPassenger_param_t

                            notifyIfExactSeatExistPassenger.Send(value)
                            Thread.sleep(1000)  //ccm处理时间是几十ms级别的，1s比较稳妥
                        }

                        3 -> {
                            val value = msg.data.getSerializable(
                                "data"
                            ) as OMS_notifyIfExactPassengerGender_param_t
                            notifyIfExactPassengerGender.Send(value)
                            Thread.sleep(1000)  //ccm处理时间是几十ms级别的，1s比较稳妥
                        }

                        4 -> {
                            val value = msg.data.getSerializable(
                                "data"
                            ) as OMS_notifyExactSeatPassengerBehavior_param_t
                            notifyExactSeatPassengerBehavior.Send(value)
                            Thread.sleep(1000)  //ccm处理时间是几十ms级别的，1s比较稳妥
                        }
                    }
                }
            }
        }

        // 使用Message发送任务
        val msg = Message.obtain()
        msg.what = item.what // 消息类型，自定义
        val bundle = Bundle()
        if (item.what == 1) { //儿童检测
            bundle.putSerializable(
                "data", OMS_notifyIfExactSeatExistChild_param_t(
                    item.position.toByte(), item.scValue.toByte()
                )
            ) // 添加数据到Bundle中
        } else if (item.what == 2) { //乘客分布检测
            bundle.putSerializable(
                "data", OMS_notifyIfExactSeatExistPassenger_param_t(
                    item.position.toByte(), item.scValue.toByte()
                )
            )
        } else if (item.what == 3) { //性别检测
            bundle.putSerializable(
                "data", OMS_notifyIfExactPassengerGender_param_t(
                    item.position.toByte(), item.scValue.toByte()
                )
            )
        } else if (item.what == 4) {//行为检测
            bundle.putSerializable(
                "data", OMS_notifyExactSeatPassengerBehavior_param_t(
                    item.position.toByte(), item.scValue.toByte()
                )
            )
        }
        msg.data = bundle
        notifyHandler!!.sendMessage(msg)// 将Bundle设置到Message中

    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG, "setStateChangedCallback==>$i"
            )
        }

        //具体位置是否有儿童，状态条件
        checkIfExactSeatExistChild.RegMethodProcess(object :
            checkIfExactSeatExistChild.checkIfExactSeatExistChildHandle {
            override fun OnCall(
                position: Byte?, scStatus: Byte?
            ): Future<checkIfExactSeatExistChild.checkIfExactSeatExistChildOutput> {
                CommonLogUtils.logI(TAG, "OnCall->checkIfExactSeatExistChild: position->$position,scStatus->$scStatus")
                //返回 int status: 0 该位置无儿童 1 该位置有儿童 -1异常情况
                val result = Promise<checkIfExactSeatExistChild.checkIfExactSeatExistChildOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        OMSManager.getChildPositionList()
                    }.await()
                    if (value?.isSuccess() == true) {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistChild.checkIfExactSeatExistChildOutput(
                                value.checkStatus(position!!.toInt()).toByte()
                            )
                        )
                    } else {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistChild.checkIfExactSeatExistChildOutput(
                                -1
                            )
                        )
                    }
                }
                return result
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->checkIfExactSeatExistChild:${errorCode?.message}")
            }
        })

        //具体位置是否有乘客，状态条件
        checkIfExactSeatExistPassenger.RegMethodProcess(object :
            checkIfExactSeatExistPassenger.checkIfExactSeatExistPassengerHandle {
            override fun OnCall(
                position: Byte?, scStatus: Byte?
            ): Future<checkIfExactSeatExistPassenger.checkIfExactSeatExistPassengerOutput> {
                CommonLogUtils.logI(
                    TAG,
                    "OnCall->checkIfExactSeatExistPassenger: position->$position,scStatus->$scStatus"
                )
                //返回 int status: 0 该位置无乘客 1 该位置有乘客 -1异常情况
                val result = Promise<checkIfExactSeatExistPassenger.checkIfExactSeatExistPassengerOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        OMSManager.getPassengerDistribution()
                    }.await()
                    if (value?.isSuccess() == true) {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistPassenger.checkIfExactSeatExistPassengerOutput(
                                value.checkStatus(position!!.toInt()).toByte()
                            )
                        )
                    } else {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkIfExactSeatExistPassenger.checkIfExactSeatExistPassengerOutput(
                                -1
                            )
                        )
                    }
                }
                return result
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->checkIfExactSeatExistPassenger:${errorCode?.message}")
            }
        })
        //具体位置的乘客性别，状态条件
        checkExactPassengerGender.RegMethodProcess(object : checkExactPassengerGender.checkExactPassengerGenderHandle {
            override fun OnCall(
                position: Byte?, scGender: Byte?
            ): Future<checkExactPassengerGender.checkExactPassengerGenderOutput> {
                CommonLogUtils.logI(TAG, "OnCall->checkExactPassengerGender: position->$position,scGender->$scGender")
                //返回 int gender: 0 男性 1 女性 -1异常情况
                val result = Promise<checkExactPassengerGender.checkExactPassengerGenderOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        OMSManager.getPassengerGenderAge()
                    }.await()
                    if (value?.isSuccess() == true) {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkExactPassengerGender.checkExactPassengerGenderOutput(
                                value.checkStatus(position!!.toInt())?.toByte()
                            )
                        )
                    } else {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkExactPassengerGender.checkExactPassengerGenderOutput(
                                -1
                            )
                        )
                    }
                }
                return result
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->checkExactPassengerGender:${errorCode?.message}")
            }
        })

        //具体位置的乘客行为，状态条件
        checkExactSeatPassengerBehavior.RegMethodProcess(object :
            checkExactSeatPassengerBehavior.checkExactSeatPassengerBehaviorHandle {
            override fun OnCall(
                position: Byte?, scBehavior: Byte?
            ): Future<checkExactSeatPassengerBehavior.checkExactSeatPassengerBehaviorOutput> {
                CommonLogUtils.logI(
                    TAG,
                    "OnCall->checkExactSeatPassengerBehavior: position->$position,scBehavior->$scBehavior"
                )
                //返回 int behavior: 0 打电话  1 未打电话  2 抽烟  3 未抽烟 4 喝水  5 未喝水  -1异常情况"
                val result = Promise<checkExactSeatPassengerBehavior.checkExactSeatPassengerBehaviorOutput>()
                CoroutineScope(Dispatchers.IO).launch {
                    val value = async {
                        OMSManager.getPassengerBehaviourList()
                    }.await()
                    if (value?.isSuccess() == true) {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkExactSeatPassengerBehavior.checkExactSeatPassengerBehaviorOutput(
                                value.checkStatus(position!!.toInt(), scBehavior!!.toInt()).toByte()
                            )
                        )
                    } else {
                        result.setValue(
                            ara.ivi.oms.ics.oms.v1.skeleton.checkExactSeatPassengerBehavior.checkExactSeatPassengerBehaviorOutput(
                                -1
                            )
                        )
                    }
                }
                return result
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->checkExactSeatPassengerBehavior:${errorCode?.message}")

            }
        })
    }
}