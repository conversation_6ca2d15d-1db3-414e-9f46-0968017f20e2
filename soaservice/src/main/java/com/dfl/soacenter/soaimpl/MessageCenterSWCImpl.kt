package com.dfl.soacenter.soaimpl

import android.os.Looper
import ara.ivi.msgcenter.ics.msgcenter.v1.skeleton.MsgCenterSkeleton
import ara.ivi.msgcenter.ics.msgcenter.v1.skeleton.showMessage
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.R
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.MessageCenterManager
import ics.com.runtime.ErrorCode

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 消息中心的SOA
 * version:1.0
 */
class MessageCenterSWCImpl(instanceId: String, loop: Looper) : MsgCenterSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("MessageCenterSWCImpl")
    }

    /**
     * 注册监听
     */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG,
                "setStateChangedCallback==>$i"
            )
        }
        showMessage.RegMethodProcess(object : showMessage.showMessageHandle {
            override fun OnCall(message: String) {
                CommonLogUtils.logI(TAG, "OnCall->showMessage:$message")
                MessageCenterManager.sendMessage(
                    CommonUtils.getApp()
                        .getString(R.string.scene_soa_text_action_text_short_prompt), message
                )

            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->showMessage:${errorCode.message}")
            }

        })
    }
}