package com.dfl.soacenter.soaimpl

import android.content.Context
import android.os.Looper
import ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.*
import ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSingerSong.playSpecifiedSingerSongHandle
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.communication.QQMusicManager
import ics.com.runtime.ErrorCode
import ics.com.runtime.utils.concurrent.Future
import ics.com.runtime.utils.concurrent.Promise

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 多媒体qq音乐的SOA
 * version:1.0
 * update : 钟文祥
 */
class QQMusicSWCImpl(var context: Context, instanceId: String, loop: Looper) : QQMusicSkeleton(instanceId, loop) {
    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("QQMusicSWCImpl")
    }

    /** 注册监听 */
    fun registerCallBack() {
        SetStateChangedCallback { i: Int ->
            CommonLogUtils.logI(
                TAG, "setStateChangedCallback==>$i"
            )
        }
        //QQ音乐播放特定心情歌单
        playSpecificMoodPlaylists.RegMethodProcess(object : playSpecificMoodPlaylists.playSpecificMoodPlaylistsHandle {
            override fun OnCall(mood: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->playSpecificMoodPlaylists:$mood")
                //1, 2, 3, 4, 5, 6, 7, 8, 9
                //对应9:快乐 10:伤感 11:安静 12:励志 13:治愈 14:思念 15:甜蜜 16:寂寞 17:宣泄
                QQMusicManager.playLists(mood + 8)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playSpecificMoodPlaylists:${errorCode.message}")
            }

        })
        //QQ音乐播放特定年代歌单
        playSpecificEraPlaylists.RegMethodProcess(object : playSpecificEraPlaylists.playSpecificEraPlaylistsHandle {
            override fun OnCall(era: Byte) {
                CommonLogUtils.logI(TAG, "OnCall->playSpecificEraPlaylists:$era")
                //1, 2, 3, 4
                //对应 18:00年代  19:90年代 20:80年代 21:70年代
                QQMusicManager.playLists(era + 17)
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playSpecificEraPlaylists:${errorCode.message}")
            }

        })
        //QQ音乐播放特定曲风歌单
        playGenreSongList.RegMethodProcess(object : playGenreSongList.playGenreSongListHandle {
            override fun OnCall(genre: Byte): Future<playGenreSongList.playGenreSongListOutput> {
                CommonLogUtils.logI(TAG, "OnCall->playGenreSongList:$genre")
                //1, 2, 3, 4, 5, 6, 7, 8
                //对应1:流行 2:中国风 3:民谣 4:摇滚 5:轻音乐 6:电子 7:爵士 8:乡村
                QQMusicManager.playLists(genre + 0)
                val result = Promise<playGenreSongList.playGenreSongListOutput>()
                result.setValue(
                    ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playGenreSongList.playGenreSongListOutput(
                        1
                    )
                )
                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playGenreSongList:${errorCode.message}")
            }
        })
        //QQ音乐播放指定歌手的歌曲
        playSpecifiedSingerSong.RegMethodProcess(object : playSpecifiedSingerSongHandle {
            override fun OnCall(
                singer: String?, song: String?
            ): Future<playSpecifiedSingerSong.playSpecifiedSingerSongOutput> {
                CommonLogUtils.logI(
                    TAG, "OnCall->playSpecifiedSingerSong:singer=$singer"
                )
                QQMusicManager.playSingerSong(singer)
                val result = Promise<playSpecifiedSingerSong.playSpecifiedSingerSongOutput>()
                result.setValue(
                    ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSingerSong.playSpecifiedSingerSongOutput(
                        1
                    )
                )
                return result
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->playSpecifiedSingerSong:${errorCode?.message}")
            }
        })
        //QQ音乐播放指定歌曲
        playSpecifiedSong.RegMethodProcess(object : playSpecifiedSong.playSpecifiedSongHandle {
            override fun OnCall(name: String): Future<playSpecifiedSong.playSpecifiedSongOutput> {
                CommonLogUtils.logI(TAG, "OnCall->playSpecifiedSong:$name")
                QQMusicManager.playSongName(name)
                val result = Promise<playSpecifiedSong.playSpecifiedSongOutput>()
                result.setValue(
                    ara.ivi.qqmusic.ics.qqmusic.v1.skeleton.playSpecifiedSong.playSpecifiedSongOutput(
                        1
                    )
                )
                return result
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playSpecifiedSong:${errorCode.message}")
            }

        })

        //QQ音乐播放指定歌单
        playSpecifiedSongList.RegMethodProcess(object : playSpecifiedSongList.playSpecifiedSongListHandle {
            override fun OnCall(type: Byte): Future<Void> {
                CommonLogUtils.logI(TAG, "OnCall->playSpecifiedSongList:$type")
                //1, 2, 3, 4, 5, 6
                //101:最近播放(歌曲歌单)102:我喜欢(歌曲) 103:下载(歌曲) 104:每日推荐 105:新歌速递 106:热门儿歌
                QQMusicManager.playLists(type + 100)
                return Promise()
            }

            override fun OnError(errorCode: ErrorCode) {
                CommonLogUtils.logE(TAG, "OnError->playSpecifiedSongList:${errorCode.message}")
            }
        })

        //播放内置音源 oem能力
        play.RegMethodProcess(object : play.playHandle {
            override fun OnCall(soundSource: Byte?): Future<Void> {
                CommonLogUtils.logI(TAG, "OnCall->play:$soundSource")
                //1 音乐 2 听书 3 电台 4 USB音乐 5 蓝牙 6 KTV 7 USB视频
                return Promise<Void>()
            }

            override fun OnError(errorCode: ErrorCode?) {
                CommonLogUtils.logE(TAG, "OnError->QQMusicPlay:${errorCode?.message}")
            }

        })
    }


}