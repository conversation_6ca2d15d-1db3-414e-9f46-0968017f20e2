package com.dfl.soacenter

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.HandlerThread
import android.os.IBinder
import android.provider.Settings
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.dfl.android.common.customapi.AppCommonManager
import com.dfl.android.common.customapi.AppCommonManagerCallback
import com.dfl.android.common.customapi.constant.ApiConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.communication.*
import com.dfl.soacenter.entity.MyScenePosition
import com.dfl.soacenter.entity.OmsPositionSendCCM
import com.dfl.soacenter.soaimpl.*
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/19
 * desc: 所有soa服务的中心服务，用以初始化所有的soa
 * version:1.0
 */
class SoaCenterService : LifecycleService() {
    private var mWeatherHandlerThread: HandlerThread? = null
    private var mWeatherSWCImpl: WeatherSWCImpl? = null
    private var mIviInfoHandlerThread: HandlerThread? = null
    private var mIviInfoSWCImpl: IvIInfoSWCImpl? = null
    private var mBltMusicHandlerThread: HandlerThread? = null
    private var mCarControlHandlerThread: HandlerThread? = null
    private var mCarControlSWCImpl: CarControlSWCImpl? = null
    private var mEnergyCenterHandlerThread: HandlerThread? = null
    private var mEnergyCenterSWCImpl: EnergyCenterSWCImpl? = null
    private var mKtvHandlerThread: HandlerThread? = null
    private var mKtvSWCImpl: KtvSWCImpl? = null
    private var mLauncherHandlerThread: HandlerThread? = null
    private var mLauncherSWCImpl: LauncherSWCImpl? = null
    private var mMediaHandlerThread: HandlerThread? = null
    private var mMediaSWCImpl: MediaSWCImpl? = null
    private var mMessageCenterHandlerThread: HandlerThread? = null
    private var mMessageCenterSWCImpl: MessageCenterSWCImpl? = null
    private var mNaviHandlerThread: HandlerThread? = null
    private var mNaviSWCImpl: NaviSWCImpl? = null
    private var mSoundEffectHandlerThread: HandlerThread? = null
    private var mSoundEffectSWCImpl: SoundEffectSWCImpl? = null
    private var mSystemSettingHandlerThread: HandlerThread? = null
    private var mSystemSettingSWCImpl: SystemSettingSWCImpl? = null
    private var mUsbMediaHandlerThread: HandlerThread? = null
    private var mUserCenterHandlerThread: HandlerThread? = null
    private var mUserCenterSWCImpl: UserCenterSWCImpl? = null
    private var mVoiceHandlerThread: HandlerThread? = null
    private var mVoiceSWCImpl: VoiceSWCImpl? = null
    private var mQQMusicHandlerThread: HandlerThread? = null
    private var mQQMusicSWCImpl: QQMusicSWCImpl? = null
    private var mOMSHandlerThread: HandlerThread? = null
    private var mOMSSWCImpl: OMSSWCImpl? = null

    private var mDayNightContentObserver = DayNightContentObserver(null, this)

    /**重新拉起服务次数*/
    private var reStartCount = 1

    /**上一次拉起服务时间*/
    private var lastTime = 0L

    /**规定计算重新拉起服务次数的时间*/
    private val MIN_TIME = 1000

    /**用于绑定服务SceneWidgetService保活，aidl接口*/
    private val binder = object : KeepServiceAliveAidlInterface.Stub() {
        override fun getProName(): String {
            return "SoaCenterService"
        }
    }

    /**绑定服务SceneWidgetService的Connection*/
    private val keepServiceAliveConnection = object : ServiceConnection {
        override fun onServiceConnected(arg0: ComponentName, arg1: IBinder) {
            CommonLogUtils.logI(TAG, "SceneWidgetService bind成功")
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            CommonLogUtils.logE(TAG, "SceneWidgetService 异常断开，重新拉起${reStartCount}次")
            val curTime = System.currentTimeMillis()
            val diff = curTime - lastTime
            lastTime = curTime
            //一秒内重连50次不再拉起服务
            if (lastTime == 0L || diff < MIN_TIME) {
                reStartCount++
            } else {
                reStartCount = 0
            }
            if (reStartCount >= 50) {
                return
            }
            // 连接的服务被杀死了，出现了异常断开了，重新拉起服务
            startConnectService()
            // 重新绑定
            bindConnectService()
        }
    }

    companion object {
        private const val TAG = SoaConstants.SOA_TAG.plus("SoaCenterService")

        /**
         * 处理天气回调的线程名称
         */
        private const val HANDLER_WEATHER = "WEATHER_SWC"

        /**
         * 处理智慧场景信息回调的线程名称
         */
        private const val HANDLER_IVI_INFO = "IVI_INFO_SWC"

        /**
         * 处理蓝牙音乐信息回调的线程名称
         */
        private const val HANDLER_BLT_MUSIC = "BLT_MUSIC_SWC"

        /**
         * 处理车控信息回调的线程名称
         */
        private const val HANDLER_CAR_CONTROL = "CAR_CONTROL_SWC"

        /**
         * 处理能源中心信息回调的线程名称
         */
        private const val HANDLER_ENERGY_CENTER = "ENERGY_CENTER_SWC"

        /**
         * 处理KTV信息回调的线程名称
         */
        private const val HANDLER_KTV = "KTV_SWC"

        /**
         * 处理智慧场景信息回调的线程名称
         */
        private const val HANDLER_LAUNCHER = "LAUNCHER_SWC"

        /**
         * 处理多媒体信息回调的线程名称
         */
        private const val HANDLER_MEDIA = "MEDIA_SWC"

        /**
         * 处理消息中心信息回调的线程名称
         */
        private const val HANDLER_MESSAGE_CENTER = "MESSAGE_CENTER_SWC"

        /**
         * 处理导航信息回调的线程名称
         */
        private const val HANDLER_NAVI = "NAVI_SWC"

        /**
         * 处理声音空间信息回调的线程名称
         */
        private const val HANDLER_SOUND_EFFECT = "SOUND_EFFECT_SWC"

        /**
         * 处理系统设置信息回调的线程名称
         */
        private const val HANDLER_SYSTEM_SETTING = "SYSTEM_SETTING_SWC"

        /**
         * 处理USB音乐信息回调的线程名称
         */
        private const val HANDLER_USB_MEDIA = "USB_MEDIA_SWC"

        /**
         * 处理个人中心信息回调的线程名称
         */
        private const val HANDLER_USER_CENTER = "USER_CENTER_SWC"

        /**
         * 处理小辰回复语音的线程名称
         */
        private const val HANDLER_VOICE = "VOICE_SWC"

        /**
         * 处理qq音乐的线程名称
         */
        private const val HANDLER_QQ_MUSIC = "QQ_MUSIC_SWC"

        /**处理oms的线程名称*/
        private const val HANDLER_OMS = "OMS_SWC"

        /**
         * 通知的ID
         */
        const val NOTIFICATION_ID = 100021

        /**
         * 通知渠道的ID
         */
        const val NOTIFICATION_CHANNEL_ID = "com.dfl.smartscene.soaservice_id"

        /**触发场景地理围栏触发条件*/
        const val KEY_SEND_POSITION_2CCM = "key_send_position_2ccm"

        /**触发场景儿童检测触发条件*/
        const val KEY_SEND_OMS_2CCM = "key_send_oms_2ccm"

        //        /**触发场景乘客分布检测触发条件*/
        //        const val KEY_SEND_OMS_PASSENGER_2CCM = "key_send_oms_passenger_2ccm"
        //
        //        /**触发场景性别检测触发条件*/
        //        const val KEY_SEND_OMS_GENDER_2CCM = "key_send_oms_gender_2ccm"
        //
        //        /**触发场景行为检测触发条件*/
        //        const val KEY_SEND_OMS_BEHAVIOR_2CCM = "key_send_oms_behavior_2ccm"
    }


    override fun onCreate() {
        super.onCreate()
        CommonLogUtils.logI(TAG, "SoaCenterService--> onCreate")
        startForegroundWithNotification() //开启前台服务并发送通知
        initSWC() //初始化所有的swc服务
        initManager() //初始化应用间通讯的管理类
        initTriggerEventBus() //初始化满足触发条件的事件监听
        contentResolver.registerContentObserver(
            Settings.System.getUriFor(DayNightContentObserver.SETTINGS_WALLPAPER_TIME_INTERVAL),
            true,
            mDayNightContentObserver
        )
    }

    /**初始化满足触发条件的事件监听*/
    private fun initTriggerEventBus() {
        //1、监听场景是否触发地理围栏
        LiveEventBus.get(KEY_SEND_POSITION_2CCM, MyScenePosition::class.java).observe(this) {
            it?.let {
                mIviInfoSWCImpl?.notifyToCCM(it.lat, it.lon, it.distance, it.type)
            }
        }

        //2、监听场景 oms
        LiveEventBus.get(KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java).observe(this) {
            it?.let {
                mOMSSWCImpl?.notifyToCCM(it)
            }
        }

        mDayNightContentObserver.onChangeListener = object : DayNightContentObserver.OnChangeListener {
            override fun onChange(display: Int) {
                if (display == DayNightContentObserver.DisplayTimeInterval.SUNRISE.value || display == DayNightContentObserver.DisplayTimeInterval.SUNSET.value) {
                    mSystemSettingSWCImpl?.notifyDayStatusToCCM(display)
                }
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        CommonLogUtils.logE(TAG, "SoaCenterService--> onStartCommand:" + intent?.getStringExtra("abc"))
        super.onStartCommand(intent, flags, startId)
        bindConnectService()
        return Service.START_STICKY
        //START_STICKY当Service因内存不足而被系统kill后，一段时间后内存再次空闲时，系统将会尝试重新创建此Service
    }


    /**
     * 初始化应用间通讯的管理类
     */
    private fun initManager() {
        CarControlManager.init()
        EnergyCenterManager.init()
        SoaLauncherManager.init()
        MediaManager.init()
        MessageCenterManager.init()
        NaviManager.init()
        QQMusicManager.init()
        SoundEffectManager.init()
        SoaSystemSettingManager.init()
        SoaUserCenterManager.init()
        OMSManager.init()
        WeatherManager.init()
        CoroutineScope(Dispatchers.IO).launch {
            TTSManager.getInstance().checkService()
        }
        AppCommonManager.init(object : AppCommonManagerCallback {
            override fun onInitResult(result: Boolean) {
            }

            override fun onApiSTRModeStatusChanged(strMode: Int?) {
                if (strMode == ApiConstant.STR_PREPARE || strMode == ApiConstant.STR_MODE) {
                    CommonLogUtils.logD(TAG, "------------进入str模式---------")
                    TTSManager.getInstance().handleFocusLost()
                }
            }
        })
    }


    /**
     * 开启前台服务并发送通知
     */
    private fun startForegroundWithNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        //8.0及以上注册通知渠道
        createNotificationChannel(notificationManager)
        val notification: Notification = createForegroundNotification()
        //将服务置于启动状态 ,NOTIFICATION_ID指的是创建的通知的ID
        startForeground(NOTIFICATION_ID, notification)
        //发送通知到状态栏
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel(notificationManager: NotificationManager) {
        //Android8.0以上的系统，新建消息通道
        //用户可见的通道名称
        val channelName = "SOA Center Service"
        //通道的重要程度 ,IMPORTANCE_MIN:无声音，不会出现在状态栏中。
        val importance: Int = NotificationManager.IMPORTANCE_MIN
        //构建通知渠道
        val notificationChannel = NotificationChannel(
            NOTIFICATION_CHANNEL_ID, channelName, importance
        )
        notificationChannel.description = "Run SOA Action And Condition"
        //向系统注册通知渠道，注册后不能改变重要性以及其他通知行为
        notificationManager.createNotificationChannel(notificationChannel)
    }

    /**
     * 创建服务通知
     */
    private fun createForegroundNotification(): Notification {
        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(applicationContext, NOTIFICATION_CHANNEL_ID)
        ////        通知小图标
        builder.setSmallIcon(R.mipmap.scene_soa_icon_launcher)
        ////        通知标题
        //        builder.setContentTitle("")
        ////        通知内容
        //        builder.setContentText("")
        ////        设置通知显示的时间
        //        builder.setWhen(System.currentTimeMillis())
        builder.priority = NotificationCompat.PRIORITY_DEFAULT
        //设置为进行中的通知
        builder.setOngoing(true)
        //创建通知并返回
        return builder.build()
    }

    /**
     * 初始化所有的swc服务
     */
    private fun initSWC() {
        initComRuntime()
        initWeatherSWC()
        initIviInfoSWC()
        initCarControlSWC()
        initEnergyCenterSWC()
        //lk1a未使用
        //initKtvSWC()
        initLauncherSWC()
        initMediaSWC()
        initMessageCenterSWC()
        initNaviSWC()
        initSoundEffectSWC()
        initSystemSettingSWC()
        initUserCenterSWC()
        initVoiceSWC()
        initQQMusicSWC()
        initOMSSWC()
    }

    /**
     * 必须先初始化ComRuntime后再进行soa操作，初始化必须在实现类之前
     */
    private fun initComRuntime() {
        ics.com.WeatherServerSWC.ComRuntime.Init()
        ics.com.IVIInfSWC.ComRuntime.Init()
        ics.com.CarCtrlSWC.ComRuntime.Init()
        ics.com.EnergyCenterSerSWC.ComRuntime.Init()
        //lk1a未使用
        //ics.com.KTVserverSWC.ComRuntime.Init()
        ics.com.LauncherserverSWC.ComRuntime.Init()
        ics.com.MediaSWC.ComRuntime.Init()
        ics.com.MsgCenterSerSWC.ComRuntime.Init()
        ics.com.NavigationSWC.ComRuntime.Init()
        ics.com.SoundEffectSWC.ComRuntime.Init()
        ics.com.SysSetSWC.ComRuntime.Init()
        ics.com.PersonalCenterSerSWC.ComRuntime.Init()
        ics.com.VoiceSWC.ComRuntime.Init()
        ics.com.QQMusicSWC.ComRuntime.Init()
        ics.com.OMSSWC.ComRuntime.Init()
        CommonLogUtils.logI(TAG, "initComRuntime finish")
    }

    private fun initQQMusicSWC() {
        mQQMusicHandlerThread = HandlerThread(HANDLER_QQ_MUSIC)
        mQQMusicHandlerThread?.start()

        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initQQMusicSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mQQMusicHandlerThread?.let {
                mQQMusicSWCImpl = QQMusicSWCImpl(this@SoaCenterService, "1", it.looper)
                mQQMusicSWCImpl?.registerCallBack()
                mQQMusicSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  QQMusic service ")
            }
        }
    }

    private fun initOMSSWC() {
        mOMSHandlerThread = HandlerThread(HANDLER_OMS)
        mOMSHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initOMSSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mOMSHandlerThread?.let {
                mOMSSWCImpl = OMSSWCImpl("1", it.looper)
                mOMSSWCImpl?.registerCallBack()
                mOMSSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  oms service ")
            }
        }
    }

    /**
     * 初始化天气SOA
     */
    private fun initWeatherSWC() {
        mWeatherHandlerThread = HandlerThread(HANDLER_WEATHER)
        mWeatherHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initWeatherSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mWeatherHandlerThread?.let {
                mWeatherSWCImpl = WeatherSWCImpl("1", it.looper)
                mWeatherSWCImpl?.registerCallBack()
                mWeatherSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  weather service ")
            }
        }
    }

    /**
     * 初始化智慧场景的soa
     */
    private fun initIviInfoSWC() {
        mIviInfoHandlerThread = HandlerThread(HANDLER_IVI_INFO)
        mIviInfoHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initIviInfoSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mIviInfoHandlerThread?.let {
                mIviInfoSWCImpl = IvIInfoSWCImpl("1", it.looper)
                mIviInfoSWCImpl?.registerCallBack()
                mIviInfoSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initIviInfoSWC service ")
            }
        }
    }


    //    /**
    //     * 初始化蓝牙音乐的soa(0919jar包删除)
    //     */
    //    private fun initBltMusicSWC() {
    //        mBltMusicHandlerThread = HandlerThread(HANDLER_BLT_MUSIC)
    //        mBltMusicHandlerThread?.start()
    //        lifecycleScope.launch(Dispatchers.IO) {
    //            CommonLogUtils.logD(TAG, "initBltMusicSWC")
    //            ics.com.BLTMusicserverSWC.ComRuntime.Init() //初始化必须在实现类之前
    //            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
    //            mBltMusicHandlerThread?.let {
    //                mBltMusicSWCImpl = BltMusicSWCImpl(this@SoaCenterService, "1", it.looper)
    //                mBltMusicSWCImpl?.registerCallBack()
    //                mBltMusicSWCImpl?.OfferService()
    //                CommonLogUtils.logD(TAG, "after offer  initBltMusicSWC service ")
    //            }
    //        }
    //    }

    /**
     * 初始化车控的soa
     */
    private fun initCarControlSWC() {
        mCarControlHandlerThread = HandlerThread(HANDLER_CAR_CONTROL)
        mCarControlHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initCarControlSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mCarControlHandlerThread?.let {
                mCarControlSWCImpl = CarControlSWCImpl("1", it.looper)
                mCarControlSWCImpl?.registerCallBack()
                mCarControlSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initCarControlSWC service ")
            }
        }
    }

    /**
     * 初始化能源中心的soa
     */
    private fun initEnergyCenterSWC() {
        mEnergyCenterHandlerThread = HandlerThread(HANDLER_ENERGY_CENTER)
        mEnergyCenterHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initEnergyCenterSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mEnergyCenterHandlerThread?.let {
                mEnergyCenterSWCImpl = EnergyCenterSWCImpl("1", it.looper)
                mEnergyCenterSWCImpl?.registerCallBack()
                mEnergyCenterSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initEnergyCenterSWC service ")
            }
        }
    }

    /**
     * 初始KTV的soa
     */
    private fun initKtvSWC() {
        mKtvHandlerThread = HandlerThread(HANDLER_KTV)
        mKtvHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initKtvSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mKtvHandlerThread?.let {
                mKtvSWCImpl = KtvSWCImpl("1", it.looper)
                mKtvSWCImpl?.registerCallBack()
                mKtvSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initKtvSWC service ")
            }
        }
    }

    /**
     * 初始化桌面的soa
     */
    private fun initLauncherSWC() {
        mLauncherHandlerThread = HandlerThread(HANDLER_LAUNCHER)
        mLauncherHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initLauncherSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mLauncherHandlerThread?.let {
                mLauncherSWCImpl = LauncherSWCImpl("1", it.looper)
                mLauncherSWCImpl?.registerCallBack()
                mLauncherSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initLauncherSWC service ")
            }
        }
    }

    /**
     * 初始化智慧场景的soa
     */
    private fun initMediaSWC() {
        mMediaHandlerThread = HandlerThread(HANDLER_MEDIA)
        mMediaHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initMediaSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mMediaHandlerThread?.let {
                mMediaSWCImpl = MediaSWCImpl(this@SoaCenterService, "1", it.looper)
                mMediaSWCImpl?.registerCallBack()
                mMediaSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initMediaSWC service ")
            }
        }
    }

    /**
     * 初始化智慧场景的soa
     */
    private fun initMessageCenterSWC() {
        mMessageCenterHandlerThread = HandlerThread(HANDLER_MESSAGE_CENTER)
        mMessageCenterHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initMessageCenterSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mMessageCenterHandlerThread?.let {
                mMessageCenterSWCImpl = MessageCenterSWCImpl("1", it.looper)
                mMessageCenterSWCImpl?.registerCallBack()
                mMessageCenterSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  MessageCenter service ")
            }
        }
    }

    /**
     * 初始化智慧场景的soa
     */
    private fun initNaviSWC() {
        mNaviHandlerThread = HandlerThread(HANDLER_NAVI)
        mNaviHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initNaviSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mNaviHandlerThread?.let {
                mNaviSWCImpl = NaviSWCImpl("1", it.looper)
                mNaviSWCImpl?.registerCallBack()
                mNaviSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initNaviSWC service ")
            }
        }
    }

    /**
     * 初始化声音空间的soa
     */
    private fun initSoundEffectSWC() {
        mSoundEffectHandlerThread = HandlerThread(HANDLER_SOUND_EFFECT)
        mSoundEffectHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initSoundEffectSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mSoundEffectHandlerThread?.let {
                mSoundEffectSWCImpl = SoundEffectSWCImpl("1", it.looper)
                mSoundEffectSWCImpl?.registerCallBack()
                mSoundEffectSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initSoundEffectSWC service ")
            }
        }
    }

    /**
     * 初始化系统设置的soa
     */
    private fun initSystemSettingSWC() {
        mSystemSettingHandlerThread = HandlerThread(HANDLER_SYSTEM_SETTING)
        mSystemSettingHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initSystemSettingSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mSystemSettingHandlerThread?.let {
                mSystemSettingSWCImpl = SystemSettingSWCImpl("1", it.looper)
                mSystemSettingSWCImpl?.registerCallBack()
                mSystemSettingSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initSystemSettingSWC service ")
            }
        }
    }

    //    /**
    //     * 初始化USB音乐的soa(0919jar包删除)
    //     */
    //    private fun initUsbMediaSWC() {
    //        mUsbMediaHandlerThread = HandlerThread(HANDLER_USB_MEDIA)
    //        mUsbMediaHandlerThread?.start()
    //        lifecycleScope.launch(Dispatchers.IO) {
    //            CommonLogUtils.logD(TAG, "initUsbMediaSWC")
    //            ics.com.USBserverSWC.ComRuntime.Init() //初始化必须在实现类之前
    //            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
    //            mUsbMediaHandlerThread?.let {
    //                mUsbMediaSWCImpl = UsbMediaSWCImpl(this@SoaCenterService, "1", it.looper)
    //                mUsbMediaSWCImpl?.registerCallBack()
    //                mUsbMediaSWCImpl?.OfferService()
    //                CommonLogUtils.logD(TAG, "after offer  initUsbMediaSWC service ")
    //            }
    //        }
    //    }

    /**
     * 初始化个人中心的soa
     */
    private fun initUserCenterSWC() {
        mUserCenterHandlerThread = HandlerThread(HANDLER_USER_CENTER)
        mUserCenterHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initUserCenterSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mUserCenterHandlerThread?.let {
                mUserCenterSWCImpl = UserCenterSWCImpl("1", it.looper)
                mUserCenterSWCImpl?.registerCallBack()
                mUserCenterSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initUserCenterSWC service ")
            }
        }
    }

    /**
     * 初始化语音的soa
     */
    private fun initVoiceSWC() {
        mVoiceHandlerThread = HandlerThread(HANDLER_VOICE)
        mVoiceHandlerThread?.start()
        lifecycleScope.launch(Dispatchers.IO) {
            CommonLogUtils.logI(TAG, "initVoiceSWC")
            // 这句话有可能会出现anr，因为有远程调用,但一般是ccm无法连接才会出现
            mVoiceHandlerThread?.let {
                mVoiceSWCImpl = VoiceSWCImpl("1", it.looper)
                mVoiceSWCImpl?.registerCallBack()
                mVoiceSWCImpl?.OfferService()
                CommonLogUtils.logI(TAG, "after offer  initVoiceSWC service ")
            }
        }
    }


    override fun onDestroy() {
        CommonLogUtils.logE(TAG, "SoaCenterService被销毁")
        deInitSwc()
        deInitManager()
        contentResolver.unregisterContentObserver(mDayNightContentObserver)
        super.onDestroy()
    }

    private fun deInitManager() {
        CarControlManager.deInit()
        EnergyCenterManager.deInit()
        SoaLauncherManager.deInit()
        MediaManager.deInit()
        MessageCenterManager.deInit()
        NaviManager.deInit()
        QQMusicManager.deInit()
        SoundEffectManager.deInit()
        SoaSystemSettingManager.deInit()
        SoaUserCenterManager.deInit()
        WeatherManager.deInit()
        OMSManager.deInit()
    }

    /**
     * 注销soa服务
     */
    private fun deInitSwc() {
        deInitWeatherSWC()
        deInitIviInfoSWC()
        deInitBltMusicSWC()
        deInitCarControlSWC()
        deInitEnergyCenterSWC()
        deInitKtvSWC()
        deInitLauncherSWC()
        deInitMediaSWC()
        deInitMessageCenterSWC()
        deInitNaviSWC()
        deInitSoundEffectSWC()
        deInitSystemSettingSWC()
        deInitUsbMediaSWC()
        deInitUserCenterSWC()
        deInitVoiceSWC()
        deInitQQMusicSWC()
    }

    private fun deInitQQMusicSWC() {
        mQQMusicHandlerThread?.quit()
        mQQMusicHandlerThread = null
        mQQMusicSWCImpl?.StopOfferService()
        mQQMusicSWCImpl = null
    }

    /**
     * 注销天气soa服务
     */
    private fun deInitWeatherSWC() {
        mWeatherHandlerThread?.quit()
        mWeatherHandlerThread = null
        mWeatherSWCImpl?.StopOfferService()
        mWeatherSWCImpl = null
    }

    /**
     * 注销智慧场景soa服务
     */
    private fun deInitIviInfoSWC() {
        mIviInfoHandlerThread?.quit()
        mIviInfoHandlerThread = null
        mIviInfoSWCImpl?.StopOfferService()
        mIviInfoSWCImpl = null
    }

    /**
     * 注销蓝牙音乐soa服务
     */
    private fun deInitBltMusicSWC() {
        mBltMusicHandlerThread?.quit()
        mBltMusicHandlerThread = null
        //        mBltMusicSWCImpl?.StopOfferService()
        //        mBltMusicSWCImpl = null
    }

    /**
     * 注销车控soa服务
     */
    private fun deInitCarControlSWC() {
        mCarControlHandlerThread?.quit()
        mCarControlHandlerThread = null
        mCarControlSWCImpl?.StopOfferService()
        mCarControlSWCImpl = null
    }

    /**
     * 注销能源中心soa服务
     */
    private fun deInitEnergyCenterSWC() {
        mEnergyCenterHandlerThread?.quit()
        mEnergyCenterHandlerThread = null
        mEnergyCenterSWCImpl?.StopOfferService()
        mEnergyCenterSWCImpl = null
    }

    /**
     * 注销ktv soa服务
     */
    private fun deInitKtvSWC() {
        mKtvHandlerThread?.quit()
        mKtvHandlerThread = null
        mKtvSWCImpl?.StopOfferService()
        mKtvSWCImpl = null
    }

    /**
     * 注销桌面soa服务
     */
    private fun deInitLauncherSWC() {
        mLauncherHandlerThread?.quit()
        mLauncherHandlerThread = null
        mLauncherSWCImpl?.StopOfferService()
        mLauncherSWCImpl = null
    }

    /**
     * 注销多媒体soa服务
     */
    private fun deInitMediaSWC() {
        mMediaHandlerThread?.quit()
        mMediaHandlerThread = null
        mMediaSWCImpl?.StopOfferService()
        mMediaSWCImpl = null
    }

    /**
     * 注销消息中心soa服务
     */
    private fun deInitMessageCenterSWC() {
        mMessageCenterHandlerThread?.quit()
        mMessageCenterHandlerThread = null
        mMessageCenterSWCImpl?.StopOfferService()
        mMessageCenterSWCImpl = null
    }

    /**
     * 注销导航soa服务
     */
    private fun deInitNaviSWC() {
        mNaviHandlerThread?.quit()
        mNaviHandlerThread = null
        mNaviSWCImpl?.StopOfferService()
        mNaviSWCImpl = null
    }

    /**
     * 注销声音空间soa服务
     */
    private fun deInitSoundEffectSWC() {
        mSoundEffectHandlerThread?.quit()
        mSoundEffectHandlerThread = null
        mSoundEffectSWCImpl?.StopOfferService()
        mSoundEffectSWCImpl = null
    }

    /**
     * 注销系统设置soa服务
     */
    private fun deInitSystemSettingSWC() {
        mSystemSettingHandlerThread?.quit()
        mSystemSettingHandlerThread = null
        mSystemSettingSWCImpl?.StopOfferService()
        mSystemSettingSWCImpl = null
    }

    /**
     * 注销USB音乐soa服务
     */
    private fun deInitUsbMediaSWC() {
        mUsbMediaHandlerThread?.quit()
        mUsbMediaHandlerThread = null
        //        mUsbMediaSWCImpl?.StopOfferService()
        //        mUsbMediaSWCImpl = null
    }

    /**
     * 注销个人中心soa服务
     */
    private fun deInitUserCenterSWC() {
        mUserCenterHandlerThread?.quit()
        mUserCenterHandlerThread = null
        mUserCenterSWCImpl?.StopOfferService()
        mUserCenterSWCImpl = null
    }

    /**
     * 注销语音soa服务
     */
    private fun deInitVoiceSWC() {
        mVoiceHandlerThread?.quit()
        mVoiceHandlerThread = null
        mVoiceSWCImpl?.StopOfferService()
        mVoiceSWCImpl = null
    }

    /**启动sceneWidgetService*/
    fun startConnectService() {
        CommonLogUtils.logI(TAG, "启动 sceneWidgetService")
        //启动soa 中心服务的service
        val intent = Intent()
        intent.action = "com.dfl.smartscene.sceneWidgetService"
        intent.setPackage("com.dfl.smartscene")
        startForegroundService(intent)
    }

    /**绑定sceneWidgetService*/
    fun bindConnectService() {
        CommonLogUtils.logI(TAG, "sceneWidgetService 开始bind")
        val intent = Intent()
        intent.action = "com.dfl.smartscene.sceneWidgetService"
        intent.setPackage("com.dfl.smartscene")
        bindService(intent, keepServiceAliveConnection, BIND_AUTO_CREATE)
    }

    override fun onBind(intent: Intent): IBinder {
        //返回aidl接口，让sceneWidgetService成功绑定自己
        super.onBind(intent)
        return binder
    }
}