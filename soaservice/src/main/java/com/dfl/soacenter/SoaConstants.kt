package com.dfl.soacenter

/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:zhus<PERSON><PERSON>@dfl.com.cn
 * time: 2023/12/19
 * desc: soa服务使用的一些常量
 * version:1.0
 */
class SoaConstants {
    companion object {
        /**
         * soa所使用的tag
         */
        const val SOA_TAG = "DFLScene-"

        /**
         * 消息类型-请求
         */
        const val MESSAGE_TYPE_REQUEST = "request"

        /**
         * 消息类型-监听
         */
        const val MESSAGE_TYPE_DISPATCH = "dispatch"
    }
}