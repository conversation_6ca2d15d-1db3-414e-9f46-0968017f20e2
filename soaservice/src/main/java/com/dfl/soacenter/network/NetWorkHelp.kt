package com.dfl.soacenter.network

import com.dfl.android.common.util.ConvertUtils
import com.dfl.android.common.util.EncryptUtils
import com.dfl.android.common.util.SM2Util
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.HolidayBean
import com.dfl.soacenter.network.NetConstant.Companion.KEY_SIGN
import com.dfl.soacenter.network.NetConstant.Companion.NONCE_STR
import com.dfl.soacenter.network.NetConstant.Companion.TIMESTAMP
import com.dfl.soacenter.network.api.NetworkService
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Locale
import java.util.Random
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.roundToInt

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/17
 * desc :
 * version: 1.0
 */
object NetWorkHelp {
    private var mGson: Gson? = null
    private const val TAG = SoaConstants.SOA_TAG.plus("NetWorkHelp")
    private var mHolidayBean: HolidayBean? = null

    init {
        mGson = Gson()
    }

    fun getHoliday(): HolidayBean? {
        return mHolidayBean
    }


    suspend fun getHolidayData() = suspendCoroutine<HolidayBean> {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val calendar = Calendar.getInstance()
                val year = calendar.get(Calendar.YEAR).toString()
                val params: MutableMap<String, String> = HashMap()
                params["year"] = year
                val json: String = mGson!!.toJson(params)
                val encrypt: ByteArray = SM2Util.encrypt(json, NetConstant.FINAL_HOLIDAY_PUBLIC_KEY)
                val v: String = ConvertUtils.bytes2HexString(encrypt).lowercase(Locale.getDefault())
                val paramsV: MutableMap<String, String> = HashMap()
                v.also { paramsV[NetConstant.V] = it }
                val headerMap: Map<String, String> = getHeaderMap(paramsV, NetConstant.HOLIDAY_API)
                //            val uri=URI.create(NetConstant.HOLIDAY_PATH)
                val result = NetworkService.api.getHolidayInfo(
                    NetConstant.URL_SPLIT_PATH,
                    headerMap,
                    paramsV
                )
                if (result.isSuccess()) {
                    val bytes = ConvertUtils.hexString2Bytes(result.v)
                    val decryptData = SM2Util.decrypt(bytes, NetConstant.FINAL_HOLIDAY_PRIVATE_KEY)
                    //                    CommonLogUtils.logE(TAG, "后的result==>$decryptData")
                    val bean = Gson().fromJson(decryptData, HolidayBean::class.java)
                    mHolidayBean = bean
                    it.resume(bean)
                } else {
                    CommonLogUtils.logE(TAG, "request=== fail===>$result")
                }
            } catch (e: Exception) {
                CommonLogUtils.logE(TAG, "request===sign===exception==>${e.message}")
            }
        }
    }

    private fun getHeaderMap(paramsV: Map<String, String>?, api: String): Map<String, String> {
        val headerMap: MutableMap<String, String> = HashMap()
        var encryptJsonStr = ""
        if (!paramsV.isNullOrEmpty()) {
            encryptJsonStr = mGson!!.toJson(paramsV)
        }
        val timestamp = System.currentTimeMillis().toString() + ""
        val nonceStr: String = getRandomString(32)
        val keySign: String =
            NetConstant.FINAL_HOLIDAY_APP_ID + NetConstant.FINAL_HOLIDAY_APP_KEY + api + nonceStr + timestamp + encryptJsonStr

        val keySignEncrypt: String =
            EncryptUtils.encryptSHA512ToString(keySign).lowercase(Locale.getDefault())
        headerMap[TIMESTAMP] = timestamp
        headerMap[NONCE_STR] = nonceStr
        headerMap[KEY_SIGN] = keySignEncrypt
        return headerMap
    }

    /**
     * 获取指定长度随机字符串
     *
     * @param length 随机字符串长度
     * @return
     */
    private fun getRandomString(length: Int): String {
        val random = Random()
        val sb = StringBuffer()
        for (i in 0 until length) {
            val number = random.nextInt(3)
            var result: Long
            when (number) {
                0 -> {
                    result = (Math.random() * 25 + 65).roundToInt().toLong()
                    sb.append(result.toInt().toChar())
                }
                1 -> {
                    result = (Math.random() * 25 + 97).roundToInt().toLong()
                    sb.append(result.toInt().toChar())
                }
                2 -> sb.append(Random().nextInt(10))
            }
        }
        return sb.toString()
    }
}