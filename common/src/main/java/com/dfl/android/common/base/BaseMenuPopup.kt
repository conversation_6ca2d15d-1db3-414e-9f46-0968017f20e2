package com.dfl.android.common.base

import android.view.View
import android.widget.PopupWindow
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2025/02/11
 * desc : rv item中更多菜单 popMenu基类
 * version: 1.0
 */
abstract class BaseMenuPopup<T>(protected var onMenuClickListener: OnMenuClickListener<T>?) {
    protected open val TAG: String = GlobalConstant.GLOBAL_TAG.plus("BaseMenuPopup")

    /**点击rv的数据位置*/
    open var rvDataIndex: Int = 0

    /**点击rv的数据位置*/
    open var rvData: T? = null

    /**popMenu*/
    protected open var mPopupWindow: PopupWindow? = null

    /**menu中选项点击事件监听器*/
    interface OnMenuClickListener<T> {
        fun onMenuItemSelected(menuPopup: BaseMenuPopup<T>, menuIndex: Int, dataIndex: Int, data: T?)
    }

    /**
     * 显示popMenu
     * @param view popMenu根据view布局显示
     */
    abstract fun show(view: View)

    /**隐藏popMenu*/
    abstract fun dismiss()

    /**菜单是否显示*/
    open fun isShowing(): Boolean {
        val isShowing = mPopupWindow?.isShowing
        CommonLogUtils.logD(TAG, "popMenu isShowing=$isShowing")
        return isShowing == true
    }

    /**注销点击事件监视器，防止持有activity引用导致内存泄漏*/
    open fun unregisterListener() {
        if (onMenuClickListener != null) {
            onMenuClickListener = null
        }
    }
}