package com.dfl.android.common.base;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :基于ViewDataBinding的基类Activity
 * version: 1.0
 */
public abstract class MVVMActivity<V extends ViewDataBinding, VM extends BaseViewModel> extends BaseActivity {
    protected V mViewDataBinding;
    protected VM mViewModel;

    public MVVMActivity() {
    }

    protected void initView() {
        this.initViewModel();
        this.initDataBinding();
    }

    protected void initData() {
    }

    protected abstract int getLayoutId();

    protected abstract int getBindingVariable();

    protected abstract Class<VM> getViewModelClass();

    private void initViewModel() {
        this.mViewModel = (new ViewModelProvider(this)).get(this.getViewModelClass());
    }

    private void initDataBinding() {
        this.mViewDataBinding = DataBindingUtil.setContentView(this, this.getLayoutId());
        if (this.getBindingVariable() > 0) {
            this.mViewDataBinding.setVariable(this.getBindingVariable(), this.mViewModel);
            this.mViewDataBinding.executePendingBindings();
            this.mViewDataBinding.setLifecycleOwner(this);
        }

    }
}

