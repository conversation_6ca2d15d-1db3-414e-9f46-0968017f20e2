package com.dfl.android.common.global

/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:z<PERSON><PERSON><PERSON>@dfl.com.cn
 * time: 2023/03/31
 * desc: 全局通用常量
 * version:1.0
 */
class GlobalConstant {
    companion object {
        /**
         * 智慧场景的应用id
         */
        const val APPLICATION_ID = "com.dfl.smartscene"

        /**
         * 白天黑夜模式切换的广播
         */
        const val ACTION_DAY_NIGHT_MODE = "com.dfl.smartscene.action.ACTION_DAY_NIGHT_MODE"

        /**
         * 全局的TAG
         */
        const val GLOBAL_TAG = "DFLScene-"

        /**vin码长度*/
        const val VIN_LENGTH = 17
    }
}

class Lk1AVinConstant {
    companion object {
        /**台架请求社区接口的vin码 低配*/
        const val TEST_LOW_VIN = "LGB52KE8RYJ000555"

        /**台架请求社区接口的vin码 中配*/
        const val TEST_MIDDLE_VIN = "LGBA12EA6RY000333"

        /**台架请求社区接口的vin码 高配*/
        const val TEST_HIGH_VIN = "LGB52KE8RYJ000999"

        /**台架请求社区接口的vin码 商用低配*/
        const val BUSINESS_LOW_VIN = "NMK1AA2410140005v"

        /**台架请求社区接口的vin码 商用中配*/
        const val BUSINESS_MIDDLE_VIN = "NHK1AC2409060030v"

        /**台架请求社区接口的vin码 商用高配*/
        const val BUSINESS_HIGH_VIN = "NHK1AA2404170034v"

        /**
         * 默认的vin码 高配
         */
        val DEFAULT_HIGH_VIN = if (GlobalConfig.IS_BUSINESS) BUSINESS_HIGH_VIN else TEST_HIGH_VIN

        /**
         * 默认的vin码 中配
         */
        val DEFAULT_MIDDLE_VIN = if (GlobalConfig.IS_BUSINESS) BUSINESS_MIDDLE_VIN else TEST_MIDDLE_VIN

        /**
         * 默认的vin码 低配
         */
        val DEFAULT_LOW_VIN = if (GlobalConfig.IS_BUSINESS) BUSINESS_LOW_VIN else TEST_LOW_VIN
    }
}

class Lk2AVinConstant {
    companion object {
        /**台架请求社区接口的vin码 低配*/
        const val L1_VIN = "LK2ATEST04170058v"

        /**台架请求社区接口的vin码 中配*/
        const val L2_VIN = "LK2ATEST10140026v"

        /**台架请求社区接口的vin码 高配*/
        const val L3_VIN = "LK2ATEST09060051v"

        /**台架请求社区接口的vin码 商用低配*/
        const val L4_VIN = "LK2ATEST04170055v"

        /**台架请求社区接口的vin码 商用中配*/
        const val L5_VIN = "LK2ATEST09060052v"

        /**台架请求社区接口的vin码 商用高配*/
        const val L6_VIN = "LK2ATEST10140039v"
    }
}