package com.dfl.android.common.util;

import com.dfl.android.commonlib.log.CommonLogUtils;

import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.util.ECUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.DecoderException;

import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Security;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @date 2022/9/13
 * @time 10:00
 */
public class SM2Util {
    private static final String TAG = "DFLScene-".concat("SM2Util");

    /**
     * 加签
     */
    public static String sign(String plainText, String privateKeyStr) {
        BouncyCastleProvider provider = new BouncyCastleProvider();
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] privateKeyData = Base64.decode(privateKeyStr);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyData);
            Signature rsaSignature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
            rsaSignature.initSign(keyFactory.generatePrivate(privateKeySpec));
            rsaSignature.update(plainText.getBytes());
            byte[] signed = rsaSignature.sign();
            return Base64.toBase64String(signed);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 验签
     */
    public static boolean verify(String plainText, String signatureValue, String publicKeyStr) {
        BouncyCastleProvider provider = new BouncyCastleProvider();
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] publicKeyData = Base64.decode(publicKeyStr);
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyData);
            // 初始化为验签状态
            Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
            signature.initVerify(keyFactory.generatePublic(publicKeySpec));
            signature.update(ConvertUtils.hexString2Bytes(plainText));
            return signature.verify(ConvertUtils.hexString2Bytes(signatureValue));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException e) {
            throw new RuntimeException(e);
        } catch (IllegalArgumentException e) {
            return false;
        } catch (DecoderException e) {
            return false;
        }
    }

    /**
     * 加密
     *
     * @param plainText
     * @return
     */
    public static byte[] encrypt(String plainText, String publicKeyStr) {
        BouncyCastleProvider provider = new BouncyCastleProvider();
        Security.addProvider(provider);
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] publicKeyData = Base64.decode(publicKeyStr);
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyData);
            PublicKey publicKey = keyFactory.generatePublic(publicKeySpec);
            CipherParameters publicKeyParamerters = ECUtil.generatePublicKeyParameter(publicKey);
            //数据加密
            StandardSM2Engine engine = new StandardSM2Engine(new SM3Digest(), SM2Engine.Mode.C1C3C2);
            engine.init(true, new ParametersWithRandom(publicKeyParamerters));
            return engine.processBlock(plainText.getBytes(), 0, plainText.getBytes().length);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException
                 | InvalidKeyException | InvalidCipherTextException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密
     *
     * @param encryptedData
     * @return
     */
    public static String decrypt(byte[] encryptedData, String privateKeyStr) {
        //        LogUtils.e(TAG,"-----------Json解密----------------");
        BouncyCastleProvider provider = new BouncyCastleProvider();
        Security.addProvider(provider);
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] privateKeyData = Base64.decode(privateKeyStr);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyData);
            PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);
            CipherParameters privateKeyParameters = ECUtil.generatePrivateKeyParameter(privateKey);
            //数据解密
            StandardSM2Engine engine = new StandardSM2Engine(new SM3Digest(), SM2Engine.Mode.C1C3C2);
            engine.init(false, privateKeyParameters);
            byte[] plainText = engine.processBlock(encryptedData, 0, encryptedData.length);
            return new String(plainText);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException
                 | InvalidKeyException | InvalidCipherTextException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * SM2算法生成密钥对
     *
     * @return 密钥对信息
     */
    public static KeyPair generateSm2KeyPair() {
        try {
            final ECGenParameterSpec sm2Spec = new ECGenParameterSpec("sm2p256v1");
            // 获取一个椭圆曲线类型的密钥对生成器
            final KeyPairGenerator kpg = KeyPairGenerator.getInstance("EC", new BouncyCastleProvider());
            SecureRandom random = new SecureRandom();
            // 使用SM2的算法区域初始化密钥生成器
            kpg.initialize(sm2Spec, random);
            // 获取密钥对
            KeyPair keyPair = kpg.generateKeyPair();
            return keyPair;
        } catch (Exception e) {
            CommonLogUtils.logD(TAG, "generate sm2 key pair failed:{}" + e.getMessage() + e);
            return null;
        }
    }

    public static void test(String data) {
        String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg3vGj0RCR4pTrRb/S " +
                "Fp8uNqQX4IoIfPTf9uMyJlX2ToagCgYIKoEcz1UBgi2hRANCAATgUe+sfGzxu7Tu " +
                "x2aMRlLW8hzypuiz7bptauNOKqqrTASX4xpqgwSNFtkGzUiRO89l3qranyRMwVSb pcu3btI0";
        String publicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEUu28DnL9XExkPOimMKMbkW" +
                "+QC7MiuDaFXlj1FPx4BCAclhqOFj0nA3HDCUckVzO1FewjP2W++qJprFogitv4sw==";

        CommonLogUtils.logD(TAG, "解密前数据：" + data);
        byte[] bytes = ConvertUtils.hexString2Bytes(data);
        String decrypt1 = null;
        try {
            decrypt1 = decrypt(bytes, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CommonLogUtils.logD(TAG, "解密后数据：" + decrypt1);
    }
}
