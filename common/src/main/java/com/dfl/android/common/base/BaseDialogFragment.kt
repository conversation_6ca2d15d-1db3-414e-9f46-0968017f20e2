package com.dfl.android.common.base

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.dfl.android.animationlib.tools.DialogAnimUtil
import com.dfl.android.common.R
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import me.jessyan.autosize.AutoSizeCompat

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/20
 * desc   : DialogFragment基类
 * version: 1.0
 */
abstract class BaseDialogFragment : DialogFragment() {
    private var isOnStart = false

    /**默认点击蒙层关闭*/
    private var canceledOnTouchOutside = true

    /**是否4级及以上的菜单弹框  */
    var isLevel4OrAbove = false

    private lateinit var dialogAnimUtil: DialogAnimUtil
    protected val TAG = GlobalConstant.GLOBAL_TAG.plus(this::class.simpleName)
    override fun onAttach(context: Context) {
        super.onAttach(context)
        CommonLogUtils.logI(TAG, this::class.simpleName + " onAttach")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        CommonLogUtils.logI(TAG, this::class.simpleName + " onCreate")
        setStyle(STYLE_NO_TITLE, R.style.Theme_AppCompat_Light_Dialog_dialogTheme)
        initParams()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        CommonLogUtils.logI(TAG, this::class.simpleName + " onCreateView")
        return inflater.inflate(getLayoutId(), container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        CommonLogUtils.logI(TAG, this::class.simpleName + " onViewCreate")
        //动效初始化，防止context为null
        dialogAnimUtil = DialogAnimUtil(context)
        //导入组件库需要的蒙层
        val maskView = LayoutInflater.from(context).inflate(R.layout.scene_widiget_maskview, view as ViewGroup, false)
        maskView.tag = "maskView"
        //为内容view设置tag
        val contentView = view.getChildAt(0)
        //蒙层在第一位，确保不覆盖内容区域
        view.addView(maskView, 0)
        dialogAnimUtil.startDialogAnim(contentView)

        initData()
        initView(view)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        CommonLogUtils.logI(TAG, this::class.simpleName + " onViewStateRestored")
    }

    override fun onStart() {
        super.onStart()
        CommonLogUtils.logI(TAG, this::class.simpleName + " onStart")
        dialog?.window?.let {
            it.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
            //5.0 全透明实现
            it.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            it.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            it.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            it.statusBarColor = Color.TRANSPARENT
            //设置全屏显示
            it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            //背景透明
            it.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            //状态栏和导航栏由应用设置深浅模式，不再跟随系统主题切换
            //val uiMode = resources.configuration.uiMode
            //val mUiMode: Int = uiMode and Configuration.UI_MODE_NIGHT_MASK
            //BarUtils.setStatusAndNaviBarStyle(dialog?.window, mUiMode)
        }
        setCanceledOnTouchOutsideListener()
        isOnStart = true
    }

    override fun onResume() {
        super.onResume()
        CommonLogUtils.logI(TAG, this::class.simpleName + " onResume")
        //val uiMode = resources.configuration.uiMode
        //val mUiMode: Int = uiMode and Configuration.UI_MODE_NIGHT_MASK
        //BarUtils.setStatusAndNaviBarStyle(dialog?.window, mUiMode)
    }

    override fun onPause() {
        CommonLogUtils.logI(TAG, this::class.simpleName + " onPause")
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        super.onPause()
    }

    override fun onStop() {
        CommonLogUtils.logI(TAG, this::class.simpleName + " onStop")
        isOnStart = false
        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        super.onStop()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if ("SceneEditContainerDialogFragment" != this::class.simpleName && !isLevel4OrAbove) { //不给自己发消息
            LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(1)
        }
        CommonLogUtils.logI(TAG, this::class.simpleName + " onDestroyView")
    }

    override fun onDestroy() {
        super.onDestroy()
        CommonLogUtils.logI(TAG, this::class.simpleName + " onDestroy")
    }

    override fun onDetach() {
        super.onDetach()
        CommonLogUtils.logI(TAG, this::class.simpleName + " onDetach")
    }

    /**
     * 设置是否点击蒙层消失
     */
    fun setCanceledOnTouchOutside(value: Boolean) {
        if (canceledOnTouchOutside == value) {
            return
        }
        canceledOnTouchOutside = value
        if (isOnStart) {
            setCanceledOnTouchOutsideListener()
        }
    }

    /**
     * 设置点击蒙层消失的点击事件
     */
    private fun setCanceledOnTouchOutsideListener() {
        view ?: return
        if (canceledOnTouchOutside) {
            view?.setOnClickListener {
                onTouchOutside()
                dismissWithAnimate()
            }
        } else {
            view?.setOnClickListener(null)
        }
    }

    /**
     * 使用组件库弹窗消失动效
     */
    open fun dismissWithAnimate() {
        if ("SceneEditContainerDialogFragment" != this::class.simpleName && !isLevel4OrAbove) { //不给自己发消息
            LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(1)
        }
        val maskView: View? = view?.findViewWithTag("maskView")
        val contentView: View? = view?.findViewWithTag("contentView")
        if (maskView == null || contentView == null) {
            dismiss()
        } else {
            dialogAnimUtil.hideDialogAnim(maskView, contentView) {
                //因为组件库动画有延迟，要先判断FragmentManage是否已经销毁
                if (isAdded) {
                    dismiss()
                }
            }
        }
    }

    /**
     * 点击蒙层区域的回调
     */
    open fun onTouchOutside() {}

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        //val mUiMode = newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK
        //CommonLogUtils.logI(TAG, "onConfigurationChanged isDayMode:${mUiMode == Configuration.UI_MODE_NIGHT_NO}")
        //切换主题density可能还原，再次设置自适应布局density
        AutoSizeCompat.autoConvertDensityOfGlobal(resources)
        //状态栏和导航栏由应用设置深浅模式，不再跟随系统主题切换
        //BarUtils.setStatusAndNaviBarStyle(dialog?.window, mUiMode)
    }

    protected open fun initParams() {}

    /**
     * 在onViewCreated调用,先执行initData()
     */
    protected open fun initView(view: View) {}

    /**
     * 在onCreateView创建对应布局视图,若重写onCreateView方法返回-1即可
     */
    protected abstract fun getLayoutId(): Int

    /**
     * 在onViewCreated调用
     */
    protected open fun initData() {}

}