package com.dfl.android.common.customapi.constant

/**
 * author:zhus<PERSON><PERSON>
 * e-mail:zhus<PERSON><PERSON>@dfl.com.cn
 * time: 2022/10/14
 * desc: customApi的常量
 * version:1.0
 */
class ApiConstant {
    companion object {
        /**
         * 未知状态
         */
        const val GEAR_TYPE_UNKNOWN = -1

        /**
         * 停车
         */
        const val GEAR_TYPE_PARKING = 0

        /**
         * 倒车挡
         */
        const val GEAR_TYPE_REVERSE = 1

        /**
         * 空挡
         */
        const val GEAR_TYPE_NEUTRAL = 2

        /**
         * 行车挡
         */
        const val GEAR_TYPE_DRIVE = 3

        /**
         * 调用失败
         */
        const val CALL_FAIL = 254

        /**
         * 非str模式
         */
        const val STR_NO = 0

        /**
         * 准备进入str
         */
        const val STR_PREPARE = 1

        /**
         * str模式进入
         */
        const val STR_MODE = 2
    }
}