package com.dfl.android.common.base;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;

public abstract class MVVMFragment<V extends ViewDataBinding, VM extends BaseViewModel> extends BaseFragment {
    public VM mViewModel;
    protected V mViewDataBinding;

    public MVVMFragment() {
    }

    @Nullable
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        this.mViewDataBinding = DataBindingUtil.inflate(inflater, this.getLayoutId(), container, false);
        return this.mViewDataBinding.getRoot();
    }

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        this.initViewModel();
        this.initDataBinding();
        super.onViewCreated(view, savedInstanceState);
    }

    protected void initParams() {
    }

    protected void initView(View view) {
    }

    protected void initData() {
    }

    protected abstract int getBindingVariable();

    protected abstract Class<VM> getViewModelClass();

    private void initViewModel() {
        this.mViewModel = (new ViewModelProvider(this)).get(this.getViewModelClass());
    }

    private void initDataBinding() {
        if (this.getBindingVariable() > 0) {
            this.mViewDataBinding.setVariable(this.getBindingVariable(), this.mViewModel);
            this.mViewDataBinding.executePendingBindings();
            this.mViewDataBinding.setLifecycleOwner(this);
        }

    }
}

