package com.dfl.android.common.base;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.dfl.android.animationlib.tools.DialogAnimUtil;
import com.dfl.android.common.R;
import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.jeremyliao.liveeventbus.LiveEventBus;

import androidx.annotation.NonNull;


/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/11/23
 * desc :
 * version: 1.0
 */
public abstract class BaseDialog extends Dialog {
    protected final String TAG = GlobalConstant.GLOBAL_TAG + this.getClass().getSimpleName();
    /**
     * 弹窗动效
     */
    private DialogAnimUtil dialogAnimUtil;
    /** 默认点击蒙层关闭 */
    private boolean canceledOnTouchOutside = true;
    //    private BroadcastReceiver receiver = new BroadcastReceiver() {
    //        @Override
    //        public void onReceive(Context context, Intent intent) {
    //            if ("THEME_CHANGED".equals(intent.getAction())) {
    //                int mUiMode = intent.getIntExtra("mUiMode", Configuration.UI_MODE_NIGHT_UNDEFINED);
    //                CommonLogUtils.logI(TAG,
    //                        this.getClass().getSimpleName() + " THEME_CHANGED BroadcastReceiver,isDayMode:" +
    //                        (mUiMode == Configuration.UI_MODE_NIGHT_NO));
    //                // 接收到黑白模式变化的通知
    //                BarUtils.setStatusAndNaviBarStyle(getWindow(), mUiMode);
    //            }
    //        }
    //    };

    /** 是否4级及以上的菜单弹框 */
    public boolean isLevel4OrAbove = false;

    public BaseDialog(@NonNull Context context) {
        this(context, R.style.Theme_Dialog_TimePickerDialogTheme);
        //registerReceiver();
    }

    public BaseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    /**
     * 对话框带动画显示，必须得包裹一层，方便后续修改
     */
    public void showWithAnimate() {
        show();
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        if (!isLevel4OrAbove) {
            LiveEventBus.get(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(1);
        }
        CommonLogUtils.logI(TAG, getClass().getSimpleName() + " onDetachedFromWindow");
    }

    /**
     * 对话框带动画关闭，必须得包裹一层，方便后续修改
     */
    public void dismissWithAnimate() {
        if (!isLevel4OrAbove) {
            LiveEventBus.get(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(1);
        }
        if (getWindow() != null) {
            View maskView = getWindow().getDecorView().findViewWithTag("maskView");
            View rootView = getWindow().getDecorView().findViewWithTag("rootView");
            if (maskView != null && rootView != null) {
                dialogAnimUtil.hideDialogAnim(maskView, rootView, () -> {
                    //防止因动画延时关闭时已经关闭
                    if (isShowing()) {
                        dismiss();
                    }
                });
                return;
            }
        }
        dismiss();
    }


    /**
     * 因为组件库要求全局遮罩view，不能使用原生点击遮罩隐藏，仿照dialogFragment逻辑
     * 通过判断点击手动添加全局遮罩隐藏
     */
    @Override
    public void setCanceledOnTouchOutside(boolean value) {
        //不使用原生点击遮罩隐藏方法
        super.setCanceledOnTouchOutside(false);
        if (canceledOnTouchOutside == value) {
            return;
        }
        canceledOnTouchOutside = value;
    }

    /**
     * 因为组件库要求全局遮罩view，不能使用原生点击遮罩隐藏，仿照dialogFragment逻辑
     * 通过判断点击手动添加全局遮罩隐藏弹框，同时回调onTouchOutside
     */
    private void setCanceledOnTouchOutsideListener() {
        if (getWindow() == null) return;
        if (canceledOnTouchOutside) {
            //rootView会消耗点击事件，不会传给下面的maskview
            getWindow().getDecorView().findViewWithTag("rootView").setOnClickListener(v -> {
                onTouchOutside();
                dismissWithAnimate();
            });
        }
    }

    public void onTouchOutside() {
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onCreate");
        //动效初始化，防止context为null
        dialogAnimUtil = new DialogAnimUtil(getContext());
        if (getWindow() != null) {
            //全屏透明显示dialog
            getWindow().getDecorView().setPadding(0, 0, 0, 0);
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
            this.getWindow().getDecorView().setSystemUiVisibility(
                    this.getWindow().getDecorView().getWindowSystemUiVisibility() |
                            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            getWindow().setAttributes(layoutParams);
            //获取的是DecorView
            ViewGroup decorView = ((ViewGroup) (getWindow().getDecorView()));
            //根据组件库要求生成全屏遮罩
            View maskView = LayoutInflater.from(getContext()).inflate(R.layout.scene_widiget_maskview, decorView,
                    false);
            //获取的是LinearLayout，层层包裹dialog布局rootView
            View rootView = decorView.getChildAt(0);
            rootView.setTag("rootView");
            decorView.addView(maskView, 0);
            dialogAnimUtil.startDialogAnim(rootView);
            //int mUiMode = this.getContext().getResources().getConfiguration().uiMode & Configuration
            // .UI_MODE_NIGHT_MASK;
            //BarUtils.setStatusAndNaviBarStyle(getWindow(), mUiMode);
        }
        setCanceledOnTouchOutsideListener();
    }

    @Override
    public void show() {
        super.show();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " show");
    }

    @Override
    protected void onStart() {
        super.onStart();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStart");
    }

    @Override
    public void cancel() {
        super.cancel();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " cancel");
    }

    @Override
    public void dismiss() {
        super.dismiss();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " dismiss");
        // 在 Dialog 关闭时取消注册广播接收器，避免内存泄漏
        //getContext().unregisterReceiver(receiver);
    }

    @Override
    protected void onStop() {
        super.onStop();
        CommonLogUtils.logI(TAG, this.getClass().getSimpleName() + " onStop");
    }

    //    private void registerReceiver() {
    //        IntentFilter filter = new IntentFilter();
    //        filter.addAction("THEME_CHANGED");
    //        getContext().registerReceiver(receiver, filter);
    //    }


}
