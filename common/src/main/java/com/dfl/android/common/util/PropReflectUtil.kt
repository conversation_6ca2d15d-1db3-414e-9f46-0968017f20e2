package com.dfl.android.common.util

/**
 * author:<PERSON><PERSON><PERSON><PERSON>
 * e-mail:z<PERSON><PERSON><PERSON>@dfl.com.cn
 * time: 2023/03/06
 * desc: 获取系统设置属性的工具类
 * version:1.0
 */
object PropReflectUtil {
    fun getProp(key: String?): String? {
        try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("get", String::class.java)
            return method.invoke(null, key) as String
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun getPropWithDef(key: String?, def: String): String {
        try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("get", String::class.java, String::class.java)
            return method.invoke(null, key, def) as String
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return def
    }
}