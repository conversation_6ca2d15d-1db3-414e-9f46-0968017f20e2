package com.dfl.android.common.util.constant;

import java.lang.reflect.Field;
import java.util.ArrayList;

public final class StringInputArgSkillIdConstant {
    /**
     * 工作日
     */
    public static final int SKILL_ID_STATUS_TIME_WORKDAY_ID = 0xFF03;

    /**
     * 节假日
     */
    public static final int SKILL_ID_STATUS_TIME_HOLIDAY_ID = 0xFF04;

    /**
     * 关闭多媒体
     */
    public static final int SKILL_ID_ACTION_MEDIA_CLOSE = 0x29;

    /**
     * 关闭应用
     */
    public static final int SKILL_ID_ACTION_APP_CLOSE_APP = 0x6a;

    /**
     * 打开并播放QQ音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_QQ_MUSIC = 0x58;
    /**
     * 打开并播放蓝牙音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_BLUE_MUSIC = 0x59;

    /**
     * 打开并播放USB音乐
     */
    public static final int SKILL_ID_ACTION_OPEN_USB_MUSIC = 0x5a;

    /**
     * 天气预报
     */
    public static final int SKILL_ID_ACTION_APP_WEATHER_REPORT = 0x71;
    /**
     * 车窗透气模式打开
     */
    public static final int SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN = 0x31f;

    /**
     * 车窗通风
     */
    public static final int SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR = 0x306;
    private static ArrayList<Integer> skills = new ArrayList<Integer>();

    public static ArrayList<Integer> getSpecialSkillList() {
        if (skills.isEmpty()) {
            ArrayList<Integer> skillList = new ArrayList<Integer>();
            Field[] fields = NoInputArgSkillIdConstant.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == int.class) {
                    try {
                        skillList.add(field.getInt(null));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
            skills = skillList;
        }
        return skills;
    }
}
