package com.dfl.android.common.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.TimeoutException

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/08
 * desc :CustomApi接口请求帮助类
 * version: 1.0
 */
object CustomApiUtils {
    private const val TIMEOUT_TIMEMILLIS = 3000L

    /**
     * 需要返回数据的接口请求，会等待io自身异步结果（例如再开一个协程不会等待），请求超时默认null
     * @param serviceIsLink 服务是否连接
     * @param io 初始化方法
     * @param error 错误处理
     * @param reconnection 重连方法
     */
    fun requestCustomApi(
        serviceIsLink: Boolean,
        io: suspend () -> Unit,
        error: (e: Exception) -> Unit,
        reconnection: () -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            if (serviceIsLink) {
                val result = withTimeoutOrNull(TIMEOUT_TIMEMILLIS) {
                    val job = CoroutineScope(Dispatchers.IO).launch {
                        try {
                            io.invoke()
                        } catch (e: Exception) {
                            error.invoke(e)
                        }
                    }
                    job.join()
                }
                if (result == null) {
                    error.invoke(TimeoutException())
                }
            } else {
                reconnection.invoke()
            }
        }
    }

    /**
     * 简单不需要判断是否注册连接的接口方法
     */
    fun requestCustomApi(block: suspend () -> Unit, error: (e: Exception) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                block.invoke()
            } catch (e: Exception) {
                error.invoke(e)
            }
        }
    }

}