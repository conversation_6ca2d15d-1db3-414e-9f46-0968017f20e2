plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk rootProject.compileSdk

    defaultConfig {
        minSdk rootProject.minSdk
        targetSdk rootProject.targetSdk

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding true
        dataBinding true
    }
    lintOptions {
        abortOnError false
    }
    configurations {
        all {
            exclude group: 'com.google.guava', module: 'listenablefuture'
        }
    }
}

dependencies {
    //    api project(path: ':SkinSDK')
    api "androidx.appcompat:appcompat:$appCompatVersion"
    api "com.google.android.material:material:$materialVersion"
    //    api "androidx.constraintlayout:constraintlayout:$constraintLayoutVersion"
    // kotlin
    api "androidx.core:core-ktx:$coreKotlinVersion"
    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    // ViewModel
    api("androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version")
    // LiveData
    api("androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version")
    //navigation
    //    api "androidx.navigation:navigation-fragment-ktx:$navigationVersion"
    //    api "androidx.navigation:navigation-ui-ktx:$navigationVersion"
    //常用工具类代码库
    //    api "com.blankj:utilcodex:$utilCodexVersion"
    //微信开源项目，替代SP
//    api "com.tencent:mmkv:$mmkvVersion"


    api files('libs\\sdk.jar')
    api 'com.iauto:uibase:+'
    api 'com.iauto:uicontrol:+'
    api 'com.dfl:dflcommonlibs:1.0.0'
    api "com.alibaba:fastjson:$fastjsonVersion"
    //接入热修复模块
    api "com.dfl:dflpatchlibs:1.0.1"
    api "androidx.multidex:multidex:2.0.1"
    api("com.tencent.tinker:tinker-android-lib:$tinkerpluginVersion") {changing= true}
    compileOnly files('libs\\framework.jar')
    api "androidx.media:media:$mediaVersion"
    api "androidx.navigation:navigation-fragment:$navigationVersion"
    api "androidx.navigation:navigation-ui:$navigationVersion"
    //MQTT
    api 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0'
    api 'org.eclipse.paho:org.eclipse.paho.android.service:1.1.1'
    api "androidx.media:media:$mediaVersion"
    api 'com.dfl:mediabar:+'


    //内存泄漏查找工具
    debugApi "com.squareup.leakcanary:leakcanary-android:$leakcanaryVersion"
    //glide,图片加载框架
    api "com.github.bumptech.glide:glide:$glideVersion"
    annotationProcessor "com.github.bumptech.glide:compiler:$glideVersion"

    // 协程核心库
    api "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutinesVersion"
    // 协程Android支持库
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion"
    // 协程Java8支持库
    api "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:$coroutinesVersion"

    //代替eventbus的消息总线
    api "io.github.jeremyliao:live-event-bus-x:$liveEventBusVersion"
    //夜间模式
    api "com.dfl.common:nightmode:$nightModeVersion"
    //通用库
    api "com.dfl.android:commonlib-n:$commonLibVersion"

    api 'com.google.guava:guava:19.0'

    api "com.squareup.retrofit2:retrofit:$retrofitVersion"
    api 'com.squareup.okhttp3:logging-interceptor:3.14.0'
    api "com.squareup.retrofit2:converter-gson:$retrofitVersion"
    api 'org.bouncycastle:bcprov-jdk15to18:1.68'

    //    api 'io.github.shixinzhang:android-bitmap-monitor:1.1.0'
    //应用间通信
    api "com.dfl.common:androhermas:$androhermasVersion"

    //屏幕适配 https://github.com/JessYanCoding/AndroidAutoSize/tree/master
    api "com.github.JessYanCoding:AndroidAutoSize:v1.2.1"

    //场景引擎
    api files('libs/scenarioadapter0.15.jar')

    //日产动效组件 https://note.youdao.com/ynoteshare/index.html?id=d6c97620f5052b6283887cc5d8fd007f&type=note&_time=1721613312396
    api "com.dfl.android:animationlib:$animationLibVersion"

    //customapi
    debugApi files("libs/customapi_${customapi_version}.jar")
    releaseCompileOnly files("libs/customapi_${customapi_version}.jar")

    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库
    api 'com.umeng.umsdk:common:9.8.0' // 必选
    api 'com.umeng.umsdk:asms:1.8.6' // 必选
    api 'com.umeng.umsdk:apm:2.0.0' // U-APM产品包依赖，必选
}