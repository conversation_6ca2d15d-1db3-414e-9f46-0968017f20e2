package com.dfl.smartscene.util

import android.content.Context
import android.view.MotionEvent
import androidx.test.core.app.ApplicationProvider
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * 测试MaskFrameLayout的事件拦截功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class MaskFrameLayoutTest {

    private lateinit var context: Context
    private lateinit var maskFrameLayout: MaskFrameLayout

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        maskFrameLayout = MaskFrameLayout(context)
    }

    @Test
    fun testMaskFrameLayoutInterceptsTouchEvents() {
        // 测试触摸事件拦截
        val motionEvent = MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0)
        
        // 验证拦截所有触摸事件
        val intercepted = maskFrameLayout.onInterceptTouchEvent(motionEvent)
        assert(intercepted) { "MaskFrameLayout should intercept all touch events" }
        
        // 验证消费所有触摸事件
        val consumed = maskFrameLayout.onTouchEvent(motionEvent)
        assert(consumed) { "MaskFrameLayout should consume all touch events" }
        
        motionEvent.recycle()
    }

    @Test
    fun testMaskClickListener() {
        var clickCallbackTriggered = false
        
        // 设置点击监听器
        maskFrameLayout.setOnMaskClickListener {
            clickCallbackTriggered = true
        }
        
        // 验证点击监听器设置成功
        assert(maskFrameLayout.isClickable) { "MaskFrameLayout should be clickable after setting listener" }
        
        // 模拟点击事件
        maskFrameLayout.performClick()
        
        // 验证回调被触发
        assert(clickCallbackTriggered) { "Click callback should be triggered" }
    }
}
