package com.dfl.smartscene.customapi

import com.dfl.smartscene.communication.CarControlLocalManager
import com.dfl.smartscene.communication.SystemSettingsLocalManager

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/04/04
 * desc: customApi的管理器
 * version:1.0
 */
object ApiManager {

    /**
     * 初始化customApi
     */
    fun initApi() {
        initPrimaryApi()
        initApplicationApi()
    }

    /**
     * 初始化部品控制的接口
     */
    fun initPrimaryApi() {
        //埋点升级sdk后不需要手动获取daid
        //AppCommonManager.init(object : AppCommonManagerCallback {
        //    override fun onInitResult(result: Boolean) {
        //        CommonLogUtils.logD(GlobalConstant.GLOBAL_TAG.plus("ApiManager"), "AppCommonManager初始化结果：$result")
        //        if (result) {
        //            DaVersionManager.init()
        //        }
        //    }
        //
        //    override fun onApiSTRModeStatusChanged(strMode: Int?) {
        //    }
        //})
        SoundManager.init()
    }

    /**
     * 初始化应用间通信的接口
     */
    fun initApplicationApi() {
        CarControlLocalManager.init()
        SystemSettingsLocalManager.init()
    }

    fun getAiDeviceState(): ArrayList<String>? {
        return CarControlLocalManager.getAiDeviceState()
    }


}