package com.dfl.smartscene.customapi

import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.Lk1AVinConstant
import com.dfl.android.common.global.Lk2AVinConstant
import com.dfl.android.commonlib.log.CommonLogUtils

/**
 * author:zhushi<PERSON>
 * e-mail:<EMAIL>
 * time: 2023/08/18
 * desc: 场景社区获取vin码的管理类
 * version:1.0
 */
object CarInfoManager {
    private val TAG = GlobalConstant.GLOBAL_TAG + "CarInfoManager"

    /**
     * 车辆的vin码
     */
    private val mVin: String? = ConfigManager.getVin()

    private var defaultVin = if (ConfigManager.isLK1A()) Lk1AVinConstant.DEFAULT_HIGH_VIN else Lk2AVinConstant.L6_VIN

    /**
     * 获取vin码，如果是台架到则返回默认的vin码
     */
    fun getVinWithDefault(): String {
        if (isTestBench(mVin)) {
            return defaultVin
        }
        return mVin!!
    }

    /**
     * adb命令设置台架请求场景社区默认vin码
     * adb shell am startservice -n com.dfl.smartscene/.service.SceneWidgetService -a com.dfl.smartscene.changeDefaultVin --ei config 1 --es vin LK2ATEST10140039v
     * @param config LK1A:其他entry低配,1bsg中配,2upr高配；LK2A：1～5 L2～L6
     * @param   vin vin码，为空或长度不为17不支持设置
     */
    fun setDefaultVin(config: Int, vin: String? = null) {
        defaultVin = if (vin.isNullOrEmpty() || vin.length != GlobalConstant.VIN_LENGTH) {
            when (config) {
                1 -> {
                    if (ConfigManager.isLK1A()) {
                        Lk1AVinConstant.DEFAULT_MIDDLE_VIN
                    } else {
                        Lk2AVinConstant.L2_VIN
                    }
                }
                2 -> {
                    if (ConfigManager.isLK1A()) {
                        Lk1AVinConstant.DEFAULT_HIGH_VIN
                    } else {
                        Lk2AVinConstant.L3_VIN
                    }
                }
                3 -> Lk2AVinConstant.L4_VIN
                4 -> Lk2AVinConstant.L5_VIN
                5 -> Lk2AVinConstant.L6_VIN
                else -> {
                    if (ConfigManager.isLK1A()) {
                        Lk1AVinConstant.DEFAULT_LOW_VIN
                    } else {
                        Lk2AVinConstant.L1_VIN
                    }
                }
            }
        } else {
            vin
        }
        CommonLogUtils.logI(TAG, "adb设置默认vin码$defaultVin,config=$config,vin=$vin")
    }

    private fun isTestBench(carVinInfo: String?): Boolean {
        if (carVinInfo == null || carVinInfo.length != GlobalConstant.VIN_LENGTH) return true
        return carVinInfo == "00000000000000000" || carVinInfo.equals("vin-0123456789ABC", true)
                || carVinInfo.equals("ABCDEFGHIJKLMNOPQ", true)
                || carVinInfo.endsWith("v", true)
    }

}