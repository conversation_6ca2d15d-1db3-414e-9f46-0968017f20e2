package com.dfl.smartscene.customapi

import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.api.base.Constants
import com.dfl.api.da.version.IVersion
import com.dfl.smartscene.bean.customapi.DaVersionBean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/6/26.
 *Describer:埋点获取Da相关信息
 */
@Deprecated("埋点新版sdk不需要手动获取")
object DaVersionManager : BaseSoaManager() {
    @Volatile
    private var iVersion: IVersion? = null
    private var mDaVersionBean: DaVersionBean? = null

    override fun init() {
        TAG = GlobalConstant.GLOBAL_TAG.plus("DaVersionManager")
        CoroutineScope(Dispatchers.IO).launch {
            initCustomApi()
        }
    }

    private suspend fun initCustomApi(): Boolean = initCustomApi(
        Constants.DFL_DA_VERSION_VERSION_SERVICE,
        iVersion,
        { if (it is IVersion) it else null },
        { iVersion = it }
    )


    suspend fun getDaVersionBean(): DaVersionBean? {
        return suspendCoroutine { continuation ->
            CustomApiUtils.requestCustomApi({
                if (initCustomApi()) {
                    mDaVersionBean = DaVersionBean(
                        iVersion?.daHwVersion,
                        iVersion?.daSwVersion,
                        iVersion?.mcuSwVersion,
                        iVersion?.meterHwVersion,
                        iVersion?.meterSwVersion,
                        iVersion?.tcuVersion,
                        iVersion?.daDefaultStatus
                    )
                    CommonLogUtils.logI(TAG, "获取DaVersionBean成功：" + mDaVersionBean.toString())
                }
                continuation.resume(mDaVersionBean)
            }, {
                CommonLogUtils.logE(TAG, "获取DaVersionBean失败")
                mDaVersionBean = null
                continuation.resume(null)
            })
        }
    }
}