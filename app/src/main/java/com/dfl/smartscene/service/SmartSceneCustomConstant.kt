package com.dfl.smartscene.service

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/01/03
 * desc : 应用间通讯的协议ID
 * version: 1.0
 */
class SmartSceneCustomConstant {
    companion object {
        /**
         * 场景变更通知--dispatch需要一直监听
         */
        const val SMART_SCENE_CHANGED_PROTOCOL_ID = 10001

        /**
         * 获取场景名称列表,语音获取
         */
        const val SMART_SCENE_NAME_LIST_PROTOCOL_ID = 10002

        /**
         * 语音执行场景
         */
        const val SMART_SCENE_VOICE_RUN_SCENE_PROTOCOL_ID = 10003

        /**
         * 获取场景列表数据信息(需要一直监听)，SystemUi获取
         */
        const val SMART_SCENE_DATA_LIST_PROTOCOL_ID = 10004

        /**
         * 控制场景自动执行的监听-打开或关闭场景自动执行
         */
        const val SMART_SCENE_CONTROL_AUTO_RUN_PROTOCOL_ID = 10005

        /**
         * 试运行场景-将试运行场景的JSON数据发送过来执行
         */
        const val SMART_SCENE_TRY_RUN_SCENE_PROTOCOL_ID = 10006


        /**打开编辑界面*/
        const val SMART_SCENE_OPEN_EDIT = 10007

        /**语音转换+过滤 并打开编辑界面*/
        const val SMART_SCENE_CONVERT_AND_OPEN_EDIT = 10008
    }
}