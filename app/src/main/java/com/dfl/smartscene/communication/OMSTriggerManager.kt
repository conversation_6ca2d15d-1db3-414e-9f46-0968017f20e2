package com.dfl.smartscene.communication

import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.soa.SceneManager
import com.dfl.soacenter.SoaCenterService
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.OmsChildPositionList
import com.dfl.soacenter.entity.OmsPassengerBehaviour
import com.dfl.soacenter.entity.OmsPassengerBehaviourList
import com.dfl.soacenter.entity.OmsPassengerDistribution
import com.dfl.soacenter.entity.OmsPassengerGenderAgeList
import com.dfl.soacenter.entity.OmsPositionSendCCM
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 *Created by 钟文祥 on 2024/11/4.
 *Describer: OMS 应用间通信管理  - 触发条件
 */
object OMSTriggerManager : BaseSoaManager() {
    private object ProtocolType {

        /**儿童位置*/
        const val CHILD_POSITION = 10002

        /**乘客分布*/
        const val PASSENGER_DISTRIBUTION = 10006

        /**乘客性别年龄*/
        const val PASSENGER_GENDER_AGE = 10010

        /**乘客行为*/
        const val PASSENGER_BEHAVIOUR = 10003

    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("OMSTriggerManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), "com.dfl.oms")
                .setReconnectMode(ConnectionClient.CONNECT_MODE_LIMITED_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    fun startDispatch() {
        if (isHasMyScene(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_CHILDREN)) {
            dispatchChildPositionList()
        }
        if (isHasMyScene(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_SOMEONE)) {
            dispatchPassengerDistribution()
        }
        if (isHasMyScene(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_GENDER)) {
            dispatchPassengerGenderAge()
        }
        if (isHasMyScene(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_BEHAVIOR)) {
            dispatchPassengerBehaviourList()
        }
    }

    fun cancelAllDispatch() {
        stopDispatch("dispatchChildPositionList")
        stopDispatch("dispatchPassengerDistribution")
        stopDispatch("dispatchPassengerGenderAge")
        stopDispatch("dispatchPassengerBehaviourList")
    }

    /**保存oms场景上一次的某位置是否有儿童*/
    private val sceneLastChildScStatusMap: MutableMap<Int, Boolean> = mutableMapOf()

    /**保存oms场景上一次的某位置是否有人*/
    private val sceneLastPassengerScStatusMap: MutableMap<Int, Boolean> = mutableMapOf()

    /**保存oms场景上一次的某位置性别 -1未识别*/
    private val sceneLastGenderMap: MutableMap<Int, Int> = mutableMapOf()

    /**保存oms场景上一次的某位置行为集 */
    private val sceneLastBehaviorMap: MutableMap<Int, OmsPassengerBehaviour> = mutableMapOf()

    /**1.3监听变化 儿童位置集合*/
    fun dispatchChildPositionList() {
        val method = "dispatchChildPositionList"
        if (isRunningDispatch(method)) return

        CommonLogUtils.logI(TAG, "初始化了儿童监听 $method")
        for (i in 0..7) {
            sceneLastChildScStatusMap[i] = false
        }
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.CHILD_POSITION, messageType = SoaConstants.MESSAGE_TYPE_DISPATCH, data = null
            )
        )
        dispatchToSendMessage(json, method, actionSuccess = { result ->
            //有数据变化，进入这个方法
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsChildPositionList>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsChildPositionList::class.java
                )
            )
            if (res.data?.isSuccess() == true) { //数据有效
                //返回的是当前有儿童的位置
                val omsDataList = res.data?.arrayList ?: return@dispatchToSendMessage
                if (omsDataList.isEmpty()) return@dispatchToSendMessage

                //1.先获取自动执行场景 -儿童检测
                val copyList = getMySceneBeanList(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_CHILDREN)
                //2.遍历副本
                copyList.forEach { mySceneBean ->

                    //该场景触发条件的左侧位置和右侧状态数值
                    val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input ?: return@forEach
                    val position = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_POSITION
                    }[0]?.value?.toInt() ?: return@forEach
                    val scStatus = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_SCSTATUS
                    }[0]?.value?.toInt() ?: return@forEach

                    if (scStatus == 1) { //从 无-》有 的过程
                        if (sceneLastChildScStatusMap[position] == false && omsDataList.filter {
                                    it.position == position
                                }.isNotEmpty()) {  //满足触发条件 发送到ccm
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scStatus, 1))
                        }
                    } else if (scStatus == 0) { //从 有-》无 的过程
                        if (sceneLastChildScStatusMap[position] == true && omsDataList.filter {
                                    it.position == position
                                }.isEmpty()) {  //满足触发条件 发送到ccm
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scStatus, 1))
                        }
                    }
                }
                for (i in 0..7) {
                    sceneLastChildScStatusMap[i] = false
                }
                omsDataList.forEach {
                    if (it.position != -1) {
                        sceneLastChildScStatusMap[it.position!!] = true
                    }
                }
            }

        }, actionFail = {})
    }

    /**1.7监听变化 查询乘客分布状态*/
    fun dispatchPassengerDistribution() {
        val method = "dispatchPassengerDistribution"
        if (isRunningDispatch(method)) return

        CommonLogUtils.logI(TAG, "初始化了乘客分布监听 $method")
        for (i in 0..7) {
            sceneLastPassengerScStatusMap[i] = false
        }
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PASSENGER_DISTRIBUTION,
                messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
                data = null
            )
        )

        dispatchToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerDistribution>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsPassengerDistribution::class.java
                )
            )

            if (res.data?.isSuccess() == true) { //数据有效
                //返回的是当前有车上人员的位置
                val omsData = res.data ?: return@dispatchToSendMessage

                //1.先获取自动执行场景 -儿童检测
                val copyList = getMySceneBeanList(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_SOMEONE)
                //2.遍历副本
                copyList.forEach { mySceneBean ->

                    //该场景触发条件的左侧位置和右侧状态数值
                    val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input ?: return@forEach
                    val position = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_POSITION
                    }[0]?.value?.toInt() ?: return@forEach
                    val scStatus = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_SCSTATUS
                    }[0]?.value?.toInt() ?: return@forEach

                    if (scStatus == 1) { //从 无-》有 的过程
                        if (omsData.isHasPeopleByPosition(position) == true && sceneLastPassengerScStatusMap[position] == false) {  //满足触发条件 发送到ccm
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scStatus, 2))
                        }
                    } else if (scStatus == 0) { //从 有-》无 的过程
                        if (omsData.isHasPeopleByPosition(position) == false && sceneLastPassengerScStatusMap[position] == true) {  //满足触发条件 发送到ccm
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scStatus, 2))
                        }
                    }
                }
                sceneLastPassengerScStatusMap[0] = omsData.driver!!
                sceneLastPassengerScStatusMap[1] = omsData.codriver!!
                sceneLastPassengerScStatusMap[2] = omsData.rearleft!!
                sceneLastPassengerScStatusMap[3] = omsData.rearmid!!
                sceneLastPassengerScStatusMap[4] = omsData.rearright!!
            }
        }, actionFail = {})
    }

    /**1.11监听变化 查询性别年龄*/
    fun dispatchPassengerGenderAge() {
        val method = "dispatchPassengerGenderAge"
        if (isRunningDispatch(method)) return

        CommonLogUtils.logI(TAG, "初始化了性别监听 $method")
        for (i in 0..7) {
            sceneLastGenderMap[i] = -1
        }
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PASSENGER_GENDER_AGE,
                messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
                data = null
            )
        )
        dispatchToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerGenderAgeList>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsPassengerGenderAgeList::class.java
                )
            )
            if (res.data?.isSuccess() == true) { //数据有效
                //返回的是当前有性别的位置
                val omsDataList = res.data?.arrayList ?: return@dispatchToSendMessage
                if (omsDataList.isEmpty()) return@dispatchToSendMessage

                val filter = omsDataList.filter {
                    it.position != -1 && it.gender != -1
                }
                if (filter.isEmpty()) { //全车能识别到的座位都没有能识别性别
                    for (i in 0..7) { //考虑到有些人下车了 ,与上一次数据相比
                        sceneLastGenderMap[i] = -1
                    }
                    return@dispatchToSendMessage
                }

                //1.先获取自动执行场景 -检测位置性别
                val copyList = getMySceneBeanList(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_GENDER)
                //2.遍历副本
                copyList.forEach { mySceneBean ->

                    //该场景触发条件的左侧位置和右侧状态数值
                    val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input ?: return@forEach
                    val position = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_POSITION
                    }[0]?.value?.toInt() ?: return@forEach
                    val scGender = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_SCGENDER
                    }[0]?.value?.toInt() ?: return@forEach // 0 男性 1 女性

                    val temp = omsDataList.filter {
                        it.position == position
                    }
                    if (temp.isNotEmpty()) {
                        if (temp[0].gender == scGender && temp[0].gender != sceneLastGenderMap[position]) { //gender -1; 无效值 0; 男性  1; 女性
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scGender, 3))
                        }
                    }
                }
                for (i in 0..7) { //考虑到有些人下车了
                    sceneLastGenderMap[i] = -1
                }
                omsDataList.forEach {
                    if (it.position!! != -1) {
                        sceneLastGenderMap[it.position!!] = it.gender!!
                    }
                }
            }
        }, actionFail = {})
    }

    /**1.4监听变化 乘客行为集合*/
    fun dispatchPassengerBehaviourList() {
        val method = "dispatchPassengerBehaviourList"
        if (isRunningDispatch(method)) return

        CommonLogUtils.logI(TAG, "初始化了乘客行为监听 $method")
        for (i in 0..7) {
            sceneLastBehaviorMap[i] = OmsPassengerBehaviour(i, false, false, false)
        }
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PASSENGER_BEHAVIOUR,
                messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
                data = null
            )
        )
        dispatchToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerBehaviourList>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsPassengerBehaviourList::class.java
                )
            )
            if (res.data?.isSuccess() == true) { //数据有效
                //返回的是当前有行为的位置
                val omsDataList = res.data?.arrayList ?: return@dispatchToSendMessage
                if (omsDataList.isEmpty()) return@dispatchToSendMessage

                val filter = omsDataList.filter {
                    it.position != -1 && (it.smoking == true || it.drinking == true || it.calling == true)
                }
                if (filter.isEmpty()) { //全车能识别到的座位没有动作
                    omsDataList.forEach {
                        if (it.position!! != -1) {
                            sceneLastBehaviorMap[it.position!!] = it
                        }
                    }
                    return@dispatchToSendMessage
                }

                //===进入到这里 表示，识别到有些位置是有动作的===

                //1.先获取自动执行场景 -检测位置行为
                val copyList = getMySceneBeanList(SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_BEHAVIOR)
                //2.遍历副本
                copyList.forEach { mySceneBean ->

                    //该场景触发条件的左侧位置和右侧状态数值
                    val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input ?: return@forEach
                    val position = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_POSITION
                    }[0]?.value?.toInt() ?: return@forEach
                    val scBehavior = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_SCBEHAVIOR
                    }[0]?.value?.toInt() ?: return@forEach //0 打电话  1 未打电话  2 抽烟  3 未抽烟 4 喝水  5 未喝水

                    val temp = omsDataList.filter {
                        it.position == position
                    }
                    if (temp.isNotEmpty()) {
                        if (temp[0].isHasBehaviourByIndex(scBehavior) == true && //
                                sceneLastBehaviorMap[position]?.isHasBehaviourByIndex(scBehavior) == false
                        ) {
                            LiveEventBus.get(
                                SoaCenterService.KEY_SEND_OMS_2CCM, OmsPositionSendCCM::class.java
                            ).postAcrossProcess(OmsPositionSendCCM(position, scBehavior, 4))
                        }
                    }
                }
                omsDataList.forEach {
                    if (it.position!! != -1) {
                        sceneLastBehaviorMap[it.position!!] = it
                    }
                }
            }
        }, actionFail = {})
    }


    //测试
    fun getPassengerDistribution2() {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PASSENGER_DISTRIBUTION,
                messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
                data = null
            )
        )
        val method = "getPassengerDistribution"
        queryToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<OmsPassengerDistribution>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, OmsPassengerDistribution::class.java
                )
            )
        }, actionFail = {})
    }


    private fun getMySceneBeanList(skillId: Int): ArrayList<MySceneBean> {
        // 1.遍历所有我的场景 是自动执行的 找出触发条件有位置要求的场景, 输出副本
        val copyList: ArrayList<MySceneBean> = arrayListOf()
        SceneManager.getSceneInfoList().forEach() { mySceneBean ->
            val edgeCondition = mySceneBean.scenario.scenarioInfo?.edgeCondition
            //不是自动执行不发送 场景
            if (mySceneBean.scenario.scenarioInfo?.autoExeFlag != 0) return@forEach
            // 符合的场景 能力id
            if (edgeCondition?.skillId == skillId) {
                // 集合的复制 https://blog.csdn.net/badme/article/details/127015675
                copyList.add(mySceneBean.copy())
            }
        }
        if (copyList.size > 0) {
            CommonLogUtils.logI(TAG, "能力id：${skillId} 当前skillId为${skillId} 的自动执行场景数量:${copyList.size}")
        }
        return copyList
    }

    /**判断是否存在 有自动执行的对应能力*/
    private fun isHasMyScene(skillId: Int): Boolean {
        val list = SceneManager.getSceneInfoList().filter {
            it.scenario.scenarioInfo?.edgeCondition?.skillId == skillId && it.scenario.scenarioInfo?.autoExeFlag == 0
        }
        return list.isNotEmpty()
    }

}