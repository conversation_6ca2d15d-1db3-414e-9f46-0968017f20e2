package com.dfl.smartscene.communication

import android.content.Context
import android.content.IntentFilter
import com.dfl.androhermas.constant.StatusCode
import com.dfl.androhermas.remote.client.ConnectCallback
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.androhermas.remote.client.RequestCallback
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.CollectSceneList
import com.dfl.smartscene.bean.communication.GeneralRequestMessage
import com.dfl.smartscene.bean.communication.RunSceneList
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.AutoRunSceneInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/12/31
 * desc: launcher通讯管理器
 * version:1.0
 */
object LauncherManager {
    private const val TAG = "${GlobalConstant.GLOBAL_TAG}LauncherManager"
    private const val PACKAGE_NAME = "com.dfl.launcher"
    private const val ACTION_NAME = "$PACKAGE_NAME.actionProtocol"

    /**
     * 显示通知卡片
     */
    const val OPERATE_TYPE_SHOW = 1

    /**
     * 隐藏通知卡片
     */
    const val OPERATE_TYPE_HIDE = 2

    /**
     * 发送收藏场景的协议
     */
    const val PROTOCOL_COLLECT = 7001

    /**
     * 发送运行场景列表的协议
     */
    const val PROTOCOL_RUN_LIST = 7002

    /**
     * 发送通知卡片的协议
     */
    @Deprecated("移动到消息中心")
    const val PROTOCOL_NOTIFY = 7003


    /**
     * aidl客户端，用于连接launcher
     */
    private var mLauncher: ConnectionClient? = null

    /**
     * 应用
     */
    private val mLauncherCallBack: LauncherCallBack = LauncherCallBack()


    /**
     * 操作卡片的广播action
     */
    const val ACTION_OPERATE = "com.dfl.launcher.smartScene"

    /**
     * 广播的extra的key值,操作类型
     */
    const val EXTRA_OPERATE_NAME = "operaType"

    /**
     * 广播的extra的key值，场景id
     */
    const val EXTRA_SCENE_ID = "id"

    /**
     * 执行的动作参数
     */
    const val EXTRA_OPERATE_RUN = 1

    /**
     * 场景中断动作的参数
     */
    const val EXTRA_OPERATE_STOP = 2

    /**
     * 通知卡片场景执行
     */
    const val EXTRA_OPERATE_NOTIFY_RUN = 3

    /**
     * 通知卡片倒计时自动结束，要自动执行
     */
    const val EXTRA_OPERATE_RUN_TIME_OUT = 4

    /**
     * 通知卡片场景主动点击关闭
     */
    const val EXTRA_OPERATE_NOTIFY_IGNORE = 5

    /**
     * 初始化
     */
    fun init() {
        initClient()
        CoroutineScope(Dispatchers.Default).launch {
            connectClient()
            //            registerLauncherActionReceiver(CommonUtils.getApp())
        }
    }

    /**
     * 初始化连接客户端
     */
    private fun initClient() {
        mLauncher =
            ConnectionClient.Builder(CommonUtils.getApp(), PACKAGE_NAME)
                    .setThreadPool(1, 1)
                    .setActionName(ACTION_NAME)
                    .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                    .build()
    }

    /**
     * 连接launcher
     */
    private fun connectClient() {
        mLauncher?.connect(object : ConnectCallback {
            override fun onConnect() {
                CommonLogUtils.logI(TAG, "Launcher已连接")
            }

            override fun onDisconnect(statusCode: Int) {
                CommonLogUtils.logI(TAG, "Launcher已断开连接$statusCode")
            }

        })
    }

    /**
     * 发送消息给launcher
     */
    private fun sendMessage(messageJson: String) {
        if (mLauncher == null) {
            initClient()
        }
        CommonLogUtils.logI(TAG, "SOA方法：-request- json===>${messageJson}")
        CoroutineScope(Dispatchers.IO).launch {
            mLauncher?.sendMessage(messageJson, mLauncherCallBack)
        }
    }

    /**
     * 注册卡片操作广播接收器
     */
    @Deprecated("在AndroidManifest静态注册了")
    private fun registerLauncherActionReceiver(context: Context) {
        CommonLogUtils.logI(TAG, "注册广播")
        val filter = IntentFilter()
        filter.addAction(ACTION_OPERATE)
        //context.registerReceiver(mLauncherReceiver, filter)
    }

    /**
     * 发送收藏的场景信息
     */
    fun sendCollectSceneInfo(collectScene: CollectSceneList) {
        val generalRequestMessage = GeneralRequestMessage()
        generalRequestMessage.messageType = SoaConstants.MESSAGE_TYPE_REQUEST
        generalRequestMessage.requestCode = GeneralInfoUtils.getRequestCode()
        generalRequestMessage.protocolId = PROTOCOL_COLLECT
        generalRequestMessage.requestAuthor = GeneralInfoUtils.getRequestAuthor()
        generalRequestMessage.versionName = GeneralInfoUtils.getVersionName()
        generalRequestMessage.data = collectScene
        val message = GsonUtils.toJson(generalRequestMessage)
        sendMessage(message)
    }

    /**
     * 发送正在执行的场景信息
     */
    fun sendRunSceneList(runSceneList: RunSceneList) {
        val generalRequestMessage = GeneralRequestMessage()
        generalRequestMessage.messageType = SoaConstants.MESSAGE_TYPE_REQUEST
        generalRequestMessage.requestCode = GeneralInfoUtils.getRequestCode()
        generalRequestMessage.protocolId = PROTOCOL_RUN_LIST
        generalRequestMessage.requestAuthor = GeneralInfoUtils.getRequestAuthor()
        generalRequestMessage.versionName = GeneralInfoUtils.getVersionName()
        generalRequestMessage.data = runSceneList
        val message = GsonUtils.toJson(generalRequestMessage)
        sendMessage(message)
    }

    /**
     * 发送通知卡片操作信息
     */
    @Deprecated("移动到消息中心")
    fun sendAutoRunInfo(autoRunSceneInfo: AutoRunSceneInfo) {
        val generalRequestMessage = GeneralRequestMessage()
        generalRequestMessage.messageType = SoaConstants.MESSAGE_TYPE_REQUEST
        generalRequestMessage.requestCode = GeneralInfoUtils.getRequestCode()
        generalRequestMessage.protocolId = PROTOCOL_NOTIFY
        generalRequestMessage.requestAuthor = GeneralInfoUtils.getRequestAuthor()
        generalRequestMessage.versionName = GeneralInfoUtils.getVersionName()
        generalRequestMessage.data = autoRunSceneInfo
        val message = GsonUtils.toJson(generalRequestMessage)
        sendMessage(message)
    }

    /**
     * Launcher的回调信息
     */
    private class LauncherCallBack : RequestCallback {
        override fun onMessage(result: String?, statusCode: Int) {
            CommonLogUtils.logI(TAG, "SOA方法：-response,statusCode:$statusCode -json===>${result}")
            if (statusCode != StatusCode.OK) {
                return
            }
        }
    }
}