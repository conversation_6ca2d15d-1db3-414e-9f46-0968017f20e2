package com.dfl.smartscene.communication

import android.annotation.SuppressLint
import android.content.Context
import android.os.RemoteException
import android.provider.Settings
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.constant.StatusCode
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.AppUtils
import com.dfl.android.common.util.CustomApiUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.SetAgreeStatus
import com.dfl.smartscene.bean.communication.SetTheme
import com.dfl.smartscene.bean.communication.SystemSettingBean
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/02/03
 * desc :
 * version: 1.0
 */
object SystemSettingsLocalManager {
    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SystemSettingsManager")

    @SuppressLint("StaticFieldLeak")
    private var mSystemSetting: ConnectionClient? = null

    var mSystemThemeIsNight = false

    @Volatile
    private var mAgreementConnected = false

    fun init() {
        CustomApiUtils.requestCustomApi({
            mSystemSetting = ConnectionClient.Builder(
                CommonUtils.getApp(),
                ServiceConstant.Package.PACKAGE_SYSTEM_SETTINGS
            )
                    .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                    .setThreadPool(1, 1)
                    .build()
        }, {
            handleException("system init===>", it)
        })
    }

    /**
     * 获取协议状态，打开协议弹窗
     */
    fun getAgreementStatus() {
        //设置监听
        CustomApiUtils.requestCustomApi({
            runBlocking {
                val result = withTimeoutOrNull(5000) {
                    while (!mAgreementConnected) {
                        checkService()
                        delay(1000)
                    }
                }
                if (result == null)
                    LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_SYSTEM_SETTING_PRIVATE_STATE_CHANGED)
                            .post(-1)
            }
            if (!mAgreementConnected)
                return@requestCustomApi
            val bean = SystemSettingBean(
                "request", "1021",
                GlobalConstant.APPLICATION_ID,
                System.currentTimeMillis().toString(), AppUtils.getAppVersionName(), null
            )
            val json = GsonUtils.toJson(bean)
            CommonLogUtils.logI(TAG, "SOA方法：getAgreementStatus -request- json===>${json}")
            mSystemSetting?.sendMessage(
                json
            ) { result, statusCode ->
                result?.let {
                    mSystemSetting?.unBindServer()
                    CommonLogUtils.logI(TAG, "SOA方法：getAgreementStatus -response- json===>${json}")
                    if (statusCode == StatusCode.OK) {
                        try {
                            val systemBean = GsonUtils.fromJson(it, SystemSettingBean::class.java)
                            val data = systemBean.data.toString()
                            val status = GsonUtils.fromJson(data, SetAgreeStatus::class.java)
                            if (status.status == 1) {
                                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_SYSTEM_SETTING_PRIVATE_STATE_CHANGED)
                                        .post(1)
                            }
                        } catch (e: Throwable) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }, {
            handleException("getAgreementStatus", it)
        })
    }


    private fun checkService(): Boolean {
        if (mSystemSetting == null) {
            init()
            return false
        }
        mAgreementConnected = true
        return true
    }

    /**
     * 处理异常场景
     */
    private fun handleException(tag: String, e: Exception) {
        if (e is RemoteException) {
            mSystemSetting = null
            init()
        }
        e.printStackTrace()
        CommonLogUtils.logD(TAG, "$tag,  $e")
    }

    fun setTheme(themeType: Int) {
        CommonLogUtils.logD(TAG, "setTheme:$themeType")
        if (checkService()) {
            CustomApiUtils.requestCustomApi({
                val bean = SystemSettingBean(
                    "request", "1028", GlobalConstant.APPLICATION_ID,
                    System.currentTimeMillis().toString(), AppUtils.getAppVersionName(),
                    SetTheme(themeType)
                )
                val json = GsonUtils.toJson(bean)
                CommonLogUtils.logD(TAG, "setTheme sendMessage====>$json")
                mSystemSetting?.sendMessage(
                    json
                ) { result, statusCode ->
                    CommonLogUtils.logD(TAG, "setTheme======>$result===statusCode===>$statusCode")
                    mSystemSetting?.unBindServer()
                }
            }, {
                handleException("systemSetting manager setTheme", it)
            })
        }
    }

    /**
     * 是否已签订隐私协议
     *  //0不同意协议。1同意协议
     *  同意返回true；不同意返回false
     */
    @JvmStatic
    fun isProtocolAgree(context: Context): Boolean {
        return getProtocolStatus(context) == 1
    }

    /**
     * 0 不同意协议，1同意协议，-1撤回协议后变成了默认状态
     */
    @JvmStatic
    fun getProtocolStatus(context: Context): Int {
        val status = Settings.System.getInt(
            context.contentResolver,
            "protocol_status", 0
        )
        CommonLogUtils.logD(TAG, "获取隐私协议:$status")
        return status
    }
}