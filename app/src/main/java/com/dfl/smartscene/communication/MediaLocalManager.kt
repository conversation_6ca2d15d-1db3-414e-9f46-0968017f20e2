package com.dfl.smartscene.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.dfl.soacenter.entity.SearchKeyBean
import com.dfl.soacenter.entity.SingerInfo
import com.dfl.soacenter.entity.SongSingerInfo
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 *Created by 钟文祥 on 2024/3/1.
 *Describer:
 */
object MediaLocalManager : BaseSoaManager() {

    private object ProtocolType {
        /**搜索歌曲*/
        const val SEARCH_SONG = 50103

        /**搜索歌手*/
        const val SEARCH_SINGER = 50104
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("MediaLocalManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_MEDIA)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .setThreadPool(1, 1)
                .build()
    }

    /**搜索歌曲*/
    suspend fun searchSong(key: String): MutableList<SongSingerInfo>? {
        return try {
            suspendCancellableCoroutine { continuation ->
                val job = CoroutineScope(Dispatchers.IO).launch {
                    val json = GsonUtils.toJson(
                        RequestSoaBaseBean(
                            protocolId = ProtocolType.SEARCH_SONG, data = SearchKeyBean(key)
                        )
                    )
                    val method = "searchSong"
                    queryToSendMessage(json, method, { result ->
                        val res = Gson().fromJson<ResponseSoaBaseBean<Any>>(
                            result, GsonUtils.type(
                                ResponseSoaBaseBean::class.java, Any::class.java
                            )
                        )
                        if (res.statusCode == 0) {
                            val list = GsonUtils.fromJson<List<SongSingerInfo>>(
                                res.data.toString(), GsonUtils.type(
                                    List::class.java, SongSingerInfo::class.java
                                )
                            ).toMutableList()
                            if (continuation.isActive && !continuation.isCancelled) {
                                continuation.resume(list)
                            }

                        } else {
                            CommonLogUtils.logE(TAG, "qq音乐回调成功但失败-方法：$method,  原因：${res.statusCode}")
                            if (continuation.isActive && !continuation.isCancelled) {
                                continuation.resume(null)
                            }
                        }
                        return@queryToSendMessage
                    }, {
                        if (continuation.isActive && !continuation.isCancelled) {
                            continuation.resume(null)
                        }
                        return@queryToSendMessage
                    })
                }
                // 当外部协程取消时，取消内部协程
                continuation.invokeOnCancellation {
                    CommonLogUtils.logI(TAG, "searchSong取消内部协程")
                    job.cancel()
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    /**搜索歌手*/
    suspend fun searchSinger(key: String): MutableList<SongSingerInfo>? {
        return try {
            suspendCancellableCoroutine { continuation ->
                val job = CoroutineScope(Dispatchers.IO).launch {
                    val json = GsonUtils.toJson(
                        RequestSoaBaseBean(
                            protocolId = ProtocolType.SEARCH_SINGER, data = SearchKeyBean(key)
                        )
                    )
                    val method = "searchSinger"
                    queryToSendMessage(json, method, { result ->
                        val res = Gson().fromJson<ResponseSoaBaseBean<Any>>(
                            result, GsonUtils.type(
                                ResponseSoaBaseBean::class.java, Any::class.java
                            )
                        )
                        if (res.statusCode == 0) {
                            val list = GsonUtils.fromJson<List<SingerInfo>>(
                                res.data.toString(), GsonUtils.type(
                                    List::class.java, SingerInfo::class.java
                                )
                            )
                            val data = list.map {
                                SongSingerInfo(0, "", it.id, it.name, it.singerPic)
                            }.toMutableList()
                            if (continuation.isActive && !continuation.isCancelled) {
                                continuation.resume(data)
                            }
                        } else {
                            CommonLogUtils.logE(TAG, "qq音乐回调成功但失败-方法：$method,  原因：${res.statusCode}")
                            if (continuation.isActive && !continuation.isCancelled) {
                                continuation.resume(null)
                            }
                        }
                        return@queryToSendMessage
                    }, {
                        if (continuation.isActive && !continuation.isCancelled) {
                            continuation.resume(null)
                        }
                        return@queryToSendMessage
                    })
                }
                // 当外部协程取消时，取消内部协程
                continuation.invokeOnCancellation {
                    CommonLogUtils.logI(TAG, "searchSinger取消内部协程")
                    job.cancel()
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

}