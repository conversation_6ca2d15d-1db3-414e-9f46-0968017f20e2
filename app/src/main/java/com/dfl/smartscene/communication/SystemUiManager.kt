package com.dfl.smartscene.communication

import com.dfl.androhermas.constant.StatusCode
import com.dfl.androhermas.remote.client.ConnectCallback
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.androhermas.remote.client.RequestCallback
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.communication.GeneralRequestMessage
import com.dfl.smartscene.bean.communication.SystemUiBean
import com.dfl.smartscene.service.SceneVoiceManager
import com.dfl.soacenter.SoaConstants
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2024/02/28
 * desc:系统界面
 * version:1.0
 */
object SystemUiManager {
    private const val TAG = "${GlobalConstant.GLOBAL_TAG}SystemUiManager"

    /**
     * 包名
     */
    private const val PACKAGE_NAME = "com.dfl.systemui"

    /**
     * Action名
     */
    private const val ACTION_NAME = "$PACKAGE_NAME.actionProtocol"

    /**
     * 智慧场景的场景数
     */
    private const val PROTOCOL_SCENE_COUNT = 1001

    /**
     * aidl客户端，用于连接launcher
     */
    private var mClient: ConnectionClient? = null

    /**
     * 调用结果回调
     */
    private val mCallBack = MyCallBack()

    /**
     * 初始化
     */
    fun init() {
        initClient()
        CoroutineScope(Dispatchers.Default).launch {
            mClient?.connect(object : ConnectCallback {
                override fun onConnect() {
                    CommonLogUtils.logI(TAG, "SystemUi已连接")
                    //连接成功发送一次数据
                    sendSceneCountData()
                }

                override fun onDisconnect(statusCode: Int) {
                    CommonLogUtils.logI(TAG, "SystemUi已断开连接$statusCode")
                }

                override fun onReconnectFail(reconnectMode: Int, connectedCount: Int, totalCount: Int) {
                    CommonLogUtils.logI(TAG, "重连失败:$reconnectMode,$connectedCount,$totalCount")
                }


            })

        }
        initListener()
    }

    /**
     * 初始化监听器
     */
    private fun initListener() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).observeForever {
            CommonLogUtils.logD(TAG, "收到智慧场景变更的通知")
            sendSceneCountData(SceneVoiceManager.getSceneInfo())
        }
    }

    /**
     * 发送一次场景的数据
     */
    private fun sendSceneCountData() {
        sendSceneCountData(SceneVoiceManager.getSceneInfo())
    }

    /**
     * 发送一次场景的数据
     */
    private fun sendSceneCountData(systemUiBean: SystemUiBean) {
        val generalRequestMessage = GeneralRequestMessage()
        generalRequestMessage.messageType = SoaConstants.MESSAGE_TYPE_REQUEST
        generalRequestMessage.requestCode = GeneralInfoUtils.getRequestCode()
        generalRequestMessage.protocolId = PROTOCOL_SCENE_COUNT
        generalRequestMessage.requestAuthor = GeneralInfoUtils.getRequestAuthor()
        generalRequestMessage.versionName = GeneralInfoUtils.getVersionName()
        generalRequestMessage.data = systemUiBean
        val message = GsonUtils.toJson(generalRequestMessage)
        sendMessage(message)
    }

    private fun initClient() {
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), PACKAGE_NAME)
                .setThreadPool(1, 1)
                .setActionName(ACTION_NAME)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT)
                .build()
    }

    /**
     * 发送消息给launcher
     */
    private fun sendMessage(messageJson: String) {
        if (mClient == null) {
            initClient()
        }
        CommonLogUtils.logI(TAG, "SOA方法 ：-request- json===>${messageJson}")
        CoroutineScope(Dispatchers.IO).launch {
            mClient?.sendMessage(messageJson, mCallBack)
        }
    }

    /**
     * 回调信息
     */
    private class MyCallBack : RequestCallback {
        override fun onMessage(result: String?, statusCode: Int) {
            CommonLogUtils.logI(TAG, "SOA方法 ：-response,statusCode:$statusCode -json===>${result}")
            if (statusCode != StatusCode.OK) {
                return
            }
        }

    }
}