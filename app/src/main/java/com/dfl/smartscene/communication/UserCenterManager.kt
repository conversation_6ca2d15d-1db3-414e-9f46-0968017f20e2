package com.dfl.smartscene.communication

import android.view.View
import androidx.fragment.app.FragmentManager
import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.communication.DispatchUserLoginStatus
import com.dfl.smartscene.bean.communication.RequestUserCenterBean
import com.dfl.smartscene.bean.communication.RequestUserLoginStatus
import com.dfl.smartscene.bean.communication.UserCenterResponse
import com.dfl.smartscene.bean.communication.UserLoginStatus
import com.dfl.smartscene.bean.community.datasource.UserCenterRepository
import com.dfl.smartscene.bean.community.reponse.CommunityRecentSearchResponse
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.soacenter.SoaConstants
import com.google.gson.JsonParseException
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/12/26
 * desc : 与用户中心APP通信管理类
 * version: 1.0
 */
object UserCenterManager : BaseSoaManager() {
    /**
     * 用户登录状态,为空说明未登录/未实名
     */
    @JvmStatic
    var userLoginStatus: UserLoginStatus? = null

    private object ProtocolType {
        /**
         * 获取⽤户登录状态.未登录打开个⼈中⼼切换账号登陆的弹窗
         */
        const val REQUEST_USER_LOGIN_STATE = 1025

        /**
         * 监听个人中心登录状态dispatch
         */
        const val DISPATCH_USER_LOGIN_STATE = 1029
    }

    override fun init() {
        TAG = GlobalConstant.GLOBAL_TAG.plus("UserCenterManager")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_USER_CENTER)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_LIMITED_RECONNECT).setThreadPool(1, 1).build()
    }

    /**
     * 点击防抖,检查网络状态和用户登录,未登录打开跳转弹窗
     * @param v view
     * @param block 网络正常用户已登录回调
     */
    fun checkDebouncingAndNetworkAndLogin(v: View, fragmentManager: FragmentManager, block: () -> Unit) {
        if (!DebouncingUtils.isValid(v)) return
        NetWorkUtils.haveNetwork({
            checkUserLoginStatusWithDialog(fragmentManager)?.let {
                block.invoke()
            }
        })
    }

    /**
     * 检查用户登录状态,并打开跳转对话框
     * @param fragmentManager
     */
    fun checkUserLoginStatusWithDialog(fragmentManager: FragmentManager): UserLoginStatus? {
        if (userLoginStatus == null) {
            DefaultDialogFragment.showDialog(
                fragmentManager,
                "",
                "",
                CommonUtils.getString(R.string.scene_text_usercenter_login),
                CommonUtils.getString(R.string.scene_button_community_user_login),
                CommonUtils.getString(R.string.scene_text_common_cancel),
                object : DefaultDialogFragment.OnDialogButtonClickListener {
                    override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                        dialog.dismissWithAnimate()
                    }

                    override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                        checkUserLoginStatus(true)
                        dialog.dismissWithAnimate()
                    }
                },
            )
        }
        return userLoginStatus
    }

    /**
     * 检查个人中心APP用户登录状态，并请求个人中心最新用户数据
     * @param isJump 是否跳转到个人中心
     * @return 未登录返回null
     */
    @JvmStatic
    fun checkUserLoginStatus(isJump: Boolean): UserLoginStatus? {
        requestUserLoginStatus(isJump, userLoginStatus == null)
        return userLoginStatus
    }

    /**
     * 获取当前用户信息,不能设置超时
     * 登录成功个人中心会给用户数据
     * @param isJump 是否跳转到个人中心
     */
    private fun requestUserLoginStatus(isJump: Boolean, notifyLoginIn: Boolean = false) {
        val bean = RequestUserCenterBean(
            protocolId = ProtocolType.REQUEST_USER_LOGIN_STATE,
            data = RequestUserLoginStatus(autoLogin = isJump, autoHide = isJump)
        )
        queryToSendMessage(GsonUtils.toJson(bean), "requestUserLoginStatus", false, { json ->
            try {
                val res = GsonUtils.fromJson<UserCenterResponse<UserLoginStatus>>(
                    json, GsonUtils.type(UserCenterResponse::class.java, UserLoginStatus::class.java)
                )
                if (res.result == UserCenterResponse.SUCCESS) {
                    res.data?.let {
                        //用户数据如果不更新不发送liveEvent事件
                        if (it == userLoginStatus) {
                            return@let
                        }
                        userLoginStatus = it
                        if (notifyLoginIn) {
                            //如果是用户登录，或者第一次获取用户数据事件
                            LiveEventBus.get<Boolean>(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS)
                                    .post(false)
                        } else {
                            //否则果是用户数据更新事件
                            LiveEventBus.get<UserLoginStatus>(GlobalLiveEventConstants.KEY_USER_CENTER_GET_USERID_LOGIN_STATUS)
                                    .post(it)
                        }
                    }
                } else if (res.result == UserCenterResponse.UN_LOGIN && userLoginStatus != null) {
                    //如果个人中心未通知用户退登
                    dealLoginOut()
                }
            } catch (e: Exception) {
                if (e is JsonParseException || e is JSONException) {
                    CommonLogUtils.logE(TAG, "requestUserLoginStatus json转换失败,json=$json,e=$e")
                }
                e.printStackTrace()
            }
        }, {})
    }


    /**
     * 监听个人中心APP用户登陆与退登事件
     */
    fun dispatchUserLoginStatus() {
        val bean = RequestUserCenterBean(
            protocolId = ProtocolType.DISPATCH_USER_LOGIN_STATE,
            messageType = SoaConstants.MESSAGE_TYPE_DISPATCH,
            data = null
        )
        dispatchToSendMessage(GsonUtils.toJson(bean), "dispatchUserLoginStatus", { json ->
            try {
                val res = GsonUtils.fromJson<UserCenterResponse<DispatchUserLoginStatus>>(
                    json, GsonUtils.type(UserCenterResponse::class.java, DispatchUserLoginStatus::class.java)
                )
                res.data?.let {
                    if (it.status == DispatchUserLoginStatus.USER_SIGN_OUT) {
                        dealLoginOut()
                    } else if (userLoginStatus == null) {
                        //用户登录,且没有缓存用户数据先缓存最新uuid,再发起请求获取最新用户数据
                        userLoginStatus = UserLoginStatus(it.uuid, "", "", "")
                        requestUserLoginStatus(isJump = false, notifyLoginIn = true)
                    }
                }
            } catch (e: Exception) {
                if (e is JsonParseException || e is JSONException) {
                    CommonLogUtils.logE(TAG, "requestUserLoginStatus json转换失败,json=$json,e=$e")
                }
                e.printStackTrace()
            }
        }, {})
    }

    private fun dealLoginOut() {
        if (userLoginStatus != null) {
            CommonLogUtils.logI(TAG, "个人中心退出登录,清空用户登录状态,发送退登事件")
            //退出登录,清空用户登录状态,发送退登事件
            userLoginStatus = null
            LiveEventBus.get<Boolean>(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS)
                    .post(true)
            //清空历史搜索记录
            CoroutineScope(Dispatchers.IO).launch {
                DbManager.getDBMaster()?.RecentSearchDao()?.insert(
                    CommunityRecentSearchResponse(emptyList())
                )
            }
            //清空当前用户的相关账号页缓存
            UserCenterRepository.getInstance().deleteAllData()
        }
    }
}

