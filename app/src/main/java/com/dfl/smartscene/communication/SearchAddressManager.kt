package com.dfl.smartscene.communication

import com.dfl.androhermas.constant.ServiceConstant
import com.dfl.androhermas.remote.client.ConnectionClient
import com.dfl.android.common.base.BaseSoaManager
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.GsonUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.soacenter.SoaConstants
import com.dfl.soacenter.entity.KeywordBean
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.RequestSoaBaseBean
import com.dfl.soacenter.entity.ResponseSoaBaseBean
import com.dfl.soacenter.entity.SearchMapAddressBean
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *Created by 钟文祥 on 2024/10/23.
 *Describer: 导航 搜索地址 应用间通信管理
 */
object SearchAddressManager : BaseSoaManager() {
    private object ProtocolType {
        const val PRE_SEARCH_KEY_PROTOCOL_ID = 20006 //发起导航预搜索
    }

    override fun init() {
        TAG = SoaConstants.SOA_TAG.plus("SearchAddressManager2")
        mClient = ConnectionClient.Builder(CommonUtils.getApp(), ServiceConstant.Package.PACKAGE_NAVI)
                .setReconnectMode(ConnectionClient.CONNECT_MODE_NOT_RECONNECT).setThreadPool(1, 1).build()
    }

    fun sendKeyWordToNavi(key: String) {
        val json = GsonUtils.toJson(
            RequestSoaBaseBean(
                protocolId = ProtocolType.PRE_SEARCH_KEY_PROTOCOL_ID, data = KeywordBean(key)
            )
        )
        val method = "sendKeyWordToNavi"
        queryToSendMessage(json, method, actionSuccess = { result ->
            val res = Gson().fromJson<ResponseSoaBaseBean<SearchMapAddressBean>>(
                result, GsonUtils.type(
                    ResponseSoaBaseBean::class.java, SearchMapAddressBean::class.java
                )
            )
            val bean = res.data
            if (bean?.isSuccess() == true) {
                LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
                        .post(bean.Pois)
            } else {
                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_GET_DATA_ERROR_CALLBACK).post(1)
            }
        }, actionFail = {
            LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_GET_DATA_ERROR_CALLBACK).post(1)
        })
    }


    suspend fun searchAddressByIM(key: String): PoiItem? {
        return suspendCoroutine { continuation ->
            val json = GsonUtils.toJson(
                RequestSoaBaseBean(
                    protocolId = ProtocolType.PRE_SEARCH_KEY_PROTOCOL_ID, data = KeywordBean(key)
                )
            )
            val method = "sendKeyWordToNavi"
            queryToSendMessage(json, method, actionSuccess = { result ->
                val res = Gson().fromJson<ResponseSoaBaseBean<SearchMapAddressBean>>(
                    result, GsonUtils.type(
                        ResponseSoaBaseBean::class.java, SearchMapAddressBean::class.java
                    )
                )
                val bean = res.data
                if (bean?.isSuccess() == true && bean.Pois != null && bean.Pois.size > 0) {
                    LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
                            .post(bean.Pois)
                    continuation.resume(bean.Pois[0])
                } else {
                    continuation.resume(null)
                }
            }, actionFail = {
                continuation.resume(null)
            })
        }
    }

}