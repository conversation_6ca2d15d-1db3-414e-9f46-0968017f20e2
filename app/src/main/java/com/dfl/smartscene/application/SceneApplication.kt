package com.dfl.smartscene.application

import android.app.ActivityManager
import android.app.UiModeManager
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.database.ContentObserver
import android.os.Bundle
import android.os.Handler
import android.os.Process
import android.os.StrictMode
import android.provider.Settings
import com.bumptech.glide.Glide
import com.dfl.androhermas.utils.LoggerUtil
import com.dfl.android.common.base.BaseApp
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.ActivityUtils
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.CrashUtils
import com.dfl.android.common.util.GeneralInfoUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.common.nightmode.NightModeManager
import com.dfl.common.nightmode.inflater.SkinAppCompatViewInflater
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.communication.LauncherManager
import com.dfl.smartscene.communication.OMSTriggerManager
import com.dfl.smartscene.communication.SearchAddressManager
import com.dfl.smartscene.communication.SystemSettingsLocalManager
import com.dfl.smartscene.communication.SystemUiManager
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.customapi.ApiManager
import com.dfl.smartscene.customapi.TcuManager
import com.dfl.smartscene.receiver.FactoryResetReceiver
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.service.SceneWidgetService
import com.dfl.smartscene.ui.main.MainActivity
import com.dfl.smartscene.util.MMKVUtils
import com.dfl.smartscene.util.TrackUtils
import com.dfl.soacenter.SoaCenterService
import com.dfl.soacenter.communication.MessageCenterManager
import com.dfl.voicehelper.VoiceHelper
import com.jeremyliao.liveeventbus.LiveEventBus
import com.secneo.iov.Helper
import com.umeng.commonsdk.UMConfigure
import com.umeng.umcrash.UMCrash
import com.umeng.umcrash.custommapping.UAPMCustomMapping
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/08
 * desc   : application，应用入口
 * version: 1.0
 */
class SceneApplication : BaseApp() {

    companion object {
        const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneApplication")
        var IS_FIRST_OPEN_SMART_SCENE = true
        fun getContextObject(): Context? {
            return CommonUtils.getApp()
        }
    }


    override fun onCreate() {
        super.onCreate()
        val processName = getProcessName1()
        CommonLogUtils.logI(TAG, "application打开:$processName}")
        //		initApp()
        if (processName == null) {
            initApp()
        } else {
            if (processName == packageName) {
                // 主进程初始化逻辑
                initApp()
            } else {
                // 其他进程不初始化
                CommonLogUtils.logD(TAG, "其他进程打开")
                //第三方库要进行初始化，否则会无法使用
                initThirdLibrary()
            }
        }
    }

    private fun initApp() {
        if (com.dfl.soacenter.BuildConfig.DEBUG) {
            enableStrictMode()
        }
        initThirdLibrary()
        initConfig()
        DbManager.initAsync()
        initListener()
        logUiModeInfo()
        initVoiceHelper()
    }


    /**
     * 设置语音可见即可说
     */
    private fun initVoiceHelper() {
        //建议在应用Application中初始化
        VoiceHelper.getInstance().setLogEnable(BuildConfig.DEBUG)
        //播放点击动效和点击同步进行
        VoiceHelper.getInstance().clickMode = 0
    }

    /**
     * 初始化一些永久监听器
     */
    private fun initListener() {
        //注册恢复出厂设置的广播接收器
        FactoryResetReceiver().registerReceiver(this)
        val contentObserver = ProtocolContentObserver(null)
        contentResolver.registerContentObserver(
            Settings.System.getUriFor("protocol_status"), true, contentObserver
        )
    }

    private fun initThirdLibrary() {
        // 基础控件换肤初始化
        NightModeManager.withoutActivity(this).addInflater(SkinAppCompatViewInflater())
        try {
            CommonUtils.init(this)
            CommonLogUtils.logI(TAG, "onCreate,version:${GeneralInfoUtils.getVersionName()}")
            CommonLogUtils.setHeadInfo(com.dfl.soacenter.BuildConfig.DEBUG)
            //commonLib v1.3之后使用动画通过windowManager实现
            CommonToastUtils.openAnimate(true)
            if (!BuildConfig.DEBUG) {
                LoggerUtil.setLogLevel(LoggerUtil.LEVEL_WARN)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        MMKVUtils.getInstance().init(this@SceneApplication)
        //初始化崩溃日志收集工具,因此日志在上线后无法导出，不再使用-20230906
        if (com.dfl.soacenter.BuildConfig.DEBUG) {
            CoroutineScope(Dispatchers.IO).launch {
                CrashUtils.init()
            }
        }
        initCrashSdk()
    }


    private fun getProcessName1(): String? {
        val am = getSystemService(ACTIVITY_SERVICE) as ActivityManager
        val runningProcesses = am.runningAppProcesses ?: return null
        for (processInfo in runningProcesses) {
            if (processInfo.pid == Process.myPid()) {
                return processInfo.processName
            }
        }
        return null
    }


    private fun initConfig() {
        TcuManager.initTcu()
        //设置卡片的点击监听
        //		WidgetUpdateHelper.setWidgetClick4EntranceAndUpdate(this)
        initCommunication()
        startForegroundService(Intent(this, SceneWidgetService::class.java))
        //启动soa 中心服务的service
        val soaIntent = Intent(this, SoaCenterService::class.java)
        startForegroundService(soaIntent)
        //        if (BuildConfig.DEBUG){
        //            initMonitorBitmap()
        //        }
        TrackUtils.initIEventTrackAPI()
    }

    /**
     * 初始化通讯服务
     */
    private fun initCommunication() {
        SearchAddressManager.init()
        MessageCenterManager.init()
        LauncherManager.init()
        SystemUiManager.init()
        UserCenterManager.init()
        ApiManager.initApi()
        OMSTriggerManager.init()

        //监听个人中心用户登陆,退出登录
        UserCenterManager.dispatchUserLoginStatus()

    }

    private fun enableStrictMode() {
        // 监测当前线程（UI线程）上的网络、磁盘读写等耗时操作
        StrictMode.setThreadPolicy(
            StrictMode.ThreadPolicy.Builder().detectDiskReads() // 监测读磁盘
                    .detectDiskWrites() // 监测写磁盘
                    .detectNetwork() // 监测网络操作
                    .detectCustomSlowCalls() // 监测哪些方法执行慢
                    .detectResourceMismatches() // 监测资源不匹配
                    .penaltyLog() // 打印日志，也可设置为弹窗提示penaltyDialog()或者直接使进程死亡penaltyDeath()
                    .penaltyDropBox() //监测到将信息存到Dropbox文件夹 data/system/dropbox
                    .build()
        )

        // 监测VM虚拟机进程级别的Activity泄漏或者其它资源泄漏
        StrictMode.setVmPolicy(
            StrictMode.VmPolicy.Builder().detectActivityLeaks() // 监测内存泄露情况
                    .detectLeakedSqlLiteObjects() // SqlLite资源未关闭，如cursor
                    .detectLeakedClosableObjects() // Closable资源未关闭，如文件流
                    //                .detectCleartextNetwork() // 监测明文网络
                    .setClassInstanceLimit(MainActivity::class.java, 1) // 设置某个类的实例上限，可用于内存泄露提示
                    .detectLeakedRegistrationObjects() // 监测广播或者ServiceConnection是否有解注册
                    .penaltyLog().penaltyDropBox().build()
        )
    }

    override fun onTerminate() {
        super.onTerminate()
        CommonLogUtils.logW(TAG, "onTerminate")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val mUiMode = newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK
        CommonLogUtils.logI(
            TAG,
            "newConfig:change->${newConfig.uiMode},isDayMode->${mUiMode == Configuration.UI_MODE_NIGHT_NO}"
        )
        NightModeManager.get().notifyUpdateSkin(this)
    }

    private fun logUiModeInfo() {
        try {
            val uiManager = getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
            val isNightModeSys = uiManager.nightMode
            val isNightModeApp = this.resources.configuration.uiMode
            CommonLogUtils.logI(
                TAG, "current sys uiMode = $isNightModeSys ,current app uiMode $isNightModeApp"
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initMonitorBitmap() {
        //        val checkInterval = 10L
        //        val threshold = 100 * 1024L
        //        val restoreImageThreshold = 100 * 1024L
        //        val dir = this.getExternalFilesDir("bitmap_monitor")?.absolutePath
        //
        //        val config = BitmapMonitor.Config.Builder()
        //            .checkRecycleInterval(checkInterval)                            // check if the image is recycled interval, in seconds (recommended not too often, default 5 seconds)
        //            .getStackThreshold(threshold)                                   // get the threshold of the stack, when an image occupies more than this value of memory will go to grab the stack
        //            .restoreImageThreshold(restoreImageThreshold)                   // restore the image threshold, when a picture occupies more memory than this value, it will restore an original picture
        //            .restoreImageDirectory(dir)                                     // the directory of the restored image
        //            .showFloatWindow(true)                                          // whether to show a hover window to see the memory size in real time (recommended to open only in debug environment)
        //            .clearAllFileWhenRestartApp(true)
        //            .clearFileWhenOutOfThreshold(true)
        //            .diskCacheLimitBytes(100 * 1024 * 1024)
        //            .isDebug(true)
        //            .context(this)
        //            .build()
        //        BitmapMonitor.init(config)
        //        BitmapMonitor.start()
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        CommonLogUtils.logD(TAG, "onTrimMemory:$level")
        if (level == Glide.TRIM_MEMORY_UI_HIDDEN) {
            Glide.get(CommonUtils.getApp()).clearMemory()
        }
        Glide.get(CommonUtils.getApp()).onTrimMemory(level)
    }

    /**
     * 当整个系统的内存不足时，就会调用此方法，并且主动运行的进程应该会减少内存使用量。
     * 虽然没有定义调用的确切点，但通常情况下，当所有后台进程都被终止时，就会发生调用。
     * 也就是说，在达到杀死托管服务和前台UI的进程之前，我们希望避免杀死这些进程。
     * 你应该实现这个方法来释放你可能持有的任何缓存或其他不必要的资源。从这个方法返回后，系统会为你执行垃圾收集。
     *最好，您应该从ComponentCallbacks2实现ComponentCallbacks2.onTrimMemory，
     * 以便根据不同级别的内存需求逐步卸载资源。
     * 该API可用于API级别14及更高级别，因此您应该只使用此onLowMemory方法作为旧版本的后备方法，
     * 旧版本可以与ComponentCallbacks2.onTrimMemory和ComponentCallbacks2_TRIM_MEMORY_COMPLETE级别相同对待。
     */
    override fun onLowMemory() {
        super.onLowMemory()
        CommonLogUtils.logD(TAG, "onLowMemory")
        Glide.get(CommonUtils.getApp()).onLowMemory()
    }

    /**隐私协议监听*/
    private class ProtocolContentObserver(handler: Handler?) : ContentObserver(handler) {
        override fun onChange(selfChange: Boolean) {
            val status = SystemSettingsLocalManager.getProtocolStatus(CommonUtils.getApp())
            //当隐私协议有变更时，被取消时需要变更卡片的界面,selfChange都为false
            CommonLogUtils.logI(TAG, "隐私协议变更:$selfChange,status=$status")
            LiveEventBus.get(GlobalLiveEventConstants.KEY_PROTOCOL_STATUS, Boolean::class.java).post(status == 1)
            if (status == 0) {
                //不同意协议，结束全部activity
                ActivityUtils.finishAllActivities(true)
            } else if (status == 1 && !UMConfigure.isInit) {
                if (BuildConfig.DEBUG) {
                    CommonLogUtils.logW(TAG, "Debug调试模式不进行友盟SDK的初始化")
                    return
                }
                //同意隐私协议,且友盟没有进行初始化，则需要进行初始化操作
                val umengAppKey = GlobalConfig.UMENG_APP_KEY
                val channel = ConfigManager.getCarProductModel()
                //禁用内存检测
                val bundle = Bundle()
                bundle.putBoolean(UMCrash.KEY_ENABLE_MEMLEAK, false)
                UMCrash.initConfig(bundle)
                UMConfigure.init(CommonUtils.getApp(), umengAppKey, channel, UMConfigure.DEVICE_TYPE_PHONE, "")
                UAPMCustomMapping.putStringParam(UAPMCustomMapping.STRING_PARAM_1, ConfigManager.getDaid())
            }
        }
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        Helper.install(this)
    }

    /**初始化友盟sdk*/
    private fun initCrashSdk() {
        if (BuildConfig.DEBUG) {
            CommonLogUtils.logW(TAG, "Debug调试模式不进行友盟SDK的初始化")
            return
        }
        if (!UMConfigure.isInit) {
            CommonLogUtils.logD(TAG, getProcessName1() + " crashSdk init")
            //是否授权隐私协议，真实状态需要从系统设置处获取
            val isGrant = SystemSettingsLocalManager.isProtocolAgree(this)
            val umengAppKey = GlobalConfig.UMENG_APP_KEY
            val channel = ConfigManager.getCarProductModel()
            if (isGrant) {
                //禁用内存检测
                val bundle = Bundle()
                bundle.putBoolean(UMCrash.KEY_ENABLE_MEMLEAK, false)
                UMCrash.initConfig(bundle)
                //UMConfigure.init(Context context, String appkey, String channel, int deviceType, String pushSecret);
                UMConfigure.init(this, umengAppKey, channel, UMConfigure.DEVICE_TYPE_PHONE, "")
                UAPMCustomMapping.putStringParam(UAPMCustomMapping.STRING_PARAM_1, ConfigManager.getDaid())
            } else {
                //判断隐私协议，如果未授权隐私协议，则先进行预初始化，等待授权后再正式调用初始化
                UMConfigure.preInit(this, umengAppKey, channel)
            }
        }
    }
}