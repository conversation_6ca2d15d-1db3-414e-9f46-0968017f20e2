package com.dfl.smartscene.widget.bannerview.shape.animation

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.animation.PathInterpolator
import com.dfl.smartscene.widget.bannerview.shape.SimpleAnimatorListener

/**
 * @className TwoIndicatorAnimaBudiler
 * <AUTHOR>
 * @version 1.0.0
 * @description 双点翻页符动画构建器
 * @createTime 2025/01/18/17:42
 */
object TwoIndicatorAnimaBuilder {

    private const val TAG = "TwoIndicatorAnimaBuilder"

    fun prevAlpha(from: Float = 1f, to: Float = 0f,callBack: (value: Float)->Unit): Animator{
        return ValueAnimator.ofFloat(from, to).apply {
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
            duration = 100L
            addUpdateListener {
                val alpha = it.animatedValue as Float
                callBack(alpha)
            }
            addListener(object: SimpleAnimatorListener(){
                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    callBack(to)
                }
            })
        }
    }
    fun prevScroll(toValue: Float,callBack: (value: Float)->Unit): Animator =  ValueAnimator.ofFloat(0f, toValue).apply {
        interpolator = PathInterpolator(0.34f, 0.46f, 0.65f, 0.78f)
        duration = 100L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(toValue)
            }
        })
    }


    fun currentSize(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator =  ValueAnimator.ofFloat(from, to).apply {
        interpolator = PathInterpolator(0f, 0f, 1f, 1f)
        duration = 100L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun sizeNotScroll(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator =  ValueAnimator.ofFloat(from, to).apply {
        interpolator = PathInterpolator(0.22f, 0.09f, 0.36f, 1f)
        duration = 250L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun color(from: Int, to: Int, callBack: (value: Int) -> Unit): Animator = ValueAnimator.ofInt(from, to).apply {
        interpolator = PathInterpolator(0f, 0f, 1f, 1f)
        duration = 100L
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                callBack(from)
            }

            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                callBack(to)
            }
        })
    }

    fun colorNotScroll(from: Int, to: Int, callBack: (value: Int) -> Unit): Animator = ValueAnimator.ofInt(from, to).apply {
        interpolator = PathInterpolator(0f, 0f, 1f, 1f)
        duration = 50L
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                callBack(from)
            }

            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                callBack(to)
            }
        })
    }

    //移动
    fun currentScroll(to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(0f, to).apply {
        interpolator = PathInterpolator(0.22f, 1f, 0.36f, 1f)
        duration = 500L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    //组件3
    fun nextSize(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator =  ValueAnimator.ofFloat(from, to).apply {
        interpolator = PathInterpolator(0.7f, 0f, 0.35f, 1f)
        duration = 333L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun nextSizeNotScroll(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator =  ValueAnimator.ofFloat(from, to).apply {
        interpolator = PathInterpolator(0.22f, 0.09f, 0.36f, 1f)
        duration = 200L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    //移动
    fun nextScroll(to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(0f, to).apply {
        interpolator = PathInterpolator(0.22f, 1f, 0.36f, 1f)
        duration = 500L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }
    //透明度
    fun nextAlpha(from: Float, to: Float,callBack: (value: Float)->Unit): Animator = ValueAnimator.ofFloat(from, to).apply {
        interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        duration = 50L
        addUpdateListener {
            val value = it.animatedValue as Float
            callBack(value)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

}