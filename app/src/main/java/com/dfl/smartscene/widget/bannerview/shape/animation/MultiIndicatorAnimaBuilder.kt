package com.dfl.smartscene.widget.bannerview.shape.animation

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.animation.PathInterpolator
import com.dfl.smartscene.widget.bannerview.shape.SimpleAnimatorListener

/**
 * @className MultiIndicatorAnimaBuilder
 * <AUTHOR>
 * @version 1.0.0
 * @description 多点翻页符动画构建器
 * @createTime 2025/01/21/11:07
 */
object MultiIndicatorAnimaBuilder {

    private const val TAG = "MultiIndicatorAnimaBuilder"

    fun scroll(to: Float, callback: (value: Float)->Unit): Animator{
        return ValueAnimator.ofFloat(0f,to).apply {
            interpolator = PathInterpolator(0.22f,1f,0.36f,1f)
            duration = 600
            addUpdateListener {
                val value = it.animatedValue as Float
                callback(value)
            }
            addListener(object: SimpleAnimatorListener(){
                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    callback(to)
                }
            })
        }
    }

    fun alpha(from: Float, to: Float, callback: (alpha: Float)->Unit): Animator{
        return ValueAnimator.ofFloat(from,to).apply {
            interpolator = PathInterpolator(0.33f,0f,0.67f,1f)
            duration = 100
            addUpdateListener {
                val alpha = it.animatedValue as Float
                callback(alpha)
            }
            addListener(object: SimpleAnimatorListener(){
                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    callback(to)
                }
            })
        }
    }

    fun color(from: Int, to: Int, time: Long = 100L,callBack: (value: Int) -> Unit): Animator = ValueAnimator.ofInt(from, to).apply {
        interpolator = PathInterpolator(0f, 0f, 1f, 1f)
        duration = time
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                callBack(from)
            }

            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                callBack(to)
            }
        })
    }
    fun colorNotScroll(from: Int, to: Int,callBack: (value: Int) -> Unit): Animator = ValueAnimator.ofInt(from, to).apply {
        interpolator = PathInterpolator(0f, 0f, 1f, 1f)
        duration = 50L
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                callBack(from)
            }

            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                callBack(to)
            }
        })
    }


    fun sizeNormalToSmall(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0.22f,1f,0.36f,1f)
        duration = 600
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun sizeSelectedToNormal(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0f,0f,1f,1f)
        duration = 100L
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }


    fun sizeSmallToSelected(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0.65f,0f,0.35f,1f)
        duration = 333L
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun sizeNormalToSelected(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0.65f,0f,0.35f,1f)
        duration = 333L
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun sizeSelectedToNormalNotScroll(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0.22f,0.09f,0.36f,1f)
        duration = 250L
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }

    fun sizeNormalToSelectedNotScroll(from: Float, to: Float, callBack: (value: Float) -> Unit): Animator = ValueAnimator.ofFloat(from,to).apply {
        interpolator = PathInterpolator(0.22f,0.09f,0.36f,1f)
        duration = 250L
        addUpdateListener {
            val size = it.animatedValue as Float
            callBack(size)
        }
        addListener(object: SimpleAnimatorListener(){
            override fun onAnimationCancel(animation: Animator) {
                super.onAnimationCancel(animation)
                callBack(to)
            }
        })
    }
}
