package com.dfl.smartscene.widget.flodlayout;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;

/**
 * 可折叠流式布局,宽度设置为wrap content会测量宽度失败遮挡收起按钮
 */
public class FoldLayout extends FlowListView implements FlowLayout.OnFoldChangedListener {
    private static final String TAG = GlobalConstant.GLOBAL_TAG + "FoldLayout";
    private final View upFoldView;
    private final View downFoldView;

    private boolean isDelete = false;

    public FoldLayout(Context context) {
        this(context, null);
    }

    public FoldLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    @SuppressLint("InflateParams")
    public FoldLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        upFoldView = LayoutInflater.from(context).inflate(R.layout.scene_fold_item_fold_up, this, false);
        downFoldView = LayoutInflater.from(context).inflate(R.layout.scene_fold_item_fold_down, this, false);
        upFoldView.setOnClickListener(v -> {
            setFoldLines(1);
            CommonLogUtils.logD(TAG, "upFoldView click");
            flowAdapter.notifyDataChanged();
        });

        downFoldView.setOnClickListener(v -> {
            setFoldLines(3);
            CommonLogUtils.logD(TAG, "downFoldView click");
            flowAdapter.notifyDataChanged();
        });

        //canfold 能否折叠，fold 当前是否折叠状态
        setOnFoldChangedListener(this);
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    /**
     * 计算折叠按钮插入位置
     *
     * @param index 发生折叠的位置，从0开始
     * @param surplusWidth 不包含最后item后间距的剩余空间
     * @param foldView 折叠view
     * @return 插入位置
     */

    private int index(int index, int surplusWidth, View foldView) {
        //已有+childWidth<=widthSize<已有+childWidth+mHorizontalSpacing时surplusWidth为负数
        if (surplusWidth + mHorizontalSpacing < 0) {
            return index;
        }
        int upIndex = index;
        int upWidth = getViewWidth(foldView);
        //当剩余空间大于等于展开View宽度直接加入index+1
        if (surplusWidth >= upWidth) {
            upIndex = index + 1;
        } else { //找到对应的位置
            for (int i = index; i >= 0; i--) {
                View view = getChildAt(i);
                //必须用view.getMeasuredWidth()，不然获取为0
                int viewWidth = view.getMeasuredWidth();
                if (viewWidth + mHorizontalSpacing + surplusWidth >= upWidth) {
                    upIndex = i;
                    break;
                } else {
                    //加上item宽度和item前间距
                    surplusWidth += viewWidth + mHorizontalSpacing;
                }
            }
        }
        return upIndex;
    }

    /**
     * 移除父布局中的子布局
     *
     * @param view
     */
    private void removeFromParent(View view) {
        if (view.getParent() != null) {
            ((ViewGroup) view.getParent()).removeView(view);
        }
    }

    /**
     * inflate加载的布局必须measure后获取宽高
     * 不改变宽高布局测量，获取正确的宽度
     *
     * @return 宽度
     */
    private int getViewWidth(View view) {
        // 获取父容器的宽度和高度
        int parentWidth = MeasureSpec.getSize(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        int parentHeight = MeasureSpec.getSize(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        // 如果视图已经在父容器中，获取它的LayoutParams
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        // 创建宽度的MeasureSpec
        int widthMeasureSpec = getChildMeasureSpec(parentWidth, layoutParams.width);
        // 创建高度的MeasureSpec
        int heightMeasureSpec = getChildMeasureSpec(parentHeight, layoutParams.height);
        // 测量视图
        view.measure(widthMeasureSpec, heightMeasureSpec);
        // 返回视图的测量宽度
        return view.getMeasuredWidth();
    }

    /**
     * 根据childDimension类型获取对应MeasureSpec
     *
     * @param parentSize 父布局的尺寸
     * @param childDimension LayoutParams类型
     * @return measureSpec
     */
    private int getChildMeasureSpec(int parentSize, int childDimension) {
        int measureSpec;
        if (childDimension >= 0) {
            measureSpec = MeasureSpec.makeMeasureSpec(childDimension, MeasureSpec.EXACTLY);
        } else if (childDimension == ViewGroup.LayoutParams.WRAP_CONTENT) {
            measureSpec = MeasureSpec.makeMeasureSpec(parentSize, MeasureSpec.AT_MOST);
        } else if (childDimension == ViewGroup.LayoutParams.MATCH_PARENT) {
            measureSpec = MeasureSpec.makeMeasureSpec(parentSize, MeasureSpec.EXACTLY);
        } else {
            measureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
        }
        return measureSpec;
    }

    @Override
    public void onFoldChange(boolean canFold, boolean fold, int index, int surplusWidth) {
        CommonLogUtils.logI(TAG,
                "canFold:" + canFold + " foldState:" + fold + " foldIndex:" + index + " maxLine:" + getMaxLines() +
                        " foldLine:" + getFoldLines() + " surplusWidth" + surplusWidth);
        removeFromParent(downFoldView);
        removeFromParent(upFoldView);
        // 只有一行，不需要处理
        if (getMaxLines() == 1) {
            return;
        }
        if (getFoldLines() == 1) {
            //index方法会测量组件宽度
            int upIndex = index(index, surplusWidth, downFoldView);
            if (getChildCount() > 0 && canFold) {
                CommonLogUtils.logI(TAG, "add downFoldView at:" + index + " width:" + downFoldView.getMeasuredWidth());
                addView(downFoldView, upIndex);
            } else {
                CommonLogUtils.logE(TAG, "add downFoldView failed,childCount:" + getChildCount());
            }
        } else if (getFoldLines() == 3) {
            //index方法会测量组件宽度
            int upIndex = index(index, surplusWidth, upFoldView);
            if (getMaxLines() >= 3) {
                CommonLogUtils.logI(TAG, "add upFoldView at:" + upIndex + " width:" + upFoldView.getMeasuredWidth());
                addView(upFoldView, upIndex);
            } else {
                CommonLogUtils.logI(TAG,
                        "add upFoldView at:" + getChildCount() + " width:" + upFoldView.getMeasuredWidth());
                addView(upFoldView);
            }
        } else {
            CommonLogUtils.logD(TAG, "remove view");
        }
        //必须延迟请求刷新布局,这样才能再次调用到父布局onMeasure方法
        post(this::requestLayout);
    }
}
