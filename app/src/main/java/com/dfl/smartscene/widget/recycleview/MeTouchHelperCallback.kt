package com.dfl.smartscene.widget.recycleview

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/06/29
 * desc: 我的rv卡片拖拽 附带设计稿阴影和线框效果 固定recycleview footer不能拖拽
 * version:1.0
 */
class MeTouchHelperCallback : ItemTouchHelper.Callback() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("MeTouchHelperCallback")

    /**线框变量，防止频繁解析*/
    private var borderDrawable: Drawable? = null

    /**阴影变量，防止频繁解析*/
    private var shadowBitmap: Bitmap? = null

    //这个方法是设置那些方向,可以拖动和滑动RecyclerView中的item
    override fun getMovementFlags(
        recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder
    ): Int {
        var dragFlags = 0 //拖动方向 ，为0没有此方向
        var swipeFlags = 0 //滑动方向,为0没有此方向,对于GridLayoutManager而言，滑动删除显然不合理
        if (recyclerView.layoutManager is GridLayoutManager || recyclerView.layoutManager is StaggeredGridLayoutManager) {
            dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
            swipeFlags = 0
        } else {
            dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
            swipeFlags = 0
        }
        if (recyclerView.adapter != null && recyclerView.adapter is BaseQuickAdapter<*, *>) {
            val adapter = recyclerView.adapter as BaseQuickAdapter<*, *>
            //设置footer不可被拖拽
            if (adapter.hasFooterLayout() && viewHolder.itemViewType == BaseQuickAdapter.FOOTER_VIEW) {
                dragFlags = 0
                swipeFlags = 0
            }
        }
        return makeMovementFlags(dragFlags, swipeFlags)
    }



    /**
     * 判断能不能被拖拽到目标ViewHolder
     */
    override fun canDropOver(
        recyclerView: RecyclerView, current: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder
    ): Boolean {
        //只要不是footer都可以拖拽到目标位置
        return target.itemViewType != BaseQuickAdapter.FOOTER_VIEW
    }

    /**
     * 开始
     * 针对swipe和drag状态，当swipe或者drag对应的ViewHolder改变的时候调用
     * 我们可以通过重写这个函数获取到swipe、drag开始和结束时机，viewHolder 不为空的时候是开始，空的时候是结束
     */
    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE && viewHolder is ItemHolderMoveCallback) {
            val position = viewHolder.absoluteAdapterPosition
            CommonLogUtils.logI(TAG, "item move start：$position")
            (viewHolder as ItemHolderMoveCallback).onItemHolderMoveStart(position) //meadapter
            if (callback != null) {
                callback?.onItemHolderMoveStart(position) //mefragment
            }
        }
    }

    private var callback: ItemHolderMoveCallback? = null
    fun setItemMoveCallBack(callback: ItemHolderMoveCallback) {
        this.callback = callback
    }

    fun destroyItemMoveCallBack() {
        if (callback != null) callback = null
    }

    /**
     * 在canDropOver()返回true时，会调用该方法让我们拖动换位置的逻辑
     * * 当用户正在拖动子View时调用，可以在这里进行子View位置的替换操作
     * * viewHolder.getAdapterPosition() : 获取item原位置
     * * target.getAdapterPosition() : 获取item目标位置
     */
    override fun onMove(
        recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder
    ): Boolean {
        //拖拽中的ViewHolder的position
        val fromPosition: Int = viewHolder.absoluteAdapterPosition
        //当前拖拽到的item的viewHolder
        val toPosition: Int = target.absoluteAdapterPosition
        CommonLogUtils.logI(TAG, "item move from：$fromPosition ，to：$toPosition")
        if (recyclerView.adapter is ItemMoveCallback) {
            (recyclerView.adapter as ItemMoveCallback).onItemMove(fromPosition, toPosition)
            if (callback != null) {
                callback?.onItemHolderMoving(fromPosition, toPosition)
            }
        }
        return true
    }

    //    用户正在滑动子View时调用，可以在这里进行子View的删除操作
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    /**
     * 针对swipe和drag状态，当一个item view在swipe、drag状态结束的时候调用
     * drag状态：当手指释放的时候会调用
     * swipe状态：当item从RecyclerView中删除的时候调用，一般我们会在onSwiped()函数里面删除掉指定的item view
     */
    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        if (viewHolder is ItemHolderMoveCallback) {
            val position = viewHolder.absoluteAdapterPosition
            CommonLogUtils.logI(TAG, "item move end：$position")
            //解决 ：https://www.jianshu.com/p/c8f2f4c45297 防止Recyclerview计算布局或滚动时计数  SCROLL_STATE_IDLE停止滚动
            if (recyclerView.scrollState == RecyclerView.SCROLL_STATE_IDLE && !recyclerView.isComputingLayout) {
                viewHolder.onItemHolderMoveEnd(position)
            }
            if (callback != null) {
                callback?.onItemHolderMoveEnd(position)
            }
        }
        //取消拖拽阴影 https://blog.xiaozk.site/blog/item-touch-helper-section/
        ViewCompat.setTranslationZ(viewHolder.itemView, 0f)
    }

    override fun isLongPressDragEnabled(): Boolean {
        return false
    }




    /**
     * 在RecycleView.onDraw里面调用，这时如果绘制阴影会在其他item下面
     * 画板每次都会重新绘制，会被频繁触发
     */
    override fun onChildDraw(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
        //取消elevation附带的原生阴影
        viewHolder.itemView.outlineProvider = null
        //在拖拽item原来的位置绘制线框，itemView的位置就是在rv布局的位置
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
            val itemView = viewHolder.itemView
            if (borderDrawable == null) {
                borderDrawable = ResourcesCompat.getDrawable(
                    recyclerView.resources, R.drawable.scene_shape_my_item_move_bg, null
                )
            }
            borderDrawable!!.setBounds(itemView.left, itemView.top, itemView.right, itemView.bottom)
            borderDrawable!!.draw(c)
        }
        //用户松手重置
        if (!isCurrentlyActive) {
            borderDrawable = null
        }
    }

    /**
     * onDrawOver 和 onChildDraw 方法作用类似, 不同的是它是绘制在 item view 图层之上
     * 画板每次都会重新绘制，会被频繁触发，用户松手clearview也会再调用一次
     * @param isCurrentlyActive 用户松手false 用户拖拽true
     */
    override fun onChildDrawOver(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder?,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        super.onChildDrawOver(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null) {
            val itemView = viewHolder.itemView
            if (shadowBitmap == null) {
                shadowBitmap = BitmapFactory.decodeResource(
                    recyclerView.resources, R.drawable.scene_img_me_adapter_bg_drag_shadow
                )
            }
            //偏移量是scene_img_me_adapter_bg_drag_shadow阴影的大小
            val heightDiff = (shadowBitmap!!.height - itemView.height) / 2
            val widthDiff = (shadowBitmap!!.width - itemView.width) / 2
            val paint = Paint()
            c.translate(itemView.left + dX - widthDiff, itemView.top + dY - heightDiff)
            c.drawBitmap(shadowBitmap!!, 0f, 0f, paint)
        }
        //用户松手重置
        if (!isCurrentlyActive) {
            shadowBitmap = null
        }
    }
}