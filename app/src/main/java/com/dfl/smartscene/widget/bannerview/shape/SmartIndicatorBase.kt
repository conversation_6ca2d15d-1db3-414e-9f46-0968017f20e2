package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import androidx.annotation.ColorInt
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.zhpan.indicator.base.BaseIndicatorView

/**
 * @className SmartIndicatorBase
 * <AUTHOR>
 * @version 1.0.0
 * @description 翻页符基类
 * @createTime 2025/01/16/19:58
 */
abstract class SmartIndicatorBase : BaseIndicatorView {


    protected var drawSelectedPointColor = 0
    protected var drawSelectedWidth = 0f
    protected var drawSelectedHeight = 0f
    protected var drawLastSelectedWidth = 0f
    protected var drawLastSelectedHeight = 0f
    protected var drawLastSelectedPointColor = 0

    private var selectedHeight = IndicatorDefaultConfig.selectedHeight
    private var normalHeight = IndicatorDefaultConfig.normalHeight


    protected val mPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    protected var shapeMargin = IndicatorDefaultConfig.shapeMargin

    protected var shapes: MutableList<Indicator> = mutableListOf<Indicator>()

    protected var animSet: AnimatorSet? = null

    protected var mLastPosition = -1

    protected var showNumber = 2

    protected var TAG: String = ""

    protected var scrollDistance = shapeMargin

    protected var startScrollPosition = 1

    protected var notExceedPointCount = 0

    constructor(
        context: Context?,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context!!, attrs, defStyleAttr) {
        setNormalColor(IndicatorDefaultConfig.normalColor)
        setCheckedColor(IndicatorDefaultConfig.selectedColor)
        setCheckedSlideWidth(IndicatorDefaultConfig.selectedWidth)
        setNormalSlideWidth(IndicatorDefaultConfig.normalWidth)
        drawSelectedWidth = getSelectedWidth()
        drawSelectedHeight = getSelectedHeight()
        drawLastSelectedWidth = getNormalWidth()
        drawLastSelectedHeight = getNormalHeight()
        drawSelectedPointColor = getSelectedColor()
        drawLastSelectedPointColor = getNormalColor()

        TAG = GlobalConstant.GLOBAL_TAG.plus("Indicator")
    }

    constructor(
        context: Context?,
        attrs: AttributeSet? = null
    ) : this(context!!, attrs, 0)

    constructor(context: Context) : this(context, null)



    fun startIndicatorAnimation() {
        animSet = AnimatorSet()

        val animations = getAnimations()
        val animationDuration = getAnimationDuration()
        val valueAnimation = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = animationDuration
            addUpdateListener {
                invalidate()
            }
            addListener(object: SimpleAnimatorListener(){
                override fun onAnimationStart(animation: Animator) {
                    super.onAnimationStart(animation)
                    invalidate()
                }

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    invalidate()
                }

                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    invalidate()
                }
            })
        }
        animations.add(valueAnimation)
        animSet?.playTogether(animations)
        animSet?.start()
    }

    abstract fun getAnimations(): MutableList<Animator>
    abstract fun getAnimationDuration(): Long


    fun getSelectedColor(): Int {
        return getCheckedColor()
    }

    fun getNormalColor(): Int {
        return mIndicatorOptions.normalSliderColor
    }

    fun getSelectedWidth(): Float {
        val selectedWidth = mIndicatorOptions.checkedSliderWidth
//        Log.d(TAG, "getSelectedWidth---selectedWidth=$selectedWidth")
        return selectedWidth
    }

    fun getSelectedHeight(): Float {
        return selectedHeight
    }

    fun getNormalWidth(): Float {
        return mIndicatorOptions.normalSliderWidth
    }

    fun getNormalHeight(): Float {
        return normalHeight
    }

   fun cancelAnimation() {
        animSet?.let {
            if (it.isRunning) {
                onCancelAnimation()
                it.cancel()
            }
        }
    }

    abstract fun onCancelAnimation()

    @Deprecated("过时")
    fun setShapeSize(position: Int, width: Float, height: Float) {

    }

    fun setShapeWidth(position: Int, width: Float) {
//        Log.d(TAG, "setShapeWidth---position=$position, width=$width")
        if (positionIsValid(position)) {
            shapes[position].setWidth(width)
            setShapeOffsetX(position)
        }
    }

    fun setShapeHeight(position: Int, height: Float) {
        if (positionIsValid(position)) {
            shapes[position].setHeight(height)
            setShapeOffsetY(position)
        }
    }

    fun setShapeColor(position: Int, @ColorInt color: Int) {
        if (positionIsValid(position)) {
            shapes[position].setColor(color)
        }
    }

    fun setShapeAlpha(position: Int, alpha: Float) {
        if (positionIsValid(position)) {
            shapes[position].setAlpha(alpha)
        }
    }

    fun setShapeAllowDraw(position: Int, allowDraw: Boolean) {
        if (positionIsValid(position)) {
            shapes[position].setAllowDraw(allowDraw)
        }
    }


    fun setShapeCenterX(position: Int, centerX: Float) {
        if (positionIsValid(position)) {
            shapes[position].setCenterX(centerX)
            setShapeOffsetX(position)
        }
    }

    fun getShapeCenterX(position: Int):Float {
        return if (positionIsValid(position)) {
            shapes[position].getCenterX()
        }else{
            0f
        }
    }

    fun getShapeDraw(position: Int):Boolean {
        return if (positionIsValid(position)) {
            shapes[position].isAllowDraw()
        }else{
            false
        }
    }

    fun isShapeShow(position: Int):Boolean {
        return if (positionIsValid(position)) {
            val alpha = shapes[position].getAlpha()
//            Log.d(TAG,"isShapeShow--alpha=$alpha")
            alpha.equals(getAlphaMax())
        }else{
            false
        }
    }

    fun setShapeOffsetX(position: Int) {
        if (positionIsValid(position)) {
            val indicator = shapes[position]
            val dx = indicator.getCenterX() - indicator.getWidth() / 2f
            indicator.setOffsetX(dx)
        }
    }

    fun getShapeOffsetX(position: Int): Float{
        return if (positionIsValid(position)) {
            shapes[position].getOffsetX()
        }else{
            0f
        }
    }

    fun setShapeScrollX(position: Int, scrollValue: Float) {
        if (positionIsValid(position)) {
            val indicator = shapes[position]
            indicator.setScrollDx(scrollValue)
        }
    }

    fun setShapeOffsetY(position: Int) {
        if (positionIsValid(position)) {
            val indicator = shapes[position]
            val dy = indicator.getCenterY() - indicator.getHeight() / 2f
            shapes[position].setOffsetY(dy)
        }
    }

    fun positionIsValid(position: Int) =
        position >= 0 && position < shapes.size


    fun isLeftSlid(): Boolean {
        val leftSlid = getCurrentPosition() > mLastPosition
//        Log.d(TAG, "isLeftSlid---isLeftSlid=$leftSlid")
        return leftSlid
    }

    fun isRightSlid(): Boolean {
        val rightSlid = getCurrentPosition() < mLastPosition
//        Log.d(TAG, "isRightSlid---rightSlid=$rightSlid")
        return rightSlid
    }


    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let {
            it.save()

            mPaint.reset()
            mPaint.isAntiAlias = true

            for (i in 0 until shapes.size) {
                val indicator = shapes[i]
                indicator.draw(canvas, mPaint)
            }
        }
    }

    open fun isAllowScroll(): Boolean {
        val allowScroll = getCurrentPosition() > startScrollPosition
//        Log.d(TAG, "isAllowScroll---allowScroll=$allowScroll, position=${getCurrentPosition()}, startScrollPosition=$startScrollPosition")
        return allowScroll
    }

    protected fun getShape(position: Int): Indicator? {
        return if (positionIsValid(position)) {
            shapes[position]
        } else {
            null
        }
    }


    override fun onPageSelected(position: Int) {
//        super.onPageSelected(position)
        if (shapes.isEmpty()) {
            return
        }
        CommonLogUtils.logI(TAG,"onPageSelected---position=$position, mLastPosition=$mLastPosition, currentPosition=${getCurrentPosition()}")
        if (position != getCurrentPosition()) {
            cancelAnimation()
            mLastPosition = getCurrentPosition()
            setCurrentPosition(position)
            toggle(true)
        }
    }

    abstract fun toggle(isAnimation: Boolean)


    fun getAlphaMax():Float = AlphaType.MAX.value
    fun getAlphaMin():Float = AlphaType.MIN.value


    protected fun initIndicators() {
        shapes.clear()
        for (i in 0 until getPageSize()) {
            val indicator = Indicator()
            indicator.setCenterY(measuredHeight / 2f)
            indicator.setPosition(i)
            indicator.setRectCorner(3f)
            shapes.add(indicator)
        }
    }

    protected fun setPositionParams(position: Int, width: Float, height: Float, alpha: Float, centerX: Float, color: Int){
        if(positionIsValid(position)){
            setShapeWidth(position, width)
            setShapeHeight(position, height)
            setShapeAlpha(position,alpha)
            setShapeCenterX(position,centerX)
            setShapeColor(position,color)
            setShapeAllowDraw(position, true)
        }
    }

    protected fun isLastPosition(position: Int): Boolean {
        return position == (getPageSize() - 1)
    }

    protected fun isLastPrePosition(position: Int): Boolean {
        return position == (getPageSize() - 2)
    }

    protected fun isNotExceedPointCount() = getPageSize() == notExceedPointCount

}