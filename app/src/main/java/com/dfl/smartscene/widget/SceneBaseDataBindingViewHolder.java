package com.dfl.smartscene.widget;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/15
 * desc :针对BaseQuickAdapter添加dataBinding之后footerView没有绑定报warning异常
 * version: 1.0
 */
public class SceneBaseDataBindingViewHolder<BD extends ViewDataBinding> extends BaseViewHolder {
    protected BD viewBing;
    public SceneBaseDataBindingViewHolder(@NonNull View view) {
        super(view);
        if (view.getTag() instanceof String){ // 判断是否是ViewDataBinding
            viewBing= DataBindingUtil.bind(view);
        }
    }
    public BD getViewBing() {
        return viewBing;
    }
}
