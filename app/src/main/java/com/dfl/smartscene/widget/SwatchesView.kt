package com.dfl.smartscene.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.android.common.global.GlobalConstant

/**
 *Created by 钟文祥 on 2024/11/18.
 *Describer: 自定义颜色板
 * https://segmentfault.com/a/1190000022867075?utm_source=sf-related
 */
class SwatchesView : ConstraintLayout {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SwatchesView")

    private var ivSwatches: CornerImageView? = null //颜色面板
    private var ivSelect: CornerImageView? = null //选择器
    private var qudao: ImageView? = null
    private var bitmapSwatches: Bitmap? = null //图片实体
    private var colorSelect: Int = 0 //选中的颜色

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    /**初始化选中颜色的坐标*/
    fun initView(x: Int, y: Int) {
        setSelectColor4Position(x, y)
    }

    /**获取选中的颜色*/
    fun getSelectColorInt(): Int {
        return colorSelect
    }

    /**获取选中的颜色的坐标*/
    fun getSelectColorPosition(): String {
        return "$setX,$setY"
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun init(context: Context) {
        val view = LayoutInflater.from(context).inflate(R.layout.scene_widget_swatches, this)
        ivSwatches = view.findViewById(R.id.iv_swatches)
        ivSelect = view.findViewById(R.id.iv_select)
        qudao = view.findViewById(R.id.iv_qudao)
        bitmapSwatches = BitmapFactory.decodeResource(context.resources, R.drawable.scene_img_light_color_pick)

        ivSwatches?.setOnTouchListener { v, event ->
            event?.let {
                if (it.action == MotionEvent.ACTION_DOWN || it.action == MotionEvent.ACTION_MOVE) {
                    //图片真实尺寸/view尺寸
                    setSelectColor4Position(it.x.toInt(), it.y.toInt())
                }
            }
            true
        }
    }


    private var setX = 0
    private var setY = 0
    private val ivSelectWH = 42
    private fun setSelectColor4Position(x: Int, y: Int) {
        setX = x
        setY = y
        if (x < 0) {
            setX = 0
        }
        if (y < 0) {
            setY = 0
        }
        if (x > bitmapSwatches?.width!! - ivSelectWH) {
            setX =
                bitmapSwatches?.width!! - ivSelectWH//  AutoSizeUtils.dp2px(context, (bitmapSwatches?.width!! - ivSelectWH).toFloat())
        }
        if (y > bitmapSwatches?.height!! - ivSelectWH) {
            setY =
                bitmapSwatches?.height!! - ivSelectWH // AutoSizeUtils.dp2px(context, (bitmapSwatches?.height!! - ivSelectWH).toFloat())
        }

        colorSelect = bitmapSwatches?.getPixel(setX, setY)!!
        val hexColor = String.format("#%06X", 0xFFFFFF and colorSelect)
        CommonLogUtils.logD(TAG, "选中颜色mColorSelect: " + colorSelect + " ,hexColor:" + hexColor)

        //动态设置margin
        val lp = ivSelect?.layoutParams as MarginLayoutParams
        lp.marginStart = setX
        lp.topMargin = setY
        ivSelect?.layoutParams = lp

        qudao?.setBackgroundColor(colorSelect)

    }

}