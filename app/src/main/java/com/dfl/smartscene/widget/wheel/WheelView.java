package com.dfl.smartscene.widget.wheel;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.Dimension;
import androidx.annotation.DrawableRes;
import androidx.core.content.res.ResourcesCompat;

import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.common.nightmode.widget.SkinCompatSupportable;
import com.dfl.smartscene.R;
import com.dfl.smartscene.widget.wheel.adapter.WheelAdapter;
import com.dfl.smartscene.widget.wheel.interfaces.IPickerViewData;
import com.dfl.smartscene.widget.wheel.listener.LoopViewGestureListener;
import com.dfl.smartscene.widget.wheel.listener.OnItemSelectedListener;
import com.dfl.smartscene.widget.wheel.timer.InertiaTimerTask;
import com.dfl.smartscene.widget.wheel.timer.MessageHandler;
import com.dfl.smartscene.widget.wheel.timer.SmoothScrollTimerTask;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 3d滚轮控件
 */
public class WheelView extends View implements SkinCompatSupportable {
    public static final int SCROLL_STATE_IDLE = 0;     // 停止滚动
    public static final int SCROLL_STATE_SETTING = 1;  // 用户设置
    public static final int SCROLL_STATE_DRAGGING = 2; // 用户按住滚轮拖拽
    public static final int SCROLL_STATE_SCROLLING = 3; // 依靠惯性滚动
    private static final String[] TIME_NUM = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09"};
    /** 修改这个值可以改变Y轴滑行速度 */
    private static final int VELOCITY_FLING = 10;
    private final ScheduledExecutorService mExecutor = Executors.newSingleThreadScheduledExecutor();
    /** 临时Rect对象，用于计算text参数 */
    private final Rect tempRect = new Rect();
    /** 非中间文字则用此控制高度，压扁形成3d错觉 */
    public float scaleContent = 0F;
    /** true 3d滚轮 false 2d平面 */
    public Boolean isEnableCurve = false;
    /** 透明度渐变 */
    public boolean isAlphaGradient = false;
    /** 选择背景圆角,默认16px */
    public int backgroundRadius = 16;
    /** 组件padding */
    public int paddingLeft, paddingRight;
    /** 附加单位label */
    public String label = "";
    public String prefix = "";
    /** 滑动状态 */
    int lastScrollState = SCROLL_STATE_IDLE, currentScrollState = SCROLL_STATE_SETTING;
    /** 分隔线类型 */
    private DividerType dividerType;
    private Context context;
    private Handler handler;
    private GestureDetector gestureDetector;
    private OnItemSelectedListener onItemSelectedListener;
    private OnItemScrollListener onItemScrollListener;
    /** 是否只有中间项显示label */
    private boolean isCenterLabel = true;
    private ScheduledFuture<?> mFuture;
    private Paint paintOuterText, paintCenterText, paintLabelText, paintIndicator, paintCenterBackground;
    /** 滚动数据适配器 */
    private WheelAdapter<?> adapter;
    @Dimension
    private int thirdTextSize = 0, secondTextSize = 32, centerTextSize = 40, textLabelSize = 40, textLabelGap = 16;
    @ColorRes
    private int textColorOutId, centerBackgroundColorId, textColorCenterId;
    @ColorInt
    private int textColorOut, centerBackgroundColor, textColorCenter, dividerColor;
    /** 分割线宽度 */
    private int dividerWidth;
    /** 蒙层id */
    private @DrawableRes int maskId;
    /** 蒙层drawable */
    private Drawable maskDrawable;
    private int maxTextWidth, maxTextHeight, textXOffset;
    /** 每行宽度 */
    private int itemWidth;
    /** 每行高度 */
    private float itemHeight;
    /** 字体样式，默认是等宽字体 */
    private Typeface typeface = Typeface.MONOSPACE;
    /** 条目间距倍数 */
    private float lineSpacingMultiplier = 3.0F;
    /** 是否循环滚动 */
    private boolean isLoop;
    /** 第一条线Y坐标值 */
    private float firstLineY;
    /** 第二条线Y坐标 */
    private float secondLineY;
    /** 中间label绘制的Y坐标 */
    private float centerY;
    /** 当前滚动总高度y值 */
    private float totalScrollY;
    /** 初始化默认选中项 */
    private int initPosition;
    /** 选中的Item是第几个 */
    private int selectedItem;
    /** 之前选择的item索引 */
    private int preCurrentIndex;
    /** 绘制条目 */
    private int itemsVisible = 11;
    /** WheelView 控件高度 */
    private int measuredHeight;
    /** WheelView 控件宽度 */
    private int measuredWidth;
    /** 3d滚轮半径 */
    private int radius;
    /** 平滑滑动偏移量 */
    private int mOffset = 0;
    /** 上次滑动y坐标 */
    private float previousY = 0;
    /** 开始滑动坐标 */
    private long startTime = 0;
    private int widthMeasureSpec;
    /** 3d滚轮文字Gravity */
    private int mGravity = Gravity.CENTER;
    /** 中间选中文字开始绘制位置 */
    private int drawCenterContentStart = 0;
    /** 非中间文字开始绘制位置 */
    private int drawOutContentStart = 0;
    /** 3d滚轮 偏移量 */
    private float CENTER_CONTENT_OFFSET;

    public WheelView(Context context) {
        this(context, null);
    }

    public WheelView(Context context, AttributeSet attrs) {
        super(context, attrs);

        DisplayMetrics dm = getResources().getDisplayMetrics();
        float density = dm.density; // 屏幕密度比（0.75/1.0/1.5/2.0/3.0）

        if (density < 1) {//根据密度不同进行适配
            CENTER_CONTENT_OFFSET = 2.4F;
        } else if (1 <= density && density < 2) {
            CENTER_CONTENT_OFFSET = 4.0F;
        } else if (2 <= density && density < 3) {
            CENTER_CONTENT_OFFSET = 6.0F;
        } else if (density >= 3) {
            CENTER_CONTENT_OFFSET = density * 2.5F;
        }

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.WheelView);
            mGravity = a.getInt(R.styleable.WheelView_wheelview_gravity, Gravity.CENTER);
            itemWidth = a.getInt(R.styleable.WheelView_wheelview_itemWidth, 330);
            textColorOutId = a.getResourceId(R.styleable.WheelView_wheelview_textColorOut, R.color.transparent);
            textColorOut = a.getColor(R.styleable.WheelView_wheelview_textColorOut, 0xFFa8a8a8);
            textColorCenterId = a.getResourceId(R.styleable.WheelView_wheelview_textColorCenter, R.color.transparent);
            textColorCenter = a.getColor(R.styleable.WheelView_wheelview_textColorCenter, 0xFF2a2a2a);
            centerBackgroundColorId = a.getResourceId(R.styleable.WheelView_wheelview_centerBackgroundColor,
                    R.color.transparent);
            centerBackgroundColor = a.getColor(R.styleable.WheelView_wheelview_centerBackgroundColor, 0);
            dividerColor = a.getColor(R.styleable.WheelView_wheelview_dividerColor, 0); //0xFFd5d5d5
            dividerWidth = a.getDimensionPixelSize(R.styleable.WheelView_wheelview_dividerWidth, 2);
            centerTextSize = a.getDimensionPixelOffset(R.styleable.WheelView_wheelview_centerTextSize, centerTextSize);
            secondTextSize = a.getDimensionPixelSize(R.styleable.WheelView_wheelview_secondTextSize, secondTextSize);
            thirdTextSize = a.getDimensionPixelSize(R.styleable.WheelView_wheelview_thirdTextSize, 0);
            textLabelSize = a.getDimensionPixelSize(R.styleable.WheelView_wheelview_textLabelSize, textLabelSize);
            textLabelGap = a.getDimensionPixelSize(R.styleable.WheelView_wheelview_textLabelGap, textLabelGap);
            lineSpacingMultiplier = a.getFloat(R.styleable.WheelView_wheelview_lineSpacingMultiplier,
                    lineSpacingMultiplier);
            maskId = a.getResourceId(R.styleable.WheelView_wheelview_maskId, R.color.transparent);
            maskDrawable = ResourcesCompat.getDrawable(getResources(), maskId, null);
            isEnableCurve = a.getBoolean(R.styleable.WheelView_wheelview_isEnableCurve, isEnableCurve);
            isLoop = a.getBoolean(R.styleable.WheelView_wheelview_isLoop, isLoop);
            isCenterLabel = a.getBoolean(R.styleable.WheelView_wheelview_isCenterLabel, isCenterLabel);
            a.recycle();//回收内存
        }
        judgeLineSpace();
        initLoopView(context);
    }

    @Override
    public void applySkin() {
        setCenterBackgroundColor(centerBackgroundColorId);
        setTextColorCenter(textColorCenterId);
        setTextColorOut(textColorOutId);
        setMaskDrawable(maskId);
        invalidate();
    }

    /**
     * 判断间距是否在1.0-4.0之间
     */
    private void judgeLineSpace() {
        if (lineSpacingMultiplier < 1.0f) {
            lineSpacingMultiplier = 1.0f;
        } else if (lineSpacingMultiplier > 4.0f) {
            lineSpacingMultiplier = 4.0f;
        }
    }

    private void initLoopView(Context context) {
        this.context = context;
        handler = new MessageHandler(this);
        gestureDetector = new GestureDetector(context, new LoopViewGestureListener(this));
        gestureDetector.setIsLongpressEnabled(false);
        isLoop = true;

        totalScrollY = 0;
        initPosition = -1;
        initPaints();
    }

    private void initPaints() {
        paintOuterText = new Paint();
        paintOuterText.setColor(textColorOut);
        paintOuterText.setAntiAlias(true);
        paintOuterText.setTypeface(typeface);
        paintOuterText.setTextSize(secondTextSize);

        paintCenterText = new Paint();
        paintCenterText.setColor(textColorCenter);
        paintCenterText.setAntiAlias(true);
        paintCenterText.setTypeface(typeface);
        paintCenterText.setTextSize(centerTextSize);
        paintCenterText.setElegantTextHeight(true);

        paintLabelText = new Paint();
        paintLabelText.setColor(textColorCenter);
        paintLabelText.setAntiAlias(true);
        paintLabelText.setTypeface(typeface);
        paintLabelText.setTextSize(textLabelSize);

        paintIndicator = new Paint();
        paintIndicator.setColor(dividerColor);
        paintIndicator.setAntiAlias(true);

        paintCenterBackground = new Paint();
        paintCenterBackground.setColor(Color.TRANSPARENT);
        paintCenterBackground.setAntiAlias(true);

        setLayerType(LAYER_TYPE_SOFTWARE, null);
    }

    private void reMeasure() {//重新测量
        if (adapter == null) {
            return;
        }
        measureTextWidthHeight();
        if (isEnableCurve) {
            //半圆的周长 = item高度乘以item数目-1
            int halfCircumference = (int) (itemHeight * (itemsVisible - 1));
            //整个圆的周长除以PI得到直径，这个直径用作控件的总高度
            measuredHeight = (int) ((halfCircumference * 2) / Math.PI);
            //求出半径
            radius = (int) (halfCircumference / Math.PI);
            //控件宽度，这里支持weight
            measuredWidth = MeasureSpec.getSize(widthMeasureSpec);
            //计算两条横线 和 选中项画笔的基线Y位置
            firstLineY = (measuredHeight - itemHeight) / 2.0F;
            secondLineY = (measuredHeight + itemHeight) / 2.0F;
            centerY = secondLineY - (itemHeight - maxTextHeight) / 2.0f - CENTER_CONTENT_OFFSET;
        } else {
            measuredWidth = getMeasuredWidth();
            measuredHeight = getMeasuredHeight();
            if (measuredWidth == 0 || measuredHeight == 0) {
                return;
            }
            paddingLeft = getPaddingLeft();
            paddingRight = getPaddingRight();
            measuredWidth = measuredWidth - paddingRight;
            //itemHeight 2D一定得是int值不然会影响滚动
            itemHeight = (int) (lineSpacingMultiplier * measuredHeight / itemsVisible);
            radius = measuredHeight / 2;
            firstLineY = (int) ((measuredHeight - itemHeight) / 2.0F);
            secondLineY = (int) ((measuredHeight + itemHeight) / 2.0F);
        }

        //初始化显示的item的position
        if (initPosition == -1) {
            if (isLoop) {
                initPosition = (adapter.getItemsCount() + 1) / 2;
            } else {
                initPosition = 0;
            }
        }
        preCurrentIndex = initPosition;
    }

    /**
     * 计算最大length的Text的宽高度
     */
    private void measureTextWidthHeight() {
        Rect rect = new Rect();
        for (int i = 0; i < adapter.getItemsCount(); i++) {
            String s1 = getContentText(adapter.getItem(i));
            paintCenterText.getTextBounds(s1, 0, s1.length(), rect);

            int textWidth = rect.width();
            if (textWidth > maxTextWidth) {
                maxTextWidth = textWidth;
            }
        }
        paintCenterText.getTextBounds("星期", 0, 2, rect); // 星期的字符编码（以它为标准高度）
        maxTextHeight = rect.height() + 2;
        itemHeight = lineSpacingMultiplier * maxTextHeight;
    }

    public void smoothScroll(ACTION action) {//平滑滚动的实现
        cancelFuture();
        //CommonLogUtils.logI("wheel", "smoothScroll action" + action);
        if (action == ACTION.FLING || action == ACTION.DAGGLE) {
            mOffset = (int) ((totalScrollY % itemHeight + itemHeight) % itemHeight);
            //CommonLogUtils.logI("wheel", "smoothScroll mOffset" + mOffset + " itemHeight" + itemHeight);
            if ((float) mOffset > itemHeight / 2.0F) {//如果超过Item高度的一半，滚动到下一个Item去
                mOffset = (int) (itemHeight - (float) mOffset);
            } else {
                mOffset = -mOffset;
            }
        }
        //CommonLogUtils.logI("wheel", "smoothScroll mOffset" + mOffset);
        //停止的时候，位置有偏移，不是全部都能正确停止到中间位置的，这里把文字位置挪回中间去
        mFuture = mExecutor.scheduleWithFixedDelay(new SmoothScrollTimerTask(this, mOffset), 0, 10,
                TimeUnit.MILLISECONDS);
        changeScrollState(SCROLL_STATE_SCROLLING);
    }

    public final void scrollBy(float velocityY) {//滚动惯性的实现
        cancelFuture();
        mFuture = mExecutor.scheduleWithFixedDelay(new InertiaTimerTask(this, velocityY), 0, VELOCITY_FLING,
                TimeUnit.MILLISECONDS);
        changeScrollState(SCROLL_STATE_DRAGGING);
    }

    public void cancelFuture() {
        if (mFuture != null && !mFuture.isCancelled()) {
            mFuture.cancel(true);
            mFuture = null;
            changeScrollState(SCROLL_STATE_IDLE);
        }
    }

    /**
     * 设置是否循环滚动
     *
     * @param cyclic 是否循环
     */
    public final void setCyclic(boolean cyclic) {
        isLoop = cyclic;
    }

    public void setTypeface(Typeface font) {
        typeface = font;
        paintOuterText.setTypeface(typeface);
        paintCenterText.setTypeface(typeface);
    }

    /**
     * 中心区域内字体大小
     *
     * @param size 大小
     */
    public final void setCenterTextSize(float size) {
        if (size > 0.0F) {
            centerTextSize = (int) (context.getResources().getDisplayMetrics().density * size);
            paintCenterText.setTextSize(centerTextSize);
            //reMeasure();
        }
    }

    /**
     * 中心区域外字体大小
     *
     * @param size 大小
     */
    public final void setSecondTextSize(float size) {
        if (size > 0.0F) {
            secondTextSize = (int) (context.getResources().getDisplayMetrics().density * size);
            paintOuterText.setTextSize(secondTextSize);
        }
    }

    /**
     * 顶部和底部字体大小,不设置默认和SecondTextSize一致
     *
     * @param size 大小
     */
    public final void setThirdTextSize(float size) {
        if (size > 0.0F) {
            thirdTextSize = (int) (context.getResources().getDisplayMetrics().density * size);
        }
    }

    /**
     * label字体大小
     *
     * @param size 大小
     */
    public final void setTextLabelSize(float size) {
        if (size > 0.0F) {
            textLabelSize = (int) (context.getResources().getDisplayMetrics().density * size);
            paintLabelText.setTextSize(textLabelSize);
        }
    }

    public final void setOnItemSelectedListener(OnItemSelectedListener OnItemSelectedListener) {
        this.onItemSelectedListener = OnItemSelectedListener;
    }

    public final void setOnItemScrollListener(OnItemScrollListener OnItemScrollListener) {
        this.onItemScrollListener = OnItemScrollListener;
    }

    /**
     * 显示数目条，先设置isEnableCurve再设置显示数目
     * 3d滚轮额外实际上第一项和最后一项Y轴压缩成0%了，所以可见的数目实际为9
     *
     * @param visibleCount 可见数目
     */
    public void setItemsVisibleCount(int visibleCount) {
        if (visibleCount % 2 == 0) {
            visibleCount += 1;
        }
        if (isEnableCurve) {
            //3d滚轮要加上第一条和最后一条
            this.itemsVisible = visibleCount + 2;
        } else {
            this.itemsVisible = visibleCount;
        }
    }

    public final WheelAdapter<?> getAdapter() {
        return adapter;
    }

    public final void setAdapter(WheelAdapter<?> adapter) {
        this.adapter = adapter;
        reMeasure();
        invalidate();
    }

    public final int getCurrentItem() {
        // return selectedItem;
        if (adapter == null) {
            return 0;
        }
        if (isLoop && (selectedItem < 0 || selectedItem >= adapter.getItemsCount())) {
            return Math.max(0, Math.min(Math.abs(Math.abs(selectedItem) - adapter.getItemsCount()),
                    adapter.getItemsCount() - 1));
        }
        //CommonLogUtils.logI("wheel", "getCurrentItem " + selectedItem);
        return Math.max(0, Math.min(selectedItem, adapter.getItemsCount() - 1));
    }

    public final void setCurrentItem(int currentItem) {
        //不添加这句,当这个wheelView不可见时,默认都是0,会导致获取到的时间错误
        this.selectedItem = currentItem;
        this.initPosition = currentItem;
        totalScrollY = 0;//回归顶部，不然重设setCurrentItem的话位置会偏移的，就会显示出不对位置的数据
        invalidate();
    }

    public final void onItemSelected() {
        if (onItemSelectedListener != null) {
            postDelayed(() -> onItemSelectedListener.onItemSelected(getCurrentItem()), 200L);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (adapter == null) {
            return;
        }
        //initPosition越界会造成preCurrentIndex的值不正确
        initPosition = Math.min(Math.max(0, initPosition), adapter.getItemsCount() - 1);

        //滚动的Y值高度除去每行Item的高度，得到滚动了多少个item，即change数
        //滚动偏移值,用于记录滚动了多少个item
        int change = (int) (totalScrollY / itemHeight);

        try {
            //滚动中实际的预选中的item(即经过了中间位置的item) ＝ 滑动前的位置 ＋ 滑动相对位置
            preCurrentIndex = initPosition + change % adapter.getItemsCount();
        } catch (ArithmeticException e) {
            CommonLogUtils.logE("WheelView", "出错了！adapter.getItemsCount() == 0，联动数据不匹配");
        }
        //CommonLogUtils.logI("wheel", "onDraw" + " totalScrollY" + totalScrollY + " preCurrentIndex" + preCurrentIndex);
        if (!isLoop) {//不循环的情况
            if (preCurrentIndex < 0) {
                preCurrentIndex = 0;
            }
            if (preCurrentIndex > adapter.getItemsCount() - 1) {
                preCurrentIndex = adapter.getItemsCount() - 1;
            }
        } else {//循环
            if (preCurrentIndex < 0) {//举个例子：如果总数是5，preCurrentIndex ＝ －1，那么preCurrentIndex按循环来说，其实是0的上面，也就是4的位置
                preCurrentIndex = adapter.getItemsCount() + preCurrentIndex;
            }
            if (preCurrentIndex > adapter.getItemsCount() - 1) {//同理上面,自己脑补一下
                preCurrentIndex = preCurrentIndex - adapter.getItemsCount();
            }
        }
        //跟滚动流畅度有关，总滑动距离与每个item高度取余，即并不是一格格的滚动，每个item不一定滚到对应Rect里的，这个item对应格子的偏移值
        float itemHeightOffset = (totalScrollY % itemHeight);
        //        if (centerBackgroundColor != -1) {
        final int left = (measuredWidth - itemWidth) / 2;
        final int right = left + itemWidth;
        //canvas.drawRect(left, firstLineY, right, secondLineY, paintCenterBackground);
        canvas.drawRoundRect(left, firstLineY, right, secondLineY, backgroundRadius, backgroundRadius,
                paintCenterBackground);
        //        }

        //绘制中间两条横线
        if (dividerType == DividerType.WRAP) {//横线长度仅包裹内容
            float startX;
            float endX;

            if (TextUtils.isEmpty(label)) {//隐藏Label的情况
                startX = (measuredWidth - maxTextWidth) / 2f - 12;
            } else {
                startX = (measuredWidth - maxTextWidth) / 4f - 12;
            }

            if (startX <= 0) {//如果超过了WheelView的边缘
                startX = 10;
            }
            endX = measuredWidth - startX;
            canvas.drawLine(startX, firstLineY, endX, firstLineY, paintIndicator);
            canvas.drawLine(startX, secondLineY, endX, secondLineY, paintIndicator);
        } else if (dividerType == DividerType.CIRCLE) {
            //分割线为圆圈形状
            paintIndicator.setStyle(Paint.Style.STROKE);
            paintIndicator.setStrokeWidth(dividerWidth);
            float startX;
            float endX;
            if (TextUtils.isEmpty(label)) {//隐藏Label的情况
                startX = (measuredWidth - maxTextWidth) / 2f - 12;
            } else {
                startX = (measuredWidth - maxTextWidth) / 4f - 12;
            }
            if (startX <= 0) {//如果超过了WheelView的边缘
                startX = 10;
            }
            endX = measuredWidth - startX;
            //半径始终以宽高中最大的来算
            float radius = Math.max((endX - startX), itemHeight) / 1.8f;
            canvas.drawCircle(measuredWidth / 2f, measuredHeight / 2f, radius, paintIndicator);
        } else {
            canvas.drawLine(0.0F, firstLineY, measuredWidth, firstLineY, paintIndicator);
            canvas.drawLine(0.0F, secondLineY, measuredWidth, secondLineY, paintIndicator);
        }
        //只显示选中项Label文字的模式，并且Label文字不为空，则进行绘制
        if (!TextUtils.isEmpty(label) && isCenterLabel) {
            //绘制文字，靠右并留出空隙
            //int drawRightContentStart = measuredWidth / 2 + (itemWidth / 2) + textLabelGap;
            //canvas.drawText(label, drawRightContentStart - CENTER_CONTENT_OFFSET, centerY, paintLabelText);
            int drawRightContentStart = (measuredWidth + maxTextWidth) / 2 + textLabelGap;
            canvas.drawText(label, drawRightContentStart, (measuredHeight - itemHeight) / 2 + getDrawingY(paintLabelText), paintLabelText);
        }
        if (!TextUtils.isEmpty(prefix)) {
            paintCenterText.getTextBounds(prefix, 0, prefix.length(), tempRect);
            int drawRightContentStart = (measuredWidth - maxTextWidth) / 2 - textLabelGap - tempRect.width();
            canvas.drawText(prefix, drawRightContentStart, (measuredHeight - itemHeight) / 2 + getDrawingY(paintCenterText), paintCenterText);
        }

        // 设置数组中每个元素的值
        int counter = 0;
        while (counter < itemsVisible) {
            Object showText;
            int index = preCurrentIndex - (itemsVisible / 2 - counter);//索引值，即当前在控件中间的item看作数据源的中间，计算出相对源数据源的index值

            //判断是否循环，如果是循环数据源也使用相对循环的position获取对应的item值，如果不是循环则超出数据源范围使用""空白字符串填充，在界面上形成空白无数据的item项
            if (isLoop) {
                index = getLoopMappingIndex(index);
                showText = adapter.getItem(index);
            } else if (index < 0) {
                showText = "";
            } else if (index > adapter.getItemsCount() - 1) {
                showText = "";
            } else {
                showText = adapter.getItem(index);
            }
            //获取内容文字
            String contentText;
            //如果是label每项都显示的模式，并且item内容不为空、label 也不为空
            if (!isCenterLabel && !TextUtils.isEmpty(label) && !TextUtils.isEmpty(getContentText(showText))) {
                contentText = getContentText(showText) + label;
            } else {
                contentText = getContentText(showText);
            }
            canvas.save();
            if (isEnableCurve) {
                // 弧长 L = itemHeight * counter - itemHeightOffset
                // 求弧度 α = L / r  (弧长/半径) [0,π]
                double radian = ((itemHeight * counter - itemHeightOffset)) / radius;
                // 弧度转换成角度(把半圆以Y轴为轴心向右转90度，使其处于第一象限及第四象限
                // angle [-90°,90°]
                float angle = (float) (90D - (radian / Math.PI) * 180D);//item第一项,从90度开始，逐渐递减到 -90度
                float translateY =
                        (float) (radius - Math.cos(radian) * radius - (Math.sin(radian) * maxTextHeight) / 2d);
                // 计算取值可能有细微偏差，保证负90°到90°以外的不绘制
                if (angle >= 90F || angle <= -90F || translateY < 0) {
                    canvas.restore();
                } else {
                    // 根据当前角度计算出偏差系数，用以在绘制时控制文字的 水平移动 透明度 倾斜程度.
                    float offsetCoefficient = (float) Math.pow(Math.abs(angle) / 90f, 2.2);
                    reMeasureTextSize(contentText);
                    //计算开始绘制的位置
                    measuredCenterContentStart(contentText);
                    measuredOutContentStart(contentText);

                    //根据Math.sin(radian)来更改canvas坐标系原点，然后缩放画布，使得文字高度进行缩放，形成弧形3d视觉差
                    canvas.translate(0.0F, translateY);
                    if (translateY <= firstLineY && maxTextHeight + translateY >= firstLineY) {
                        // 条目经过第一条线
                        canvas.save();
                        canvas.clipRect(0, 0, measuredWidth, firstLineY - translateY);
                        if (scaleContent > 0) {
                            canvas.scale(1.0F, (float) Math.sin(radian) * scaleContent);
                        }
                        setOutPaintStyle(offsetCoefficient, angle);
                        canvas.drawText(contentText, drawOutContentStart, maxTextHeight, paintOuterText);
                        canvas.restore();
                        canvas.save();
                        canvas.clipRect(0, firstLineY - translateY, measuredWidth, itemHeight);
                        canvas.scale(1.0F, (float) Math.sin(radian));
                        canvas.drawText(contentText, drawCenterContentStart, maxTextHeight - CENTER_CONTENT_OFFSET,
                                paintCenterText);
                        canvas.restore();
                    } else if (translateY <= secondLineY && maxTextHeight + translateY >= secondLineY) {
                        // 条目经过第二条线
                        canvas.save();
                        canvas.clipRect(0, 0, measuredWidth, secondLineY - translateY);
                        canvas.scale(1.0F, (float) Math.sin(radian));
                        canvas.drawText(contentText, drawCenterContentStart, maxTextHeight - CENTER_CONTENT_OFFSET,
                                paintCenterText);
                        canvas.restore();
                        canvas.save();
                        canvas.clipRect(0, secondLineY - translateY, measuredWidth, itemHeight);
                        if (scaleContent > 0) {
                            canvas.scale(1.0F, (float) Math.sin(radian) * scaleContent);
                        }
                        setOutPaintStyle(offsetCoefficient, angle);
                        canvas.drawText(contentText, drawOutContentStart, maxTextHeight, paintOuterText);
                        canvas.restore();
                    } else if (translateY >= firstLineY && maxTextHeight + translateY <= secondLineY) {
                        // 中间条目
                        // canvas.clipRect(0, 0, measuredWidth, maxTextHeight);
                        //让文字居中
                        float Y = maxTextHeight - CENTER_CONTENT_OFFSET;//因为圆弧角换算的向下取值，导致角度稍微有点偏差，加上画笔的基线会偏上，因此需要偏移量修正一下
                        canvas.drawText(contentText, drawCenterContentStart, Y, paintCenterText);
                        //设置选中项
                        selectedItem = preCurrentIndex - (itemsVisible / 2 - counter);
                    } else {
                        // 其他条目
                        canvas.save();
                        canvas.clipRect(0, 0, measuredWidth, itemHeight);
                        if (scaleContent > 0) {
                            canvas.scale(1.0F, (float) Math.sin(radian) * scaleContent);
                        }
                        setOutPaintStyle(offsetCoefficient, angle);
                        // 控制文字水平偏移距离
                        canvas.drawText(contentText, drawOutContentStart + textXOffset * offsetCoefficient,
                                maxTextHeight - 10, paintOuterText);
                        canvas.restore();
                    }
                    canvas.restore();
                    paintCenterText.setTextSize(centerTextSize);
                }
            } else {
                int translateY = (int) (itemHeight * counter - itemHeightOffset);
                //CommonLogUtils.logI("wheelview", "translateY:" + translateY + " firstLineY" + firstLineY + " " +
                //        "secondLineY" + secondLineY + " totalScrollY" + totalScrollY + " index" + index + " " +
                //        "initPosition" + initPosition +
                //        " preCurrentIndex" + preCurrentIndex + " change" + change + "");
                canvas.translate(0.0F, translateY);
                if (translateY <= firstLineY && itemHeight + translateY >= firstLineY) {
                    //CommonLogUtils.logI("wheel", counter + "first line");
                    // 中间高亮上面第一条分割线
                    canvas.save();
                    canvas.clipRect(0, 0, measuredWidth, firstLineY - translateY);
                    paintOuterText.setTextSize(secondTextSize);
                    canvas.drawText(contentText, getTextX(contentText, paintOuterText, tempRect),
                            getDrawingY(paintOuterText), paintOuterText);
                    canvas.restore();
                    canvas.save();
                    canvas.clipRect(0, firstLineY - translateY, measuredWidth, itemHeight);
                    canvas.drawText(contentText, getTextX(contentText, paintCenterText, tempRect),
                            getDrawingY(paintCenterText), paintCenterText);
                    canvas.restore();
                } else if (translateY <= secondLineY && itemHeight + translateY >= secondLineY) {
                    //CommonLogUtils.logI("wheel", counter + "sceond line");
                    // 中间高亮下面第二条分割线
                    canvas.save();
                    canvas.clipRect(0, 0, measuredWidth, secondLineY - translateY);
                    canvas.drawText(contentText, getTextX(contentText, paintCenterText, tempRect),
                            getDrawingY(paintCenterText), paintCenterText);
                    canvas.restore();
                    canvas.save();
                    canvas.clipRect(0, secondLineY - translateY, measuredWidth, itemHeight);
                    paintOuterText.setTextSize(secondTextSize);
                    canvas.drawText(contentText, getTextX(contentText, paintOuterText, tempRect),
                            getDrawingY(paintOuterText), paintOuterText);
                    canvas.restore();
                } else if (translateY >= firstLineY && itemHeight + translateY <= secondLineY) {
                    // center item
                    //CommonLogUtils.logI("wheel", counter + "center " + index + "index " + contentText);
                    canvas.clipRect(0, 0, measuredWidth, itemHeight);
                    canvas.drawText(contentText, getTextX(contentText, paintCenterText, tempRect),
                            getDrawingY(paintCenterText), paintCenterText);
                    selectedItem = index;
                } else {
                    //CommonLogUtils.logI("wheel", counter + "other item");
                    // other item
                    canvas.clipRect(0, 0, measuredWidth, itemHeight);
                    //设置了末端字体大小才改变
                    if (thirdTextSize != 0 && (counter == 0 || counter == itemsVisible - 1)) {
                        paintOuterText.setTextSize(thirdTextSize);
                    } else {
                        paintOuterText.setTextSize(secondTextSize);
                    }
                    canvas.drawText(contentText, getTextX(contentText, paintOuterText, tempRect),
                            getDrawingY(paintOuterText), paintOuterText);
                }
                canvas.restore();
            }
            counter++;
        }

        if (currentScrollState != lastScrollState) {
            int oldScrollState = lastScrollState;
            lastScrollState = currentScrollState;
            if (onItemScrollListener != null) {
                onItemScrollListener.onItemScrollStateChanged(this, preCurrentIndex, oldScrollState,
                        currentScrollState, totalScrollY);
            }

        }
        if (currentScrollState == SCROLL_STATE_DRAGGING || currentScrollState == SCROLL_STATE_SCROLLING) {
            if (onItemScrollListener != null) {
                onItemScrollListener.onItemScrolling(this, preCurrentIndex, currentScrollState, totalScrollY);
            }
        }
        //绘制渐变蒙层
        if (maskDrawable != null) {
            maskDrawable.setBounds(0, 0, measuredWidth, measuredHeight);
            maskDrawable.draw(canvas);
        }
    }

    /**
     * 返回文字在itemHeight上下居中的y坐标
     *
     * @param paint 文字paint
     */
    private float getDrawingY(Paint paint) {
        //float baseLine = (paint.getFontMetrics().descent - paint.getFontMetrics().ascent) / 2 - paint.getFontMetrics().descent;
        //return itemHeight / 2 + baseLine;
        paint.getTextBounds("星期", 0, 2, tempRect);
        //文本内容垂直中心（相对于基线）,防止数字与文字中心不对齐
        return (itemHeight - tempRect.bottom - tempRect.top) / 2;
    }

    // text start drawing position
    private int getTextX(String a, Paint paint, Rect rect) {
        paint.getTextBounds(a, 0, a.length(), rect);
        int textWidth = rect.width();
        textWidth *= 1.05;
        return (measuredWidth - paddingLeft - textWidth) / 2 + paddingLeft;
    }

    //设置文字倾斜角度，透明度
    private void setOutPaintStyle(float offsetCoefficient, float angle) {
        // 控制文字倾斜角度
        float DEFAULT_TEXT_TARGET_SKEW_X = 0.5f;
        int multiplier = 0;
        if (textXOffset > 0) {
            multiplier = 1;
        } else if (textXOffset < 0) {
            multiplier = -1;
        }
        paintOuterText.setTextSkewX(multiplier * (angle > 0 ? -1 : 1) * DEFAULT_TEXT_TARGET_SKEW_X * offsetCoefficient);
        // 控制透明度
        int alpha = isAlphaGradient ? (int) ((90F - Math.abs(angle)) / 90f * 255) : 255;
        paintOuterText.setAlpha(alpha);
    }

    /**
     * reset the size of the text Let it can fully display
     *
     * @param contentText item text content.
     */
    private void reMeasureTextSize(String contentText) {
        Rect rect = new Rect();
        paintCenterText.getTextBounds(contentText, 0, contentText.length(), rect);
        int width = rect.width();
        int size = centerTextSize;
        while (width > measuredWidth) {
            size--;
            //设置2条横线中间的文字大小
            paintCenterText.setTextSize(size);
            paintCenterText.getTextBounds(contentText, 0, contentText.length(), rect);
            width = rect.width();
        }
        //设置2条横线外面的文字大小
        paintOuterText.setTextSize(secondTextSize);
    }

    //递归计算出对应的index
    private int getLoopMappingIndex(int index) {
        if (index < 0) {
            index = index + adapter.getItemsCount();
            index = getLoopMappingIndex(index);
        } else if (index > adapter.getItemsCount() - 1) {
            index = index - adapter.getItemsCount();
            index = getLoopMappingIndex(index);
        }
        return index;
    }

    /**
     * 获取所显示的数据源
     *
     * @param item data resource
     * @return 对应显示的字符串
     */
    private String getContentText(Object item) {
        if (item == null) {
            return "";
        } else if (item instanceof IPickerViewData) {
            return ((IPickerViewData) item).getPickerViewText();
        } else if (item instanceof Integer) {
            //如果为整形则最少保留两位数.
            return getFixNum((int) item);
        }
        return item.toString();
    }

    private String getFixNum(int timeNum) {
        return timeNum >= 0 && timeNum < 10 ? TIME_NUM[timeNum] : String.valueOf(timeNum);
    }

    private void measuredCenterContentStart(String content) {
        Rect rect = new Rect();
        paintCenterText.getTextBounds(content, 0, content.length(), rect);
        switch (mGravity) {
            case Gravity.CENTER://显示内容居中
                drawCenterContentStart = (int) ((measuredWidth - rect.width()) * 0.5);
                break;
            case Gravity.START:
                drawCenterContentStart = 0;
                break;
            case Gravity.END://添加偏移量
                drawCenterContentStart = measuredWidth - rect.width() - (int) CENTER_CONTENT_OFFSET;
                break;
        }
    }

    private void measuredOutContentStart(String content) {
        Rect rect = new Rect();
        paintOuterText.getTextBounds(content, 0, content.length(), rect);
        switch (mGravity) {
            case Gravity.CENTER:
                drawOutContentStart = (int) ((measuredWidth - rect.width()) * 0.5);
                break;
            case Gravity.START:
                drawOutContentStart = 0;
                break;
            case Gravity.END:
                drawOutContentStart = measuredWidth - rect.width() - (int) CENTER_CONTENT_OFFSET;
                break;
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        this.widthMeasureSpec = widthMeasureSpec;
        reMeasure();
        setMeasuredDimension(measuredWidth, measuredHeight);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean eventConsumed = gestureDetector.onTouchEvent(event);
        boolean isIgnore = false;//超过边界滑动时，不再绘制UI。

        float top = -initPosition * itemHeight;
        float bottom = (adapter.getItemsCount() - 1 - initPosition) * itemHeight;
        //float ratio = 0.25f;
        //CommonLogUtils.logI("wheel", "onTouchEvent" + event.getAction() + " top" + top + " bottom" + bottom);
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startTime = System.currentTimeMillis();
                cancelFuture();
                previousY = event.getRawY();
                if (getParent() != null) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                }
                break;

            case MotionEvent.ACTION_MOVE:
                float dy = previousY - event.getRawY();
                previousY = event.getRawY();
                if (isEnableCurve) {
                    totalScrollY = totalScrollY + dy;
                } else {
                    //2d滚轮必须得是int值，不然会出现滑动后末端没有加载数据
                    totalScrollY = (int) (totalScrollY + dy);
                }

                // normal mode。
                //                if (!isLoop) {
                //                    if ((totalScrollY - itemHeight * ratio < top && dy < 0)
                //                            || (totalScrollY + itemHeight * ratio > bottom && dy > 0)) {
                //                        //快滑动到边界了，设置已滑动到边界的标志
                //                        totalScrollY -= dy;
                //                        isIgnore = true;
                //                    } else {
                //                        isIgnore = false;
                //                    }
                //                }
                if (!isLoop) {
                    if (totalScrollY < top) {
                        totalScrollY = (int) top;
                    } else if (totalScrollY > bottom) {
                        totalScrollY = (int) bottom;
                    }
                }
                //CommonLogUtils.logI("wheel", "onTouchEvent totalScrollY" + totalScrollY);
                changeScrollState(SCROLL_STATE_DRAGGING);
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
            default:
                dealUnconsumedAction(eventConsumed, event);
                if (getParent() != null) {
                    getParent().requestDisallowInterceptTouchEvent(false);
                }
                break;
        }
        if (!isIgnore && event.getAction() != MotionEvent.ACTION_DOWN) {
            invalidate();
        }
        return true;
    }

    private void changeScrollState(int scrollState) {
        if (scrollState != currentScrollState && !handler.hasMessages(MessageHandler.WHAT_SMOOTH_SCROLL_INERTIA)) {
            lastScrollState = currentScrollState;
            currentScrollState = scrollState;
        }
    }

    public int getItemsCount() {
        return adapter != null ? adapter.getItemsCount() : 0;
    }

    /**
     * 是否只中间选中的item显示单位
     *
     * @param isCenterLabel true是
     */
    public void isCenterLabel(boolean isCenterLabel) {
        this.isCenterLabel = isCenterLabel;
    }

    public void setGravity(int gravity) {
        this.mGravity = gravity;
    }

    public int getTextWidth(Paint paint, String str) { //calculate text width
        int iRet = 0;
        if (str != null && str.length() > 0) {
            int len = str.length();
            float[] widths = new float[len];
            paint.getTextWidths(str, widths);
            for (int j = 0; j < len; j++) {
                iRet += (int) Math.ceil(widths[j]);
            }
        }
        return iRet;
    }

    /**
     * 未选中字体颜色
     *
     * @param textColorOutId 颜色
     */
    public void setTextColorOut(@ColorRes int textColorOutId) {
        this.textColorOutId = textColorOutId;
        this.textColorOut = context.getColor(textColorOutId);
        paintOuterText.setColor(this.textColorOut);
    }

    /**
     * 选中字体颜色
     *
     * @param textColorCenterId 颜色
     */
    public void setTextColorCenter(@ColorRes int textColorCenterId) {
        this.textColorCenterId = textColorCenterId;
        this.textColorCenter = context.getColor(textColorCenterId);
        paintCenterText.setColor(this.textColorCenter);
        paintLabelText.setColor(this.textColorCenter);
    }

    /**
     * 选中背景色
     *
     * @param centerBackgroundColorId 颜色
     */
    public void setCenterBackgroundColor(@ColorRes int centerBackgroundColorId) {
        this.centerBackgroundColorId = centerBackgroundColorId;
        this.centerBackgroundColor = context.getColor(centerBackgroundColorId);
        paintCenterBackground.setColor(this.centerBackgroundColor);
    }

    public void setTextXOffset(int textXOffset) {
        this.textXOffset = textXOffset;
        if (textXOffset != 0) {
            paintCenterText.setTextScaleX(1.0f);
        }
    }

    /**
     * 分割线高度
     *
     * @param dividerWidth 高度
     */
    public void setDividerWidth(int dividerWidth) {
        this.dividerWidth = dividerWidth;
        paintIndicator.setStrokeWidth(dividerWidth);
    }

    /**
     * 分割线颜色
     *
     * @param dividerColor 颜色
     */
    public void setDividerColor(int dividerColor) {
        this.dividerColor = dividerColor;
        paintIndicator.setColor(dividerColor);
    }

    public void setDividerType(DividerType dividerType) {
        this.dividerType = dividerType;
    }

    /**
     * 设置行间距倍数，3d滚轮一般3f，2d滚轮1f
     *
     * @param lineSpacingMultiplier 行间距倍数
     */
    public void setLineSpacingMultiplier(float lineSpacingMultiplier) {
        if (lineSpacingMultiplier != 0) {
            this.lineSpacingMultiplier = lineSpacingMultiplier;
            judgeLineSpace();
        }
    }

    public void dealUnconsumedAction(Boolean eventConsumed, MotionEvent event) {
        if (!eventConsumed) {//未消费掉事件
            /*
             *@describe <关于弧长的计算>
             *
             * 弧长公式： L = α*R
             * 反余弦公式：arccos(cosα) = α
             * 由于之前是有顺时针偏移90度，
             * 所以实际弧度范围α2的值 ：α2 = π/2-α    （α=[0,π] α2 = [-π/2,π/2]）
             * 根据正弦余弦转换公式 cosα = sin(π/2-α)
             * 代入，得： cosα = sin(π/2-α) = sinα2 = (R - y) / R
             * 所以弧长 L = arccos(cosα)*R = arccos((R - y) / R)*R
             */

            float y = event.getY();
            double L = Math.acos((radius - y) / radius) * radius;
            //item0 有一半是在不可见区域，所以需要加上 itemHeight / 2
            int circlePosition = (int) ((L + itemHeight / 2) / itemHeight);
            float extraOffset = (totalScrollY % itemHeight + itemHeight) % itemHeight;
            //已滑动的弧长值
            mOffset = (int) ((circlePosition - itemsVisible / 2) * itemHeight - extraOffset);

            if ((System.currentTimeMillis() - startTime) > 120) {
                // 处理拖拽事件
                smoothScroll(ACTION.DAGGLE);
            } else if (isEnableCurve) {
                //以前的3d滚轮点击逻辑
                //CommonLogUtils.logI("wheel", "y" + y + " mOffset" + mOffset);
                if (y < itemHeight) {
                    totalScrollY -= itemHeight;
                    smoothScroll(ACTION.DAGGLE);
                } else if (y > itemHeight * 2) {
                    totalScrollY += itemHeight;
                    smoothScroll(ACTION.DAGGLE);
                } else {
                    smoothScroll(ACTION.DAGGLE);
                }
            } else {
                int clickPosition = (int) (y / itemHeight);
                if (y < firstLineY) {
                    /*如果不循环，中间上面有空白行，且点击空白行不滑动
                     * selectedItem < itemsVisible / 2，例如可见5项，选中0，1项
                     * clickPosition < itemsVisible / 2 - selectedItem，
                     * 例如可见7项，选中0，点击0，1，2行不响应；选中1，点击0，1行不响应，点击2行响应；选中2，点击0不响应，点击1，2行响应
                     * 即点击位置<上半可见数-选中索引的全部拦截*/
                    if (!isLoop && selectedItem < itemsVisible / 2 && clickPosition < itemsVisible / 2 - selectedItem) {
                        return;
                    }
                    //点击中间上半，滑动到点击行
                    totalScrollY -= (itemHeight * ((int) (itemsVisible / 2) - clickPosition));
                    smoothScroll(ACTION.DAGGLE);
                } else if (y > secondLineY) {
                    /*如果不循环，中间下面有空白行，且点击空白行不滑动
                     * adapter.getItemsCount() - itemsVisible / 2 <= selectedItem，例如可见5项，选中倒一倒二
                     * adapter.getItemsCount() - 1 - selectedItem < clickPosition - itemsVisible / 2，
                     * 例如可见7项，选中倒一，点击4，5，6行不响应；选中倒二，点击6，5行不响应，点击4行响应；选中倒三，点击6不响应，点击5，4行响应
                     * 即点击位置-下半可见数>总数-1-选中位置 的全部拦截，例如选中倒一 点击4行，4-3=1 > 总数-1-倒一（总数-1）=0
                     */
                    if (!isLoop && adapter.getItemsCount() - itemsVisible / 2 <= selectedItem && adapter.getItemsCount() - 1 - selectedItem < clickPosition - itemsVisible / 2) {
                        return;
                    }
                    //点击中间下半，滑动到点击行
                    totalScrollY += (itemHeight * (clickPosition - (int) (itemsVisible / 2)));
                    smoothScroll(ACTION.DAGGLE);
                }
            }
        }
    }

    public boolean isNotLoop() {
        return !isLoop;
    }

    public float getTotalScrollY() {
        return totalScrollY;
    }

    public void setTotalScrollY(float totalScrollY) {
        this.totalScrollY = totalScrollY;
    }

    public float getItemHeight() {
        return itemHeight;
    }

    /**
     * 每行高度
     *
     * @param dpSize 高度单位dp
     */
    public void setItemHeight(float dpSize) {
        this.itemHeight = dpSize;
        setLineSpacingMultiplier(this.itemHeight / this.maxTextHeight);
    }

    /**
     * 每行宽度
     *
     * @param dpSize 宽度单位dp
     */
    public void setItemWidth(int dpSize) {
        this.itemWidth = dpSize;
    }

    public int getInitPosition() {
        return initPosition;
    }

    @Override
    public Handler getHandler() {
        return handler;
    }

    public void setMaskDrawable(@DrawableRes int resId) {
        maskId = resId;
        maskDrawable = ResourcesCompat.getDrawable(getResources(), maskId, null);
    }

    public enum ACTION { // 点击，滑翔(滑到尽头)，拖拽事件
        CLICK, FLING, DAGGLE
    }

    public enum DividerType { // 分隔线类型
        FILL, WRAP, CIRCLE
    }
}