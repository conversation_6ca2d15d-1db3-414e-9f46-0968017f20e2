package com.dfl.smartscene.widget.bannerview.utils;

/**
 * Created by 钟文祥 on 2024/11/29.
 * Describer: 轮播图滑动时左右两个图的index
 */

//Android 图形混合模式PorterDuffXfermode模式介绍 https://blog.csdn.net/jinmie0193/article/details/80815868
//        https://blog.csdn.net/lfq88/article/details/127559144
//       https://www.jianshu.com/p/026c140a6ece

public class BannerScrollLeftRightBean {
    private boolean isEnable = false;  //是否能用   ，不可用时
    private int leftVisiblePosition; //左图可见index 大数
    private int rightVisiblePosition; //右图可见index 大数
    private int leftRealPosition; //左图真实index 小数   图片的数组index
    private int rightRealPosition;//右图真实index 小数   图片的数组index

    public BannerScrollLeftRightBean() {
    }

    public BannerScrollLeftRightBean(boolean isEnable, int leftVisiblePosition, int rightVisiblePosition,
                                     int leftRealPosition, int rightRealPosition) {
        this.isEnable = isEnable;
        this.leftVisiblePosition = leftVisiblePosition;
        this.rightVisiblePosition = rightVisiblePosition;
        this.leftRealPosition = leftRealPosition;
        this.rightRealPosition = rightRealPosition;
    }

    public BannerScrollLeftRightBean(boolean isEnable) {
        this.isEnable = isEnable;
        this.leftVisiblePosition = -1;
        this.rightVisiblePosition = -1;
        this.leftRealPosition = -1;
        this.rightRealPosition = -1;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public void setEnable(boolean enable) {
        isEnable = enable;
    }

    public int getLeftVisiblePosition() {
        return leftVisiblePosition;
    }

    public void setLeftVisiblePosition(int leftVisiblePosition) {
        this.leftVisiblePosition = leftVisiblePosition;
    }

    public int getRightVisiblePosition() {
        return rightVisiblePosition;
    }

    public void setRightVisiblePosition(int rightVisiblePosition) {
        this.rightVisiblePosition = rightVisiblePosition;
    }

    public int getLeftRealPosition() {
        return leftRealPosition;
    }

    public void setLeftRealPosition(int leftRealPosition) {
        this.leftRealPosition = leftRealPosition;
    }

    public int getRightRealPosition() {
        return rightRealPosition;
    }

    public void setRightRealPosition(int rightRealPosition) {
        this.rightRealPosition = rightRealPosition;
    }

    @Override
    public String toString() {
        return "BannerScrollLeftRightBean{" +
                "isEnable=" + isEnable +
                ", leftVisiblePosition=" + leftVisiblePosition +
                ", rightVisiblePosition=" + rightVisiblePosition +
                ", leftRealPosition=" + leftRealPosition +
                ", rightRealPosition=" + rightRealPosition +
                '}';
    }
}
