package com.dfl.smartscene.widget.recycleview

import android.content.Context
import android.util.AttributeSet
import com.dfl.common.nightmode.widget.SkinCompatRecycleView

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :自定义RecycleView重新计算高度
 * version: 1.0
 */
class HeightRecycleView : SkinCompatRecycleView {
	constructor(context: Context) : super(context)
	constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
	constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(
		context,
		attrs,
		defStyleAttr
	)


	override fun onMeasure(widthSpec: Int, heightSpec: Int) {
		val height = MeasureSpec.makeMeasureSpec(Int.MAX_VALUE shr 2, MeasureSpec.AT_MOST)
		super.onMeasure(widthSpec, height)
	}

}