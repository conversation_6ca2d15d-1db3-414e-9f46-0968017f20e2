package com.dfl.smartscene.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import com.dfl.smartscene.R;

/**
 * author : ly-shijc
 * desc:自定义垂直TabLayout
 * 实现垂直排列的Tab选项卡，支持虚线连接和圆形指示器
 */
public class VerticalTabLayout extends LinearLayout {
    private int selectedPosition = 0;
    private OnTabSelectedListener tabSelectedListener;
    private Paint indicatorPaint;
    private Paint linePaint;
    private int indicatorColor;
    private int lineColor;
    private int selectedTextColor;
    private int normalTextColor;
    private String[] tabTitles;
    private int circleRadius = 4;
    
    /**
     * Tab选择监听器接口
     */
    public interface OnTabSelectedListener {
        void onTabSelected(int position);
    }
    
    public VerticalTabLayout(Context context) {
        super(context);
        init(context, null);
    }
    
    public VerticalTabLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }
    
    public VerticalTabLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }
    
    /**
     * 初始化组件
     */
    private void init(Context context, AttributeSet attrs) {
        setOrientation(VERTICAL);
        setWillNotDraw(false);
        
        // 初始化默认值
        indicatorColor = ContextCompat.getColor(context, R.color.scene_primary_color_highlight); // 改为scene_primary_color_highlight
        selectedTextColor = ContextCompat.getColor(context, R.color.scene_color_text_1);
        normalTextColor = ContextCompat.getColor(context, R.color.scene_color_text_3);
        lineColor = ContextCompat.getColor(context, R.color.scene_color_text_3);
        
        // 初始化选中圆形指示器画笔
        indicatorPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        indicatorPaint.setColor(indicatorColor);
        indicatorPaint.setStyle(Paint.Style.FILL);
        
        // 初始化虚线画笔
        linePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        linePaint.setColor(lineColor);
        linePaint.setStyle(Paint.Style.STROKE);
        linePaint.setStrokeWidth(dpToPx(2));
        // 设置虚线效果：线段长度2dp，间隔2dp
        linePaint.setPathEffect(new DashPathEffect(new float[]{dpToPx(2), dpToPx(2)}, 0));
    }
    
    /**
     * 设置Tab标题数组
     */
    public void setTabTitles(String[] titles) {
        this.tabTitles = titles;
        setupTabs();
    }
    
    /**
     * 创建Tab视图
     */
    private void setupTabs() {
        removeAllViews();
        
        if (tabTitles == null) return;
        
        for (int i = 0; i < tabTitles.length; i++) {
            TextView tabView = createTabView(tabTitles[i], i);
            addView(tabView);
        }
        
        // 设置第一个为选中状态
        if (getChildCount() > 0) {
            setSelectedTab(0);
        }
    }
    
    /**
     * 创建单个Tab视图
     */
    private TextView createTabView(String title, int position) {
        TextView tabView = new TextView(getContext());
        tabView.setText(title);
        // 设置文本大小为30sp
        tabView.setTextSize(30);
        tabView.setTextColor(normalTextColor);
        // 指示器与文字之间的间隔为24dp，左侧留出空间给圆形指示器
        tabView.setPadding(dpToPx(40), dpToPx(0), dpToPx(0), dpToPx(0));
        tabView.setGravity(android.view.Gravity.START | android.view.Gravity.CENTER_VERTICAL);
        
        // 设置布局参数 - 两个圆形指示器的间隔为78dp
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        // 设置tab之间的间隔为78dp（除了最后一个）
        if (position < tabTitles.length - 1) {
            params.bottomMargin = dpToPx(78); // 改为78dp间隔
        }
        tabView.setLayoutParams(params);
        
        // 设置点击事件
        tabView.setOnClickListener(v -> {
            setSelectedTab(position);
            if (tabSelectedListener != null) {
                tabSelectedListener.onTabSelected(position);
            }
        });
        
        return tabView;
    }
    
    /**
     * 设置选中的Tab
     */
    public void setSelectedTab(int position) {
        if (position < 0 || position >= getChildCount()) return;
        
        // 重置所有Tab状态
        for (int i = 0; i < getChildCount(); i++) {
            TextView tabView = (TextView) getChildAt(i);
            tabView.setTextColor(normalTextColor);
        }
        
        // 设置选中Tab状态
        TextView selectedTab = (TextView) getChildAt(position);
        selectedTab.setTextColor(selectedTextColor);
        
        selectedPosition = position;
        invalidate(); // 重绘以更新指示器位置
    }
    
    /**
     * 获取当前选中位置
     */
    public int getSelectedPosition() {
        return selectedPosition;
    }
    
    /**
     * 设置Tab选择监听器
     */
    public void setOnTabSelectedListener(OnTabSelectedListener listener) {
        this.tabSelectedListener = listener;
    }
    
    /**
     * 绘制指示器和连接线
     */
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (getChildCount() == 0) return;
        
        // 绘制所有圆形指示器和连接线
        for (int i = 0; i < getChildCount(); i++) {
            View tabView = getChildAt(i);
            if (tabView == null) continue;
            

            int centerX = dpToPx(8);
            int centerY = tabView.getTop() + tabView.getHeight() / 2;
            
            // 绘制圆形指示器
            if (i == selectedPosition) {
                // 选中状态：绘制高亮色实心圆
                canvas.drawCircle(centerX, centerY, circleRadius, indicatorPaint);
            } else {
                // 未选中状态：绘制灰色实心圆
                Paint circlePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                circlePaint.setColor(lineColor);
                circlePaint.setStyle(Paint.Style.FILL);
                canvas.drawCircle(centerX, centerY, circleRadius, circlePaint);
            }
            
            // 绘制连接线（除了最后一个Tab）
            if (i < getChildCount() - 1) {
                View nextTabView = getChildAt(i + 1);
                if (nextTabView != null) {
                    int startY = centerY + circleRadius + dpToPx(4);
                    int endY = nextTabView.getTop() + nextTabView.getHeight() / 2 - circleRadius - dpToPx(4);
                    
                    // 绘制虚线
                    canvas.drawLine(centerX, startY, centerX, endY, linePaint);
                }
            }
        }
    }
    
    /**
     * dp转px工具方法
     */
    private int dpToPx(int dp) {
        return (int) (dp * getContext().getResources().getDisplayMetrics().density);
    }
    
    /**
     * 设置指示器颜色
     */
    public void setIndicatorColor(int color) {
        this.indicatorColor = color;
        indicatorPaint.setColor(color);
        invalidate();
    }
    
    /**
     * 设置选中文本颜色
     */
    public void setSelectedTextColor(int color) {
        this.selectedTextColor = color;
        if (getChildCount() > selectedPosition) {
            ((TextView) getChildAt(selectedPosition)).setTextColor(color);
        }
    }
    
    /**
     * 设置普通文本颜色
     */
    public void setNormalTextColor(int color) {
        this.normalTextColor = color;
        for (int i = 0; i < getChildCount(); i++) {
            if (i != selectedPosition) {
                ((TextView) getChildAt(i)).setTextColor(color);
            }
        }
    }
    
    /**
     * 设置连接线颜色
     */
    public void setLineColor(int color) {
        this.lineColor = color;
        linePaint.setColor(color);
        invalidate();
    }
}