package com.dfl.smartscene.widget

import android.content.Context
import android.util.AttributeSet

import androidx.appcompat.widget.AppCompatImageView
import com.dfl.smartscene.R
import com.dfl.smartscene.widget.bannerview.provider.ViewStyleSetter

/**
 * 工具类 圆角imageview
 * */
class CornerImageView @JvmOverloads constructor(
	context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
	init {
		val attributes = context.obtainStyledAttributes(attrs, R.styleable.CornerImageView)
		val cornerRadius = attributes.getDimension(R.styleable.CornerImageView_corner_radius, 0f)
		attributes.recycle()
		setRoundCorner(cornerRadius)
	}

	fun setRoundCorner(radius: Int) {
		ViewStyleSetter.applyRoundCorner(this, radius.toFloat())
	}

	fun setRoundCorner(radius: Float) {
		ViewStyleSetter.applyRoundCorner(this, radius)
	}
}
