package com.dfl.smartscene.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.android.animationlib.R;
import com.dfl.android.animationlib.tools.AnLog;
import com.dfl.common.nightmode.res.SkinCompatVectorResources;

import java.math.BigDecimal;

public class FadeRecyclerView extends RecyclerView {

    int position_type = 0;//视图在ScrollView中的位置 0顶部 1非顶部也非底部的中间区域  2底部

    int scroll_type = 0;//滑动状态 0滑动停止 1正在滑动

    int fade_type = 1;//渐消类型 0实底背景渐消 1无背景纯文本类
    int fade_length_type = 0;//渐消长度类型 0为1% 1为3%

    private int backgroundResId = 0; // 背景资源Id

    public FadeRecyclerView(@NonNull Context context) {
        super(context);
        init();
    }

    public FadeRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initRes(attrs);
        init();
    }

    public FadeRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initRes(attrs);
        init();
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        adapterSkinColor();
    }

    public void adapterSkinColor() {
        if (backgroundResId > 0 ) {
            Drawable drawable = SkinCompatVectorResources.getDrawableCompat(getContext(), backgroundResId);
            setBackground(drawable); //设置控件背景
        }
    }

    public void initRes(AttributeSet attrs) {
        TypedArray a = getContext().obtainStyledAttributes(attrs, new int[]{android.R.attr.background});
        backgroundResId = a.getResourceId(0, -1);  // 通过索引 0 获取 原生的background 属性
        a.recycle();

        TypedArray ta = getContext().obtainStyledAttributes(attrs, R.styleable.FadeRecyclerView, 0, 0);

        boolean hasFadeType = ta.hasValue(R.styleable.FadeRecyclerView_recyclerFadeType);
        boolean hasFadeLengthType = ta.hasValue(R.styleable.FadeRecyclerView_recyclerFadeLengthType);

        if (hasFadeType) {
            fade_type = ta.getInt(R.styleable.FadeRecyclerView_recyclerFadeType, 0);
        }
        if (hasFadeLengthType) {
            fade_length_type = ta.getInt(R.styleable.FadeRecyclerView_recyclerFadeLengthType, 0);
        }
    }

    public void init() {
        setVerticalFadingEdgeEnabled(true);
        setFadingEdgeLength(0);

        //RecyclerView的初始高度为0,所以要监听RecyclerView的高度变化,高度不为0时设置渐消长度
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (FadeRecyclerView.this.getHeight() != 0) {
                    FadeRecyclerView.this.setBasicHeight(FadeRecyclerView.this.getHeight());

                    getViewTreeObserver().removeOnGlobalLayoutListener(this);
                }
            }
        });

        addOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (!recyclerView.canScrollVertically(-1)) {
                    // 已经滑动到顶部
                    position_type = 0;
                } else if (!recyclerView.canScrollVertically(1)) {
                    // 已经滑动到底部
                    position_type = 2;
                } else {
                    //非顶部也非底部的中间区域
                    position_type = 1;
                }
            }
        });
    }

    //可以不用手动调用该方法设置渐消长度
    public void setBasicHeight(int height) {
        if (fade_length_type == 0) {
            BigDecimal bigDecimal = new BigDecimal(height * 0.01);
            setFadingEdgeLength(bigDecimal.intValue());//渐消长度为RecyclerView的高度乘以1%
        } else if (fade_length_type == 1) {
            BigDecimal bigDecimal = new BigDecimal(height * 0.03);
            setFadingEdgeLength(bigDecimal.intValue());//渐消长度为RecyclerView的高度乘以1%
        }

    }

    //该动效的顶部渐消效果为 RecyclerView如果没有滑动到顶部,顶部渐消效果一直存在
    @Override
    protected float getTopFadingEdgeStrength() {
        if (fade_type == 0) {
            if (scroll_type == 1) {
                return 1f;
            } else if (scroll_type == 0) {
                if (position_type == 0) {
                    return 0f;
                } else {
                    return 1f;
                }
            }
        } else if (fade_type == 1) {
            if (scroll_type == 1) {
                if (position_type == 0) {
                    return 0f;
                } else {
                    return 1f;
                }
            } else if (scroll_type == 0) {
                if (position_type == 2 || position_type == 1) {
                    return 1f;
                } else {
                    return 0f;
                }
            }
        }
        return 0f;
    }

    //底部减消效果一直隐藏
    @Override
    protected float getBottomFadingEdgeStrength() {
        if (fade_type == 0) {
            return 0f;
        } else if (fade_type == 1) {
            if (scroll_type == 1) {
                return 1f;
            } else if (scroll_type == 0) {
                if (position_type != 2) {
                    return 1f;
                } else {
                    return 0f;
                }
            }
        }
        return 0f;
    }

    @Override
    public void onScrollStateChanged(int state) {
        super.onScrollStateChanged(state);
        switch (state) {
            case RecyclerView.SCROLL_STATE_IDLE:
                // 当RecyclerView停止滚动时执行的代码
                scroll_type = 0;
                break;
            case RecyclerView.SCROLL_STATE_DRAGGING:
                // 当用户开始拖动时执行的代码
                scroll_type = 1;
                break;
            case RecyclerView.SCROLL_STATE_SETTLING:
                // 当滚动停止结束，RecyclerView开始滑动以达到最终位置时执行的代码
                break;
        }
    }

}
