package com.dfl.smartscene.widget.bannerview.shape

import android.animation.Animator
import android.content.Context
import android.util.AttributeSet
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.widget.bannerview.shape.animation.TwoIndicatorAnimaBuilder
import kotlin.math.abs

/**
 * @className TwoScrollIndicatorView
 * <AUTHOR>
 * @version 1.0.0
 * @description 双点翻页符
 * @createTime 2025/01/16/15:42
 */
class TwoIndicatorView : SmartIndicatorBase {


    //组件1
    private var indicatorPrev: Int = -1

    private var indicator0CenterX: Float = 0f
    private var indicator1CenterX: Float = 0f
    private var indicator2CenterX: Float = 0f

    //隐藏不显示的
    private var indicator3CenterX: Float = 0f

    //组件3
    private var indicatorNext: Int = -1

    private var lastSlidType = SlidType.NONE

    constructor(
        context: Context?,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context!!, attrs, defStyleAttr) {
        showNumber = 2
        scrollDistance = shapeMargin
        notExceedPointCount = showNumber
    }

    constructor(
        context: Context?,
        attrs: AttributeSet? = null
    ) : this(context!!, attrs, 0)

    constructor(context: Context) : this(context, null)

    override fun getAnimations(): MutableList<Animator> {
        val animators = mutableListOf<Animator>()
        val currentPosition = mLastPosition
        val nextPosition = getCurrentPosition()

        CommonLogUtils.logI(TAG,"getAnimations---currentPosition=$currentPosition, nextPosition=$nextPosition")

        if(isAllowScroll()){
            if(isLeftSlid()){
                animators.addAll(buildLeftSlidScrollAnimations(currentPosition, nextPosition))
            }else{
                animators.addAll(buildRightSlidScrollAnimations(currentPosition,nextPosition))
            }
        }else{
            if(isLeftSlid()) {
                animators.addAll(buildLeftSlidAnimations(currentPosition,nextPosition))
            }else{
                animators.addAll(buildRightSlidAnimations(currentPosition,nextPosition))
            }
        }
        return animators
    }

        private fun buildLeftSlidAnimations(currentPosition: Int, nextPosition: Int): Collection<Animator> {
//            Log.d(TAG,"buildLeftSlidAnimations----currentPosition=$currentPosition, nextPosition=$nextPosition")
            val animators = mutableListOf<Animator>()

            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedWidth(), getNormalWidth()) { setShapeWidth(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedHeight(), getNormalHeight()) { setShapeHeight(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.colorNotScroll(getSelectedColor(), getNormalColor()) { setShapeColor(currentPosition, it) })

            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalWidth(), getSelectedWidth()) { setShapeWidth(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalHeight(), getSelectedHeight()) { setShapeHeight(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.colorNotScroll(getNormalColor(), getSelectedColor()) { setShapeColor(nextPosition, it) })

            return animators
        }

        private fun buildRightSlidAnimations(currentPosition: Int, nextPosition: Int): Collection<Animator> {
//            Log.d(TAG,"buildRightSlidAnimations----currentPosition=$currentPosition, nextPosition=$nextPosition")

            val animators = mutableListOf<Animator>()

            if(isRightScrollThreshold(currentPosition) && !isNotExceedPointCount()){ //到达右滑临界点

                animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedWidth(),getNormalWidth()){setShapeWidth(currentPosition,it)})
                animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedHeight(),getNormalHeight()){setShapeHeight(currentPosition,it)})
                animators.add(TwoIndicatorAnimaBuilder.color(getSelectedColor(),getNormalColor()){setShapeColor(currentPosition,it)})
                animators.add(buildCurrentScrollAnimation(currentPosition))

                animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalWidth(),getSelectedWidth()){setShapeWidth(nextPosition,it)})
                animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalHeight(),getSelectedHeight()){setShapeHeight(nextPosition,it)})
                animators.add(TwoIndicatorAnimaBuilder.nextAlpha(getAlphaMin(), getAlphaMax()) { setShapeAlpha(nextPosition, it) })
                animators.add(TwoIndicatorAnimaBuilder.color(getNormalColor(),getSelectedColor()){setShapeColor(nextPosition,it)})
                animators.add(buildNextScrollAnimation(nextPosition))

                val currentRightPosition = currentPosition + 1
                animators.add(TwoIndicatorAnimaBuilder.prevAlpha { setShapeAlpha(currentRightPosition, it) })
                animators.add(buildPrevScrollAnimation(currentRightPosition))

            }else{
                animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedWidth(), getNormalWidth()) { setShapeWidth(currentPosition, it) })
                animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedHeight(), getNormalHeight()) { setShapeHeight(currentPosition, it) })
                animators.add(TwoIndicatorAnimaBuilder.colorNotScroll(getSelectedColor(), getNormalColor()) { setShapeColor(currentPosition, it) })

                animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalWidth(), getSelectedWidth()) { setShapeWidth(nextPosition, it) })
                animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalHeight(), getSelectedHeight()) { setShapeHeight(nextPosition, it) })
                animators.add(TwoIndicatorAnimaBuilder.colorNotScroll(getNormalColor(), getSelectedColor()) { setShapeColor(nextPosition, it) })

            }
            return animators
        }

    private fun isRightScrollThreshold(currentPosition: Int) =
        currentPosition == startScrollPosition || (currentPosition == startScrollPosition + 1) && !isFirstRightSlidScroll(currentPosition)


    private fun buildRightSlidScrollAnimations(currentPosition: Int, nextPosition: Int): Collection<Animator> {
        val animators = mutableListOf<Animator>()

        //左滑到最后或者首次向右滑
        if(isLastPosition(currentPosition) || isFirstRightSlidScroll(currentPosition)){
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedWidth(),getNormalWidth()){setShapeWidth(currentPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedHeight(),getNormalHeight()){setShapeHeight(currentPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.color(getSelectedColor(),getNormalColor()){setShapeColor(currentPosition,it)})

            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalWidth(),getSelectedWidth()){setShapeWidth(nextPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalHeight(),getSelectedHeight()){setShapeHeight(nextPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.color(getNormalColor(),getSelectedColor()){setShapeColor(nextPosition,it)})

        }else{
            val currentRightPosition = currentPosition + 1

            animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedWidth(),getNormalWidth()){setShapeWidth(currentPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedHeight(),getNormalHeight()){setShapeHeight(currentPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.color(getSelectedColor(),getNormalColor()){setShapeColor(currentPosition,it)})
            buildNextScrollAnimation(currentPosition).let { animators.add(it) }

            animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalWidth(),getSelectedWidth()){setShapeWidth(nextPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalHeight(),getSelectedHeight()){setShapeHeight(nextPosition,it)})
            animators.add(TwoIndicatorAnimaBuilder.nextAlpha(getAlphaMin(),getAlphaMax()) { setShapeAlpha(nextPosition,it) })
            animators.add(TwoIndicatorAnimaBuilder.color(getNormalColor(),getSelectedColor()){setShapeColor(nextPosition,it)})
            buildNextScrollAnimation(nextPosition).let { animators.add(it) }

            buildPrevScrollAnimation(currentRightPosition).let {  animators.add(it) }
            animators.add(TwoIndicatorAnimaBuilder.prevAlpha { setShapeAlpha(currentRightPosition,it) })
        }
        return animators
    }


    private fun buildLeftSlidScrollAnimations(currentPosition: Int, nextPosition: Int): Collection<Animator> {
        val animators = mutableListOf<Animator>()

        if(isFirstLeftSlidScroll(currentPosition)){
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedWidth(), getNormalWidth()) { setShapeWidth(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getSelectedHeight(), getNormalHeight()) { setShapeHeight(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.color(getSelectedColor(), getNormalColor()) { setShapeColor(currentPosition, it) })

            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalWidth(), getSelectedWidth()) { setShapeWidth(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.sizeNotScroll(getNormalHeight(), getSelectedHeight()) { setShapeHeight(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.color(getNormalColor(), getSelectedColor()) { setShapeColor(nextPosition, it) })
        }else{
            val currentLeftPosition = mLastPosition - 1

            animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedWidth(), getNormalWidth()) { setShapeWidth(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.currentSize(getSelectedHeight(), getNormalHeight()) { setShapeHeight(currentPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.color(getSelectedColor(), getNormalColor()) { setShapeColor(currentPosition, it) })
            buildCurrentScrollAnimation(currentPosition).let { animators.add(it) }

            animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalWidth(), getSelectedWidth()) { setShapeWidth(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.nextSize(getNormalHeight(), getSelectedHeight()) { setShapeHeight(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.nextAlpha(getAlphaMin(), getAlphaMax()) { setShapeAlpha(nextPosition, it) })
            animators.add(TwoIndicatorAnimaBuilder.color(getNormalColor(), getSelectedColor()) { setShapeColor(nextPosition, it) })
            buildNextScrollAnimation(nextPosition).let { animators.add(it) }

            buildNextScrollAnimation(currentLeftPosition).let { animators.add(it) }
            animators.add(TwoIndicatorAnimaBuilder.prevAlpha { setShapeAlpha(currentLeftPosition, it) })

        }
        return animators
    }

    private fun buildCurrentScrollAnimation(position: Int): Animator {
        val startShapeCenterX = getShapeCenterX(position)
        val scrollAnimation = TwoIndicatorAnimaBuilder.currentScroll(getDistance()) {
            setShapeCenterX(position, startShapeCenterX + it)
        }
        return scrollAnimation
    }

    private fun buildPrevScrollAnimation(position:Int): Animator{
        val startShapeCenterX = getShapeCenterX(position)
        val scrollAnimation = TwoIndicatorAnimaBuilder.prevScroll(getDistance()) {
            setShapeCenterX(position, startShapeCenterX + it)
        }
        return scrollAnimation
    }

    private fun buildNextScrollAnimation(position: Int): Animator{
        val startShapeCenterX = getShapeCenterX(position)
        val scrollAnimation = TwoIndicatorAnimaBuilder.nextScroll(getDistance()) {
            setShapeCenterX(position, startShapeCenterX + it)
        }
        return scrollAnimation
    }

     fun getDistance() =  if (isLeftSlid()) {
         -scrollDistance
     } else {
         scrollDistance
     }

    override fun getAnimationDuration(): Long {
        return 500L
    }


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (getPageSize() > 1) {
            var length = showNumber + 1
            val width = shapeMargin * length + shapeMargin + paddingStart + paddingEnd
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom

            setMeasuredDimension(width.toInt(), height.toInt())
        } else if(getPageSize() == 1){
            val width = shapeMargin + paddingStart + paddingEnd
            val height = getSelectedHeight() * 2 + paddingTop + paddingBottom
            setMeasuredDimension(width.toInt(), height.toInt())
        }else{
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
//        Log.d(TAG, "onMeasure---width=$measuredWidth, height=$measuredHeight")

        if (getPageSize() > 1) {
            indicatorPrev = getCurrentPosition() - 1
            indicatorNext = getCurrentPosition() + 1

            val centerX = measuredWidth/2f
            val distance = shapeMargin/2f

//            Log.d(TAG,"onMeasure---centerX=$centerX, distance=$distance")

            indicator1CenterX = centerX - distance
            indicator2CenterX = centerX + distance
            indicator0CenterX = indicator1CenterX - shapeMargin
            indicator3CenterX = indicator2CenterX + shapeMargin

        }else if(getPageSize() == 1){
            indicator1CenterX = measuredWidth/2f
        }

        if(getPageSize() > 0){
            CommonLogUtils.logI(
                TAG,
                "onMeasure---indicatorPrevCenterX=$indicator1CenterX, indicatorCurrentCenterX=$indicator3CenterX, indicatorNexCenterX=$indicator2CenterX"
            )

            initIndicators()
            if (getCurrentPosition() == 0) {
                mLastPosition = 1
            } else {
                mLastPosition = getCurrentPosition() - 1
            }
            toggle(false)

//            Log.d(TAG, "onMeasure---shapes=$shapes")
        }
    }

    override fun toggle(isAnimation: Boolean) {
        CommonLogUtils.logI(
            TAG,
            "toggle---isAnimation=$isAnimation, currentPosition=${mLastPosition}, mLastPosition=${getCurrentPosition()}, showNumber$showNumber, pageSize=${getPageSize()}"
        )
        resetIndicators()
        if (isAnimation && getPageSize() >= showNumber) {
            resetAnimationStartPositionParams()
            startIndicatorAnimation()
        } else {
            resetNotAnimationPositionParams()
        }
    }

    private fun resetNotAnimationPositionParams() {
        val currentPosition = getCurrentPosition()

        if(getPageSize() == 1){
            setPositionParams(0,getSelectedWidth(),getSelectedHeight(),getAlphaMax(),indicator1CenterX,getSelectedColor())
        }else{
            if(currentPosition == 0){
                setPositionParams(1,getNormalWidth(),getNormalHeight(),getAlphaMax(),indicator2CenterX,getNormalColor())
                setPositionParams(currentPosition,getSelectedWidth(),getSelectedHeight(),getAlphaMax(),indicator1CenterX,getSelectedColor())
            }else if(currentPosition == 1){
                setPositionParams(0,getNormalWidth(),getNormalHeight(),getAlphaMax(),indicator1CenterX,getNormalColor())
                setPositionParams(currentPosition,getSelectedWidth(),getSelectedHeight(),getAlphaMax(),indicator2CenterX,getSelectedColor())
            }else{
                val currentLeftPosition = currentPosition - 1
                setPositionParams(currentLeftPosition,getNormalWidth(),getNormalHeight(),getAlphaMax(),indicator1CenterX,getNormalColor())
                setPositionParams(currentPosition,getSelectedWidth(),getSelectedHeight(),getAlphaMax(),indicator2CenterX,getSelectedColor())
            }
        }
    }


    override fun onCancelAnimation() {

    }

    private fun resetAnimationStartPositionParams() {
        if(getPageSize() >= showNumber){
            val currentPosition = mLastPosition
            val nextPosition = getCurrentPosition()
            if(isAllowScroll()){
                if(isLeftSlid()){
                    resetLeftSlidScrollParams(currentPosition, nextPosition)
                }else{
                    resetRightSlidScrollParams(currentPosition, nextPosition)
                }
            }else{
                if(isLeftSlid()){
                    resetLeftSlidParams(currentPosition, nextPosition)
                }else{
                    resetRightSlidParams(currentPosition, nextPosition)
                }
            }
            lastSlidType = if(currentPosition > nextPosition) SlidType.RIGHT else SlidType.LEFT
        }
    }

    private fun resetRightSlidParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(TAG,"resetRightSlidParams---currentPosition=$currentPosition, nextPosition=$nextPosition, startScrollPosition=$startScrollPosition")
        if (isRightScrollThreshold(currentPosition) && !isNotExceedPointCount()) { //滚动到临界点
            val currentRightPosition = currentPosition + 1
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator1CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMin(), indicator0CenterX, getNormalColor())
            setPositionParams(currentRightPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator2CenterX, getNormalColor())
        }else {
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator1CenterX, getNormalColor())
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator2CenterX, getSelectedColor())
        }
    }

    private fun resetLeftSlidParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(TAG,"resetLeftSlidParams---currentPosition=$currentPosition, nextPosition=$nextPosition")
        setPositionParams(
            currentPosition,
            getSelectedWidth(),
            getSelectedHeight(),
            getAlphaMax(),
            indicator1CenterX,
            getSelectedColor()
        )
        setPositionParams(
            nextPosition,
            getNormalWidth(),
            getNormalHeight(),
            getAlphaMax(),
            indicator2CenterX,
            getNormalColor()
        )
    }

    private fun resetRightSlidScrollParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(TAG,"resetRightSlidScrollParams---currentPosition=$currentPosition, nextPosition=$nextPosition")
        if (isLastPosition(currentPosition)) {
//            Log.d(TAG,"resetRightSlidScrollParams---isLastPosition")
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator2CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator1CenterX, getNormalColor())
        }else if(isFirstRightSlidScroll(currentPosition)){
//            Log.d(TAG,"resetRightSlidScrollParams---isFirstRightSlidScroll")
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator2CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator1CenterX, getNormalColor())
        }else{
            val currentRightPosition = currentPosition + 1
            setPositionParams(currentRightPosition,getNormalWidth(),getNormalHeight(),getAlphaMax(),indicator2CenterX,getNormalColor())

            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator1CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMin(), indicator0CenterX, getNormalColor())
        }

    }

    private fun isFirstRightSlidScroll(position: Int): Boolean{
        val shapeCenterX = getShapeCenterX(position)
//        Log.d(TAG,"isFirstRightSlidScroll---position=$position, shapeCenterX=$shapeCenterX, indicator2CenterX=$indicator2CenterX")
         return  (abs(shapeCenterX - indicator2CenterX) < 0.1f) && !isLastPrePosition(position)
    }

    private fun isFirstLeftSlidScroll(position: Int): Boolean{
        val shapeCenterX = getShapeCenterX(position)
//        Log.d(TAG,"isFirstLeftSlidScroll---position=$position, shapeCenterX=$shapeCenterX, indicator2CenterX=$indicator2CenterX")
        return  abs(shapeCenterX - indicator1CenterX) < 0.1f
    }

    private fun resetLeftSlidScrollParams(currentPosition: Int, nextPosition: Int) {
//        Log.d(TAG,"resetLeftSlidScrollParams--currentPosition=$currentPosition,nextPosition=$nextPosition")
        if(isFirstLeftSlidScroll(currentPosition)){
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator1CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator2CenterX, getNormalColor())
        }else{
            val currentLeftPosition = mLastPosition - 1
            setPositionParams(currentPosition, getSelectedWidth(), getSelectedHeight(), getAlphaMax(), indicator2CenterX, getSelectedColor())
            setPositionParams(nextPosition, getNormalWidth(), getNormalHeight(), getAlphaMin(), indicator3CenterX, getNormalColor())
            setPositionParams(currentLeftPosition, getNormalWidth(), getNormalHeight(), getAlphaMax(), indicator1CenterX, getNormalColor())
        }

    }

    //允许向右滚动

    private fun resetIndicators() {
        indicatorPrev = -1
        indicatorNext = -1
        for (i in 0 until shapes.size) {
            setShapeAlpha(i, 0f)
            setShapeAllowDraw(i, false)
        }
    }


    private enum class SlidType{
        NONE, LEFT,RIGHT
    }
}