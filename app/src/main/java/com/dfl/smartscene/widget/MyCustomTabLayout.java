package com.dfl.smartscene.widget;

import android.animation.AnimatorInflater;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.UiModeManager;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.animation.PathInterpolator;
import android.widget.TextView;

import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.common.nightmode.res.SkinCompatVectorResources;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.view.ViewCompat;

import static android.content.Context.UI_MODE_SERVICE;

/**
 * Created by 钟文祥 on 2025/1/13.
 * Describer:
 */
@Deprecated
public class MyCustomTabLayout extends ConstraintLayout {
    private int mType = 0;//0:一级居中橘色指示器tabLayout；1:二级左对齐橘色指示器tabLayout；2:二级左对齐小字号橘色指示器tabLayout；3:胶囊tabLayout

    private static final String TAG = "CustomTabLayout";

    private Context mContext;
    private int textColor = 0, textColorSelected = 0; // textColor颜色值
    private int textColorId = 0, textColorSelectedId = 0; // textColor颜色值
    private int textSize = 40; // textColor Id
    private int textBottomMargin = 0; // textColor Id
    private int itemWidth = 0; // 单个item宽度
    private int itemGap = 0; // item之间的间距，默认0
    private int lastSelectedPosition = -1; // 上一个选中的Tab位置
    private int selectedPosition = 0; // 当前选中的Tab位置
    private View slidingIndicator; // 指示器，背景滑块
    private int indicatorDrawable = 0;
    private OnTabSelectedListener listener; // Tab选中回调接口
    private int tabCount = 0; // 记录tab的数量
    private int[] tabIds; // 保存每个Tab的ID
    private Boolean isSwitchTab = true; //切换是否生效

    private int textWidth = 0;
    private List<Integer> titltWidth = new ArrayList<>();

    public int currentIndex = 0;

    public MyCustomTabLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initRes(attrs, context);
        init(context);
    }

    public MyCustomTabLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initRes(attrs, context);
        init(context);
    }

    public MyCustomTabLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                             int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initRes(attrs, context);
        init(context);
    }

    private void init(Context context) {

        setVisibility(View.INVISIBLE); //初始化时隐藏

        // 初始化指示器View
        slidingIndicator = new View(context);
        slidingIndicator.setId(View.generateViewId());
        if (mType == 3) {
            indicatorDrawable = com.dfl.android.animationlib.R.drawable.nissan_hor_pop_tab_selected_bg;
            slidingIndicator.setBackgroundResource(indicatorDrawable);
        } else if (mType == 2) {
            slidingIndicator.setBackground(getResources().getDrawable(com.dfl.android.animationlib.R.drawable.nissan_hor_second_tab_selected_bg));
        } else {
            slidingIndicator.setBackground(getResources().getDrawable(com.dfl.android.animationlib.R.drawable.nissan_hor_first_tab_selected_bg));
        }
        addView(slidingIndicator);

        // 设置指示器初始约束
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(this);

        //设置指示器的样式
        if (mType == 3) {
            constraintSet.connect(slidingIndicator.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM, 4);
            constraintSet.connect(slidingIndicator.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP);
        } else if (mType == 0) {
            constraintSet.connect(slidingIndicator.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM, textBottomMargin);
        } else {
            constraintSet.connect(slidingIndicator.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM, textBottomMargin);
        }

        constraintSet.applyTo(this);

        if (listener != null) {
            listener.onInitViewComplete(false);
        }
    }

    @SuppressLint("ResourceType")
    private void initRes(AttributeSet attrs, Context context) {
        mContext = context;

        TypedArray ta = context.obtainStyledAttributes(attrs, com.dfl.android.animationlib.R.styleable.CustomTabLayout);

        boolean hasType = ta.hasValue(com.dfl.android.animationlib.R.styleable.CustomTabLayout_layout_type);//方向，左右还是上下
        boolean hasTextColor =
                ta.hasValue(com.dfl.android.animationlib.R.styleable.CustomTabLayout_unselect_title_color);//选中item背景
        boolean hasTextColorSelected =
                ta.hasValue(com.dfl.android.animationlib.R.styleable.CustomTabLayout_select_title_color);//item图标个数
        boolean hasTextBottomMargin =
                ta.hasValue(com.dfl.android.animationlib.R.styleable.CustomTabLayout_title_bottom_margin);//item图标个数


        if (hasType) {
            mType = ta.getInt(com.dfl.android.animationlib.R.styleable.CustomTabLayout_layout_type, 0);
        } else {
            mType = 0;
        }

        if (hasTextColor) {
            textColorId =
                    ta.getResourceId(com.dfl.android.animationlib.R.styleable.CustomTabLayout_unselect_title_color, 0);
            textColor = ta.getColor(com.dfl.android.animationlib.R.styleable.CustomTabLayout_unselect_title_color,
                    getResources().getColor(com.dfl.android.animationlib.R.color.nissan_custom_hor_tab_tittle));
        } else {
            textColorId = com.dfl.android.animationlib.R.color.nissan_custom_hor_tab_tittle;
            textColor = getResources().getColor(com.dfl.android.animationlib.R.color.nissan_custom_hor_tab_tittle);
        }

        if (hasTextColorSelected) {
            textColorSelectedId =
                    ta.getResourceId(com.dfl.android.animationlib.R.styleable.CustomTabLayout_select_title_color, 0);
            textColorSelected =
                    ta.getColor(com.dfl.android.animationlib.R.styleable.CustomTabLayout_select_title_color,
                            getResources().getColor(com.dfl.android.animationlib.R.color.nissan_custom_hor_select_tab_tittle));
        } else {
            textColorSelectedId = com.dfl.android.animationlib.R.color.nissan_custom_hor_select_tab_tittle;
            textColorSelected =
                    getResources().getColor(com.dfl.android.animationlib.R.color.nissan_custom_hor_select_tab_tittle);
        }

        if (hasTextBottomMargin) {
            textBottomMargin =
                    ta.getDimensionPixelSize(com.dfl.android.animationlib.R.styleable.CustomTabLayout_title_bottom_margin, 0);
        } else {
            if (mType == 0 || mType == 1) {
                textBottomMargin = 11;
            } else {
                textBottomMargin = 22;
            }
        }

        //根据类型设置字体大小
        if (mType == 2 || mType == 3) {
            textSize = 36;
        } else {
            textSize = 40;
        }
    }

    /**
     * 实际上真正添加item的地方，要等到OnMeasure之后
     *
     * @param items 列表数据Object
     */
    private void realAddTabs(List<Object> items) {

        tabCount = items.size();
        tabIds = new int[tabCount];
        int tabWidth = 0;
        int previousTabId = ConstraintSet.PARENT_ID; // 用于动态设置前一个Tab的约束

        for (int i = 0; i < items.size(); i++) {
            View tabView = null;

            TextView textView = null;
            //根据类型实例化
            if (mType == 0 || mType == 3) {
                textView = (TextView) createTextView((String) items.get(i), Gravity.CENTER);
            } else {
                textView = (TextView) createTextView((String) items.get(i), Gravity.START);
            }

            tabView = textView;
            Paint paint = textView.getPaint();
            tabWidth = (int) paint.measureText(textView.getText().toString());

            titltWidth.add(tabWidth);

            if (tabWidth > textWidth) {
                textWidth = tabWidth;
                itemWidth = textWidth;
            }

            if (tabView != null) {
                tabView.setId(View.generateViewId()); // 为每个Tab生成唯一ID
                tabIds[i] = tabView.getId(); // 保存Tab的ID
//                setupTouchListener(tabView, i);
                addView(tabView);
            }
        }

        for (int i = 0; i < items.size(); i++) {
            View tabView = getTabView(i);
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(this);
            // 设置Tab项等宽
            constraintSet.constrainHeight(tabView.getId(), 92);

            if (mType == 0) {
                constraintSet.constrainWidth(tabView.getId(), titltWidth.get(i) + 88); // 宽度为计算出的单个宽度
            } else if (mType == 3) {
                constraintSet.constrainWidth(tabView.getId(), titltWidth.get(i) + 96);
            } else {
                constraintSet.constrainWidth(tabView.getId(), ConstraintSet.WRAP_CONTENT); // 宽度为计算出的单个宽度
            }

            constraintSet.connect(tabView.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);
            constraintSet.connect(tabView.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM, 14);
            if (i > 0) {
                constraintSet.setMargin(tabView.getId(), 6, itemGap);//start=3
            }

            if (previousTabId == ConstraintSet.PARENT_ID) {
                if (mType == 0 || mType == 3) {
                    constraintSet.connect(tabView.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID,
                            ConstraintSet.START);
                } else {
                    constraintSet.connect(tabView.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID,
                            ConstraintSet.START);
                }
            } else {
                if (mType == 0 || mType == 3) {
                    constraintSet.connect(tabView.getId(), ConstraintSet.START, previousTabId, ConstraintSet.END);
                } else if (mType == 1) {
                    constraintSet.connect(tabView.getId(), ConstraintSet.START, previousTabId, ConstraintSet.END, 88);
                } else {
                    constraintSet.connect(tabView.getId(), ConstraintSet.START, previousTabId, ConstraintSet.END, 96);
                }
            }

            constraintSet.applyTo(this);
            previousTabId = tabView.getId(); // 更新previousTabId

            final int position = i;
            tabView.setOnClickListener(v -> onTabClicked(position, true));
        }
        post(() -> initSelectedItemUI(selectedPosition));
    }

    /**
     * 创建纯文本TextView作为Tab项
     *
     * @param title 文本内容
     * @return TextView
     */
    @SuppressLint("ResourceType")
    private TextView createTextView(String title, int gravity) {
        TextView textView = new TextView(getContext());
        textView.setText(title);
        textView.setGravity(gravity);
        textView.setTextSize(textSize); // 设置文字大小
        textView.setTextColor(textColor); // 未选中文字颜色
        textView.setStateListAnimator(AnimatorInflater.loadStateListAnimator(getContext(),
                com.dfl.android.animationlib.R.animator.tab_switch_image_change));

        return textView;
    }

    /**
     * tab🈶选中到未选中到动画
     */
    private void unSelectTabAnim(int position) {
        if (lastSelectedPosition != -1) {
            if (findViewById(tabIds[lastSelectedPosition]) instanceof TextView) {
                View view = (TextView) findViewById(tabIds[lastSelectedPosition]);
                ((TextView) findViewById(tabIds[lastSelectedPosition])).setTextColor(textColor);

                ValueAnimator animator = ValueAnimator.ofFloat(1f, 0.95f);
                animator.setDuration(67); // 放大时间
                animator.setInterpolator(new PathInterpolator(0.00f, 0.00f, 1.00f, 1.00f));
                animator.addUpdateListener(animation -> {
                    float scale = (float) animation.getAnimatedValue();
                    view.setScaleX(scale);
                    view.setScaleY(scale);
                });
                animator.start();
            }
            if (listener != null) { //上一个TAB变成未选中时返回回调
                listener.onTabUnSelected(lastSelectedPosition);
            }
        }

        if (selectedPosition != -1) {
            findViewById(tabIds[selectedPosition]).setSelected(true);
            if (findViewById(tabIds[selectedPosition]) instanceof TextView) {
                ((TextView) findViewById(tabIds[selectedPosition])).setTextColor(textColorSelected);
            }
        }
    }

    /**
     * 处理Tab点击事件
     *
     * @param position 点击的位置
     */
    public void onTabClicked(int position, Boolean isManual) {
        // 回调给外部监听者
        if (listener != null) {
            CommonLogUtils.logV("sdfjkl",
                    "onTabClicked1——0: selectedPosition:" + selectedPosition + " position:" + position);
            listener.onTabClicked(position, isManual);
        }

        if (position != selectedPosition || position < 0) {
            if (tabCount == 0) {
                selectedPosition = position;
                CommonLogUtils.logV("sdfjkl", "onTabClicked1-1: " + selectedPosition);
                return;
            }
            // 回调给外部监听者
            if (listener != null && isManual) { //
                listener.onTabSelected(position, isManual);
                CommonLogUtils.logV("sdfjkl", "onTabClicked1——2: 回调" + position);
            }

            //手点击同时设置了只回调开关的话，不做逻辑处理，只回调
            if (isManual && !isSwitchTab) {
                return;
            }

            slidingIndicator.setVisibility(View.VISIBLE);//设置一遍可见，清除设置的不可见状态

            lastSelectedPosition = selectedPosition;
            selectedPosition = position;

            CommonLogUtils.logV("sdfjkl", "onTabClicked2: " + selectedPosition);
            if (currentIndex == 0) {
                moveIndicator(selectedPosition);
                CommonLogUtils.logV("sdfjkl", "onTabClicked3: " + selectedPosition);
            } else {
                if (currentIndex == position) {
                    currentIndex = 0;
                    CommonLogUtils.logV("sdfjkl", "onTabClicked4_1: " + selectedPosition);
                }
                CommonLogUtils.logV("sdfjkl", "onTabClicked4_2: " + selectedPosition);
            }
            if (lastSelectedPosition != selectedPosition) {
                if (isSwitchTab) {
                    toggleTouchAnimation(getTabView(position));
                    CommonLogUtils.logV("sdfjkl", "onTabClicked5_1: " + selectedPosition);
                }
                unSelectTabAnim(lastSelectedPosition);
                CommonLogUtils.logV("sdfjkl", "onTabClicked5_2: " + selectedPosition);
            } else {
                CommonLogUtils.logV("sdfjkl", "onTabClicked5_3: " + lastSelectedPosition + " " + selectedPosition);
            }
        } else {
            CommonLogUtils.logV("sdfjkl", "onTabClicked6: " + selectedPosition);
            if (listener != null && isManual) {
                listener.onTabSelected(position, isManual);
                slidingIndicator.setVisibility(View.VISIBLE);//设置一遍可见，清除设置的不可见状态

                findViewById(tabIds[selectedPosition]).setSelected(true);
                if (findViewById(tabIds[selectedPosition]) instanceof TextView) {
                    ((TextView) findViewById(tabIds[selectedPosition])).setTextColor(textColorSelected);
                }
                if (isSwitchTab && isManual) {
                    toggleTouchAnimation(getTabView(position));
                }
            }
        }

    }

    /**
     * 初始化选中item样式
     *
     * @param newPosition 位置索引
     */
    private void initSelectedItemUI(int newPosition) {
        // 动态调整指示器的宽度
        final View selectedTab = findViewById(tabIds[newPosition]);
        if (mType == 3) {
            slidingIndicator.getLayoutParams().width = selectedTab.getWidth();
            slidingIndicator.getLayoutParams().height = selectedTab.getHeight() - 20;
        } else if (mType == 0 || mType == 1) {
            slidingIndicator.getLayoutParams().width = 59;
            slidingIndicator.getLayoutParams().height = 6;
        } else {
            slidingIndicator.getLayoutParams().width = 51;
            slidingIndicator.getLayoutParams().height = 4;
        }
        slidingIndicator.requestLayout();

        setVisibility(View.VISIBLE); //添加完所有View后显示

        if (listener != null) {
            listener.onInitViewComplete(true);
        }
        if (tabCount == 0) {
            return;
        }
        TextView textView = (TextView) selectedTab;
        Paint paint = textView.getPaint();
        int tabWidth = (int) paint.measureText(textView.getText().toString());

        if (mType == 0) {
            slidingIndicator.setX(selectedTab.getLeft() - getPaddingStart() + (titltWidth.get(newPosition) + 30) / 2);
        } else if (mType == 1 || mType == 2) {
            slidingIndicator.setX(selectedTab.getLeft() - getPaddingStart() + (tabWidth - 56) / 2);
        } else {
            slidingIndicator.setX(selectedTab.getLeft() - getPaddingStart());
        }

        findViewById(tabIds[newPosition]).setSelected(true);
        updateTabSelection(newPosition);
    }

    /**
     * 选中item的动效
     *
     * @param view 选中item的View
     */
    private void toggleTouchAnimation(View view) {
        ViewCompat.animate(view)
                .scaleX(1.02f)
                .scaleY(1.02f)
                .setDuration(167)
                .setInterpolator(new PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        ViewCompat.animate(view)
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(133)
                                .setInterpolator(new PathInterpolator(0.0f, 0.0f, 1.0f, 1.0f))
                                .start();
                    }
                })
                .start();
    }

    /**
     * 移动指示器的动画
     *
     * @param newPosition 位置索引
     */
    private void moveIndicator(final int newPosition) {
        if (tabCount == 0) {
            return;
        }
        if (slidingIndicator.getVisibility() != VISIBLE) {
            slidingIndicator.setVisibility(VISIBLE);
        }
        final View selectedTab = findViewById(tabIds[newPosition]);
        TextView textView = (TextView) selectedTab;
        Paint paint = textView.getPaint();
        int tabWidth = (int) paint.measureText(textView.getText().toString());
        ValueAnimator animator;
        if (mType == 0) {
            animator = ValueAnimator.ofFloat(slidingIndicator.getTranslationX(),
                    selectedTab.getLeft() - getPaddingStart() + (titltWidth.get(newPosition) + 30) / 2);
            //位置要减去padding的宽度
        } else if (mType == 1 || mType == 2) {
            animator = ValueAnimator.ofFloat(slidingIndicator.getTranslationX(),
                    selectedTab.getLeft() - getPaddingStart() + (tabWidth - 56) / 2); //位置要减去padding的宽度
        } else {
            slidingIndicator.getLayoutParams().width = selectedTab.getWidth();
            slidingIndicator.getLayoutParams().height = selectedTab.getHeight() - 20;
            slidingIndicator.requestLayout();
            animator = ValueAnimator.ofFloat(slidingIndicator.getTranslationX(),
                    selectedTab.getLeft() - getPaddingStart()); //位置要减去padding的宽度
        }

        animator.addUpdateListener(animation -> slidingIndicator.setTranslationX((float) animation.getAnimatedValue()));

        animator.setInterpolator(new PathInterpolator(0.22f, 1.00f, 0.36f, 1.00f));
        animator.setDuration(500); // 动画时长400ms
        animator.start();
    }

    /**
     * 更新选中的Tab的外观
     *
     * @param newPosition 位置
     */
    private void updateTabSelection(int newPosition) {
        if (lastSelectedPosition != -1) {
            findViewById(tabIds[lastSelectedPosition]).setSelected(false);
            if (findViewById(tabIds[lastSelectedPosition]) instanceof TextView) {
                ((TextView) findViewById(tabIds[lastSelectedPosition])).setTextColor(textColor);
            }
            if (listener != null) { //上一个TAB变成未选中时返回回调
                listener.onTabUnSelected(lastSelectedPosition);
            }
        }
        findViewById(tabIds[newPosition]).setSelected(true);
        if (findViewById(tabIds[newPosition]) instanceof TextView) {
            ((TextView) findViewById(tabIds[newPosition])).setTextColor(textColorSelected);
        }
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        adapterSkinColor(newConfig);
    }

    public void setIsSwitchTab(Boolean s) {
        isSwitchTab = s;
    }

    /**
     * 适配白天黑夜模式
     */
    @SuppressLint("ResourceType")
    public void adapterSkinColor(Configuration newConfig) {
        boolean isNightMode = getIsNightMode();

        if (textColorId != 0) {
            Drawable drawableBackgroundBaseColor = SkinCompatVectorResources.getDrawableCompat(getContext(),
                    textColorId);
            if (drawableBackgroundBaseColor instanceof ColorDrawable) {
                ColorDrawable colorDrawableBackgroundBaseColor = (ColorDrawable) drawableBackgroundBaseColor;
                textColor = colorDrawableBackgroundBaseColor.getColor();
            }
        }

        if (textColorSelectedId != 0) {
            Drawable drawableBackgroundBaseColor1 = SkinCompatVectorResources.getDrawableCompat(getContext(),
                    textColorSelectedId);
            if (drawableBackgroundBaseColor1 instanceof ColorDrawable) {
                ColorDrawable colorDrawableBackgroundBaseColor = (ColorDrawable) drawableBackgroundBaseColor1;
                textColorSelected = colorDrawableBackgroundBaseColor.getColor();
            }
        }

        if (indicatorDrawable != 0) {
            Drawable drawable = SkinCompatVectorResources.getDrawableCompat(getContext(), indicatorDrawable);
            slidingIndicator.setBackground(drawable); //设置指示器背景
        }

        for (int i = 0; i < tabCount; i++) {
            TextView textView = findViewById(tabIds[i]);
            if (i == selectedPosition) {
                textView.setTextColor(textColorSelected);
            } else {
                textView.setTextColor(textColor);
            }
        }
    }

    /**
     * 判断是否是黑夜模式
     *
     * @return true:是黑夜模式，false:不是黑夜模式
     */
    private Boolean getIsNightMode() {
        UiModeManager uiManager = (UiModeManager) mContext.getSystemService(UI_MODE_SERVICE);
        return uiManager.getNightMode() == UiModeManager.MODE_NIGHT_YES;
    }

    /**
     * 设置Tab选中回调监听
     *
     * @param listener
     */
    public void setOnTabSelectedListener(OnTabSelectedListener listener) {
        this.listener = listener;
    }

    /**
     * 添加横向Tab的外部接口
     *
     * @param items 对象列表，可有文言和图片资源Id
     */
    public void generateViews(List<Object> items) {
        post(new Runnable() {
            @Override
            public void run() {
                realAddTabs(items);
            }
        });
    }

    /**
     * 外部回调接口定义
     * 选中事件的外部回调接口定义
     * 参数说明:
     * position选中下标，从0开始；
     * viewId选中的ViewId，用于取得VIew进行额外处理；
     * isManual为区分是否是手动点击回调还是代码设置点击回调
     */
    public interface OnTabSelectedListener {
        default void onTabClicked(int position, Boolean isManual) {

        }

        //从未选中态变为选中态的回调
        void onTabSelected(int position, Boolean isManual);

        //从选中态变为未选中态的回调
        void onTabUnSelected(int lastPosition);

        void onInitViewComplete(Boolean isComplete);
    }


    /**
     * 外部接口，设置选中Tab的索引,不会回调透出
     *
     * @param index 0~n
     */
    public void setCurrentItem(int index) {
        post(() -> {
            currentIndex = index;
            onTabClicked(index, false);
        });
    }


    public void setSetCurrentItem(int index) {
        this.currentIndex = index;
    }

    /**
     * 外部接口，根据position获取View
     *
     * @return 对应位置的点击View
     */
    public View getTabView(int position) {
        if (tabCount == 0 || position >= tabCount) {
            return null;
        }
        return findViewById(tabIds[position]);
    }

    /**
     * 清楚选中状态
     */
    public void clearCheckStatus() {
        if (slidingIndicator != null) {
            slidingIndicator.setVisibility(INVISIBLE);
            if (selectedPosition != -1 && selectedPosition < tabCount) {
                updateUnselectedItem(selectedPosition);
            }
        }
    }

    /**
     * 更新上一个选中的tab变成未选中状态
     *
     * @param position
     */
    private void updateUnselectedItem(int position) {
        if (position != -1) {
            findViewById(tabIds[position]).setSelected(false);
            if (findViewById(tabIds[position]) instanceof TextView) {
                ((TextView) findViewById(tabIds[position])).setTextColor(textColor);
            }
            if (listener != null) { //上一个TAB变成未选中时返回回调
                listener.onTabUnSelected(position);
            }
        }
    }

    public void setClickTab(int index) {
        post(() -> {
//            currentIndex = index;
            onTabClicked(index, true);
        });
    }

    public int getSelectedPosition() {
        return selectedPosition;
    }
}