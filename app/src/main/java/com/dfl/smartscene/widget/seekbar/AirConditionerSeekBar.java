package com.dfl.smartscene.widget.seekbar;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.commonlib.CommonUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.common.nightmode.widget.SkinCompatView;
import com.dfl.smartscene.R;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/29
 * desc :lk1a ui1.3已不再使用,使用IndicatorSeekBarDialog
 * version: 1.0
 */
@Deprecated
public class AirConditionerSeekBar extends SkinCompatView {
    private static final String TAG = GlobalConstant.GLOBAL_TAG.concat("AirConditionerSeekBar");
    private final int mSeekBarTop = 65;
    private Context mContext;
    private Paint mBackgroundPaint;//第一层背景
    private Paint mGrayBgPaint;//第二层刻度灰色背景
    private Paint mPaint;//进度条
    private Paint mTextPaint;//文字
    private float mProgressWidth = 400;//进度条长度
    private int leftPadding = 25;//左边距
    private int mTotalBlockNum = 5;//方块总数量默认5个
    private int mLineSpaceWidth = 5;//中间间隔竖线宽度
    private int mSeekBarHeight = 80;//设置进度条高度
    private int mRoundRadius = 14;//设置圆角大小
    private int mBlockNum = 0;//当前方块数量默认为0
    private String mUnit = CommonUtils.getString(R.string.scene_text_indicator_unit_air_volume);
    //当前方块单位，默认为风速单位：挡，例：1挡、2挡
    private Path mBgPath;//第一层背景路径
    private Path mGrayBgPath;//第二层灰色方块路径
    private Path mPath;//第三层当前方块路径
    private Path mTrianglePath;//当前方块指针路径
    private int mMinBlockNum = 1;//最小方块数目限制，默认为1
    private float mUnitWidth;//每个方块的宽度
    private Drawable mLeftDrawable;//设置左边图标
    private Drawable mRightDrawable;//设置右边图标

    public AirConditionerSeekBar(Context context) {
        super(context);
        init(context);
    }

    public AirConditionerSeekBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.AirConditionerSeekBar);
            if (typedArray != null) {
                mTotalBlockNum = typedArray.getInt(R.styleable.AirConditionerSeekBar_asb_max_block_num, 5);
                mMinBlockNum = typedArray.getInt(R.styleable.AirConditionerSeekBar_asb_min_block_num, 0);
                mLineSpaceWidth =
                        typedArray.getDimensionPixelSize(R.styleable.AirConditionerSeekBar_asb_line_space_width, 5);
                mUnit = typedArray.getString(R.styleable.AirConditionerSeekBar_asb_default_block_unit);
                mRoundRadius =
                        typedArray.getDimensionPixelSize(R.styleable.AirConditionerSeekBar_asb_round_radius_size, 10);
                mSeekBarHeight =
                        typedArray.getDimensionPixelSize(R.styleable.AirConditionerSeekBar_asb_seekbar_height, 80);
                mBlockNum = typedArray.getInt(R.styleable.AirConditionerSeekBar_asb_default_block_num, 0);
                mLeftDrawable = typedArray.getDrawable(R.styleable.AirConditionerSeekBar_asb_left_icon_src);
                mRightDrawable = typedArray.getDrawable(R.styleable.AirConditionerSeekBar_asb_right_icon_src);
                typedArray.recycle();
            }
        }
        if (mUnit == null) {
            mUnit = mContext.getString(R.string.scene_text_indicator_unit_air_volume);
        }
        init(context);
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int realWidth = startMeasure(widthMeasureSpec);
        int realHeight = startMeasure(heightMeasureSpec);
        setMeasuredDimension(realWidth, realHeight);
    }

    private void init(Context context) {
        mContext = context;
        initPaint();
    }

    public void setBlockLevel(int level) {
        if (level > mTotalBlockNum) {
            throw new RuntimeException("setBlockLevel must Less than or equal to mTotalBlockNum");
        }
        if (level < mMinBlockNum) {
            throw new RuntimeException("setBlockLevel must more than or equal to mMinBlockNum");
        }
        this.mBlockNum = level;
        invalidate();
    }

    public String getUnit() {
        if (mUnit == null) {
            mUnit = mContext.getString(R.string.scene_text_indicator_unit_air_volume);
        }
        return mUnit;
    }

    @Override
    public void applySkin() {
        super.applySkin();
        mBackgroundPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_medal_bg));
        mGrayBgPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_bg_2));
        mPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        invalidate();
    }

    private void initPaint() {
        //初始化绘制背景的画笔
        mBackgroundPaint = new Paint();
        mBackgroundPaint.setStyle(Paint.Style.FILL);
        mBackgroundPaint.setAntiAlias(true);
        mBackgroundPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_medal_bg));
        mBgPath = new Path();
        //初始化绘制灰色方格的画笔
        mGrayBgPaint = new Paint();
        mGrayBgPaint.setStyle(Paint.Style.FILL);
        mGrayBgPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_bg_2));
        mGrayBgPaint.setAntiAlias(true);
        mGrayBgPath = new Path();
        //初始化绘制当前方格的画笔
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        mPaint.setAntiAlias(true);
        mPath = new Path();
        //初始化绘制文字的画笔
        mTextPaint = new Paint();
        mTextPaint.setAntiAlias(true);
        mTextPaint.setStrokeWidth(5);
        mTextPaint.setTextSize(32f);
        mTextPaint.setElegantTextHeight(true);
        mTextPaint.setStyle(Paint.Style.FILL);//实心画笔
        mTextPaint.setDither(true);
        mTrianglePath = new Path();

    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawBackground(canvas);//绘制背景进度条
        drawGrayBackground(canvas);//绘制灰色方格背景进度条
        drawCurrentScale(canvas);//绘制当前进度条
        drawText(canvas);
        drawDrawable(canvas);
    }

    private void drawDrawable(Canvas canvas) {
        if (mLeftDrawable != null) {
            int width = mLeftDrawable.getIntrinsicWidth();
            int height = mLeftDrawable.getIntrinsicHeight();
            mLeftDrawable.setBounds(leftPadding - width - 26, (mSeekBarHeight / 2 - height / 2) + mSeekBarTop,
                    leftPadding - 26, mSeekBarTop + mSeekBarHeight / 2 + height / 2);
            mLeftDrawable.draw(canvas);
        }
        if (mRightDrawable != null) {
            int width = mRightDrawable.getIntrinsicWidth();
            int height = mRightDrawable.getIntrinsicHeight();
            mRightDrawable.setBounds((int) (leftPadding + mProgressWidth) + 26,
                    (mSeekBarHeight / 2 - height / 2) + mSeekBarTop,
                    (int) (leftPadding + mProgressWidth + width) + 26, mSeekBarTop + mSeekBarHeight / 2 + height / 2);
            mRightDrawable.draw(canvas);
        }
    }

    private void drawBackground(Canvas canvas) {
        mBgPath.addRoundRect(new RectF(leftPadding, mSeekBarTop,
                        mProgressWidth + leftPadding, mSeekBarHeight + mSeekBarTop),
                new float[]{mRoundRadius, mRoundRadius, mRoundRadius, mRoundRadius, mRoundRadius,
                        mRoundRadius, mRoundRadius, mRoundRadius},
                Path.Direction.CW);
        canvas.drawPath(mBgPath, mBackgroundPaint);
    }

    private void drawGrayBackground(Canvas canvas) {
        float left = leftPadding;
        float width = (mProgressWidth - (mTotalBlockNum - 1) * mLineSpaceWidth) / mTotalBlockNum;
        RectF rectF = new RectF();

        for (int i = 1; i <= mTotalBlockNum; i++) {
            rectF.left = left;
            rectF.right = left + width;
            rectF.top = mSeekBarTop;
            rectF.bottom = mSeekBarTop + mSeekBarHeight;
            if (i == 1) {
                if (i == mBlockNum) {
                    mGrayBgPath.addRoundRect(rectF,
                            new float[]{mRoundRadius, mRoundRadius, mRoundRadius, mRoundRadius,
                                    mRoundRadius, mRoundRadius, mRoundRadius, mRoundRadius}, Path.Direction.CW);
                } else {
                    mGrayBgPath.addRoundRect(rectF,
                            new float[]{mRoundRadius, mRoundRadius, 0, 0, 0, 0,
                                    mRoundRadius, mRoundRadius}, Path.Direction.CW);
                }
            } else if (i == mTotalBlockNum) {
                mGrayBgPath.addRoundRect(rectF,
                        new float[]{0, 0, mRoundRadius, mRoundRadius,
                                mRoundRadius, mRoundRadius, 0, 0}, Path.Direction.CW);
            } else {
                if (i == mBlockNum) {
                    mGrayBgPath.addRoundRect(rectF,
                            new float[]{0, 0, mRoundRadius, mRoundRadius,
                                    mRoundRadius, mRoundRadius, 0, 0}, Path.Direction.CW);
                } else {
                    mGrayBgPath.addRoundRect(rectF,
                            new float[]{0, 0, 0, 0, 0, 0, 0, 0}, Path.Direction.CW);
                }
            }
            canvas.drawPath(mGrayBgPath, mGrayBgPaint);
            left = left + mLineSpaceWidth + width;
        }
        mGrayBgPath.reset();
    }

    private void drawCurrentScale(Canvas canvas) {
        float left = leftPadding;
        float width = (mProgressWidth - (mTotalBlockNum - 1) * mLineSpaceWidth) / mTotalBlockNum;
        RectF rectF = new RectF();

        for (int i = 1; i <= mBlockNum; i++) {
            rectF.left = left;
            rectF.right = left + width;
            rectF.top = mSeekBarTop;
            rectF.bottom = mSeekBarTop + mSeekBarHeight;
            if (i == 1) {
                mPath.addRoundRect(rectF,
                        new float[]{mRoundRadius, mRoundRadius, 0, 0, 0, 0,
                                mRoundRadius, mRoundRadius}, Path.Direction.CW);
            } else if (i == mBlockNum && i == mTotalBlockNum) {
                mPath.addRoundRect(rectF,
                        new float[]{0, 0, mRoundRadius, mRoundRadius,
                                mRoundRadius, mRoundRadius, 0, 0}, Path.Direction.CW);
            } else {
                mPath.addRoundRect(rectF,
                        new float[]{0, 0, 0, 0, 0, 0, 0, 0}, Path.Direction.CW);
            }
            canvas.drawPath(mPath, mPaint);
            left = left + mLineSpaceWidth + width;
        }
        mPath.reset();
    }

    private void drawText(Canvas canvas) {
        if (mBlockNum == 0 && !mUnit.equals("%"))//刻度等于0的时候不绘制文本
            return;
        mTextPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_color_text_1));
        String content = String.valueOf(mUnit.equals("%") ? (mBlockNum) * 10 : mBlockNum).concat(mUnit);
        float textLength = mTextPaint.measureText(content);
        float left = leftPadding;
        float unitWidth = (mProgressWidth - (mTotalBlockNum - 1) * mLineSpaceWidth) / mTotalBlockNum;
        float width = left + unitWidth * mBlockNum + mLineSpaceWidth * (mBlockNum - 1);
        float height = mUnit.equals("%") ? 20 : textLength / 2;
        float x = width - height;
        if (mUnit.equals("%")) {
            if (mBlockNum == mTotalBlockNum) {
                x = x - 10;
            }
        }
        canvas.drawText(content, x, height + 5, mTextPaint);
        mTextPaint.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        mTrianglePath.moveTo(width - 9, height + 15);
        mTrianglePath.lineTo(width + 9, height + 15);
        mTrianglePath.lineTo(width, height + 30);
        mTrianglePath.close();
        canvas.drawPath(mTrianglePath, mTextPaint);
        mTrianglePath.reset();
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                break;
            case MotionEvent.ACTION_MOVE:
                if (x < leftPadding + mUnitWidth * mMinBlockNum) {
                    calculateBlockNum(leftPadding + mUnitWidth * mMinBlockNum);
                } else if (x > mProgressWidth + leftPadding) {
                    mBlockNum = mTotalBlockNum;
                    invalidate();
                } else {
                    calculateBlockNum(x);
                }
                break;
            case MotionEvent.ACTION_DOWN:
                if (y >= mSeekBarTop) {
                    if (x < leftPadding && x > leftPadding - 20) {
                        mBlockNum = mMinBlockNum;
                        invalidate();
                    } else if (x > mProgressWidth + leftPadding && x < mProgressWidth + leftPadding + 30) {
                        mBlockNum = mTotalBlockNum;
                        invalidate();
                    } else if (y > mSeekBarTop + mSeekBarHeight) {
                        CommonLogUtils.logE(TAG, "can not click y<0 or y>seekbarHeight");
                    } else if (x > leftPadding && x < leftPadding + mUnitWidth * mMinBlockNum) {
                        calculateBlockNum(leftPadding + mUnitWidth * mMinBlockNum);
                    } else {
                        calculateBlockNum(x);
                    }
                }
                break;
        }

        return true;
    }

    private void calculateBlockNum(float x) {
        float width = x - leftPadding + mLineSpaceWidth;
        float unit = mUnitWidth + mLineSpaceWidth;
        float blockNum = width / unit;
        int result = (int) blockNum;
        if (blockNum < result + 0.01) {
            blockNum = 1f;
        }
        if (mUnit.equals("%") && width == mLineSpaceWidth) {
            blockNum = 0f;
        }
        mBlockNum = (int) Math.ceil(blockNum);
        if (mBlockNum > mTotalBlockNum)
            mBlockNum = mTotalBlockNum;
        invalidate();
    }


    public int getProgress() {
        return mBlockNum;
    }

    public void setProgress(int progress) {
        this.mBlockNum = progress;
        invalidate();
    }


    @Override
    protected void onSizeChanged(int w, int h, int oldWidth, int oldHeight) {
        super.onSizeChanged(w, h, oldWidth, oldHeight);
        int mWidth = getWidth();
        //        int mHeight = getHeight();
        mProgressWidth = mWidth * 0.85f;
        leftPadding = (int) ((mWidth - mProgressWidth) / 2);
        if (mTotalBlockNum != 0) {
            mUnitWidth = (mProgressWidth - (mTotalBlockNum - 1) * mLineSpaceWidth) / mTotalBlockNum;
        }
    }

    private int startMeasure(int msSpec) {
        int result;
        int mode = MeasureSpec.getMode(msSpec);
        int size = MeasureSpec.getSize(msSpec);
        if (mode == MeasureSpec.EXACTLY) {
            result = size;
        } else {
            result = AutoSizeUtils.dp2px(mContext, 200);
        }
        return result;
    }
}
