package com.dfl.smartscene.widget.bannerview.shape

import android.graphics.Canvas
import android.graphics.Paint
import androidx.annotation.ColorInt

/**
 * @className IShape
 * <AUTHOR>
 * @version 1.0.0
 * @description 图形接口
 * @createTime 2025/01/14/08:35
 */
interface IShape {

    fun draw(canvas: Canvas, paint: Paint)

    fun setWidth(width: Float)
    fun getWidth(): Float

    fun setHeight(height: Float)
    fun getHeight(): Float

    fun setCenterX(x: Float)
    fun getCenterX(): Float

    fun setCenterY(y: Float)
    fun getCenterY(): Float

    fun setOffsetX(offset: Float)
    fun getOffsetX(): Float

    fun setOffsetY(offset: Float)
    fun getOffsetY(): Float

    fun setColor(@ColorInt color: Int)
    fun getColor(): Int

    fun setAlpha(alpha: Float)

    fun getAlpha(): Float

    fun setAllowDraw(allowDraw: Boolean)

    fun isAllowDraw(): Boolean

     fun setPosition(position: Int)

     fun getPosition(): Int
}