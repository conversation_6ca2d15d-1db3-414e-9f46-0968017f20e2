package com.dfl.smartscene.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

/**
 *Created by 钟文祥 on 2024/7/8.
 *Describer: 内嵌的RecyclerView，事件不触发。
 * 触摸事件的响应机制是怎么样的：由上至下，最下层不消费后，则由下至上
 */
class NoTouchRecyclerView : RecyclerView {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    //返回true代表事件消费，返回false不消费，事件往上传递
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(e: MotionEvent?): Bo<PERSON>an {
        return false //不消费，事件往上传递
    }

    //事件拦截，返回true的话，则不向下传递，事件到onTouchEvent，返回false事件往下传递
    override fun onInterceptTouchEvent(e: MotionEvent?): Boolean {
        return true //不向下传递
    }

    //事件分发，一般不处理，返回false，事件到onInterceptTouchEvent中处理。
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(ev)
    }
}