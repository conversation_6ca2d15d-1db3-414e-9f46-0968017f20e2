package com.dfl.smartscene.widget.seekbar;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Shader;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.dfl.common.nightmode.widget.SkinCompatView;
import com.dfl.smartscene.R;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/09
 * desc :包含刻度的进度条
 * version: 1.0
 */
public class ScaleSeekBar extends SkinCompatView {

    private final int textSpan = 2;//数值文字与进度条的间隔
    private final int progressHeight = 48;//中间蓝色进度条高度
    private final int mScaleLineHeight = 15;//刻度线高度
    private final float numY = 160;//在进度条底部绘制，相当于进度条的高度
    private Context mContext;
    private Paint paintProgressBackground;
    private Paint paintProgress;
    private Paint paintNum;
    private Paint paintScaleStr;
    private float mProgressPercent = 0;
    private float progressWidth = 320;
    private float startNum;
    private float maxNum;//最大的数值
    private int ticksNum = 10;//默认刻度条每个文字间隔为10个刻度
    private int ticksTextNum = 10;//刻度条下面显示的对应刻度数字数量
    private float[] scaleStrArray = null;
    private Paint paintText;
    private int leftPadding = 25;//左边距
    private LinearGradient linearGradient;
    private int defaultTicks = 0;//进度条默认值
    private Path mTrianglePath;
    private int minNum = 0;
    private float minWidth = 0;
    private int mSeekBarProgress = 0;
    private int multiple = 1;//颗粒度即刻度值倍数，例如0-5000，颗粒度100，刻度值为100
    private String ticksUnit = "m";//文本单位

    public ScaleSeekBar(Context context) {
        super(context);
        init(context);
    }

    public ScaleSeekBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        if (attrs == null)
            return;
        init(context);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ScaleSeekBar);
        if (typedArray != null) {
            maxNum = typedArray.getInteger(R.styleable.ScaleSeekBar_maxNum, 100);
            int percent = typedArray.getInteger(R.styleable.ScaleSeekBar_percent, 100);
            ticksNum = typedArray.getInteger(R.styleable.ScaleSeekBar_ticksNum, 10);
            ticksTextNum = typedArray.getInteger(R.styleable.ScaleSeekBar_ticksTextNum, 10);

            this.mProgressPercent = percent / 100f;
            defaultTicks = typedArray.getInteger(R.styleable.ScaleSeekBar_startNum, 0);
            if (defaultTicks != 0)
                this.mProgressPercent = defaultTicks / 100f;

            minNum = typedArray.getInteger(R.styleable.ScaleSeekBar_minNum, 0);
            if (minNum != 0)
                this.minWidth = (minNum / 100f) * progressWidth;

            multiple = typedArray.getInteger(R.styleable.ScaleSeekBar_numMultiple, 1);
            ticksUnit = typedArray.getString(R.styleable.ScaleSeekBar_ticksUnit);

            setMaxNum(maxNum);
            typedArray.recycle();
        }
    }

    public void setSeekBarUnit(String unit) {
        this.ticksUnit = unit;
    }

    public void setMinNum(int minNum) {
        this.minNum = minNum;
        this.minWidth = (minNum / 100f) * progressWidth;
    }

    public void setSeekBarMultiple(int multiple) {
        this.multiple = multiple;
        setMaxNum(maxNum);
        invalidate();
    }

    public void setDefaultTicks(int ticks) {
        this.defaultTicks = ticks;
        if (defaultTicks != 0)
            this.mProgressPercent = defaultTicks / 100f;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int realWidth = startMeasure(widthMeasureSpec);
        int realHeight = startMeasure(heightMeasureSpec);
        setMeasuredDimension(realWidth, realHeight);
    }

    private void init(Context context) {
        mContext = context;
        initPaint();
    }

    private void initPaint() {
        //画进度条静态空心背景
        paintProgressBackground = new Paint();
        paintProgressBackground.setAntiAlias(true);
        paintProgressBackground.setStyle(Paint.Style.FILL);
        paintProgressBackground.setColor(ContextCompat.getColor(mContext, R.color.scene_color_text_3));
        paintProgressBackground.setDither(true);
        paintProgressBackground.setStrokeWidth(textSpan);
        //画进度的画笔，实心
        paintProgress = new Paint();
        paintProgress.setAntiAlias(true);
        paintProgress.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        paintProgress.setStyle(Paint.Style.FILL);
        paintProgress.setDither(true);
        //画刻度的画笔
        paintNum = new Paint();
        paintNum.setAntiAlias(true);
        paintNum.setColor(ContextCompat.getColor(mContext, R.color.scene_color_text_3));
        paintNum.setStrokeWidth(textSpan);
        paintNum.setStyle(Paint.Style.FILL);
        paintNum.setDither(true);
        //画刻度数值的画笔
        paintScaleStr = new Paint();
        paintScaleStr.setAntiAlias(true);
        paintScaleStr.setStyle(Paint.Style.FILL);
        paintScaleStr.setTextAlign(Paint.Align.LEFT);
        paintScaleStr.setColor(ContextCompat.getColor(mContext, R.color.scene_color_text_3));
        paintScaleStr.setTextSize(32);
        //画数值的画笔
        paintText = new Paint();
        paintText.setAntiAlias(true);
        paintText.setStrokeWidth(1);
        paintText.setStyle(Paint.Style.FILL);//实心画笔
        paintText.setDither(true);
        mTrianglePath = new Path();

    }

    private LinearGradient getLinearGradient() {
        if (linearGradient == null) {
            linearGradient = new LinearGradient(leftPadding, 0, progressWidth + leftPadding
                    , progressHeight, new int[]{ContextCompat.getColor(mContext, R.color.scene_primary_color)}, null,
                    Shader.TileMode.CLAMP); //根据R文件中的id获取到color
        }
        return linearGradient;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldWidth, int oldHeight) {
        super.onSizeChanged(w, h, oldWidth, oldHeight);
        int mWidth = getWidth();
        //        int mHeight = getHeight();
        progressWidth = mWidth * 0.8f;
        leftPadding = (int) (mWidth * 0.1);
    }

    private int startMeasure(int msSpec) {
        int result;
        int mode = MeasureSpec.getMode(msSpec);
        int size = MeasureSpec.getSize(msSpec);
        if (mode == MeasureSpec.EXACTLY) {
            result = size;
        } else {
            result = AutoSizeUtils.dp2px(mContext, 200);
        }
        return result;
    }

    private float getTextViewLength(Paint paint, String text) {
        if (TextUtils.isEmpty(text)) return 0;
        float textLength;
        textLength = paint.measureText(text);
        return textLength;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //进度条的底框
        canvas.drawRect(leftPadding, numY + mScaleLineHeight - 2,
                progressWidth + leftPadding, numY + mScaleLineHeight, paintProgressBackground);
        //进度条的当前进度
        if (mProgressPercent <= minNum / 100f) {
            canvas.drawRect(leftPadding, numY - progressHeight - mScaleLineHeight - 5,
                    progressWidth * mProgressPercent + leftPadding + textSpan, numY - mScaleLineHeight - 5,
                    paintProgress);
        } else {
            canvas.drawRect(leftPadding, numY - progressHeight - mScaleLineHeight - 5,
                    progressWidth * mProgressPercent + leftPadding, numY - mScaleLineHeight - 5, paintProgress);
        }
        drawScale(canvas);
        drawText(canvas, mProgressPercent);
    }

    /**
     * 绘制刻度和刻度下的数字
     */
    private void drawScale(Canvas canvas) {
        float span = progressWidth / maxNum;
        for (int i = 0; i <= maxNum; i++) {
            canvas.save(); //记录画布状态
            canvas.translate(span * i + leftPadding, 0);
            float startY = numY + mScaleLineHeight - 10;
            paintNum.setStrokeWidth(1);
            if (i % ticksNum == 0) {
                int a = i / ticksNum;
                startY = numY + mScaleLineHeight - 20;
                String text = String.valueOf((int) scaleStrArray[a]);
                //                if (a == scaleStrArray.length - 1) {
                //                    text = text + ticksUnit;
                //                }
                paintNum.setStrokeWidth(textSpan);
                Paint.FontMetricsInt fontMetrics = paintScaleStr.getFontMetricsInt();
                float baseline = ((numY + 30) + ((fontMetrics.bottom - fontMetrics.top) >> 1));
                canvas.drawText(text, -getTextViewLength(paintScaleStr, text) / 2, baseline, paintScaleStr);
            }
            canvas.drawLine(0, startY, 0, numY + mScaleLineHeight - 2, paintNum);
            canvas.restore();
        }
    }

    /**
     * 绘制显示的数值
     */
    private void drawText(Canvas canvas, float percent) {
        //显示单位
        paintText.setColor(ContextCompat.getColor(mContext, R.color.scene_color_text_1));
        String unit = ticksUnit;
        if (TextUtils.isEmpty(unit)) return;
        float length;
        paintText.setTextSize(32);
        if (percent < (minNum / 100f)) {
            percent = minNum / 100f;
        }
        int width = Math.round((maxNum) * percent);
        mSeekBarProgress = width * multiple;
        String numerical = ((width * multiple)) + unit;
        length = paintText.measureText(numerical);
        float height = length / 2;
        canvas.drawText(numerical, progressWidth * percent + leftPadding - height, 50, paintText);

        mTrianglePath.reset();
        paintText.setColor(ContextCompat.getColor(mContext, R.color.scene_primary_color));
        float x = progressWidth * percent + leftPadding;
        if (percent <= (minNum / 100f)) {
            if (percent != 0) {
                x = x + textSpan;
            }
        }
        mTrianglePath.moveTo(x - 10, 50 + 15);
        mTrianglePath.lineTo(x + 10, 50 + 15);
        mTrianglePath.lineTo(x, 50 + 30);
        mTrianglePath.close();
        canvas.drawPath(mTrianglePath, paintText);
    }

    /**
     * 设置百分比
     */
    public void setPercent(int percent) {
        this.mProgressPercent = percent / 100f;
        invalidate();
    }

    /**
     * 设置起始值
     */
    public void setStartNum(float startNum) {
        this.startNum = startNum;
    }

    /**
     * 设置最大值
     */
    public void setMaxNum(float maxNum) {
        this.maxNum = maxNum;
        float[] scaleIntArray = new float[ticksTextNum + 1];
        //默认10个大刻度
        scaleIntArray[0] = startNum * multiple;
        for (int i = 1; i < ticksTextNum; i++) {
            scaleIntArray[i] = scaleIntArray[i - 1] + ((maxNum - startNum) / ticksTextNum) * multiple;
        }
        scaleIntArray[ticksTextNum] = maxNum * multiple;
        setScaleArray(scaleIntArray);
    }

    public void setScaleArray(float[] array) {
        this.scaleStrArray = array;
        if (scaleStrArray == null) {
            scaleStrArray = new float[0];
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                break;
            case MotionEvent.ACTION_MOVE:
                if (x <= leftPadding + minWidth) {
                    calculateProgress(x, -1);
                } else if (x >= progressWidth + leftPadding) {
                    calculateProgress(x, 1);
                } else {
                    calculateProgress(x, 0);
                }
                break;
            case MotionEvent.ACTION_DOWN:
                if (x <= leftPadding + minWidth && x >= leftPadding) {
                    calculateProgress(x, -1);
                } else if (x > progressWidth + leftPadding || x < leftPadding) {
                    return false;
                } else if (y < 0 || y > numY + progressHeight) {
                    return false;
                } else {
                    calculateProgress(x, 0);
                }
                break;
        }

        return true;
    }

    //type -1:最小值，0正常计算，1最大值
    private void calculateProgress(float x, int type) {
        if (type == -1) {
            this.mProgressPercent = minNum / 100f;
        } else if (type == 1) {
            this.mProgressPercent = 1;
        } else {
            this.mProgressPercent = (x - leftPadding) / progressWidth;
        }
        invalidate();
    }

    public int getProgress() {
        return mSeekBarProgress;
    }
}
