package com.dfl.smartscene.widget.recycleview

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/06/29
 * desc: recycleView拖拽效果回调
 * version:1.0
 */
class MyItemTouchHelperCallback : ItemTouchHelper.Callback() {
    override fun getMovementFlags(
        recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder
    ): Int {
        return if (recyclerView.layoutManager is GridLayoutManager || recyclerView.layoutManager is StaggeredGridLayoutManager) {
            val dragFlags: Int =
                ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
            val swipeFlags = 0
            makeMovementFlags(dragFlags, swipeFlags)
        } else {
            val dragFlags: Int = ItemTouchHelper.UP or ItemTouchHelper.DOWN
            val swipeFlags = 0
            makeMovementFlags(dragFlags, swipeFlags)
        }
    }

    override fun onMove(
        recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder
    ): Boolean {
        //拖拽中的ViewHolder的position
        val fromPosition: Int = viewHolder.absoluteAdapterPosition
        //当前拖拽到的item的viewHolder
        val toPosition: Int = target.absoluteAdapterPosition
        if (recyclerView.adapter is ItemMoveCallback) {
            (recyclerView.adapter as ItemMoveCallback).onItemMove(fromPosition, toPosition)
            if (callback != null) {
                callback?.onItemHolderMoving(fromPosition, toPosition)
            }
        }
        return true
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE && viewHolder is ItemHolderMoveCallback) {
            val position = viewHolder.absoluteAdapterPosition
            (viewHolder as ItemHolderMoveCallback).onItemHolderMoveStart(position)
            if (callback != null) {
                callback?.onItemHolderMoveStart(position)
            }
        }
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        if (viewHolder is ItemHolderMoveCallback) {
            val position = viewHolder.absoluteAdapterPosition
            viewHolder.onItemHolderMoveEnd(position)
            if (callback != null) {
                callback?.onItemHolderMoveEnd(position)
            }
        }
    }

    override fun isLongPressDragEnabled(): Boolean {
        return true
    }

    private var callback: ItemHolderMoveCallback? = null
    fun setItemMoveCallBack(callback: ItemHolderMoveCallback) {
        this.callback = callback
    }

    fun destroyItemMoveCallBack() {
        if (callback != null) callback = null
    }
}