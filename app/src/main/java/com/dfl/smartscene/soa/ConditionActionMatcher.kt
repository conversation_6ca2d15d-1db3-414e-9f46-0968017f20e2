package com.dfl.smartscene.soa

import com.dfl.smartscene.bean.main.ScenarioBean
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * Description:
 * @data 2023/10/12 14:16
 */
fun getActionValueBySkillIdAndName(
    list: MutableList<ScenarioInfo.Sequence>?, skillId: Int, name: String
): String? {
    if (list.isNullOrEmpty()) return null
    val sequence = list.find { it.action.skillId == skillId }
    val inputs = sequence?.action?.input
    val input = inputs?.find { it.name == name }
    return input?.value
}

//根据键值对 获取值
fun getSequenceValueByName(sequence: ScenarioInfo.Sequence?, name: String): String? {
    if (sequence == null) return null
    val inputs = sequence?.action?.input
    val input = inputs?.find { it.name == name }
    return input?.value
}

fun getInputByName(sequence: ScenarioInfo.Sequence?, name: String): InputArgInfo? {
    if (sequence == null) return null
    val inputs = sequence?.action?.input
    val input = inputs?.find { it.name == name }
    return input
}

fun getActionSequenceBySkillId(
    list: MutableList<ScenarioInfo.Sequence>?, skillId: Int?
): ScenarioInfo.Sequence? {
    if (list.isNullOrEmpty()) return null
    if (skillId == null) return null
    return list.find { it.action.skillId == skillId }
}

fun getConditionBySkillId(scenario: ScenarioBean?, skillId: Int): ScenarioInfo.Condition? {
    if (scenario == null) return null
    return scenario.scenarioInfo?.conditions?.find { it.skillId == skillId }
}

fun getSceneEdgeCondition(scenario: ScenarioBean?): ScenarioInfo.Condition? {
    if (scenario == null) return null
    return scenario.scenarioInfo?.edgeCondition
}