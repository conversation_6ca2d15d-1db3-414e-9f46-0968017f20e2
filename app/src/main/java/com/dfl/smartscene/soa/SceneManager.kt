package com.dfl.smartscene.soa

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.CNToNumberUtil
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.NumberToCNUtil
import com.dfl.android.common.util.TimeUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.*
import com.dfl.smartscene.communication.OMSTriggerManager
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.MMKVUtils
import com.dfl.smartscene.util.TrackUtils
import com.dfl.soacenter.SoaCenterService
import com.dfl.soacenter.communication.NaviManager
import com.dfl.soacenter.entity.MyScenePosition
import com.iauto.scenarioadapter.ScenarioAdapter
import com.iauto.scenarioadapter.ScenarioInfo
import com.iauto.scenarioadapter.SkillInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArraySet
import java.util.concurrent.TimeUnit

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/12/21
 * desc:场景调用CCM逻辑处理，统一在此类进行处理
 * version:1.0
 */
object SceneManager {
    private const val TAG = GlobalConstant.GLOBAL_TAG + "SceneManager"

    /**
     * CCM的IP地址
     */
    private const val SERVICE_IP = "************"

    /**
     * CCM的端口号
     */
    private const val SERVICE_PORT = 4099

    /**
     * 场景处理结果-成功
     */
    private const val RESULT_SUCCESS = ScenarioAdapter.REQUEST_SUCCESS

    /**
     * 场景处理结果-失败，无此场景
     */
    private const val RESULT_NO_SCENE = 1

    /**
     * 场景处理结果-失败，参数错误
     */
    private const val RESULT_PARAM_ERROR = 2

    /**
     * 场景处理结果-失败，其他原因
     */
    private const val RESULT_OTHER_REASON = 3

    /**
     * 手动执行
     */
    private const val EXECUTE_TYPE_MANUAL = 0

    /**
     * 自动执行
     */
    private const val EXECUTE_TYPE_AUTO = 1

    /**
     * 试用执行
     */
    private const val EXECUTE_TYPE_TRY = 2

    /**
     * 未执行
     */
    private const val EXECUTE_TYPE_NOT = 3

    /**
     * 是否连接
     */
    @Volatile
    private var mIsConnect = false

    /**
     * 连接的次数
     */
    @Volatile
    private var mConnectCount = 0

    /**
     * CCM场景引擎调用类
     */
    private val mScenarioAdapter = ScenarioAdapter()

    /**
     * CCM场景引擎回调监听类
     */
    private val mScenarioDispatcher = ScenarioDispatcher()

    /**
     * 当前保存的我的场景列表数据
     */
    private val mSceneInfoList = ArrayList<MySceneBean>()

    /**
     * 当前保存的发现场景列表数据
     */
    private val mDiscoverInfoList = ArrayList<SceneCardBean>()


    /**
     * 场景列表数据获取的锁
     */
    private val mSceneInfoListLock = Mutex()

    /**
     * 发现场景列表数据获取的锁
     */
    private val mDiscoverInfoListLock = Mutex()

    /**
     * 能力列表数据获取的锁
     */
    private val mSkillInfoListLock = Mutex()

    /**
     * 当前正在运行的场景ID列表
     */
    private var mRunningSceneSet = CopyOnWriteArraySet<String>()

    /**
     * 当前正在添加的场景信息
     */
    private val mAddSceneMap = ConcurrentHashMap<String, MySceneBean>()

    /**
     * 当前正在修改的场景信息
     */
    private val mModifySceneMap = ConcurrentHashMap<String, MySceneBean>()

    /**
     * 当前正在删除的场景信息
     */
    private val mDeleteSceneMap = ConcurrentHashMap<String, MySceneBean>()

    /**
     * 试运行的场景ID集合，用以判断当前的执行结果是否为试运行的场景
     */
    private var mTrySceneIdSet = CopyOnWriteArraySet<String>()

    /**
     * 自动执行场景的结果信息
     */
    private val mAutoSceneMap = ConcurrentHashMap<String, AutoSceneResult>()

    /**
     * 展厅模式试用场景ID的集合
     */
    private val mTrySceneIdList = arrayListOf("VEHICLE_1681179185683", "VEHICLE_1681179681722")

    /**
     * 当前正在等待执行的场景队列，自动执行时需要保存这个队列
     */
    private val mAutoRunMap = ConcurrentHashMap<String, Long>()

    /**间隔执行 处理*/
    private var intervalDisposable: Disposable? = null

    /**
     * 初始化场景引擎
     */
    fun init() {
        CommonLogUtils.logI(TAG, "start init scene adapter")
        mSceneInfoList.clear()
        CoroutineScope(Dispatchers.IO).launch {
            mScenarioAdapter.LogSwitch(ScenarioAdapter.SKILL_LIST, false)
            mScenarioAdapter.LogSwitch(ScenarioAdapter.DATA_CONVERT, false)
            mScenarioAdapter.registListener(mScenarioDispatcher)
            mScenarioAdapter.init(
                SERVICE_IP, SERVICE_PORT
            )
            SceneSortManager.getSceneSequence()
            mScenarioAdapter.connect()

        }
        if (BuildConfig.NO_CCM) {
            SceneSortManager.getSceneSequence()
            requestSceneInfoList()
        }
    }

    /**
     * 注销场景引擎
     */
    fun deInit() {
        CommonLogUtils.logI(TAG, "start deInit")
        CoroutineScope(Dispatchers.IO).launch {
            mScenarioAdapter.unRegistListener(mScenarioDispatcher)
            try {
                mScenarioAdapter.deinit()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * CCM是否已连接
     * @return 已连接返回true;未连接返回false
     */
    @JvmStatic
    fun isCCMConnect(): Boolean {
        if (BuildConfig.NO_CCM) {
            return true
        }
        return mIsConnect
    }

    /**
     * 处理连接状态
     */
    fun handleConnectStatus(isConnect: Boolean) {
        mIsConnect = isConnect
        LiveEventBus.get(GlobalLiveEventConstants.KEY_CCM_CONNECT_STATE, Boolean::class.java).post(isConnect)
        if (isConnect) {
            handleCCMConnect()
        } else {
            handleCCMDisconnect()
        }
    }

    /**ccm连接成功*/
    private fun handleCCMConnect() {
        mConnectCount = 0
        initData()
        //        traverseSceneGetPosition2CCM()
        traverseSceneGetPosition2CCM4Trigger()
    }

    /**处理ccm没有连接*/
    private fun handleCCMDisconnect() {
        // 重新连接
        CoroutineScope(Dispatchers.IO).launch {
            CommonLogUtils.logE(TAG, "reconnect：$mConnectCount")

            if (mConnectCount == 0) {
                LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java).post(2)
            }
            // 10次之后，每隔1分钟重连;5到10次，每隔15秒重连一次;小于5次，每隔5秒连接一次
            mConnectCount++
            if (mConnectCount > 10) {
                delay(60 * 1000)
                mScenarioAdapter.connect()
            } else if (mConnectCount > 5) {
                delay(15 * 1000)
                mScenarioAdapter.connect()
            } else {
                delay(5 * 1000)
                mScenarioAdapter.connect()
            }
        }
        cancelIntervalDisposable()
        OMSTriggerManager.cancelAllDispatch()
    }

    /**
     * 清空所有缓存的数据
     */
    fun clearCacheData() {
        mRunningSceneSet.clear()
        WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
        mAddSceneMap.clear()
        mDeleteSceneMap.clear()
        mModifySceneMap.clear()
        mTrySceneIdSet.clear()
        mAutoRunMap.clear()
    }

    /**
     * 初始化数据,连接成功后需要去请求一次数据
     */
    private fun initData() {
        requestSceneInfoList()
        // 延迟加载，在打开界面时才去加载数据
        //		requestDiscoverInfoList()
        //        requestSkillInfoList()
        clearCacheData()
    }

    @JvmStatic
    fun getSceneInfoList(): ArrayList<MySceneBean> {
        return mSceneInfoList
    }

    fun getDiscoverInfoList(): ArrayList<SceneCardBean> {
        return mDiscoverInfoList
    }


    fun hasSceneRunning(): Boolean {
        return mRunningSceneSet.size > 0
    }

    /**
     * 请求获取场景列表信息的数据
     */
    fun requestSceneInfoList() {
        if (!isCCMConnect()) {
            CommonLogUtils.logE(TAG, "CCM未连接，无法请求场景列表信息")
            LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java).post(2)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            mSceneInfoListLock.lock()
            try {
                val sceneInfoList = mScenarioAdapter.userScenarioList
                val mySceneBeanList = ArrayList<MySceneBean>()
                sceneInfoList.forEachIndexed { index, sceneInfo ->
                    mySceneBeanList.add(
                        MySceneBean(
                            ScenarioBean(
                                sceneInfo,
                                sceneInfo.secondAskeFlag == 0,
                                false,
                                sceneInfo.autoExeFlag == 0,
                                executeFrequency = 0
                            ), isSceneStart = false, isTop = index <= 2
                        )
                    )
                }
                mSceneInfoList.clear()
                mSceneInfoList.addAll(SceneSortManager.sceneSequenceInterceptor(mySceneBeanList))
                if (BuildConfig.DEBUG) {
                    CommonLogUtils.logD(TAG, "SceneInfoList:$sceneInfoList")
                }
                CommonLogUtils.logI(TAG, "SceneInfoListSize=" + mSceneInfoList.size)
                // 发送消息告知用户可去获取场景列表数据
                LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java)
                    .post(if (mSceneInfoList.size > 0) 0 else 1) //0是有数据，1是没有数据
                WidgetUpdateHelper.updateDeskCardSceneList(mSceneInfoList)
                LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(0)
                if (mSceneInfoList.size > 0) {
                    OMSTriggerManager.startDispatch()
                }
            } catch (e: java.lang.Exception) {
                if (!initTestData4Scene()) {
                    // 发送消息告知用户获取场景列表数据超时
                    LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java).post(2)
                }
                e.printStackTrace()
            } finally {
                mSceneInfoListLock.unlock()
            }
        }
    }

    /**
     * 初始化测试的数据，不能用在生产环境,场景列表数据
     */
    private fun initTestData4Scene(): Boolean {
        if (BuildConfig.NO_CCM) {
            val mySceneBeanList = DataManager.initTestData4Scene()
            // 这里的筛选有问题，会出现筛选后数据大于原本数据的情况
            val finalMySceneBeanList = SceneSortManager.sceneSequenceInterceptor(mySceneBeanList)
            mSceneInfoList.clear()
            mSceneInfoList.addAll(finalMySceneBeanList)
            LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java)
                .post(if (mSceneInfoList.size > 0) 0 else 1) //0是有数据，1是没有数据
            return true
        }
        return false
    }

    /**
     * 请求获取发现场景信息的数据
     */
    fun requestDiscoverInfoList() {
        if (!isCCMConnect()) {
            CommonLogUtils.logE(TAG, "CCM未连接，无法请求发现场景信息")
            LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_DISCOVER_LIST, Int::class.java).post(1)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            mDiscoverInfoListLock.lock()
            try {
                val sceneInfoList = mScenarioAdapter.oemScenarioList
                mDiscoverInfoList.clear()
                for (sceneInfo in sceneInfoList) {
                    mDiscoverInfoList.add(
                        SceneCardBean(
                            ScenarioBean(
                                sceneInfo,
                                isAskBeforeAutoRun = true,
                                isTemplateScene = true,
                                isAutoRun = false,
                                executeFrequency = 0
                            )
                        )
                    )
                }
                CommonLogUtils.logI(TAG, "DiscoverSize=${mDiscoverInfoList.size},")
                // 发送消息告知用户可去获取场景列表数据
                LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_DISCOVER_LIST, Int::class.java).post(0)
            } catch (e: java.lang.Exception) {
                CommonLogUtils.logE(TAG, "获取ccm发现场景数据失败：" + e.message)
                // 发送消息告知用户可去获取场景列表数据
                LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_DISCOVER_LIST, Int::class.java).post(1)
                initDiscoverTestData()
                e.printStackTrace()
            } finally {
                mDiscoverInfoListLock.unlock()
            }
        }
    }

    private fun initDiscoverTestData() {
        if (BuildConfig.NO_CCM) {
            mDiscoverInfoList.clear()
            mDiscoverInfoList.addAll(DataManager.initTestData4Discover())
            LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_DISCOVER_LIST, Int::class.java).post(0)
        }
    }

    /**
     * 请求获取能力列表的数据
     */
    fun requestSkillInfoList() {
        CoroutineScope(Dispatchers.IO).launch {
            mSkillInfoListLock.lock()
            val mSkillInfoList = ArrayList<SkillInfo>()
            try {
                // ccm返回本车所有能力 包括启用与禁用
                val sceneInfoList = mScenarioAdapter.skillList
                mSkillInfoList.addAll(sceneInfoList)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            } finally {
                // 初始化自定义的条件和动作ID是否被禁用
                SceneEditManager.loadAllSkillIdData(mSkillInfoList)
                mSkillInfoListLock.unlock()
            }
        }
    }

    /**
     * 新增场景
     */
    fun addSceneInfo(scenarioInfo: MySceneBean): Boolean {
        CommonLogUtils.logI(TAG, "addScene:$scenarioInfo")
        val finalAddSceneInfo = scenarioInfo.scenario.scenarioInfo
        finalAddSceneInfo ?: return false
        val sceneId: String? = scenarioInfo.scenario.scenarioInfo?.scenarioId
        sceneId ?: return false
        val addSceneInfo = mAddSceneMap[sceneId]
        if (addSceneInfo != null) {
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_add)
            return false
        }
        CoroutineScope(Dispatchers.IO).launch {
            finalAddSceneInfo.scenarioTimeStamp = System.currentTimeMillis()
            // 在调用接口之前就保存这个场景ID至map中，避免出现结果先比回调先回来的情况出现
            mAddSceneMap[sceneId] = scenarioInfo
            val result = mScenarioAdapter.addScenario(finalAddSceneInfo)
            if (result == RESULT_SUCCESS) {
                CommonLogUtils.logI(TAG, "AddScenario Result Success!")
            } else {
                if (BuildConfig.NO_CCM) {
                    mAddSceneMap[sceneId] = scenarioInfo
                    delay(500)
                    mScenarioDispatcher.onScenarioAdd(
                        finalAddSceneInfo.scenarioId, 0
                    )
                } else {
                    // 调用失败则直接移除该场景ID
                    mAddSceneMap.remove(sceneId)
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
        return true
    }

    /**
     * 修改场景
     */
    fun modifySceneInfo(scenarioInfo: MySceneBean): Boolean {
        CommonLogUtils.logI(TAG, "modifyScene:$scenarioInfo")
        val finalSceneInfo = scenarioInfo.scenario.scenarioInfo
        finalSceneInfo ?: return false
        val modifySceneInfo = mModifySceneMap[finalSceneInfo.scenarioId]
        if (modifySceneInfo != null) {
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_modify)
            return false
        }
        CoroutineScope(Dispatchers.IO).launch {
            finalSceneInfo.version = finalSceneInfo.version + 1
            finalSceneInfo.scenarioTimeStamp = System.currentTimeMillis()
            mModifySceneMap[finalSceneInfo.scenarioId] = scenarioInfo
            val result = mScenarioAdapter.modifyScenario(finalSceneInfo)
            if (result == RESULT_SUCCESS) {
                CommonLogUtils.logI(TAG, "ModifyScenario Result Success!")
            } else {
                if (BuildConfig.NO_CCM) {
                    mModifySceneMap[finalSceneInfo.scenarioId] = scenarioInfo
                    delay(500)
                    mScenarioDispatcher.onScenarioModify(
                        finalSceneInfo.scenarioId, 0
                    )
                } else {
                    mModifySceneMap.remove(finalSceneInfo.scenarioId)
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
        return true
    }

    /**
     * 删除场景
     */
    fun deleteSceneInfo(scenarioInfo: MySceneBean): Boolean {
        CommonLogUtils.logI(TAG, "deleteScene:$scenarioInfo")
        val finalSceneInfo = scenarioInfo.scenario.scenarioInfo
        finalSceneInfo ?: return false
        val modifySceneInfo = mDeleteSceneMap[finalSceneInfo.scenarioId]
        if (modifySceneInfo != null) {
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_delete)
            return false
        }
        CoroutineScope(Dispatchers.IO).launch {

            mDeleteSceneMap[finalSceneInfo.scenarioId] = scenarioInfo
            val result = mScenarioAdapter.deleteScenario(
                finalSceneInfo.scenarioId, System.currentTimeMillis()
            )
            if (result == RESULT_SUCCESS) {
                CommonLogUtils.logI(TAG, "DeleteScenario Result Success")
            } else {
                if (BuildConfig.NO_CCM) {
                    mDeleteSceneMap[finalSceneInfo.scenarioId] = scenarioInfo
                    delay(500)
                    mScenarioDispatcher.onScenarioDelete(
                        finalSceneInfo.scenarioId, 0
                    )
                } else {
                    mDeleteSceneMap.remove(finalSceneInfo.scenarioId)
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
        return true
    }

    fun handleDeleteScene(sceneId: String, ret: Int) {
        // 发送信息告知界面更新数据，当有数据成功删除后
        if (ret == RESULT_SUCCESS) {
            val scenarioInfoIndex = findSceneIndexById(sceneId)
            if (scenarioInfoIndex != -1) {
                mSceneInfoList.removeAt(scenarioInfoIndex)
            }
            // 发送消息给界面刷新
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_DELETE_SUCCESS, String::class.java).post(sceneId)
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(3)
        } else {
            // 删除失败，给个提示
            CommonToastUtils.show(R.string.scene_toast_scene_delete_fail)
        }
        mDeleteSceneMap.remove(sceneId)
    }

    fun handleModifyScene(sceneId: String, ret: Int) {
        // 发送信息告知界面更新数据，当有数据成功新增后
        if (ret == RESULT_SUCCESS) {
            val scenarioInfo = mModifySceneMap[sceneId]
            val index = findSceneIndexById(sceneId)
            if (scenarioInfo != null) {
                // 更新相应下标对应的场景信息
                if (index != -1) {
                    mSceneInfoList[index] = scenarioInfo
                }
                // 发送消息给界面刷新
                LiveEventBus.get(
                    GlobalLiveEventConstants.KEY_SCENE_MODIFY_SUCCESS, MySceneBean::class.java
                ).post(scenarioInfo)
                LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(2)
                OMSTriggerManager.startDispatch()
            }
        } else {
            // 修改失败，给个提示
            CommonToastUtils.show(R.string.scene_toast_scene_modify_fail)
        }
        // 将map中保存的场景清除,避免异常时无法清除掉缓存的场景
        mModifySceneMap.remove(sceneId)

    }

    fun handleAddScene(sceneId: String, ret: Int) {
        // 发送信息告知界面更新数据，当有数据成功新增后
        if (ret == RESULT_SUCCESS) {
            val sceneInfo = mAddSceneMap[sceneId]
            if (sceneInfo != null) {
                mSceneInfoList.add(sceneInfo)
            }
            // 发送消息给界面刷新
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).post(sceneInfo)
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(1)
            OMSTriggerManager.startDispatch()
        } else {
            // 新增失败，给个提示
            CommonToastUtils.show(R.string.scene_toast_scene_add_fail)
        }
        mAddSceneMap.remove(sceneId)
    }

    fun handleStartScene(sceneId: String, exeType: Int) {
        // 执行类型（0: 手动执行 , 1: 自动执行,2:试用执行，3：未执行）
        if (exeType == EXECUTE_TYPE_TRY) {
            mTrySceneIdSet.add(sceneId)
            if (mTrySceneIdList.contains(sceneId)) {
                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_PLAY_MODE_TRY_RUN_SCENE_LISTENER).post(2)
            }
        } else if (exeType == EXECUTE_TYPE_MANUAL || exeType == EXECUTE_TYPE_AUTO) {
            mRunningSceneSet.add(sceneId)
            WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
            val myScene = findSceneById(sceneId)
            //ux1.8删除toast
            //if (myScene != null) {
            //val sceneName = myScene.scenario.scenarioInfo?.scenarioName
            //CommonToastUtils.show(
            //    CommonUtils.getApp().getString(R.string.scene_toast_widget_start_scene) + sceneName
            //)
            //}
            // 发送消息让界面更新，场景已经开始执行
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_START, String::class.java).post(sceneId)
        }
    }

    /**
     * 处理场景执行结果，正常执行结束和中途中止执行都会回调此接口
     *
     * @param scenarioId 场景id
     * @param ret     执行结果（0: 成功， 1: 中止）
     * @param exeType 场景执行类型，0：手动执行，1：自动执行，2：试用执行，3：未执行
     */
    fun handleExecuteResult(sceneId: String, ret: Int, exeType: Int) {
        // 执行类型（0: 手动执行 , 1: 自动执行,2:试用执行，3：未执行）
        if (exeType == EXECUTE_TYPE_TRY) {
            LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, String::class.java).post(sceneId)
            mTrySceneIdSet.remove(sceneId)
            if (mTrySceneIdList.contains(sceneId)) {
                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_PLAY_MODE_TRY_RUN_SCENE_LISTENER).post(3)
            }
            return
        } else {
            if (mRunningSceneSet.contains(sceneId)) {
                // 将该场景从运行中的列表中移除
                mRunningSceneSet.remove(sceneId)
                WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
                LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_END, String::class.java).post(sceneId)
                if (exeType == 0) TrackUtils.resultMyManualScene(sceneId, ret == 0)
                if (exeType == 1) TrackUtils.resultMyAutomaticScene(sceneId, ret == 0)
                // 0成功，1中止
                if (ret == 0) {
                    val myScene = findSceneById(sceneId)
                    if (myScene != null) {
                        val sceneName = myScene.scenario.scenarioInfo?.scenarioName
                        CommonToastUtils.show(
                            sceneName.plus(
                                CommonUtils.getApp().getString(R.string.scene_toast_widget_stop_scene)
                            )
                        )
                    }
                    SceneRecordManager.insertSceneRecord(sceneId, true)
                } else {
                    // 不管执行类型是否为未执行，都需要记录日志为执行失败
                    SceneRecordManager.insertSceneRecord(sceneId, false)
                    //场景执行失败toast,试用不弹出
                    CommonToastUtils.show(CommonUtils.getString(R.string.scene_text_scene_execution_fail))
                    SoundManager.playSoundEffect(SoundManager.SoundType.FAILED)
                    if (exeType == EXECUTE_TYPE_NOT) {
                        CommonLogUtils.logI(TAG, "$sceneId 场景未执行")
                    }
                }
            } else {
                if (exeType == EXECUTE_TYPE_NOT) {
                    if (mTrySceneIdSet.contains(sceneId)) {
                        LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, String::class.java).post(sceneId)
                        mTrySceneIdSet.remove(sceneId)
                    }
                }
            }
        }
    }

    /**试用场景，输入参数不为空*/
    fun trySceneInfo(scenarioInfo: ScenarioInfo?): Boolean {
        CommonLogUtils.logI(TAG, "trySceneInfo ${scenarioInfo?.scenarioId}")
        if (mTrySceneIdSet.contains(scenarioInfo?.scenarioId)) {
            CommonLogUtils.logE(TAG, "重复试用场景")
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_try)
            return false
        }
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = mScenarioAdapter.trySceario(scenarioInfo)
                if (result != RESULT_SUCCESS) {
                    CommonLogUtils.logE(TAG, "tryScenario request send fail:$result")
                    if (BuildConfig.NO_CCM) {
                        scenarioInfo?.let {
                            mTrySceneIdSet.add(it.scenarioId)
                        }
                        delay(3000)
                        mScenarioDispatcher.notifyScenarioExecuteResult(
                            scenarioInfo?.scenarioId, 0, 2, System.currentTimeMillis()
                        )
                    } else {
                        CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                        LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, String::class.java)
                            .post(scenarioInfo?.scenarioId)
                    }
                } else {
                    CommonLogUtils.logI(TAG, "tryScenario request send success")
                    scenarioInfo?.let {
                        mTrySceneIdSet.add(it.scenarioId)
                    }
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }


        }
        return true
    }

    /**
     * 试运行场景-展厅模式专用
     */
    fun trySceneInfo4ShowRoom(scenarioInfo: ScenarioInfo?): Boolean {
        if (!isCCMConnect()) {
            return false
        }
        if (mTrySceneIdSet.contains(scenarioInfo?.scenarioId)) {
            CommonLogUtils.logE(TAG, "重复试用场景")
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_try)
            return false
        }
        return try {
            val result = mScenarioAdapter.trySceario(scenarioInfo)
            result == RESULT_SUCCESS
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            false
        }
    }

    /**停止试用场景，离开编辑页面停止执行*/
    fun stopTryScene(sceneId: String): Boolean {
        CommonLogUtils.logI(TAG, "stopTryScene $sceneId")
        if (!mTrySceneIdSet.contains(sceneId)) {
            CommonLogUtils.logE(TAG, "场景未在试用")
            CommonToastUtils.show(R.string.scene_toast_not_try_now)
            val index = findSceneIndexById(sceneId)
            // 需要主动发送一个失败的信息，避免无法停止
            LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, Int::class.java).post(index)
            return false
        }
        // 0成功，2没有这个场景，4场景没有执行,1其他原因如超时
        CoroutineScope(Dispatchers.IO).launch {
            val result = mScenarioAdapter.abortScenario(sceneId)
            if (result != RESULT_SUCCESS) {
                CommonLogUtils.logE(TAG, "stopTryScene request send fail:$result")
                if (BuildConfig.NO_CCM) {
                    delay(500)
                    mScenarioDispatcher.notifyScenarioExecuteResult(
                        sceneId, 1, 2, System.currentTimeMillis()
                    )
                } else {
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            } else {
                CommonLogUtils.logI(TAG, "stopScenario request send success")
            }

        }
        return true
    }

    /**手动执行场景*/
    fun startScene(sceneId: String): Boolean {
        if (!beforeStartSceneJudgement(sceneId)) {
            return false
        }
        //val myScene = findSceneById(sceneId)
        CoroutineScope(Dispatchers.IO).launch {
            val result = mScenarioAdapter.manualExecuteScenario(sceneId)
            if (result == RESULT_SUCCESS) {
                CommonLogUtils.logI(TAG, "StartScene Success:$sceneId")
            } else {
                CommonLogUtils.logE(TAG, "StartScene Error:$sceneId,result:$result")
                if (BuildConfig.NO_CCM) {
                    mRunningSceneSet.add(sceneId)
                    WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
                    //ux1.8删除toast
                    //if (myScene != null) {
                    //val sceneName = myScene.scenario.scenarioInfo?.scenarioName
                    //CommonToastUtils.show(
                    //    CommonUtils.getApp().getString(R.string.scene_toast_widget_start_scene) + sceneName
                    //)
                    //}
                    delay(500)
                    mScenarioDispatcher.notifyScenarioStartExecute(
                        sceneId, 0, System.currentTimeMillis()
                    )
                    delay(3000)
                    mScenarioDispatcher.notifyScenarioExecuteResult(
                        sceneId, 0, 0, System.currentTimeMillis()
                    )
                } else {
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
        return true
    }

    /**
     * 语音执行场景,区别在于不在协程中执行场景，会把场景引擎请求结果给语言
     */
    fun startScene4Voice(sceneId: String): Boolean {
        if (!beforeStartSceneJudgement(sceneId)) {
            return false
        }
        val result = mScenarioAdapter.manualExecuteScenario(sceneId)
        var voiceResult = false
        CommonLogUtils.logI(TAG, "StartScene from voice result:$result")
        if (result == RESULT_SUCCESS) {
            voiceResult = true
        } else {
            CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
        }
        return voiceResult
    }

    /**停止执行场景*/
    fun stopScene(sceneId: String): Boolean {
        CommonLogUtils.logI(TAG, "stopScene $sceneId")
        if (!mRunningSceneSet.contains(sceneId)) {
            CommonLogUtils.logE(TAG, "场景未执行")
            CommonToastUtils.show(R.string.scene_toast_not_run_now)
            // 需要主动发送一个失败的信息，避免无法停止
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_END, String::class.java).post(sceneId)
            return false
        }
        // 0成功，2没有这个场景，4场景没有执行,1其他原因如超时
        CoroutineScope(Dispatchers.IO).launch {
            val result = mScenarioAdapter.abortScenario(sceneId)
            CommonLogUtils.logI(TAG, "StopSceneRequest:$result")
            if (result != RESULT_SUCCESS) {
                if (BuildConfig.NO_CCM) {
                    delay(500)
                    mScenarioDispatcher.notifyScenarioExecuteResult(
                        sceneId, 1, 0, System.currentTimeMillis()
                    )
                } else {
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                    handleExecuteResult(sceneId, 1, 0)
                }
            }
        }
        return true
    }


    /**
     * 请求打开/关闭自动执行场景
     * autoExeRunState-0，ccm允许自动执行;1表示ccm终止自动执行。
     * autoRunBtnState-0，表示自动执行开关为开;1表示自动执行开关为关
     * scenarioId 场景ID
     */
    fun autoExecuteScene(autoExeRunState: Int, scenario: ScenarioBean?, autoRunBtnState: Int, executeFrequency: Int) {
        scenario ?: return
        val sceneId = scenario.scenarioInfo?.scenarioId
        sceneId ?: return
        // 自动执行开关为开时，进入编辑页面，不做改动后退出，会执行下述判断。（20240820-autoRun分开成两个参数后此判断似乎不再需要？）
        CommonLogUtils.logI(
            TAG,
            "autoExecuteScene sceneId=${scenario.scenarioInfo?.scenarioId} autoExeRunState=$autoExeRunState,autoRunBtnState=$autoRunBtnState"
        )
        if (mAutoSceneMap.contains(sceneId)) {
            CommonLogUtils.logE(TAG, "重复设置自动执行")
            CommonToastUtils.show(R.string.scene_toast_forbid_repeat_auto_run)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            mAutoSceneMap[sceneId] = AutoSceneResult(sceneId, autoExeRunState, autoRunBtnState, executeFrequency)
            val result = mScenarioAdapter.setAutoExecute(sceneId, autoExeRunState, System.currentTimeMillis())
            if (result == ScenarioAdapter.REQUEST_SUCCESS) {
                CommonLogUtils.logI(TAG, "setAutoExecute success")
            } else {
                CommonLogUtils.logE(TAG, "scenario update is fail,result=$result")
                if (BuildConfig.NO_CCM) {
                    mAutoSceneMap[sceneId] = AutoSceneResult(
                        sceneId, autoExeRunState, autoRunBtnState, executeFrequency
                    )
                    delay(500)
                    mScenarioDispatcher.onSetAutoExecute(
                        scenario.scenarioInfo?.scenarioId!!, 0
                    )
                } else {
                    mAutoSceneMap.remove(sceneId)
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
    }

    /**
     * 处理ccm响应自动执行设置结果
     */
    fun handleAutoExecuteConfig(sceneId: String, result: Int) {
        CommonLogUtils.logI(TAG, "AutoExecuteConfig:$sceneId,$result")
        if (result == RESULT_SUCCESS) {
            val autoSceneResult = mAutoSceneMap[sceneId]
            if (autoSceneResult != null) {
                // 更新场景列表中自动执行中的相应的开关配置
                val sceneBean = findSceneById(sceneId)
                if (sceneBean != null) {
                    sceneBean.scenario.isAutoRun = autoSceneResult.autoBtnRun == 0
                    sceneBean.scenario.scenarioInfo?.autoExeFlag = autoSceneResult.autoExeRun
                }
                LiveEventBus.get(
                    GlobalLiveEventConstants.KEY_SCENE_AUTO_SUCCESS, AutoSceneResult::class.java
                ).post(autoSceneResult)
                OMSTriggerManager.startDispatch()
            }
        } else {
            CommonToastUtils.show(R.string.scene_toast_auto_run_fail)
        }
        mAutoSceneMap.remove(sceneId)
    }

    /**
     * 禁止/恢复监听或者执行命令
     * @param state true:打开监听 false:关闭监听
     * 0:禁止所有场景 1:恢复所有场景
     */
    fun setEnableOrDisableScenes(state: Boolean): Boolean {
        CommonLogUtils.logI(TAG, "setEnableOrDisableScenes state:$state")
        CoroutineScope(Dispatchers.IO).launch {
            mScenarioAdapter.enableOrDisableScenes(if (state) 1 else 0)
        }
        return true
    }

    /**
     * 回复ccm自动执行前询问
     * @param scenarioId 场景id
     * @param isRunNow 是否自动执行
     */
    fun notifyAskIfExecuteScenario(
        scenarioId: String, isRunNow: Boolean
    ) {
        replyAndConfigScenario(
            scenarioId, isRunNow = isRunNow
        )
    }


    /**
     *当自动执行场景满足触发条件后， IVI的回复和配置
     * @param scenarioId 场景id
     * @param isRunNow 是否自动执行
     */
    private fun replyAndConfigScenario(
        scenarioId: String, isRunNow: Boolean
    ) {
        CommonLogUtils.logI(TAG, "replyAndConfigScenario: $scenarioId , $isRunNow")
        // 从自动执行的队列中移除
        mAutoRunMap.remove(scenarioId)
        // 只有需要自动执行的才去判断是否有重复执行，取消执行的可以直接发指令
        if (isRunNow && mRunningSceneSet.contains(scenarioId)) {
            //理论上不会进入该判断，用户重复点击是取消执行
            //CommonToastUtils.show(R.string.scene_toast_forbid_repeat_run)
            CommonLogUtils.logE(TAG, "重复执行场景")
            return
        }
        val myScene = findSceneById(scenarioId)
        CoroutineScope(Dispatchers.IO).launch {
            TrackUtils.clickMyAutomaticScene( // 触发CCM满足自动执行条件埋点
                myScene?.scenario?.scenarioInfo?.scenarioName ?: "", myScene?.scenario?.scenarioInfo?.scenarioId ?: ""
            )
            val ret = mScenarioAdapter.replyAndConfigScenario(
                scenarioId, if (isRunNow) 0 else 1, System.currentTimeMillis()
            )
            if (ret == ScenarioAdapter.REQUEST_SUCCESS) {
                CommonLogUtils.logI(TAG, "replyAndConfigScenario: request success")
                if (isRunNow) {
                    mRunningSceneSet.add(scenarioId)
                    WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
                    if (myScene != null) {
                        //ux1.8删除toast
                        //val sceneName = myScene.scenario.scenarioInfo?.scenarioName
                        //CommonToastUtils.show(
                        //    CommonUtils.getApp().getString(R.string.scene_toast_widget_start_scene) + sceneName
                        //)
                        // 发送消息让界面更新，场景已经开始执行,20231204添加，避免多场景触发时界面无提示
                        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_START, String::class.java)
                            .post(myScene.scenario.scenarioInfo?.scenarioId)
                    }
                }
            } else {
                CommonLogUtils.logE(TAG, "replyAndConfigScenario: request fail,result=$ret")
                if (BuildConfig.NO_CCM) {
                    mRunningSceneSet.add(scenarioId)
                    WidgetUpdateHelper.sendRunSceneList2Launcher(mRunningSceneSet)
                    //ux1.8删除toast
                    //if (myScene != null) {
                    //val sceneName = myScene.scenario.scenarioInfo?.scenarioName
                    //CommonToastUtils.show(
                    //    CommonUtils.getApp().getString(R.string.scene_toast_widget_start_scene) + sceneName
                    //)
                    //}
                    delay(500)
                    mScenarioDispatcher.notifyScenarioStartExecute(
                        scenarioId, 1, System.currentTimeMillis()
                    )
                    delay(3000)
                    mScenarioDispatcher.notifyScenarioExecuteResult(
                        scenarioId, 0, 1, System.currentTimeMillis()
                    )
                } else {
                    CommonToastUtils.show(R.string.scene_toast_execute_ccm_error)
                }
            }
        }
    }

    /**通过sceneId找到sceneInfo数据*/
    fun findSceneById(sceneId: String): MySceneBean? {
        for (sceneInfo in mSceneInfoList) {
            if (sceneInfo.scenario.scenarioInfo?.scenarioId == sceneId) {
                return sceneInfo
            }
        }
        return null
    }

    /**通过sceneId找到在mSceneInfoList索引位置*/
    fun findSceneIndexById(sceneId: String): Int {
        for (i in mSceneInfoList.indices) {
            if (mSceneInfoList[i].scenario.scenarioInfo?.scenarioId == sceneId) {
                return i
            }
        }
        return -1
    }

    /**获取新的场景名称*/
    fun getNewSceneName(firstDefaultSceneName: String, defaultSceneNamePrefix: String): String {
        val list = mSceneInfoList
        if (list.size == 0) {
            return firstDefaultSceneName
        }
        val i = CNToNumberUtil.toNumber(defaultSceneNamePrefix, list.size) { index ->
            list[index].scenario.scenarioInfo?.scenarioName ?: ""
        }
        //        val i = CNToNumberUtil.test2(defaultSceneNamePrefix)
        return "$defaultSceneNamePrefix${NumberToCNUtil.toChineseLower(i)}"
    }

    /**检查场景名是否存在*/
    fun checkSceneName(sceneName: String, sceneId: String): Boolean {
        val list = mSceneInfoList.filter {
            sceneName == it.scenario.scenarioInfo?.scenarioName
        }
        if (list.isNotEmpty()) {
            return if (list.size > 1) {
                true
            } else {
                list[0].scenario.scenarioInfo?.scenarioId != sceneId
            }

        }
        return false
    }

    /**
     * 生成一个新的场景ID
     */
    fun getNewSceneId(): String {
        return "VEHICLE_" + System.currentTimeMillis()
    }


    /**
     * 获取收藏的场景列表
     */
    fun getCollectSceneList(sceneList: ArrayList<MySceneBean>): ArrayList<MySceneBean> {
        val collectSceneList = ArrayList<MySceneBean>()
        for (sceneInfo in sceneList) {
            if (sceneInfo.isTop) {
                collectSceneList.add(sceneInfo)
            }
        }
        return collectSceneList
    }

    /**
     * widget添加时，需要刷新一次数据,否则用户删除卡片再添加时无法同步最新数据
     */
    fun updateScene4WidgetUpdate(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            // 将数据从
            if (mSceneInfoList.isEmpty()) {
                return@launch
            }
            val sortSceneList = SceneSortManager.sceneSequenceInterceptor(mSceneInfoList)
            val topSceneList = getCollectSceneList(sortSceneList)
            WidgetUpdateHelper.updateDeskCardSceneList(topSceneList)
        }
    }

    /**
     * 判断场景是否在执行中
     */
    fun isSceneRunning(sceneId: String?): Boolean {
        if (TextUtils.isEmpty(sceneId)) {
            return false
        }
        return mRunningSceneSet.contains(sceneId)
    }

    /**
     * 收到ccm场景禁用结果,更新临停状态记录
     * @param disableScene true禁用所有场景，false恢复所有场景
     */
    fun handleDisableScene(disableScene: Boolean) {
        MMKVUtils.getInstance().saveCarControlDisableScenes(disableScene)
    }

    /**获取场景禁用状态*/
    fun getDisableSceneStatus(): Boolean {
        return MMKVUtils.getInstance().carControlDisableScenes
    }

    /**
     * 删除所有的用户场景,遍历删除
     */
    fun deleteAllUserScene() {
        //        mSceneInfoList.forEach {
        //            deleteSceneInfo(it)
        //        }
        mScenarioAdapter.deleteAllUserScenarios(System.currentTimeMillis())
    }

    /**
     * 处理删除所有用户场景的结果，ccm删除成功清空本地数据
     */
    fun handleDeleteAllScene(ret: Int) {
        if (ret == RESULT_SUCCESS) {
            // 清除所有用户场景
            mSceneInfoList.clear()
            // 发送消息清除用户场景
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_DELETE_ALL_SUCCESS, Boolean::class.java).post(true)
            LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(3)
        }
    }

    /**
     * 测试场景触发执行前询问
     */
    fun testSceneAsk(sceneId: String) {
        mScenarioDispatcher.askIfExecuteScenario(sceneId)
    }

    /**
     * 处理场景自动执行的事件
     */
    fun handleSceneAutoRun(scenarioId: String?) {
        if (scenarioId != null && "" != scenarioId) {
            val mySceneBean = findSceneById(scenarioId)
            // 如果执行时自动执行已经被关闭了，则直接回复取消
            if (mySceneBean == null || mySceneBean.scenario.scenarioInfo?.autoExeFlag == 1) {
                CommonLogUtils.logE(TAG, "执行时自动执行已经被关闭了，则直接回复取消")
                replyAndConfigScenario(scenarioId, isRunNow = false)
                return
            }
            // 添加到自动场景执行的列表
            mAutoRunMap[scenarioId] = System.currentTimeMillis()
            WidgetUpdateHelper.sendAutoRunInfo2Launcher(mySceneBean)
            //			//开启执行前询问的widget服务
            //			val intent = Intent(CommonUtils.getApp(), SceneWidgetService::class.java)
            //			intent.action = SceneWidgetService.ACTION_START_COUNT
            //			intent.putExtra(
            //				SceneWidgetService.EXTRA_SCENE_NAME,
            //				mySceneBean.scenario.scenarioInfo?.scenarioName
            //			)
            //			intent.putExtra(
            //				SceneWidgetService.EXTRA_SCENE_ID,
            //				mySceneBean.scenario.scenarioInfo?.scenarioId
            //			)
            //			CommonUtils.getApp().startForegroundService(intent)

        }
    }

    /**
     * 该场景是否在自动执行的队列中,
     * ccm自带11s超时机制
     * @return 当在队列中时返回true;否则返回false
     */
    fun isInAutoRunList(sceneId: String?): Boolean {
        if (TextUtils.isEmpty(sceneId) || !mAutoRunMap.containsKey(sceneId!!)) {
            return false
        }
        val startTime = mAutoRunMap[sceneId]!!
        return if (System.currentTimeMillis() - startTime < 11000) {
            //未超时还在等待自动执行询问结果
            true
        } else {
            CommonLogUtils.logI(TAG, "ccm超过11s默认超时取消，移除$sceneId")
            //已经超过11s，ccm默认超时取消执行，移除队列
            mAutoRunMap.remove(sceneId)
            false
        }
    }

    /**遍历我的场景，有触发位置条件的 获取当前位置，并发送当前距离 给CCM*/
    @SuppressLint("CheckResult")
    @Deprecated("用新的 traverseSceneGetPosition2CCM4Trigger")
    private fun traverseSceneGetPosition2CCM() {
        CommonLogUtils.logD(TAG, "初始化:traverseSceneGetPosition2CCM ")
        intervalDisposable = Observable.interval(0, 3, TimeUnit.SECONDS).subscribe {
            if (getSceneInfoList().size == 0) return@subscribe

            // 1.遍历所有我的场景 找出触发条件有位置要求的场景, 输出副本
            val copyList: ArrayList<MySceneBean> = arrayListOf()
            getSceneInfoList().forEach() { mySceneBean ->
                val edgeCondition = mySceneBean.scenario.scenarioInfo?.edgeCondition
                //不是自动执行不发送地理围栏
                if (mySceneBean.scenario.scenarioInfo?.autoExeFlag != 0) return@forEach
                // 车辆到达 和 车辆离开
                if (edgeCondition?.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL || edgeCondition?.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE) {
                    // 集合的复制 https://blog.csdn.net/badme/article/details/127015675
                    copyList.add(mySceneBean.copy())
                }
            }
            if (copyList.size == 0) return@subscribe
            CommonLogUtils.logI(TAG, "地理围栏场景数:${copyList.size}")

            // 2.副本有数据，请求导航获取当前位置
            CoroutineScope(Dispatchers.IO).launch {
                val cPostion = async {
                    NaviManager.getCurrentPosition()
                }.await()
                cPostion?.let { postion ->
                    CommonLogUtils.logD(
                        TAG, "发送CCM距离---》有副本个数: " + copyList.size + " ,当前时间: " + TimeUtils.millis2String(
                            System.currentTimeMillis(), "HH:mm:ss"
                        ) + "\n获取导航当前位置lat: " + postion.lat + " log:" + postion.lon
                    )
                    // 3.遍历副本 计算距离
                    copyList.forEach { mySceneBean ->
                        // 该场景触发条件的位置信息
                        val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input
                        val argLat = inputList?.filter { inputArgInfo ->
                            inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE
                        }?.get(0)?.value?.toDouble()
                        val argLon = inputList?.filter { inputArgInfo ->
                            inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE
                        }?.get(0)?.value?.toDouble()

                        if (postion.lat != null && postion.lon != null && argLat != null && argLon != null) {
                            val distance = NaviManager.distance(postion.lat!!, postion.lon!!, argLat, argLon)
                            CommonLogUtils.logD(
                                TAG, "发送CCM距离---》场景坐标，lat:$argLat ,lon:$argLon ,直线距离: $distance 米"
                            )

                            // 4 发送给CCM
                            LiveEventBus.get(SoaCenterService.KEY_SEND_POSITION_2CCM, MyScenePosition::class.java)
                                .postAcrossProcess(MyScenePosition(argLat, argLon, distance.toInt()))
                        }
                    }
                }
            }
        }
    }

    /**保存场景上一次的直线距离*/
    private val sceneLastDistanceMap: MutableMap<String, Double> = mutableMapOf()

    /**地理围栏逻辑升级，遍历我的场景，有触发位置条件的 判断是否有距离触发，并发送选点坐标与设置的距离 给CCM*/
    @SuppressLint("CheckResult")
    private fun traverseSceneGetPosition2CCM4Trigger() {
        CommonLogUtils.logD(TAG, "初始化:traverseSceneGetPosition2CCM4Trigger ")
        //每3秒判断一次
        intervalDisposable = Observable.interval(0, 3, TimeUnit.SECONDS).subscribe {
            if (getSceneInfoList().size == 0) return@subscribe

            // 1.遍历所有我的场景 是自动执行的 找出触发条件有位置要求的场景, 输出副本
            val copyList: ArrayList<MySceneBean> = arrayListOf()
            getSceneInfoList().forEach() { mySceneBean ->
                val edgeCondition = mySceneBean.scenario.scenarioInfo?.edgeCondition
                //不是自动执行不发送地理围栏
                if (mySceneBean.scenario.scenarioInfo?.autoExeFlag != 0) return@forEach
                // 车辆到达 和 车辆离开
                if (edgeCondition?.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL || edgeCondition?.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE) {
                    // 集合的复制 https://blog.csdn.net/badme/article/details/127015675
                    copyList.add(mySceneBean.copy())
                }
            }
            if (copyList.size == 0) return@subscribe
            CommonLogUtils.logI(TAG, "每3秒获取一次地理围栏场景数，当前数量:${copyList.size}")

            // 2.副本有数据，请求导航获取当前位置
            CoroutineScope(Dispatchers.IO).launch {
                val cPosition = async { //当前车位置坐标
                    NaviManager.getCurrentPosition()
                }.await() ?: return@launch
                if (cPosition.lat == null || cPosition.lon == null) return@launch

                CommonLogUtils.logD(
                    TAG,
                    "发送CCM距离---》有地理围栏场景数（副本个数）: " + copyList.size + " ,当前时间: " + TimeUtils.millis2String(
                        System.currentTimeMillis(), "HH:mm:ss"
                    ) + "，获取导航当前位置lat: " + cPosition.lat + " ，log:" + cPosition.lon
                )

                // 3.遍历副本 计算距离
                copyList.forEach { mySceneBean ->
                    //场景id 与 触发条件
                    val sceneId = mySceneBean.scenario.scenarioInfo?.scenarioId ?: return@forEach
                    val edgeCondition = mySceneBean.scenario.scenarioInfo?.edgeCondition ?: return@forEach

                    // 该场景触发条件的位置信息
                    val inputList = mySceneBean.scenario.scenarioInfo?.edgeCondition?.input ?: return@forEach
                    val argLat = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE
                    }[0]?.value?.toDouble() ?: return@forEach
                    val argLon = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE
                    }[0]?.value?.toDouble() ?: return@forEach
                    val argDistance = inputList.filter { inputArgInfo ->
                        inputArgInfo.name == SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE
                    }[0]?.value?.toInt() ?: return@forEach

                    //计算当前位置距离场景设置的选点间直线距离
                    val cDistance = NaviManager.distance(cPosition.lat!!, cPosition.lon!!, argLat, argLon)
                    if (cDistance < 0) return@forEach
                    //没有记录的先保存退出，下一次使用
                    if (!sceneLastDistanceMap.containsKey(sceneId)) {
                        sceneLastDistanceMap[sceneId] = cDistance
                        return@forEach //退出继续循环
                    }

                    //4判断是否触发
                    val isCanTrigger =
                        if (edgeCondition.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL) { //到达
                            sceneLastDistanceMap[sceneId]!! >= argDistance && argDistance > cDistance
                        } else { //离开
                            sceneLastDistanceMap[sceneId]!! < argDistance && argDistance <= cDistance
                        }
                    sceneLastDistanceMap[sceneId] = cDistance //判断后记录当前距离
                    if (isCanTrigger) { //场景触发
                        val isDaoda =
                            edgeCondition.skillId == SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL
                        // 5 发送给CCM
                        CommonLogUtils.logD(
                            TAG,
                            "场景触发发送CCM距离---》场景坐标，lat:$argLat ,lon:$argLon ,${if (isDaoda) "到达" else "离开"} 场景设置的距离: $argDistance 米"
                        )
                        LiveEventBus.get(SoaCenterService.KEY_SEND_POSITION_2CCM, MyScenePosition::class.java)
                            .postAcrossProcess(MyScenePosition(argLat, argLon, argDistance, if (isDaoda) 1 else 2))
                    }
                }
            }
        }
    }

    /**
     * 取消间隔发送距离的定时器
     */
    private fun cancelIntervalDisposable() {
        intervalDisposable?.takeIf { !it.isDisposed }?.dispose()
        intervalDisposable = null
    }

    /**在开始执行场景前，若临停，自在，重复执行返回false*/
    private fun beforeStartSceneJudgement(sceneId: String): Boolean {
        CommonLogUtils.logI(TAG, "startScene $sceneId")
        if (isOnDisableSceneStatus()) {
            return false
        }
        if (mRunningSceneSet.contains(sceneId)) {
            //理论上不会进入该判断，用户重复点击是取消执行
            CommonLogUtils.logE(TAG, "重复执行场景")
            //CommonToastUtils.show(R.string.scene_toast_forbid_repeat_run)
            return false
        }
        return true
    }

    /**检测临停和自在 false场景可用，true不可用*/
    fun isOnDisableSceneStatus(): Boolean {
        val comfortSetDisableScenes = MMKVUtils.getInstance().comfortDisableScenes
        val mIsDisableScenes = MMKVUtils.getInstance().carControlDisableScenes
        CommonLogUtils.logI(TAG, "isOnDisableSceneStatus 临停启动:$mIsDisableScenes,自在运行:$comfortSetDisableScenes")
        if (mIsDisableScenes && comfortSetDisableScenes) {
            CommonToastUtils.show(R.string.scene_toast_forbid_run_at_comfort)
            SoundManager.playSoundEffect(SoundManager.SoundType.FAILED)
            return true
        } else if (mIsDisableScenes) {
            CommonToastUtils.show(R.string.scene_toast_forbid_run_at_carcontrol)
            SoundManager.playSoundEffect(SoundManager.SoundType.FAILED)
            return true
        }
        return false
    }

}

