package com.dfl.smartscene.soa

import android.util.SparseArray
import com.dfl.android.common.util.RawUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.DiscoverSceneDataBean
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.bean.main.SceneCardBean
import com.dfl.android.common.global.GlobalConstant
import com.google.gson.Gson
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import com.iauto.scenarioadapter.SkillInfo
import kotlinx.coroutines.sync.Mutex
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   : 数据源获取管理类,生成debug测试数据
 * version: 1.0
 */
object DataManager {
    // common
    const val TAG: String = GlobalConstant.GLOBAL_TAG.plus("DataManager")
    val timeStampFormat = SimpleDateFormat("HH:mm", Locale.CHINA)
    val dateStampFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)

    // skill info
    @Volatile
    private var isSkillInfoDataReady = false
    private var mSkillInfoList = SparseArray<ArrayList<SkillInfo>>()
    private val mSkillInfoLock = Mutex()

    init {
        timeStampFormat.timeZone = TimeZone.getTimeZone("Asia/Shanghai")
    }

    //我的场景测试数据
    fun initTestData4Scene(): ArrayList<MySceneBean> {
        val mySceneBeanList = ArrayList<MySceneBean>()
        //场景条件列表
        val condition1 = ScenarioInfo.Condition(1, "6122", 777, "部品1", ArrayList())
        val condition11 = ScenarioInfo.Condition(1, "6123", 666, "部品1", ArrayList())
        val action1 = ScenarioInfo.Action("打开车窗", 6122, "车窗", ArrayList())
        val action2 = ScenarioInfo.Action("打开雨刷", 6123, "雨刷", ArrayList())
        val action3 = ScenarioInfo.Action("打开后背门", 6124, "后背门", ArrayList())
        val sequence1 = ScenarioInfo.Sequence(1, 1, action1)
        val sequence2 = ScenarioInfo.Sequence(2, 1, action2)
        val sequence3 = ScenarioInfo.Sequence(3, 1, action3)
        val conditionList1 = mutableListOf<ScenarioInfo.Condition>()
        conditionList1.add(condition1)
        val sequenceList1 = mutableListOf<ScenarioInfo.Sequence>()
        sequenceList1.add(sequence1)
        sequenceList1.add(sequence2)
        sequenceList1.add(sequence3)
        val detailInfo = ScenarioInfo(
            "123", "观影模式", "看电影", 1, 1, 1, 1, "", 0, condition11, conditionList1, sequenceList1
        )
        val sceneBean1 = ScenarioBean(
            detailInfo, isAskBeforeAutoRun = false, isTemplateScene = false, isAutoRun = false, executeFrequency = 0
        )
        val mySceneBean1 = MySceneBean(sceneBean1, isSceneStart = false, isTop = false)
        mySceneBeanList.add(mySceneBean1)

        val condition2 = ScenarioInfo.Condition(7122, "7122", 7119, "部品2", ArrayList())
        val condition22 = ScenarioInfo.Condition(7123, "7123", 7118, "部品2", ArrayList())
        val sequence21 = ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action("详情1", 7122, "车窗", ArrayList()))
        val sequence22 = ScenarioInfo.Sequence(2, 1, ScenarioInfo.Action("详情2", 7123, "后背门", ArrayList()))
        val sequence23 = ScenarioInfo.Sequence(3, 1, ScenarioInfo.Action("详情3", 7124, "雨刷", ArrayList()))
        val sequence24 = ScenarioInfo.Sequence(4, 1, ScenarioInfo.Action("详情4", 7125, "导航", ArrayList()))
        val sequence25 = ScenarioInfo.Sequence(5, 1, ScenarioInfo.Action("详情5", 7126, "空调", ArrayList()))
        val sequence26 = ScenarioInfo.Sequence(6, 1, ScenarioInfo.Action("详情6", 7127, "氛围灯", ArrayList()))
        val sequence27 = ScenarioInfo.Sequence(7, 1, ScenarioInfo.Action("详情7", 7128, "后视镜", ArrayList()))
        val sequence28 = ScenarioInfo.Sequence(8, 1, ScenarioInfo.Action("详情8", 7129, "多媒体", ArrayList()))
        val sequence29 = ScenarioInfo.Sequence(9, 1, ScenarioInfo.Action("详情9", 7130, "座椅", ArrayList()))
        val conditionList21 = mutableListOf<ScenarioInfo.Condition>()
        conditionList21.add(condition2)
        val sequenceList21 = mutableListOf(
            sequence21, sequence22, sequence23, sequence24, sequence25, sequence26, sequence27, sequence28, sequence29
        )
        val detailInfo2 = ScenarioInfo(
            "345", "睡眠模式", "休息时间", 1, 1, 1, 1, "", 0, condition22, conditionList21, sequenceList21
        )
        val sceneBean2 = ScenarioBean(
            detailInfo2, isAskBeforeAutoRun = false, isTemplateScene = false, isAutoRun = false, executeFrequency = 0
        )
        val mySceneBean2 = MySceneBean(sceneBean2, isSceneStart = false, isTop = false)
        mySceneBeanList.add(mySceneBean2)

        val condition3 = ScenarioInfo.Condition(8122, "8122", 8119, "部品3", ArrayList())
        val condition33 = ScenarioInfo.Condition(8123, "8123", 8118, "部品3", ArrayList())
        val sequence31 = ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action("详情1", 8122, "屏保", ArrayList()))
        val sequence32 = ScenarioInfo.Sequence(2, 1, ScenarioInfo.Action("详情21", 8123, "语音", ArrayList()))
        val sequence33 = ScenarioInfo.Sequence(3, 1, ScenarioInfo.Action("详情31", 8124, "多媒体", ArrayList()))
        val sequence34 = ScenarioInfo.Sequence(4, 1, ScenarioInfo.Action("详情41", 8125, "导航", ArrayList()))
        val sequence35 = ScenarioInfo.Sequence(5, 1, ScenarioInfo.Action("详情51", 8126, "后背门", ArrayList()))
        val sequence36 = ScenarioInfo.Sequence(6, 1, ScenarioInfo.Action("详情61", 8127, "加油口盖", ArrayList()))
        val sequence37 = ScenarioInfo.Sequence(7, 1, ScenarioInfo.Action("详情71", 8128, "雨刮", ArrayList()))
        val conditionList31 = mutableListOf<ScenarioInfo.Condition>()
        conditionList31.add(condition3)
        val sequenceList31 = mutableListOf(
            sequence31, sequence32, sequence33, sequence34, sequence35, sequence36, sequence37
        )
        val detailInfo3 = ScenarioInfo(
            "678", "露营模式", "野外露营中...", 1, 1, 1, 1, "", 0, condition33, conditionList31, sequenceList31
        )
        val sceneBean3 = ScenarioBean(
            detailInfo3, isAskBeforeAutoRun = false, isTemplateScene = false, isAutoRun = false, executeFrequency = 0
        )
        val mySceneBean3 = MySceneBean(sceneBean3, isSceneStart = false, isTop = false)
        mySceneBeanList.add(mySceneBean3)

        val condition4 = ScenarioInfo.Condition(9122, "9122", 9119, "部品4", ArrayList())
        val condition44 = ScenarioInfo.Condition(9123, "9123", 9118, "部品4", ArrayList())
        val sequence41 = ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action("详情1", 9122, "后视镜", ArrayList()))
        val sequence42 = ScenarioInfo.Sequence(2, 1, ScenarioInfo.Action("详情21", 9123, "车灯", ArrayList()))
        val sequence43 = ScenarioInfo.Sequence(3, 1, ScenarioInfo.Action("详情31", 9124, "车窗", ArrayList()))
        val sequence44 = ScenarioInfo.Sequence(4, 1, ScenarioInfo.Action("详情41", 9125, "氛围灯", ArrayList()))
        val sequence45 = ScenarioInfo.Sequence(5, 1, ScenarioInfo.Action("详情51", 9126, "座椅", ArrayList()))
        val sequence46 = ScenarioInfo.Sequence(6, 1, ScenarioInfo.Action("详情61", 9127, "空调", ArrayList()))
        val sequence47 = ScenarioInfo.Sequence(7, 1, ScenarioInfo.Action("详情71", 9128, "雨刮", ArrayList()))
        val conditionList41 = mutableListOf<ScenarioInfo.Condition>()
        conditionList41.add(condition4)
        val sequenceList41 = mutableListOf(
            sequence41, sequence42, sequence43, sequence44, sequence45, sequence46, sequence47
        )
        val detailInfo4 = ScenarioInfo(
            "789", "雨夜模式", "野外露营中...", 1, 1, 1, 1, "", 0, condition44, conditionList41, sequenceList41
        )
        val sceneBean4 = ScenarioBean(
            detailInfo4, isAskBeforeAutoRun = false, isTemplateScene = false, isAutoRun = false, executeFrequency = 0
        )
        val mySceneBean4 = MySceneBean(sceneBean4, isSceneStart = false, isTop = false)
        mySceneBeanList.add(mySceneBean4)

        for (i: Int in 0..9) {
            val detailInfo4 = ScenarioInfo(
                "789" + i,
                "雨夜模式" + i,
                "野外露营中...",
                1,
                1,
                1,
                1,
                "",
                0,
                condition44,
                conditionList41,
                sequenceList41
            )
            val sceneBean4 = ScenarioBean(
                detailInfo4,
                isAskBeforeAutoRun = false,
                isTemplateScene = false,
                isAutoRun = false,
                executeFrequency = 0
            )
            val mySceneBean4 = MySceneBean(sceneBean4, isSceneStart = false, isTop = false)
            mySceneBeanList.add(mySceneBean4)
        }

        return mySceneBeanList
    }


    /**
     * 初始化发现列表测试数据
     */
    fun initTestData4Discover(): ArrayList<SceneCardBean> {
        val cardJson = RawUtils.get(CommonUtils.getApp(), R.raw.discover_scene_group_data)
        val cardBean = Gson().fromJson(cardJson, DiscoverSceneDataBean::class.java)
        val mSceneCardList = ArrayList<SceneCardBean>()
        if (!cardBean.isNullOrEmpty()) {
            if (cardBean.size > 0) {
                val list = ArrayList<SceneCardBean>()
                cardBean.forEach {
                    val conditions = ArrayList<ScenarioInfo.Condition>()
                    val actions = ArrayList<ScenarioInfo.Sequence>()
                    val input = ArrayList<InputArgInfo>()
                    it.conditions.forEach { condition ->
                        if (condition.args.isNotEmpty()) {
                            condition.args.forEach { arg ->
                                input.add(
                                    InputArgInfo(
                                        arg.name, ArgType.INT32, arg.value, arg.desc
                                    )
                                )
                            }
                        }
                    }
                    it.conditions.forEach { condition ->
                        conditions.add(
                            ScenarioInfo.Condition(
                                condition.id,
                                condition.desc,
                                Integer.decode(condition.skill_id),
                                condition.class_name,
                                input
                            )
                        )
                    }

                    it.sequence.items.forEach { sequence ->
                        actions.add(
                            ScenarioInfo.Sequence(
                                sequence.id, sequence.type, ScenarioInfo.Action(
                                    sequence.item.desc,
                                    Integer.decode(sequence.item.skill_id),
                                    sequence.item.class_name,
                                    ArrayList()
                                )
                            )
                        )
                    }
                    val map = ArrayList<InputArgInfo>()
                    it.trigger_con.args.let { args ->
                        args.forEach { arg ->
                            map.add(InputArgInfo(arg.name, ArgType.INT32, arg.value, arg.desc))
                        }
                    }
                    val triggerCondition = ScenarioInfo.Condition(
                        it.trigger_con.id,
                        it.trigger_con.desc,
                        Integer.decode(it.trigger_con.skill_id),
                        it.trigger_con.class_name,
                        map
                    )

                    val bean = SceneCardBean(
                        ScenarioBean(
                            ScenarioInfo(
                                it.scenario_id,
                                it.name,
                                it.desc,
                                it.version,
                                it.auto_execute,
                                it.second_ask,
                                System.currentTimeMillis(),
                                "",
                                0,
                                triggerCondition,
                                conditions,
                                actions
                            ),
                            isAskBeforeAutoRun = true,
                            isTemplateScene = true,
                            isAutoRun = false,
                            executeFrequency = 0
                        )
                    )
                    list.add(bean)
                }

                if (list.size > 0) {
                    mSceneCardList.addAll(list)
                }
            }
        }
        return mSceneCardList
    }

}