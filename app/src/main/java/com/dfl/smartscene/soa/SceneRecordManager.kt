package com.dfl.smartscene.soa

import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.SceneRecordTable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/12/28
 * desc: 场景执行记录的管理器
 * version:1.0
 */
object SceneRecordManager {
    private const val TAG = GlobalConstant.GLOBAL_TAG + "SceneRecordManager"
    private val mRecordDateStampFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA)
    val dateStampFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)

    // SceneRecord
    private val mSceneRecordLock = Mutex()

    /**
     * 保存场景执行日志
     * @param scenarioId 执行场景的ID
     * @param isSuccess 场景执行的结果
     */
    fun insertSceneRecord(scenarioId: String, isSuccess: Boolean) {
        val mySceneBean = SceneManager.findSceneById(scenarioId) ?: return
        val sceneName = mySceneBean.scenario.scenarioInfo?.scenarioName ?: ""
        val recordType = if (isSuccess) SceneRecordTable.VALUE_START_SCENE_RESULT_SUCCESS else SceneRecordTable.VALUE_START_SCENE_RESULT_FAIL
        val entity = SceneRecordEntity(
            null,
            scenarioId,
            sceneName,
            System.currentTimeMillis(),
            recordType
        )

        CoroutineScope(Dispatchers.IO).launch {
            mSceneRecordLock.withLock {
                DbManager.db { db ->
                    val dao = db.SceneRecordDao()
                    entity.srId = dao.insert(entity)
                    //                    CommonLogUtils.logD(TAG,"执行日志记录插入:$scenarioId")
                    return@db entity
                }.flowOn(Dispatchers.IO).map { _ ->

                }.flowOn(Dispatchers.Main).catch { cause ->
                    CommonLogUtils.logW(TAG, cause.message)
                }.collect { }
            }
        }
    }

    /**
     * 删除超过30天的执行日志
     * 例：今10月26号 那么30天前9月26号，9月26号包含26号以前的数据都删除
     */
    fun deleteTimeOutSceneRecordList() {
        CoroutineScope(Dispatchers.IO).launch {
            mSceneRecordLock.withLock {
                DbManager.db { db ->
                    val dao = db.SceneRecordDao()
                    val duration = 2505600000 //29天的毫秒数
                    val time = System.currentTimeMillis() - duration
                    val day = dateStampFormat.format(time).plus(" 00:00:00")
                    val untilTime = mRecordDateStampFormat.parse(day)?.time
                    untilTime?.let {
                        dao.deleteUntil(it)
                    }
                }.flowOn(Dispatchers.IO).catch { e ->
                    CommonLogUtils.logE(TAG, "===》" + e.message)
                }.onCompletion { cause ->
                    if (cause == null) {
                        CommonLogUtils.logE(TAG, "delete success")
                    }
                }.flowOn(Dispatchers.Main).collect {
                    CommonLogUtils.logE(TAG, "delete TimeOut Record success")
                }
            }
        }

    }
}