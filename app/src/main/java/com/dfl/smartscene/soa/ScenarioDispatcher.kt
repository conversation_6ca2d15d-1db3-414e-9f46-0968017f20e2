package com.dfl.smartscene.soa

import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.iauto.scenarioadapter.ScenarioAdapterListener

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/25
 * desc   : 场景CCM后台返回的接口回调
 * version: 1.0
 */
class ScenarioDispatcher : ScenarioAdapterListener {
    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("ScenarioDispatcher")
    }

    /**ccm连接状态通知*/
    override fun onConnected(connect: Boolean) {
        CommonLogUtils.logI(TAG, "CCM_Connect：$connect")
        SceneManager.handleConnectStatus(connect)
    }


    /**
     * 场景列表变更通知
     * @param reason      触发我的场景变更的原因
     * ScenarioAdapterListener.CLOUD_UPDATE: 云端修改 （包括删除，新增， 修改，修改配置等操作），值为 0
     * ScenarioAdapterListener.IVI_UPDATE：车机 APP 修改（包括删除，新增， 修改修改配置等操作），值为 1
     */
    override fun onUserScenarioListChanged(reason: Int) {
        CommonLogUtils.logI(TAG, "onUserScenarioListChanged:" + reason)
        val updateBy = when (reason) {
            // IVI变更不做处理，云端变更需要重新获取一次场景列表数据
            ScenarioAdapterListener.IVI_UPDATE -> "IVI_UPDATE"
            ScenarioAdapterListener.CLOUD_UPDATE -> {
                CommonLogUtils.logI(TAG, "getScenarioGroupsList:" + reason + ";cloud-update")
                //重新调用一次请求场景列表的接口
                SceneManager.requestSceneInfoList()
                "CLOUD_UPDATE"
            }
            //            ScenarioAdapterListener.OEM_UPDATE -> "OEM_UPDATE"
            else -> "type:$reason"
        }
        CommonLogUtils.logD(
            TAG,
            "onUserScenarioListChanged: update by: $updateBy"
        )
        //        LiveEventBus.get(LiveEventConstants.KEY_SCENE_CHANGED,Int::class.java).post(reason)
    }

    /**
     * 修改场景处理结果
     * @param scenarioId 场景id
     * @param ret 场景修改结果0: 成功, 1：没有这个场景, 2：参数错误， 3：其他原因
     */
    override fun onScenarioModify(scenarioId: String, ret: Int) {
        CommonLogUtils.logI(TAG, "onScenarioModify ==id:$scenarioId ===ret:$ret")
        SceneManager.handleModifyScene(scenarioId, ret)
    }

    /**
     * 删除场景处理结果
     * @param scenarioId 场景id
     * @param ret 场景删除结果0: 成功, 1：没有这个场景, 2：参数错误， 3：其他原因
     */
    override fun onScenarioDelete(scenarioId: String, ret: Int) {
        CommonLogUtils.logI(TAG, "onScenarioDelete  ==id$scenarioId  ====ret:$ret")
        SceneManager.handleDeleteScene(scenarioId, ret)
    }

    /**
     * 添加场景处理结果
     * @param scenarioId 场景id
     * @param ret 场景添加结果0: 成功, 1：没有这个场景, 2：参数错误， 3：其他原因
     */
    override fun onScenarioAdd(scenarioId: String, ret: Int) {
        CommonLogUtils.logI(TAG, "onScenarioAdd  ==id$scenarioId  ====ret:$ret")
        SceneManager.handleAddScene(scenarioId, ret)
    }

    /**
     * 询问是否执行满足触发条件的自动场景
     *
     * @param scenarioId 场景id
     */
    override fun askIfExecuteScenario(scenarioId: String?) {
        CommonLogUtils.logI(TAG, "askIfExecuteScenario $scenarioId")
        SceneManager.handleSceneAutoRun(scenarioId)
    }

    /**
     * 场景执行开始通知
     *
     * @param scenarioId 场景id
     * @param exeType    执行类型（0: 手动执行 , 1: 自动执行,2:试用执行，3：未执行）
     * @param timeStamp  时间戳
     */
    override fun notifyScenarioStartExecute(scenarioId: String?, exeType: Int, timeStamp: Long) {
        CommonLogUtils.logI(
            TAG,
            "notifyScenarioStartExecute, scenario id:$scenarioId exeType:$exeType timeStamp:$timeStamp"
        )
        if (scenarioId != null) {
            SceneManager.handleStartScene(scenarioId, exeType)
        }
    }

    /**
     * 场景执行结束通知,只有正常运行完场景所有动作才会调用执行此回调
     *
     * @param scenarioId 场景id
    //     * @param exeType    执行类型
     * @param timeStamp  时间戳
     */
    override fun notifyScenarioStopExecute(scenarioId: String?, timeStamp: Long) {
        CommonLogUtils.logI(
            TAG,
            "notifyScenarioStopExecute, scenario id:$scenarioId , timeStamp:$timeStamp"
        )

    }

    /**
     * 场景中Action执行开始通知
     *
     * @param scenarioId 场景id
     * @param stepId     步骤id
     * @param skillId    能力id
     * @param timeStamp  时间戳
     */
    override fun notifyActionStartExecute(
        scenarioId: String?,
        stepId: Int,
        skillId: Int,
        timeStamp: Long
    ) {
        CommonLogUtils.logI(
            TAG,
            "notifyActionStartExecute, id:$scenarioId stepId:$stepId skillId:$skillId timeStamp:$timeStamp"
        )
        scenarioId ?: return
    }

    /**
     * 场景执行结果通知，正常执行结束和中途中止执行都会回调此接口
     *
     * @param scenarioId 场景id
     * @param result     执行结果（0: 成功， 1: 中止）
     * @param exeType 场景执行类型，0：手动执行，1：自动执行，2：试用执行，3：未执行
     * @param timeStamp  时间戳
     */
    override fun notifyScenarioExecuteResult(scenarioId: String?, result: Int, exeType: Int, timeStamp: Long) {
        CommonLogUtils.logI(
            TAG,
            "notifyScenarioExecuteResult, id:$scenarioId result:$result exeType:$exeType timeStamp:$timeStamp"
        )
        scenarioId ?: return
        // 执行成功的通知在notifyScenraioStopExecute回调中进行处理
        SceneManager.handleExecuteResult(scenarioId, result, exeType)
    }

    /** 场景禁用的通知
     * @param state 0:禁止所有场景；1：恢复所有场景
     */
    override fun notifyenableOrDisableScenes(state: Int) {
        CommonLogUtils.logI(
            TAG,
            "notifyenableOrDisableScenes, state:$state"
        )
        val disable = state == 0
        SceneManager.handleDisableScene(disable)
    }

    /**ccm响应自动执行设置结果*/
    override fun onSetAutoExecute(scenarioId: String?, result: Int) {
        scenarioId ?: return
        SceneManager.handleAutoExecuteConfig(scenarioId, result)
    }

    /**ccm用户场景全部删除结果*/
    override fun onAllUserScenariosDelete(result: Int) {
        CommonLogUtils.logI(
            TAG,
            "onAllUserScenariosDelete, result:$result"
        )
        SceneManager.handleDeleteAllScene(result)
    }
}