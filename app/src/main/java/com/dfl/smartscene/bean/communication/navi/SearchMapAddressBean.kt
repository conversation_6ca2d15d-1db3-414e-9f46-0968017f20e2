//package com.dfl.smartscene.bean.customapi
//
//import java.io.Serializable
//
///**
// * author : wbwangws
// * e-mail : <EMAIL>
// * time : 2022/12/16
// * desc :搜索关键字返回数据
// * version: 1.0
// */
//data class SearchMapAddressBean(
//	val resultCode: Int, val Pois: List<PoiItem>
//) : Serializable{
//	fun isSuccess(): Boolean {
//		return resultCode == 10000
//	}
//}
//
//data class PoiItem(
//	val adCode: String,
//	val addr: String,
//	val category: String,
//	val childPois: List<PoiItem>?,
//	val childType: Int,
//	val cityCode: String,
//	val cityName: String,
//	val deepInfo: String,
//	val dis: String,
//	val distToVia: Int,
//	val distance: String,
//	val district: String,
//	val districtAdcode: Int,
//	val etaToVia: Int,
//	val floorNo: String,
//	val historyType: Int,
//	val id: String,
//	val industry: String,
//	val labelType: Int,
//	val mChargeData: List<MChargeData>,
//	val mGasInfos: List<SearchGasInfo>?,
//	val name: String?,
//	val parent: String,
//	val pid: String,
//	val poiExtra: PoiExtra,
//	val poiPolygonBounds: List<PoiPolygonBound>,
//	val poiTag: String,
//	val point: PointX,
//	val price: String,
//	val shortName: String,
//	val telPhone: String,
//	val time: Int,
//	val toll: Int,
//	val type: Int,
//	val typeCode: String,
//	val typecode: String,
//	val vehicleChargeLeft: Int,
//	val viaLevel: Int,
//	val workTime: String
//) : Serializable
//
//
//data class SearchGasInfo(
//	val price: String, val type: String, val unit: String
//) : Serializable
//
//
//data class MChargeData(
//	val business: String,
//	val chargeInfo: List<ChargeInfo>,
//	val charge_src_name: String,
//	val chargingPrice: List<ChargingPrice>,
//	val cscf: String,
//	val currentChargingPrice: CurrentChargingPrice,
//	val national_standard: String,
//	val num_fast: Int,
//	val num_slow: Int,
//	val pay_type: String,
//	val price_parking: String,
//	val src_id: String,
//	val src_type: String
//) : Serializable
//
//data class PoiPolygonBound(
//	val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
//) : Serializable
//
//data class PointX(
//	val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
//) : Serializable
//
//data class PoiExtra(
//	val entranceList: List<Entrance>
//) : Serializable
//
//data class Entrance(
//	val lat: Double, val lon: Double, val m_X: Int, val m_Y: Int
//) : Serializable
//
//data class ChargeInfo(
//	val charge_Plugs_Info: List<ChargePlugsInfo>,
//	val max_vol: Int,
//	val min_vol: Int,
//	val plugstype: String,
//	val vol_type: String
//) : Serializable
//
//data class ChargingPrice(
//	val ele_price: String, val ser_price: String, val time: String
//) : Serializable
//
//data class CurrentChargingPrice(
//	val ele_price: String, val ser_price: String, val time: String
//) : Serializable
//
//data class ChargePlugsInfo(
//	val brand_desc: String,
//	val concur: Int,
//	val conpower: Int,
//	val convol: Int,
//	val fastcur: Int,
//	val fastpower: Int,
//	val fastvol: Int,
//	val speed_type: Int,
//	val vol: Int,
//	val vol_type: String
//) : Serializable
