package com.dfl.smartscene.bean.communication.navi

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/16
 * desc :地图返回当前位置信息
 * version: 1.0
 */
@Parcelize
data class LocalAddressInfoBean(
    val resultCode: Int, //10000 成功 10001 未找到城市信息
    val province: String? = "", //省
    val cityName: String? = "", //城市名
    val townName: String? = "", //区/县
    val longitude: Double, //精度
    val latitude: Double, //纬度
    val adcode: Int //当前城市编码
) : Parcelable