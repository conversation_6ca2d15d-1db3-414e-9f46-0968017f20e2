package com.dfl.smartscene.bean.main

import android.os.Parcelable
import com.dfl.smartscene.R
import com.dfl.smartscene.util.ConditionOrActionIconUtils
import kotlinx.parcelize.Parcelize
import kotlin.math.ceil

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :我的场景
 * version: 1.0
 */
@Parcelize
data class MySceneBean(
    /**
     * 场景实体
     */
    var scenario: ScenarioBean,
    /**
     * 该场景是否已启动
     */
    var isSceneStart: Boolean?,
    /**
     * 是否置顶
     */
    var isTop: Boolean,
    /**
     * 是否是New标志的场景
     */
    var isNewSign: Boolean? = false,
    /**
     * 是否是车机端新增的场景，用于动效
     */
    var isAddNewScene2ShowAnima: Boolean? = false,
    /**
     * 是为需要创建并保存到我的场景的场景 ，否为需要编辑并保存的场景
     */
    var isAddScene: Boolean = false,

    /**小图标集合*/
    var meIconBeanList: MutableList<MeIconBean>? = null
) : Parcelable {

    /**
     * 先根据场景提供的条件动作内容去判断对应的图标显示规则，然后按照规则去添加对应的图标,6种规则
     * 1:只有动作<=maxNum;
     * 2:条件+动作<=maxNum-1;
     * 3:只有动作且>maxNum;
     * 4:条件+动作>maxNum-1且0<条件<=maxNum/2;
     * 5:条件+动作>maxNum-1且条件>maxNum/2,动作<maxNum/2;
     * 6:条件+动作>maxNum-1,条件>maxNum/2,动作>=maxNum/2
     * 即条件最多有(maxNum-1)/2向上取整,动作最多有(maxNum-1)/2向下取整(暂定等ux确认)
     */
    fun findIconBeanList(): MutableList<MeIconBean>? {
        val detailInfo = scenario.scenarioInfo
        //当前场景动作集合（注--如果场景没有动作，那么这个场景为异常数据）
        val actionList = detailInfo?.sequence
        if (actionList.isNullOrEmpty()) return null

        //添加触发条件图标
        val triggerCondition = detailInfo.edgeCondition
        //添加状态条件图标
        val statusConditions = detailInfo.conditions
        //包括连接符号的最大icon数量
        val maxNum = 8
        //不包括连接符号包括省略号
        val maxConditionNum = ceil((maxNum - 1) / 2.0).toInt()
        val maxActionNum = (maxNum - 1) / 2
        //定义一个规则变量代替对应的标签
        var state = 0
        //记录添加几个图标
        var mIconSize = 0
        //只有动作的情况
        if (triggerCondition == null && (statusConditions == null || statusConditions.isEmpty())) {
            //动作大于maxNum为3反之为1
            state = if (actionList.size > maxNum) 3 else 1
        } else {
            //注意如果有条件的场景必定有触发条件
            val statusSize = statusConditions?.size ?: 0
            val conditionSize = statusSize + 1
            val actionSize = actionList.size
            state = if (conditionSize + actionSize < maxNum) {
                //条件+动作<=maxNum-1
                2
            } else if (conditionSize <= maxConditionNum) {
                //条件+动作>maxNum-1且0<条件<=(maxNum)/2,动作>=(maxNum)/2;
                4
            } else if (actionSize <= maxActionNum) {
                //条件+动作>maxNum-1且条件>(maxNum)/2,动作<(maxNum)/2
                5
            } else {
                //条件+动作>maxNum-1,条件>(maxNum)/2,动作>=(maxNum)/2
                6
            }
        }

        val iconList: MutableList<MeIconBean> = ArrayList()
        if (triggerCondition != null) {
            iconList.add(
                MeIconBean(
                    0, ConditionOrActionIconUtils.getTriggerConditionIcon(triggerCondition.skillId, triggerCondition)
                )
            )
            mIconSize++
        }

        if (statusConditions != null && statusConditions.isNotEmpty()) {
            for (condition in statusConditions) {
                if ((state == 6 || state == 5) && mIconSize == maxConditionNum - 1) { //添加省略号
                    iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                    mIconSize++
                    break
                }
                //if (state == 5 && mIconSize == maxNum - 1 - actionList.size - 1) { //添加省略号
                //    iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                //    mIconSize++
                //    break
                //}
                iconList.add(MeIconBean(0, ConditionOrActionIconUtils.getStatusConditionIcon(condition.skillId)))
                mIconSize++
            }
        }
        if (mIconSize != 0) { //添加箭头
            iconList.add(
                MeIconBean(
                    -2, R.drawable.scene_icon_me_action_link
                )
            )
            mIconSize++
        }
        for (action in actionList) { //添加动作
            if ((state == 3 || state == 4 || state == 6) && mIconSize == maxNum - 1) {
                iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                break
            }
            val currentAction = action.action
            if (currentAction != null) {
                iconList.add(MeIconBean(1, ConditionOrActionIconUtils.getActionIcon(currentAction.skillId)))
            }
            mIconSize++
        }


        return iconList
    }

}
