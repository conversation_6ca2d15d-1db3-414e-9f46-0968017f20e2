package com.dfl.smartscene.bean.customapi;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/01
 * desc :
 * version: 1.0
 */
public class DataEventSceneBean {
    private String scenario_id;//场景ID
    private Long scenario_name_edit_time;//场景名称修改时间
    private String scenario_name;//场景名称
    private Long scenario_type_edit_time;//场景类型修改时间
    private Boolean scenario_type;//是否自动执行
    private Long trigger_condition_edit_time;//触发条件修改时间
    private String trigger_condition_type;//触发条件类型
    private String status_condition_type;//状态条件类型
    private String action_type; //执行动作类型
    private String trigger_condition_detail;//触发条件详情
    private Long status_condition_edit_time;//状态条件时间
    private String status_condition_detail;//状态条件详情
    private Long action_edit_time;//添加和编辑动作的时间取最后一次
    private String action_detail;//动作详情
    private String execution_switch;//自动执行开关状态取开关情况：开/关/无
    private Long inquiry_switch_edit_time;//自动执行前询问开关，取修改时间
    private String inquiry_switch;//自动执行前询问开关状态取开关情况：开/关/无
    private Boolean try_click;//点击是否试用
    private Long try_click_time;//点击"试用"的时间
    private Boolean save_click;//点击保存
    private Long save_click_time;//点击保存时间

    private Integer executeFrequency;

    public Integer getExecute_frequency() {
        return executeFrequency;
    }

    public void setExecute_frequency(Integer execute_frequency) {
        this.executeFrequency = execute_frequency;
    }

    public DataEventSceneBean() {
    }

    public String getScenario_id() {
        return scenario_id;
    }

    public void setScenario_id(String scenario_id) {
        this.scenario_id = scenario_id;
    }

    public Long getScenario_name_edit_time() {
        return scenario_name_edit_time;
    }

    public void setScenario_name_edit_time(Long scenario_name_edit_time) {
        this.scenario_name_edit_time = scenario_name_edit_time;
    }

    public String getScenario_name() {
        return scenario_name;
    }

    public void setScenario_name(String scenario_name) {
        this.scenario_name = scenario_name;
    }

    public Long getScenario_type_edit_time() {
        return scenario_type_edit_time;
    }

    public void setScenario_type_edit_time(Long scenario_type_edit_time) {
        this.scenario_type_edit_time = scenario_type_edit_time;
    }

    public Boolean getScenario_type() {
        return scenario_type;
    }

    public void setScenario_type(Boolean scenario_type) {
        this.scenario_type = scenario_type;
    }

    public Long getTrigger_condition_edit_time() {
        return trigger_condition_edit_time;
    }

    public void setTrigger_condition_edit_time(Long trigger_condition_edit_time) {
        this.trigger_condition_edit_time = trigger_condition_edit_time;
    }

    public String getTrigger_condition_detail() {
        return trigger_condition_detail;
    }

    public void setTrigger_condition_detail(String trigger_condition_detail) {
        this.trigger_condition_detail = trigger_condition_detail;
    }

    public Long getStatus_condition_edit_time() {
        return status_condition_edit_time;
    }

    public void setStatus_condition_edit_time(Long status_condition_edit_time) {
        this.status_condition_edit_time = status_condition_edit_time;
    }

    public String getStatus_condition_detail() {
        return status_condition_detail;
    }

    public void setStatus_condition_detail(String status_condition_detail) {
        this.status_condition_detail = status_condition_detail;
    }

    public Long getAction_edit_time() {
        return action_edit_time;
    }

    public void setAction_edit_time(Long action_edit_time) {
        this.action_edit_time = action_edit_time;
    }

    public String getAction_detail() {
        return action_detail;
    }

    public void setAction_detail(String action_detail) {
        this.action_detail = action_detail;
    }

    public Long getInquiry_switch_edit_time() {
        return inquiry_switch_edit_time;
    }

    public void setInquiry_switch_edit_time(Long inquiry_switch_edit_time) {
        this.inquiry_switch_edit_time = inquiry_switch_edit_time;
    }

    public String getInquiry_switch() {
        return inquiry_switch;
    }

    public void setInquiry_switch(String inquiry_switch) {
        this.inquiry_switch = inquiry_switch;
    }

    public String getExecution_switch() {
        return execution_switch;
    }

    public void setExecution_switch(String execution_switch) {
        this.execution_switch = execution_switch;
    }

    public Boolean getTry_click() {
        return try_click;
    }

    public void setTry_click(Boolean try_click) {
        this.try_click = try_click;
    }

    public Long getTry_click_time() {
        return try_click_time;
    }

    public void setTry_click_time(Long try_click_time) {
        this.try_click_time = try_click_time;
    }

    public Boolean getSave_click() {
        return save_click;
    }

    public void setSave_click(Boolean save_click) {
        this.save_click = save_click;
    }

    public Long getSave_click_time() {
        return save_click_time;
    }

    public void setSave_click_time(Long save_click_time) {
        this.save_click_time = save_click_time;
    }

    public String getTrigger_condition_type() {
        return trigger_condition_type;
    }

    public void setTrigger_condition_type(String trigger_condition_type) {
        this.trigger_condition_type = trigger_condition_type;
    }

    public String getStatus_condition_type() {
        return status_condition_type;
    }

    public void setStatus_condition_type(String status_condition_type) {
        this.status_condition_type = status_condition_type;
    }

    public String getAction_type() {
        return action_type;
    }

    public void setAction_type(String action_type) {
        this.action_type = action_type;
    }


}
