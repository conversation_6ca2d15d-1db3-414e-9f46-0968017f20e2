package com.dfl.smartscene.bean.mechanism;

import java.util.List;

/**
 * Created by 钟文祥 on 2023/6/27.
 * Describer:
 * 1.本地和远程数据操作回调
 * 2.remote 和 local 的DataSource 的 T 需要一致
 */
public interface IBaseDataSourceCallBack<T> {

    /**
     * 远程获取数据
     */
    void loadDataFromWeb(ILoadDataCallBack<T> callBack);

    /**
     * 本地获取数据
     */
    T loadDataFromLocal();

    /**
     * 删除本地
     */
    int deleteAll2Local();

    /**
     * 本地新增
     */
    List<Long> add2Local(T values);
}
