package com.dfl.smartscene.bean.community.reponse

import androidx.room.*
import com.dfl.android.common.util.GsonUtils
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/03/04
 * desc : 社区搜素最近搜索
 * version: 1.0
 */
@Entity(tableName = "recent_search")
@TypeConverters(CommunityRecentSearchResponse.RecentSearchListConverters::class)
data class CommunityRecentSearchResponse(
	@SerializedName("words")
	var recentSearchList: List<String>? = emptyList(),
	@PrimaryKey(autoGenerate = false)
	var id: Int = 1,
) {
	class RecentSearchListConverters {
		@TypeConverter
		fun stringToRecentSearchList(json: String): List<String> {
			val type = object : TypeToken<List<String>>() {}.type
			return GsonUtils.fromJson(json, type) ?: emptyList()
		}

		@TypeConverter
		fun recentSearchListToString(obj: List<String>?): String {
			return GsonUtils.toJson(obj) ?: ""
		}
	}
}