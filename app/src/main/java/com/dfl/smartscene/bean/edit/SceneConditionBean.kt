package com.dfl.smartscene.bean.edit

import android.os.Parcelable
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.iauto.scenarioadapter.ScenarioInfo
import kotlinx.parcelize.Parcelize

@Parcelize
class SceneConditionBean(
	val title: String?,
	val condition: ScenarioInfo.Condition?,
	val conditionType: Int,
	val type: ConditionType,
	val conditionIcon: Int,
	val item: Int,
	val isActive: Boolean = true //条件能力是否可用
) : Parcelable, MultiItemEntity {
	companion object {
		const val ADD_CONDITION_ITEM = 0
		const val ADD_CONDITION_CONTENT_ITEM = 1

		const val ITEM_TITLE = 2
		const val ITEM_CONTENT = 3
	}

	enum class ConditionType {
		STATUS_CONDITION, TRIGGER_CONDITION
	}

	override val itemType: Int
		get() = item
}