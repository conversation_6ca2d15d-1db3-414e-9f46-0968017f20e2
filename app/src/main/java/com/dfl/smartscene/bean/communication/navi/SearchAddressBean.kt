//package com.dfl.smartscene.bean.communication.navi
//
///**
// * author : wbwangws
// * e-mail : <EMAIL>
// * time : 2022/10/21
// * desc :动作->去某地，途径某地点击搜索框输入内容获取到的CustomApi返回值实体类
// * version: 1.0
// */
//data class SearchAddressBean(
//    val poiResult: PoiResult
//)
//
//data class PoiResult(
//    val Citysuggestion: CitySuggestion, //推荐城市
//    val Count: Int, //搜索结果返回最大个数
//    val Pois: List<Poi>, //poi结果列表
//    val categories: List<Category> //分类
//)
//
//data class CitySuggestion(
//    val Citycount: Int,
//    val SuggestionCityDetail: List<SuggestionCityDetail>
//)
//
//data class Poi(
//    val Address: String,
//    val Latitude: Double,
//    val Name: String,
//    val Poiid: String,
//    val Tel: String,
//    val Typecode: String,
//    val childPoiList: List<ChildPoi>,
//    val distaceToSearchLocation: Int,
//    val distance: Int,
//    val enteryList: List<EnterX>,
//    val longitude: Double
//)
//
//data class Category(
//    val categoryItems: List<CategoryItem>,
//    val checkedvalue: String,
//    val ctype: String,
//    val name: String
//)
//
//data class SuggestionCityDetail(
//    val Cityname: String,
//    val Citynum: Int
//)
//
//data class ChildPoi(
//    val Address: String,
//    val Latitude: Double,
//    val Name: String,
//    val Poiid: String,
//    val Tel: String,
//    val Typecode: String,
//    val distance: Int,
//    val enteryList: List<EnterX>,
//    val longitude: Double
//)
//
//data class EnterX(
//    val entry_latitude: Double,
//    val entry_longitude: Double
//)
//
//data class CategoryItem(
//    val categoryItems: List<CategoryItemX>,
//    val name: String,
//    val value: String
//)
//
//data class CategoryItemX(
//    val name: String,
//    val value: String
//)