//package com.dfl.smartscene.basemodule.entity.navi
//
//import java.io.Serializable
//
///**
// * author : wbwangws
// * e-mail : <EMAIL>
// * time : 2022/12/21
// * desc :导航路线规划
// * version: 1.0
// */
//data class NaviRoutePlanBean(
//    var startPOI: RoutePoiBean? = null,//非必选
//    var endPoi: RoutePoiBean? = null,//终点,必选
//    var midPois: List<RoutePoiBean?>? = null,//途经点，非必选
//    var routePref: Int?//偏好组合,非必选
//):Serializable