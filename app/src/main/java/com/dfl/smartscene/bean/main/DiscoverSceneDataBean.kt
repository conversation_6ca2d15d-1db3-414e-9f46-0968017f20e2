package com.dfl.smartscene.bean.main

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * author : wswang
 * e-mail : <EMAIL>
 * time   : 2022/08/05
 * desc   : 发现场景列表模拟数据实体类
 * version: 1.0
 */
@Parcelize
class DiscoverSceneDataBean : ArrayList<DiscoverSceneDataBeanItem>(), Parcelable

@Parcelize
data class DiscoverSceneDataBeanItem(
    val auto_execute: Int,
    val conditions: List<Condition>,
    val desc: String,
    val name: String,
    val scenario_id: String,
    val second_ask: Int,
    val sequence: Sequence,
    val timestamp: Int,
    val trigger_con: TriggerCon,
    val version: Int
) : Parcelable

@Parcelize
data class Condition(
    val args: List<Arg>,
    val class_name: String,
    val desc: String,
    val id: Int,
    val skill_id: String
) : Parcelable

@Parcelize
data class Sequence(
    val items: List<Item>
) : Parcelable

@Parcelize
data class TriggerCon(
    val args: List<Arg>,
    val class_name: String,
    val desc: String,
    val id: Int,
    val skill_id: String
) : Parcelable

@Parcelize
data class Arg(
    val desc: String,
    val name: String,
    val type: Int,
    val value: String
) : Parcelable

@Parcelize
data class Item(
    val id: Int,
    val item: ItemX,
    val type: Int
) : Parcelable

@Parcelize
data class ItemX(
    val args: List<Arg>,
    val class_name: String,
    val desc: String,
    val skill_id: String
) : Parcelable