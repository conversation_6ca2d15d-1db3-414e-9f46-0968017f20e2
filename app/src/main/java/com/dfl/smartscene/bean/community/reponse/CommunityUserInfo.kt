package com.dfl.smartscene.bean.community.reponse

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import kotlinx.parcelize.Parcelize

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2023/10/30
 * desc: 社区场景用户账号信息
 * version:1.0
 */
@Parcelize
@Entity(tableName = "CommunityUserInfo")
@TypeConverters(CommunityListResponse.ConvertersCommunitySceneInfo::class)
data class CommunityUserInfo(
    /**
     * 用户头像URL
     */
    val userAvatar: String?="",
    /**用户是否关注，三种状态:0未关注，1已关注，2用户自己的场景*/
    var userSubscribe: Int = 0,
    /**
     * 用户获赞数
     */
    var likeNum: Int=0,
    /**
     * 用户的粉丝数
     */
    var userFanNum: Int=0,
    /**
     * 用户唯一标识
     */
    @PrimaryKey(autoGenerate = false)
    val uuid: String="",
    /**
     * 用户名称
     */
    val userName: String?="",
    /**
     * 用户的关注数
     */
    val userSubScribeNum: Int=0,
    /**
     * 用户上传场景数
     */
    val userUploadNum: Int=0,
    /**
     * 用户发布的场景列表详情
     */
    val sceneList: List<CommunitySceneInfo> = (emptyList<CommunitySceneInfo>()),
) : Parcelable {
}

