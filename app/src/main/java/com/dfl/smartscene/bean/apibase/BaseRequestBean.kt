package com.dfl.smartscene.bean.apibase

import com.dfl.smartscene.communication.UserCenterManager.userLoginStatus
import com.dfl.smartscene.customapi.CarInfoManager

/**
 *Created by 钟文祥 on 2023/11/16.
 *Describer: 请求类基类
 */
abstract class BaseRequestBean {
    var uuid: String = getUUID()
    var vin: String = getVIN()
    var port: String = "da" //操作端 车机:da 手机:app
    var appCode: String = "nissan" //日产：nissan ， 启辰：venucia


    companion object {
        fun getUUID(): String {
            return if (userLoginStatus != null) userLoginStatus!!.uuid else ""
        }
    }


    fun getVIN(): String {
        return CarInfoManager.getVinWithDefault()
    }

}