package com.dfl.smartscene.bean.communication.navi

import java.io.Serializable

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/15
 * desc :
 * version: 1.0
 */
data class RequestMapBean<T>(
    val protocolId: Int, //具体的协议ID
    val requestCode: String?, //请求码，保证唯一性，用来标记 区分每一次请求。对应的应答消息的responseCode和requestCode是一致的
    val responseCode: String?, //应答求码用来标记区分每一次请求对应的应答消息.应答消息的responseCode和对应的requestCode是一致的
    val versionName: String, //协议版本号，本次请求协议的版本号
    val requestAuthor: String, //发送方标志，用来区分请求的发送方，建议设置成应用的包名
    val messageType: String, //消息类型 request：请求的消息  response：响应的消息  dispatch：主动透出的消息
    val data: T //见具体协议 data 数据说明
) : Serializable {
    companion object {
        const val VERSION_NAME = "v_1.0" //地图版本请求协议版本号
        const val SEARCH_KEY_PROTOCOL_ID = 20001 //地图关键字搜索ID
        const val PRE_SEARCH_KEY_PROTOCOL_ID = 20006 //发起导航预搜索
        const val LOCAL_INFO_PROTOCOL_ID = 30015 //获取/透出车位当前城市信息ID
        const val ROUTE_PLAN_PROTOCOL_ID = 30001 //发起路线规划ID
        const val START_NAVI_PROTOCOL_ID = 30003 //开始导航ID
        const val OPEN_MAP_PROTOCOL_ID = 10004 //打开地图
    }
}

enum class RequestMessageType(val content: String) {
    REQUEST("request"), RESPONSE("response"), DISPATCH("dispatch")
}

data class KeywordBean(
    val keyWord: String //搜索的关键字
) : Serializable
