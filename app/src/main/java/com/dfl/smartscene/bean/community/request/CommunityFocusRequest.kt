package com.dfl.smartscene.bean.community.request

import com.dfl.smartscene.bean.apibase.BaseRequestBean

/**
 *Created by 钟文祥 on 2023/11/23.
 *Describer: 社区场景 关注或者取消关注  请求类
 */
data class CommunityFocusRequest(
    var subscribedUuid: String, //被关注或者被取消关注的用户uuid
    var isSubscribe: Boolean //用户是否被关注
) : BaseRequestBean() {
    companion object {
        /**
         * 是否以及关注
         * @param userSubscribe 关注状态参数
         * @return
         */
        fun isSubscribed(userSubscribe: Int): Boolean {
            return userSubscribe == SUBSCRIBED
        }

        /**未关注*/
        const val UNSUBSCRIBED = 0

        /**关注*/
        const val SUBSCRIBED = 1

        /**用户自身*/
        const val SELF = 2
    }
}