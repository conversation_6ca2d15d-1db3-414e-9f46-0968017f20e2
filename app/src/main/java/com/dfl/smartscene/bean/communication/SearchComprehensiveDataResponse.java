package com.dfl.smartscene.bean.communication;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created with Android Studio.
 * Description:
 *
 * @author: <PERSON><PERSON><PERSON>
 * @CreateDate: 2022/12/27
 */
public class SearchComprehensiveDataResponse implements Parcelable {
    private String responseData;

    public SearchComprehensiveDataResponse(String responseData) {
        this.responseData = responseData;
    }

    protected SearchComprehensiveDataResponse(Parcel in) {
        responseData = in.readString();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(responseData);
    }

    public static final Creator<SearchComprehensiveDataResponse> CREATOR = new Creator<SearchComprehensiveDataResponse>() {
        @Override
        public SearchComprehensiveDataResponse createFromParcel(Parcel in) {
            return new SearchComprehensiveDataResponse(in);
        }

        @Override
        public SearchComprehensiveDataResponse[] newArray(int size) {
            return new SearchComprehensiveDataResponse[size];
        }
    };

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    @Override
    public String toString() {
        return "SearchComprehensiveDataResponse{" +
                "responseData='" + responseData + '\'' +
                '}';
    }
}
