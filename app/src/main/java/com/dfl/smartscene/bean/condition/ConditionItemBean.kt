package com.dfl.smartscene.bean.condition

import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/22
 * desc : 条件显示的item数据 二级列表
 * version: 1.0
 */
data class ConditionItemBean<T>(
    /**adapter专用类型,是小标题 还是内容栏 类型*/
    val item: Int,
    /*** 条件名称 */
    val conditionName: String,
    /*** 条件图标 */
    val conditionIcon: Int,
    /** 条件skill id*/
    val conditionSkillId: ArrayList<Int>?,
    /** 条件类型,触发、状态的标志类型*/
    val conditionType: T?,
    /** true： 界面没有同类型的item则设置能用，false：界面有同类型的item则设置不能用*/
    var isEnable: Boolean? = true,
    /** 标题的标志类型 */
    val conditionItemType: T? = null,
) : MultiItemEntity {

    override val itemType: Int
        get() = item

    companion object {
        const val SKILL_TITLE = 0
        const val SKILL_CONTENT = 1
    }
}

//---------一下都是 触发和状态的标志类型------------
enum class TriggerTime {
    /**时间点*/
    TIME_POINT,

    /**日出日落*/
    DAY_STATUS
}

enum class TriggerDoor {
    /**全车门锁*/
    ALL_DOOR_LOCK, UPPER_LEFT_DOOR, UPPER_RIGHT_DOOR, LOWER_LEFT_DOOR, LOWER_RIGHT_DOOR,

    /**
     * 左侧儿童锁
     */
    LEFT_CHILD_LOCK,

    /**
     * 右侧儿童锁
     */
    RIGHT_CHILD_LOCK,
    DOOR_ANY_OPEN,
}

enum class TriggerCharger {
    CHARGER, CHARGER_END_TIME, DISCHARGE
}

enum class TriggerLighting {
    FAR_LIGHT, NEAR_LIGHT, FOG_LIGHT
}

enum class StatusLighting {
    FAR_LIGHT, NEAR_LIGHT, FOG_LIGHT
}

enum class TriggerSeat {
    LEFT_SEAT_STATE, RIGHT_SEAT_STATE, LEFT_BELT_STATE, RIGHT_BELT_STATE,
    BACK_LEFT_BELT_STATE, BACK_MIDDLE_BELT_STATE, BACK_RIGHT_BELT_STATE
}

enum class TriggerEnvironment {
    /**
     * 车内温度
     */
    CAR_INNER_TEMP,

    /**
     * 车外温度
     */
    CAR_OUTSIDE_TEMP,
}

enum class TriggerLocation {
    LOCATION_VEHICLE_ARRIVAL, LOCATION_VEHICLE_DEPARTURE
}

enum class TriggerOMS {
    OMS_POSITION_CHILDREN, OMS_POSITION_SOMEONE, OMS_POSITION_GENDER, OMS_POSITION_EXPRESSION, OMS_POSITION_BEHAVIOR
}

enum class StatusOMS {
    OMS_POSITION_CHILDREN, OMS_POSITION_SOMEONE, OMS_POSITION_GENDER, OMS_POSITION_EXPRESSION, OMS_POSITION_BEHAVIOR
}

enum class StatusNavigation {
    NAVIGATION_EXPECTED_ARRIVAL_TIME, NAVIGATION_EXPECTED_ARRIVAL_DISTANCE
}

enum class StatusLocation {
    LOCATION_VEHICLE_ARRIVAL, LOCATION_VEHICLE_DEPARTURE
}

enum class StatusSmartDevice {
    AIR_QUALITY, RIDING_STATUS, TITLE_AIR_PURIFIER, TITLE_CHILD_SEAT
}


enum class TriggerDrive {
    BATTERY_LIFE, BATTERY_POWER, DRIVE_SPEED, DRIVE_TIME, DRIVE_DISTANCE, DRIVE_GEAR
}

enum class StatusTime {
    TIME_INTERVAL, TIME_CYCLE
}

enum class StatusSeat {
    LEFT_SEAT_STATE, RIGHT_SEAT_STATE, LEFT_BELT_STATE, RIGHT_BELT_STATE,
    BACK_LEFT_BELT_STATE, BACK_MIDDLE_BELT_STATE, BACK_RIGHT_BELT_STATE
}

enum class StatusOther {
    TIRE_PRESSURE
}

enum class StatusLink {
    BLUETOOTH, HOTSPOT, WLAN, MOBILE
}

enum class StatusEnvironment {
    /**
     *空气质量
     */
    AIR_QUALITY,

    /**
     * 天气预报
     */
    WEATHER_FORECAST,

    /**
     * 车外天气
     */
    OUTSIDE_WEATHER,

    /**
     * 车内湿度
     */
    HUMIDITY_IN_CAR
}

enum class StatusDoors {
    ALL_DOOR_LOCK, LEFT_UP_WINDOW, RIGHT_UP_WINDOW, LEFT_LOW_WINDOW, RIGHT_LOW_WINDOW,

    /**
     * 后背门,电动行李箱
     */
    DOOR_BACK,

    /**
     * 行李箱
     */
    TRUNK,

    /**
     * 左侧儿童锁
     */
    LEFT_CHILD_LOCK,

    /**
     * 右侧儿童锁
     */
    RIGHT_CHILD_LOCK,

    //标题+item
    CAR_WINDOW_ITEM, CAR_DOOR_ITEM
}