package com.dfl.smartscene.bean.community.datasource;

import com.dfl.smartscene.apiweb.service.CommunityService;
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.smartscene.bean.mechanism.BaseDataSource;
import com.dfl.smartscene.bean.mechanism.IBaseDataSourceCallBack;
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack;
import com.dfl.smartscene.room.DbManager;
import com.dfl.smartscene.room.dao.CommunityListResponseDao;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by 钟文祥 on 2023/11/18.
 * Describer: 社区场景 轮播图 数据来源
 */
public class CommunityDataSource
        extends BaseDataSource
        implements IBaseDataSourceCallBack<CommunityListResponse> {

    private CommunityListResponseDao mDao;
    private CommunityTabType mTabType;
    private int pageNum;


    public static CommunityDataSource init(CommunityTabType mTabType, int pageNum) {
        CommunityDataSource source = new CommunityDataSource();
        source.mDao = DbManager.INSTANCE.getDBMaster().CommunityListResponseDao();
        source.mTabType = mTabType;
        source.pageNum = pageNum;
        return source;
    }

    @Override
    public void loadDataFromWeb(ILoadDataCallBack<CommunityListResponse> callBack) {
        if (mTabType == CommunityTabType.Banner) {
            subscribeGoData(CommunityService.INSTANCE.getBannerList(), callBack);
        } else {
            subscribeGoData(CommunityService.INSTANCE.getCommunityList(mTabType, pageNum),
                    callBack);
        }
    }

    @Override
    public <T> void modifyValue(T value) {
        ((CommunityListResponse) value).getRows().forEach(communitySceneInfo ->
                communitySceneInfo.convert()
        );
    }

    @Override
    public CommunityListResponse loadDataFromLocal() {
        return mDao.findFirstPageByTabId(mTabType.getValue());
    }

    @Override
    public int deleteAll2Local() {
        return mDao.deleteAllByTabId(mTabType.getValue());
    }

    @Override
    public List<Long> add2Local(CommunityListResponse values) {
        values.setTabId(mTabType.getValue());
        mDao.insert(values);
        return new ArrayList<>();
    }
}
