package com.dfl.smartscene.bean.communication;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;


/**
 * auther ly-zhouxyi
 * date 2021/4/12
 * time 15:25
 */
public class ItemBean implements Parcelable {
    public static final Creator<ItemBean> CREATOR = new Creator<ItemBean>() {
        @Override
        public ItemBean createFromParcel(Parcel in) {
            return new ItemBean(in);
        }

        @Override
        public ItemBean[] newArray(int size) {
            return new ItemBean[size];
        }
    };
    private int type;
    private long cardId;
    private String name;
    private String imageUrl;
    private int groupType;
    private int contentType;
    private int rankId;
    private long cardSegmentId;
    private String albumId = "";
    private String albumName = "";
    private String playListId = "";
    private long trackId;
    private String songId;
    private long playId;
    private String code;
    private long playCount;
    private String playUrl;
    private String playingProgramName;
    private String lastUpdateTrackName;
    private boolean isPlaying;
    private int position = -1;
    private long radioId;
    private String shortIntro;
    private String artist;
    private String artistId;
    private String intro;
    private String kind;
    private String categoryId;
    private String playlistExtraId;
    private int mediaSource;
    private int albumUpdateCount;
    private boolean isSubscribe;
    private String parent;
    private boolean isPlayHistory;
    private boolean isSelfBuild;
    private boolean isVip;
    private String songExtraId;
    private String keyWord;
    private String sort = "asc";
    private int offset = 0;
    private int limit = 20;
    private String tag;
    private String weekday;
    private String month;
    private double longitude;
    private double latitude;
    private int playState;
    private boolean selected;
    private int totalCount;
    private String startTime;
    private String endTime;
    private long playingProgramId;
    private int freeTrackCount;
    private boolean isPaid;
    private int isFinished;
    private boolean isVipFree;
    private double price;
    private String priceUnit;
    private boolean canDownload;
    private String downloadUrl;
    private int commentCount;
    private long createdAt;
    private int duration;
    private int favoriteCount;
    private int orderNum;
    private boolean isAuthorized;
    private boolean isFree;
    private String backPlayUrl;
    private boolean isHaveSubItem;
    private List<ItemBean> subItems;
    private String payUrl;
    private String payOrderNo;
    private String lyric;
    private long lyricOffset;
    /**
     * usb音源歌曲所在文件夹名称
     */
    private String folderName;
    /**
     * usb音源歌曲所在父文件夹名称
     */
    private String parentFolderName;
    /**
     * usb音源歌曲类型，0：文件夹，1：歌曲
     */
    private int usbSongType;
    private String accId;
    private String songName;
    private String singerId;
    private String singerName;
    private String albumImg;
    private String albumImgLarge;
    private int bitRate;
    private boolean hasOrigin;
    private boolean hasPitch;
    private boolean isHQ;
    private boolean hasMv;
    private long weight;
    private int songHot;
    private int playableCode;
    private String freeToken;
    private String formSource;
    private String fromSourceId;
    private String accompanyId;
    private String opusId;
    private String opusName;
    private int isPrivate = 1;
    private int listenNum;
    private float score;
    private float averageScore;
    private String grade;
    private String createTime;
    private long updateTime;
    private String opusHash;
    private String playerId;
    private String nickName;
    private String groupName;
    private String groupId;
    private String topId;
    private String topName;
    private String headerUrl;
    private List<ItemBean> letterSingerListList;
    private String letter;
    private long productItemId;
    private int durationDays;
    private boolean isAutoRenew;
    private String productName;
    private String productPrice;
    private String showPrice;
    private String productDiscountedPrice;
    private String guideDesc;
    private String activityDesc;
    /**
     * 付费类型，专辑为免费专辑时该字段固定为0；专辑为付费专辑时， 1为按单个声音付费 ，2为按整张专辑付费
     */
    private int priceTypeId;
    /**
     * 播放到多少秒
     */
    private double playedSecs;
    private int timeline;
    private String mvId;
    private String imageUri;
    private String VideoThumbnailsId;
    private String displayName;
    private String isMusic;
    private String size;
    private String folderMediaId;
    /*
     * 播放mv
     */
    private String mvName;
    private String mvImg;
    private String authorName;
    private String tagId;
    private String tagName;
    private String isPublish;
    private String parentId;
    private List<ItemBean> tagsList;
    private String photoUrls;
    private String singerImg;
    private String categoryName;
    private String roomId;
    private String programId;
    private String status;
    private int payType;
    private int remainTimes = -1;
    private boolean playable;
    private int mediaType;

    public ItemBean() {
    }

    protected ItemBean(Parcel in) {
        roomId = in.readString();
        programId = in.readString();
        status = in.readString();
        payType = in.readInt();

        categoryName = in.readString();
        photoUrls = in.readString();
        singerImg = in.readString();

        mvName = in.readString();
        mvImg = in.readString();
        authorName = in.readString();
        tagId = in.readString();
        tagName = in.readString();
        isPublish = in.readString();
        parentId = in.readString();
        tagsList = in.createTypedArrayList(ItemBean.CREATOR);

        type = in.readInt();
        cardId = in.readLong();
        name = in.readString();
        imageUrl = in.readString();
        groupType = in.readInt();
        contentType = in.readInt();
        rankId = in.readInt();
        cardSegmentId = in.readLong();
        albumId = in.readString();
        albumName = in.readString();
        playListId = in.readString();
        trackId = in.readLong();
        songId = in.readString();
        playId = in.readLong();
        code = in.readString();
        playCount = in.readLong();
        playUrl = in.readString();
        playingProgramName = in.readString();
        lastUpdateTrackName = in.readString();
        isPlaying = in.readByte() != 0;
        position = in.readInt();
        radioId = in.readLong();
        shortIntro = in.readString();
        artist = in.readString();
        artistId = in.readString();
        intro = in.readString();
        kind = in.readString();
        categoryId = in.readString();
        playlistExtraId = in.readString();
        mediaSource = in.readInt();
        albumUpdateCount = in.readInt();
        isSubscribe = in.readByte() != 0;
        parent = in.readString();
        isPlayHistory = in.readByte() != 0;
        isSelfBuild = in.readByte() != 0;
        isVip = in.readByte() != 0;
        songExtraId = in.readString();
        keyWord = in.readString();
        sort = in.readString();
        offset = in.readInt();
        limit = in.readInt();
        tag = in.readString();
        weekday = in.readString();
        month = in.readString();
        longitude = in.readDouble();
        latitude = in.readDouble();
        playState = in.readInt();
        selected = in.readByte() != 0;
        totalCount = in.readInt();
        startTime = in.readString();
        endTime = in.readString();
        playingProgramId = in.readLong();
        freeTrackCount = in.readInt();
        isPaid = in.readByte() != 0;
        isFinished = in.readInt();
        isVipFree = in.readByte() != 0;
        price = in.readDouble();
        priceUnit = in.readString();
        canDownload = in.readByte() != 0;
        downloadUrl = in.readString();
        commentCount = in.readInt();
        createdAt = in.readLong();
        duration = in.readInt();
        favoriteCount = in.readInt();
        orderNum = in.readInt();
        isAuthorized = in.readByte() != 0;
        isFree = in.readByte() != 0;
        backPlayUrl = in.readString();
        isHaveSubItem = in.readByte() != 0;
        subItems = in.createTypedArrayList(ItemBean.CREATOR);
        payUrl = in.readString();
        payOrderNo = in.readString();
        lyric = in.readString();
        lyricOffset = in.readLong();
        folderName = in.readString();
        parentFolderName = in.readString();
        usbSongType = in.readInt();
        accId = in.readString();
        songName = in.readString();
        singerId = in.readString();
        singerName = in.readString();
        albumImg = in.readString();
        albumImgLarge = in.readString();
        bitRate = in.readInt();
        hasOrigin = in.readByte() != 0;
        hasPitch = in.readByte() != 0;
        isHQ = in.readByte() != 0;
        hasMv = in.readByte() != 0;
        weight = in.readLong();
        songHot = in.readInt();
        playableCode = in.readInt();
        freeToken = in.readString();
        formSource = in.readString();
        fromSourceId = in.readString();
        accompanyId = in.readString();
        opusId = in.readString();
        opusName = in.readString();
        isPrivate = in.readInt();
        listenNum = in.readInt();
        score = in.readFloat();
        averageScore = in.readFloat();
        grade = in.readString();
        createTime = in.readString();
        updateTime = in.readLong();
        opusHash = in.readString();
        playerId = in.readString();
        nickName = in.readString();
        groupName = in.readString();
        groupId = in.readString();
        topId = in.readString();
        topName = in.readString();
        headerUrl = in.readString();
        letterSingerListList = in.createTypedArrayList(ItemBean.CREATOR);
        letter = in.readString();
        productItemId = in.readLong();
        durationDays = in.readInt();
        isAutoRenew = in.readByte() != 0;
        productName = in.readString();
        productPrice = in.readString();
        showPrice = in.readString();
        productDiscountedPrice = in.readString();
        guideDesc = in.readString();
        activityDesc = in.readString();
        priceTypeId = in.readInt();
        playedSecs = in.readDouble();
        timeline = in.readInt();
        mvId = in.readString();
        imageUri = in.readString();
        VideoThumbnailsId = in.readString();
        displayName = in.readString();
        isMusic = in.readString();
        size = in.readString();
        playable = in.readByte() != 0;
        mediaType = in.readInt();
        folderMediaId = in.readString();
        remainTimes = in.readInt();
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getProgramId() {
        return programId;
    }

    public void setProgramId(String programId) {
        this.programId = programId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(String photoUrls) {
        this.photoUrls = photoUrls;
    }

    public String getSingerImg() {
        return singerImg;
    }

    public void setSingerImg(String singerImg) {
        this.singerImg = singerImg;
    }

    public String getMvName() {
        return mvName;
    }

    public void setMvName(String mvName) {
        this.mvName = mvName;
    }

    public String getMvImg() {
        return mvImg;
    }

    public void setMvImg(String mvImg) {
        this.mvImg = mvImg;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(String isPublish) {
        this.isPublish = isPublish;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<ItemBean> getTagsList() {
        return tagsList;
    }

    public void setTagsList(List<ItemBean> tagsList) {
        this.tagsList = tagsList;
    }

    public int getRemainTimes() {
        return remainTimes;
    }

    public void setRemainTimes(int remainTimes) {
        this.remainTimes = remainTimes;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(roomId);
        dest.writeString(programId);
        dest.writeString(status);
        dest.writeInt(payType);

        dest.writeString(categoryName);
        dest.writeString(photoUrls);
        dest.writeString(singerImg);

        dest.writeString(mvName);
        dest.writeString(mvImg);
        dest.writeString(authorName);
        dest.writeString(tagId);
        dest.writeString(tagName);
        dest.writeString(isPublish);
        dest.writeString(parentId);
        dest.writeTypedList(tagsList);

        dest.writeInt(type);
        dest.writeLong(cardId);
        dest.writeString(name);
        dest.writeString(imageUrl);
        dest.writeInt(groupType);
        dest.writeInt(contentType);
        dest.writeInt(rankId);
        dest.writeLong(cardSegmentId);
        dest.writeString(albumId);
        dest.writeString(albumName);
        dest.writeString(playListId);
        dest.writeLong(trackId);
        dest.writeString(songId);
        dest.writeLong(playId);
        dest.writeString(code);
        dest.writeLong(playCount);
        dest.writeString(playUrl);
        dest.writeString(playingProgramName);
        dest.writeString(lastUpdateTrackName);
        dest.writeByte((byte) (isPlaying ? 1 : 0));
        dest.writeInt(position);
        dest.writeLong(radioId);
        dest.writeString(shortIntro);
        dest.writeString(artist);
        dest.writeString(artistId);
        dest.writeString(intro);
        dest.writeString(kind);
        dest.writeString(categoryId);
        dest.writeString(playlistExtraId);
        dest.writeInt(mediaSource);
        dest.writeInt(albumUpdateCount);
        dest.writeByte((byte) (isSubscribe ? 1 : 0));
        dest.writeString(parent);
        dest.writeByte((byte) (isPlayHistory ? 1 : 0));
        dest.writeByte((byte) (isSelfBuild ? 1 : 0));
        dest.writeByte((byte) (isVip ? 1 : 0));
        dest.writeString(songExtraId);
        dest.writeString(keyWord);
        dest.writeString(sort);
        dest.writeInt(offset);
        dest.writeInt(limit);
        dest.writeString(tag);
        dest.writeString(weekday);
        dest.writeString(month);
        dest.writeDouble(longitude);
        dest.writeDouble(latitude);
        dest.writeInt(playState);
        dest.writeByte((byte) (selected ? 1 : 0));
        dest.writeInt(totalCount);
        dest.writeString(startTime);
        dest.writeString(endTime);
        dest.writeLong(playingProgramId);
        dest.writeInt(freeTrackCount);
        dest.writeByte((byte) (isPaid ? 1 : 0));
        dest.writeInt(isFinished);
        dest.writeByte((byte) (isVipFree ? 1 : 0));
        dest.writeDouble(price);
        dest.writeString(priceUnit);
        dest.writeByte((byte) (canDownload ? 1 : 0));
        dest.writeString(downloadUrl);
        dest.writeInt(commentCount);
        dest.writeLong(createdAt);
        dest.writeInt(duration);
        dest.writeInt(favoriteCount);
        dest.writeInt(orderNum);
        dest.writeByte((byte) (isAuthorized ? 1 : 0));
        dest.writeByte((byte) (isFree ? 1 : 0));
        dest.writeString(backPlayUrl);
        dest.writeByte((byte) (isHaveSubItem ? 1 : 0));
        dest.writeTypedList(subItems);
        dest.writeString(payUrl);
        dest.writeString(payOrderNo);
        dest.writeString(lyric);
        dest.writeLong(lyricOffset);
        dest.writeString(folderName);
        dest.writeString(parentFolderName);
        dest.writeInt(usbSongType);
        dest.writeString(accId);
        dest.writeString(songName);
        dest.writeString(singerId);
        dest.writeString(singerName);
        dest.writeString(albumImg);
        dest.writeString(albumImgLarge);
        dest.writeInt(bitRate);
        dest.writeByte((byte) (hasOrigin ? 1 : 0));
        dest.writeByte((byte) (hasPitch ? 1 : 0));
        dest.writeByte((byte) (isHQ ? 1 : 0));
        dest.writeByte((byte) (hasMv ? 1 : 0));
        dest.writeLong(weight);
        dest.writeInt(songHot);
        dest.writeInt(playableCode);
        dest.writeString(freeToken);
        dest.writeString(formSource);
        dest.writeString(fromSourceId);
        dest.writeString(accompanyId);
        dest.writeString(opusId);
        dest.writeString(opusName);
        dest.writeInt(isPrivate);
        dest.writeInt(listenNum);
        dest.writeFloat(score);
        dest.writeFloat(averageScore);
        dest.writeString(grade);
        dest.writeString(createTime);
        dest.writeLong(updateTime);
        dest.writeString(opusHash);
        dest.writeString(playerId);
        dest.writeString(nickName);
        dest.writeString(groupName);
        dest.writeString(groupId);
        dest.writeString(topId);
        dest.writeString(topName);
        dest.writeString(headerUrl);
        dest.writeTypedList(letterSingerListList);
        dest.writeString(letter);
        dest.writeLong(productItemId);
        dest.writeInt(durationDays);
        dest.writeByte((byte) (isAutoRenew ? 1 : 0));
        dest.writeString(productName);
        dest.writeString(productPrice);
        dest.writeString(showPrice);
        dest.writeString(productDiscountedPrice);
        dest.writeString(guideDesc);
        dest.writeString(activityDesc);
        dest.writeInt(priceTypeId);
        dest.writeDouble(playedSecs);
        dest.writeInt(timeline);
        dest.writeString(mvId);
        dest.writeString(imageUri);
        dest.writeString(VideoThumbnailsId);
        dest.writeString(displayName);
        dest.writeString(isMusic);
        dest.writeString(size);
        dest.writeByte((byte) (playable ? 1 : 0));
        dest.writeInt(mediaType);
        dest.writeString(folderMediaId);
        dest.writeInt(remainTimes);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public String getMvId() {
        return mvId;
    }

    public void setMvId(String mvId) {
        this.mvId = mvId;
    }

    public String getPlayListId() {
        return playListId;
    }

    public void setPlayListId(String playListId) {
        this.playListId = playListId;
    }

    public boolean isHaveSubItem() {
        return isHaveSubItem;
    }

    public void setHaveSubItem(boolean haveSubItem) {
        isHaveSubItem = haveSubItem;
    }

    public String getLetter() {
        return letter;
    }

    public void setLetter(String letter) {
        this.letter = letter;
    }

    public List<ItemBean> getLetterSingerListList() {
        return letterSingerListList;
    }

    public void setLetterSingerListList(List<ItemBean> letterSingerListList) {
        this.letterSingerListList = letterSingerListList;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getTopId() {
        return topId;
    }

    public void setTopId(String topId) {
        this.topId = topId;
    }

    public String getTopName() {
        return topName;
    }

    public void setTopName(String topName) {
        this.topName = topName;
    }

    public String getHeaderUrl() {
        return headerUrl;
    }

    public void setHeaderUrl(String headerUrl) {
        this.headerUrl = headerUrl;
    }


    public String getAccompanyId() {
        return accompanyId;
    }

    public void setAccompanyId(String accompanyId) {
        this.accompanyId = accompanyId;
    }

    public String getOpusId() {
        return opusId;
    }

    public void setOpusId(String opusId) {
        this.opusId = opusId;
    }

    public String getOpusName() {
        return opusName;
    }

    public void setOpusName(String opusName) {
        this.opusName = opusName;
    }

    public int getIsPrivate() {
        return isPrivate;
    }

    public void setIsPrivate(int isPrivate) {
        this.isPrivate = isPrivate;
    }

    public int getListenNum() {
        return listenNum;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    public float getAverageScore() {
        return averageScore;
    }

    public void setAverageScore(float averageScore) {
        this.averageScore = averageScore;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getOpusHash() {
        return opusHash;
    }

    public void setOpusHash(String opusHash) {
        this.opusHash = opusHash;
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAccId() {
        return accId;
    }

    public void setAccId(String accId) {
        this.accId = accId;
    }

    public String getSongName() {
        return songName;
    }

    public void setSongName(String songName) {
        this.songName = songName;
    }

    public String getSingerId() {
        return singerId;
    }

    public void setSingerId(String singerId) {
        this.singerId = singerId;
    }

    public String getSingerName() {
        return singerName;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public String getAlbumImg() {
        return albumImg;
    }

    public void setAlbumImg(String albumImg) {
        this.albumImg = albumImg;
    }

    public String getAlbumImgLarge() {
        return albumImgLarge;
    }

    public void setAlbumImgLarge(String albumImgLarge) {
        this.albumImgLarge = albumImgLarge;
    }

    public int getBitRate() {
        return bitRate;
    }

    public void setBitRate(int bitRate) {
        this.bitRate = bitRate;
    }

    public boolean isHasOrigin() {
        return hasOrigin;
    }

    public void setHasOrigin(boolean hasOrigin) {
        this.hasOrigin = hasOrigin;
    }

    public boolean isHasPitch() {
        return hasPitch;
    }

    public void setHasPitch(boolean hasPitch) {
        this.hasPitch = hasPitch;
    }

    public boolean isHQ() {
        return isHQ;
    }

    public void setHQ(boolean HQ) {
        isHQ = HQ;
    }

    public boolean isHasMv() {
        return hasMv;
    }

    public void setHasMv(boolean hasMv) {
        this.hasMv = hasMv;
    }

    public long getWeight() {
        return weight;
    }

    public void setWeight(long weight) {
        this.weight = weight;
    }

    public int getSongHot() {
        return songHot;
    }

    public void setSongHot(int songHot) {
        this.songHot = songHot;
    }

    public int getPlayableCode() {
        return playableCode;
    }

    public void setPlayableCode(int playableCode) {
        this.playableCode = playableCode;
    }

    public String getFreeToken() {
        return freeToken;
    }

    public void setFreeToken(String freeToken) {
        this.freeToken = freeToken;
    }

    public String getFormSource() {
        return formSource;
    }

    public void setFormSource(String formSource) {
        this.formSource = formSource;
    }

    public String getFromSourceId() {
        return fromSourceId;
    }

    public void setFromSourceId(String fromSourceId) {
        this.fromSourceId = fromSourceId;
    }

    public String getFolderName() {
        return folderName;
    }

    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }

    public String getParentFolderName() {
        return parentFolderName;
    }

    public void setParentFolderName(String parentFolderName) {
        this.parentFolderName = parentFolderName;
    }

    public int getUsbSongType() {
        return usbSongType;
    }

    public void setUsbSongType(int usbSongType) {
        this.usbSongType = usbSongType;
    }


    public String getArtistId() {
        return artistId;
    }

    public void setArtistId(String artistId) {
        this.artistId = artistId;
    }

    public String getLyric() {
        return lyric;
    }

    public void setLyric(String lyric) {
        this.lyric = lyric;
    }

    public long getLyricOffset() {
        return lyricOffset;
    }

    public void setLyricOffset(long lyricOffset) {
        this.lyricOffset = lyricOffset;
    }

    public List<ItemBean> getSubItems() {
        return subItems;
    }

    public void setSubItems(List<ItemBean> subItems) {
        this.subItems = subItems;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public String getSongId() {
        return songId;
    }

    public void setSongId(String songId) {
        this.songId = songId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getCardId() {
        return cardId;
    }

    public void setCardId(long cardId) {
        this.cardId = cardId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupType(int groupType) {
        this.groupType = groupType;
    }

    public int getContentType() {
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }

    public int getRankId() {
        return rankId;
    }

    public void setRankId(int rankId) {
        this.rankId = rankId;
    }

    public long getCardSegmentId() {
        return cardSegmentId;
    }

    public void setCardSegmentId(long cardSegmentId) {
        this.cardSegmentId = cardSegmentId;
    }

    public String getAlbumId() {
        return albumId;
    }

    public void setAlbumId(String albumId) {
        this.albumId = albumId;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public long getTrackId() {
        return trackId;
    }

    public void setTrackId(long trackId) {
        this.trackId = trackId;
    }

    public long getPlayId() {
        return playId;
    }

    public void setPlayId(long playId) {
        this.playId = playId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getPlayCount() {
        return playCount;
    }

    public void setPlayCount(long playCount) {
        this.playCount = playCount;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getPlayingProgramName() {
        return playingProgramName;
    }

    public void setPlayingProgramName(String playingProgramName) {
        this.playingProgramName = playingProgramName;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setPlaying(boolean playing) {
        isPlaying = playing;
    }

    public void setIsPlaying(boolean playing) {
        isPlaying = playing;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public long getRadioId() {
        return radioId;
    }

    public void setRadioId(long radioId) {
        this.radioId = radioId;
    }

    public String getShortIntro() {
        return shortIntro;
    }

    public void setShortIntro(String shortIntro) {
        this.shortIntro = shortIntro;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getPlaylistExtraId() {
        return playlistExtraId;
    }

    public void setPlaylistExtraId(String playlistExtraId) {
        this.playlistExtraId = playlistExtraId;
    }

    public int getMediaSource() {
        return mediaSource;
    }

    public void setMediaSource(int mediaSource) {
        this.mediaSource = mediaSource;
    }

    public int getAlbumUpdateCount() {
        return albumUpdateCount;
    }

    public void setAlbumUpdateCount(int albumUpdateCount) {
        this.albumUpdateCount = albumUpdateCount;
    }

    public boolean isAlbumUpdate() {
        return albumUpdateCount > 0;
    }

    public boolean isSubscribe() {
        return isSubscribe;
    }

    public void setSubscribe(boolean subscribe) {
        isSubscribe = subscribe;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public boolean isPlayHistory() {
        return isPlayHistory;
    }

    public void setPlayHistory(boolean playHistory) {
        isPlayHistory = playHistory;
    }

    public boolean isSelfBuild() {
        return isSelfBuild;
    }

    public void setSelfBuild(boolean selfBuild) {
        isSelfBuild = selfBuild;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public String getWeekday() {
        return weekday;
    }

    public void setWeekday(String weekday) {
        this.weekday = weekday;
    }

    public int getPlayState() {
        return playState;
    }

    public void setPlayState(int playState) {
        this.playState = playState;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public long getPlayingProgramId() {
        return playingProgramId;
    }

    public void setPlayingProgramId(long playingProgramId) {
        this.playingProgramId = playingProgramId;
    }

    public int getFreeTrackCount() {
        return freeTrackCount;
    }

    public void setFreeTrackCount(int freeTrackCount) {
        this.freeTrackCount = freeTrackCount;
    }

    public boolean isPaid() {
        return isPaid;
    }

    public void setPaid(boolean paid) {
        isPaid = paid;
    }

    public int isFinished() {
        return isFinished;
    }

    public void setFinished(int finished) {
        isFinished = finished;
    }

    public boolean isVipFree() {
        return isVipFree;
    }

    public void setVipFree(boolean vipFree) {
        isVipFree = vipFree;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit;
    }

    public boolean isCanDownload() {
        return canDownload;
    }

    public void setCanDownload(boolean canDownload) {
        this.canDownload = canDownload;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(int favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public boolean isAuthorized() {
        return isAuthorized;
    }

    public void setAuthorized(boolean authorized) {
        isAuthorized = authorized;
    }

    public boolean isFree() {
        return isFree;
    }

    public void setFree(boolean free) {
        isFree = free;
    }

    public String getBackPlayUrl() {
        return backPlayUrl;
    }

    public void setBackPlayUrl(String backPlayUrl) {
        this.backPlayUrl = backPlayUrl;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public String getSongExtraId() {
        return songExtraId;
    }

    public void setSongExtraId(String songExtraId) {
        this.songExtraId = songExtraId;
    }

    public long getProductItemId() {
        return productItemId;
    }

    public void setProductItemId(long productItemId) {
        this.productItemId = productItemId;
    }

    public int getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(int durationDays) {
        this.durationDays = durationDays;
    }

    public boolean isAutoRenew() {
        return isAutoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        isAutoRenew = autoRenew;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(String productPrice) {
        this.productPrice = productPrice;
    }

    public String getProductDiscountedPrice() {
        return productDiscountedPrice;
    }

    public void setProductDiscountedPrice(String productDiscountedPrice) {
        this.productDiscountedPrice = productDiscountedPrice;
    }

    public String getGuideDesc() {
        return guideDesc;
    }

    public void setGuideDesc(String guideDesc) {
        this.guideDesc = guideDesc;
    }

    public String getShowPrice() {
        return showPrice;
    }

    public void setShowPrice(String showPrice) {
        this.showPrice = showPrice;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public int getPriceTypeId() {
        return priceTypeId;
    }

    public void setPriceTypeId(int priceTypeId) {
        this.priceTypeId = priceTypeId;
    }

    public double getPlayedSecs() {
        return playedSecs;
    }

    public void setPlayedSecs(double playedSecs) {
        this.playedSecs = playedSecs;
    }

    public String getLastUpdateTrackName() {
        return lastUpdateTrackName;
    }

    public void setLastUpdateTrackName(String lastUpdateTrackName) {
        this.lastUpdateTrackName = lastUpdateTrackName;
    }

    public int getTimeline() {
        return timeline;
    }

    public void setTimeline(int timeline) {
        this.timeline = timeline;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getVideoThumbnailsId() {
        return VideoThumbnailsId;
    }

    public void setVideoThumbnailsId(String videoThumbnailsId) {
        VideoThumbnailsId = videoThumbnailsId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getIsMusic() {
        return isMusic;
    }

    public void setIsMusic(String isMusic) {
        this.isMusic = isMusic;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public boolean isPlayable() {
        return playable;
    }

    public void setPlayable(boolean playable) {
        this.playable = playable;
    }

    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    public String getFolderMediaId() {
        return folderMediaId;
    }

    public void setFolderMediaId(String folderMediaId) {
        this.folderMediaId = folderMediaId;
    }

    @Override
    public String toString() {
        return "ItemBean{" +
                "type=" + type +
                ", cardId=" + cardId +
                ", name='" + name + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", groupType=" + groupType +
                ", contentType=" + contentType +
                ", rankId=" + rankId +
                ", cardSegmentId=" + cardSegmentId +
                ", albumId='" + albumId + '\'' +
                ", albumName='" + albumName + '\'' +
                ", playListId='" + playListId + '\'' +
                ", trackId=" + trackId +
                ", songId='" + songId + '\'' +
                ", playId=" + playId +
                ", code='" + code + '\'' +
                ", playCount=" + playCount +
                ", playUrl='" + playUrl + '\'' +
                ", playingProgramName='" + playingProgramName + '\'' +
                ", lastUpdateTrackName='" + lastUpdateTrackName + '\'' +
                ", isPlaying=" + isPlaying +
                ", position=" + position +
                ", radioId=" + radioId +
                ", shortIntro='" + shortIntro + '\'' +
                ", artist='" + artist + '\'' +
                ", artistId='" + artistId + '\'' +
                ", intro='" + intro + '\'' +
                ", kind='" + kind + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", playlistExtraId='" + playlistExtraId + '\'' +
                ", mediaSource=" + mediaSource +
                ", albumUpdateCount=" + albumUpdateCount +
                ", isSubscribe=" + isSubscribe +
                ", parent='" + parent + '\'' +
                ", isPlayHistory=" + isPlayHistory +
                ", isSelfBuild=" + isSelfBuild +
                ", isVip=" + isVip +
                ", songExtraId='" + songExtraId + '\'' +
                ", keyWord='" + keyWord + '\'' +
                ", sort='" + sort + '\'' +
                ", offset=" + offset +
                ", limit=" + limit +
                ", tag='" + tag + '\'' +
                ", weekday='" + weekday + '\'' +
                ", month='" + month + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", playState=" + playState +
                ", selected=" + selected +
                ", totalCount=" + totalCount +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", playingProgramId=" + playingProgramId +
                ", freeTrackCount=" + freeTrackCount +
                ", isPaid=" + isPaid +
                ", isFinished=" + isFinished +
                ", isVipFree=" + isVipFree +
                ", price=" + price +
                ", priceUnit='" + priceUnit + '\'' +
                ", canDownload=" + canDownload +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", commentCount=" + commentCount +
                ", createdAt=" + createdAt +
                ", duration=" + duration +
                ", favoriteCount=" + favoriteCount +
                ", orderNum=" + orderNum +
                ", isAuthorized=" + isAuthorized +
                ", isFree=" + isFree +
                ", backPlayUrl='" + backPlayUrl + '\'' +
                ", isHaveSubItem=" + isHaveSubItem +
                ", subItems=" + subItems +
                ", payUrl='" + payUrl + '\'' +
                ", payOrderNo='" + payOrderNo + '\'' +
                ", lyric='" + lyric + '\'' +
                ", lyricOffset=" + lyricOffset +
                ", folderName='" + folderName + '\'' +
                ", parentFolderName='" + parentFolderName + '\'' +
                ", usbSongType=" + usbSongType +
                ", accId='" + accId + '\'' +
                ", songName='" + songName + '\'' +
                ", singerId='" + singerId + '\'' +
                ", singerName='" + singerName + '\'' +
                ", albumImg='" + albumImg + '\'' +
                ", albumImgLarge='" + albumImgLarge + '\'' +
                ", bitRate=" + bitRate +
                ", hasOrigin=" + hasOrigin +
                ", hasPitch=" + hasPitch +
                ", isHQ=" + isHQ +
                ", hasMv=" + hasMv +
                ", weight=" + weight +
                ", songHot=" + songHot +
                ", playableCode=" + playableCode +
                ", freeToken='" + freeToken + '\'' +
                ", formSource='" + formSource + '\'' +
                ", fromSourceId='" + fromSourceId + '\'' +
                ", accompanyId='" + accompanyId + '\'' +
                ", opusId='" + opusId + '\'' +
                ", opusName='" + opusName + '\'' +
                ", isPrivate=" + isPrivate +
                ", listenNum=" + listenNum +
                ", score=" + score +
                ", averageScore=" + averageScore +
                ", grade='" + grade + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime=" + updateTime +
                ", opusHash='" + opusHash + '\'' +
                ", playerId='" + playerId + '\'' +
                ", nickName='" + nickName + '\'' +
                ", groupName='" + groupName + '\'' +
                ", groupId='" + groupId + '\'' +
                ", topId='" + topId + '\'' +
                ", topName='" + topName + '\'' +
                ", headerUrl='" + headerUrl + '\'' +
                ", letterSingerListList=" + letterSingerListList +
                ", letter='" + letter + '\'' +
                ", productItemId=" + productItemId +
                ", durationDays=" + durationDays +
                ", isAutoRenew=" + isAutoRenew +
                ", productName='" + productName + '\'' +
                ", productPrice='" + productPrice + '\'' +
                ", showPrice='" + showPrice + '\'' +
                ", productDiscountedPrice='" + productDiscountedPrice + '\'' +
                ", guideDesc='" + guideDesc + '\'' +
                ", activityDesc='" + activityDesc + '\'' +
                ", priceTypeId=" + priceTypeId +
                ", playedSecs=" + playedSecs +
                ", timeline=" + timeline +
                ", mvId='" + mvId + '\'' +
                '}';
    }
}
