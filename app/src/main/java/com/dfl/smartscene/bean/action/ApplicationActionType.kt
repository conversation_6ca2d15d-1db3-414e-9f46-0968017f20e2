package com.dfl.smartscene.bean.action


/**
 * author:z<PERSON><PERSON><PERSON>
 * e-mail:zhus<PERSON><PERSON>@dfl.com.cn
 * time: 2022/09/21
 * desc: 应用动作
 * version:1.0
 */
enum class ApplicationActionType {


    //标题
    NAVIGATION, MEDIA, BROADCAST, MESSAGE_CENTER, SOUND_SPACE, ENERGY_CENTER, CONTROL_APP,

    //adapter 类型
    ADAPTER_ITEM_TYPE_NAVIGATION,
    ADAPTER_ITEM_TYPE_MEDIA,
    ADAPTER_ITEM_TYPE_BROADCAST,
    ADAPTER_ITEM_TYPE_MESSAGE_CENTER, ADAPTER_ITEM_TYPE_SOUND_SPACE, ADAPTER_ITEM_TYPE_ENERGY_CENTER,
    ADAPTER_ITEM_TYPE_CONTROL_APP,

    //内容

    /**
     * 发起导航
     */
    START_NAVI,

    /**
     * 路线偏好
     */
    NAVIGATION_ROUTE,

    //多媒体
    /**
     * 打开多媒体
     */
    OPEN_MEDIA,

    /**
     * 关闭多媒体
     */
    CLOSE_MEDIA,

    /**
     * 播放模式
     */
    MEDIA_PLAY_MODE,

    /**
     * 播放QQ音乐
     */
    PLAY_QQ,

    /**
     * 播放控制
     */
    MEDIA_PLAY_CONTROL,


    /**
     * 小尼回复
     */
    VPA_REPLY,

    /**
     * 天气播报
     */
    WEATHER_BROADCAST,

    /**
     * 行程播报
     */
    TRIP_BROADCAST,

    /**
     * 消息通知
     */
    APP_NOTIFY,

    /**
     * 播放整蛊音,int,1 2 3 4
     */
    PLAY_PRANK_MUSIC,

    /**
     * 沉浸音效 自然原声:1,动感激情:2,身临其境:3,轻松舒适:4,高保真原声:5,影院模式:6,重低音增强:7
     */
    SOUND_EFFECT,

    /**
     * 动感声浪开关 关闭0打开1
     */
    VOICE_SWITCH,

    /**
     * 声浪设置 低:1,中:2,高:3
     */
    VOICE_VOLUME,

    /**
     * 使用指定声浪
     */
    USE_VOICE,

    /**大喇叭实时喊话*/
    OUTER_SPEAKER,

    /**
     * 在途预热开关,int,0关闭1打开
     */
    PREHEAT_SWITCH,

    /**
     * 对外放电开关,int,0关闭1打开
     */
    DISCHARGE_SWITCH,

    /**续航模式*/
    ENDURANCE_MODE,

    /**
     * 打开APP
     */
    APP_OPEN,

    /**
     * 关闭APP
     */
    APP_CLOSE

}