package com.dfl.smartscene.bean.edit

import androidx.databinding.BaseObservable
import com.dfl.smartscene.ui.edit.container.SceneNewEditType

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/14
 * desc :条件 动作 一级菜单adapter的 item
 * version: 1.0
 */
data class EditConditionAndActionItemBean(
    /**对应的自定义code,状态ConditionEventCode,动作ActionEventCode*/
    var eventCode: Int,
    /** 类别名称*/
    var name: String,
    /** 能力id,一级菜单，触发条件和动作为-1，状态条件为statusSkillId*/
    var skillId: Int? = -1,
    /** 默认图片*/
    var image: Int,
    /** 选中的图片 */
    var imageSelect: Int,
    /** 一级目录类型:状态,触发条件,动作*/
    var sceneEditType: SceneNewEditType,
    /** 是否是添加动作或条件按钮*/
    var isAddConditionOrAction: Boolean,
    /** 是否被点击*/
    var isClicked: Boolean?
) : BaseObservable()