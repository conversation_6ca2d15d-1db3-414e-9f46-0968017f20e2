package com.dfl.smartscene.bean.action

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/08
 * desc :动作->空调枚举类
 * version: 1.0
 */
enum class AirConditionerType {
    /**
     * 空调开关
     */
    AIR_CONDITIONER_SWITCH,

    /**
     * auto
     */
    AUTO,

    /**
     * 前除雾
     */
    FRONT_DEFOG,

    /**
     * 自动前除雾
     */
    AUTO_FRONT_DEFOG,

    /**
     * 后除雾
     */
    AFTER_DEFOG,

    /**
     * 制冷制热
     */
    COOLING_HEATING,

    /**
     * 鼓风
     */
    BLOWER,

    /**
     * 空调温度 单区
     */
    AIR_CONDITIONER_TEMPERATURE,

    /**
     * 空调温度 双区
     */
    AIR_CONDITIONER_TEMPERATURE_TWO,

    /**
     * 空调风量
     */
    AIR_CONDITIONER_VOLUME,

    /**
     * 吹风模式
     */
    BLOW_MODE,

    /**
     * 循环模式
     */
    LOOP_MODE,

    /**
     * 极速制热模式
     */
    AIR_MAX_HEAT,

    /**
     * 极速制冷模式
     */
    AIR_MAX_COOL,

    /**同步*/
    AIR_SYNC,

    /**节能模式*/
    AIR_ENERGY_CONSERVATION,

    /**自干燥模式*/
    AIR_SELF_DESICCATION,

}
