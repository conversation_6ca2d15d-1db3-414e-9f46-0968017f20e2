package com.dfl.smartscene.bean.action

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/10/24
 * desc :动作->其他枚举类
 * version: 1.0
 */
enum class OtherType {
    /**标题*/
    ACTION_TITLE,

    /**后视镜折叠*/
    REARVIEW_MIRROR_SWITCH,

    /**后视镜加热*/
    REARVIEW_MIRROR_HEATING,


    /**延时*/
    DELAY,

    /**
     * 屏保模式,1关闭,0打开
     */
    SCREEN_PROTECT,

    /**
     * 无线充电，关闭:0 , 打开:1
     */
    WIRELESS_CHARGER,

    WIRELESS_CHARGER_TYPE,

    /**
     * 熄屏,1关闭,0打开
     */
    SCREEN_OFF,

    //itemActionType item+action 分类类别
    OTHER_REARVIEW_MIRROR_TYPE, //后视镜

    /**
     * 屏保模式分类
     */
    SCREEN_PROTECT_TYPE,

    /**
     * 熄屏分类
     */
    SCREEN_OFF_TYPE,

    //OTHER_MESSAGE_CENTER_TYPE,//消息中心
    OTHER_DELAY_TYPE //延时
}