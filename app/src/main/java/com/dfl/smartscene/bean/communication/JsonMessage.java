package com.dfl.smartscene.bean.communication;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <pre>
 * author : ly-louhyu
 * e-mail : <EMAIL>
 * time   : 2022/12/08
 * desc   : aidl 通用响应实体
 * version: 1.0
 * </pre>
 */

public class JsonMessage implements Parcelable {
    private int protocolId;
    private String requestCode;
    private String responseCode;
    private String versionName;
    private String requestAuthor;
    private String messageType;
    private Object data;

    public JsonMessage() {
    }

    public JsonMessage(int protocolId, String versionName, String requestAuthor,
                       String messageType, Object data) {
        this.protocolId = protocolId;
        this.versionName = versionName;
        this.requestAuthor = requestAuthor;
        this.messageType = messageType;
        this.data = data;
    }

    public JsonMessage(int protocolId, String requestCode, String responseCode,
                       String versionName, String requestAuthor, String messageType, Object data) {
        this.protocolId = protocolId;
        this.requestCode = requestCode;
        this.responseCode = responseCode;
        this.versionName = versionName;
        this.requestAuthor = requestAuthor;
        this.messageType = messageType;
        this.data = data;
    }

    protected JsonMessage(Parcel in) {
        protocolId = in.readInt();
        requestCode = in.readString();
        responseCode = in.readString();
        versionName = in.readString();
        requestAuthor = in.readString();
        messageType = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(protocolId);
        dest.writeString(requestCode);
        dest.writeString(responseCode);
        dest.writeString(versionName);
        dest.writeString(requestAuthor);
        dest.writeString(messageType);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<JsonMessage> CREATOR = new Creator<JsonMessage>() {
        @Override
        public JsonMessage createFromParcel(Parcel in) {
            return new JsonMessage(in);
        }

        @Override
        public JsonMessage[] newArray(int size) {
            return new JsonMessage[size];
        }
    };

    public int getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(int protocolId) {
        this.protocolId = protocolId;
    }

    public String getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(String requestCode) {
        this.requestCode = requestCode;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getRequestAuthor() {
        return requestAuthor;
    }

    public void setRequestAuthor(String requestAuthor) {
        this.requestAuthor = requestAuthor;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "JsonRequest{" +
                "protocolId=" + protocolId +
                ", requestCode='" + requestCode + '\'' +
                ", responseCode='" + responseCode + '\'' +
                ", versionName='" + versionName + '\'' +
                ", requestAuthor='" + requestAuthor + '\'' +
                ", messageType='" + messageType + '\'' +
                ", data=" + data +
                '}';
    }
}
