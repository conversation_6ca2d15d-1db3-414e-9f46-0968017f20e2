package com.dfl.smartscene.bean.edit

import android.os.Parcel
import android.os.Parcelable
import com.dfl.smartscene.bean.main.ScenarioBean

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/02
 * desc   :
 * version: 1.0
 */
class SceneEditBean(
    var scenarioBean: ScenarioBean?, // 场景实体
    var isNewScene: Boolean, // 是否为新场景
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readParcelable(ScenarioBean::class.java.classLoader),
        parcel.readByte() != 0.toByte()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(scenarioBean, flags)
        parcel.writeByte(if (isNewScene) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SceneEditBean> {
        override fun createFromParcel(parcel: Parcel): SceneEditBean {
            return SceneEditBean(parcel)
        }

        override fun newArray(size: Int): Array<SceneEditBean?> {
            return arrayOfNulls(size)
        }
    }
}