package com.dfl.smartscene.bean.mechanism;


import com.dfl.smartscene.apiweb.utils.ApiException;
import com.dfl.smartscene.apiweb.utils.RxSubscriber;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * Created by 钟文祥 on 2023/6/28.
 * Describer:基类 所有自定义的DataSource 都继承这个类
 */
public abstract class BaseDataSource {

    public Disposable disposable;

    /**
     * 根据需要修改返回来的业务值
     */
    public <T> void modifyValue(T value) {
    }

    /**
     * 订阅数据并处理回调
     *
     * @param observable 要订阅的Observable对象
     * @param callBack 处理回调
     * @param <T> 订阅数据的类型
     */
    public <T> void subscribeGoData(Observable<T> observable,
                                    ILoadDataCallBack<T> callBack) {
        observable.subscribe(new RxSubscriber<T>() {
            @Override
            public void onAddDisposable(Disposable d) {
                cancelDisposable();
                disposable = d;
                // 开始加载数据时的回调
                callBack.onLoadRemoteStart();
            }

            @Override
            public void onApiNext(T value) {
                modifyValue(value); //根据需要修改返回来的业务值
                if (value != null) {
                    //数据加载完成回调
                    callBack.onDataLoaded(value, DataSourceType.Remote);
                } else {
                    //数据为空回调
                    callBack.onDataIsNullOrError(DataSourceType.Remote);
                }
                cancelDisposable();
            }

            @Override
            public void onApiError(ApiException ex) {
                //加载错误回调
                callBack.onDataIsNullOrError(DataSourceType.Remote);
                callBack.onApiError(ex);
                cancelDisposable();
            }
        });
    }

    /**
     * 数据来源于
     */
    public enum DataSourceType {
        /**
         * 内存缓存数据
         */
        Cached(0),
        /**
         * 远程数据
         */
        Remote(1),
        /**
         * 本地数据库数据
         */
        Local(2);


        private int value;

        DataSourceType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public String getValueStr() {
            switch (value) {
                case 0:
                    return "内存";
                case 1:
                    return "远程";
                case 2:
                    return "本地";
            }
            return "";
        }
    }


    public void cancelDisposable() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        disposable = null;
    }

}
