package com.dfl.smartscene.bean.main

import android.os.Parcelable
import com.dfl.smartscene.R
import kotlinx.parcelize.Parcelize

/**
 *Created by 钟文祥 on 2024/6/30.
 *Describer: 图片icon类型类
 */
@Parcelize
data class MeIconBean(
    var type: Int = -1,  //-1 更多 ，-2 连头 ，0 触发条件+状态条件，1 动作
    var icon: Int? //图标资源
) : Parcelable {

    fun getIconWidth(): Int {
        return when (type) {
            -1 -> 40
            -2 -> 48
            0, 1 -> 72
            else -> 0
        }
    }

    fun getPadding(): Int {
        return when (type) {
            0, 1 -> 5
            else -> 0
        }
    }

    fun getBackgroundResource(): Int {
        return when (type) {
            0 -> R.drawable.scene_shape_bg_me_action_yuan
            1 -> R.drawable.scene_shape_bg_me_action_fang
            else -> R.drawable.scene_shape_bg_me_action_null
        }
    }

}
