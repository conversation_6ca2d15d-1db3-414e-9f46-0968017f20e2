package com.dfl.smartscene.bean.communication

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/01/05
 * desc :
 * version: 1.0
 */
data class CarControlRequestBean<T>(
    val data: T?,
    val messageType: String,
    val protocolId: Int,
    val requestAuthor: String,
    val requestCode: String,
    val responseCode: String,
    val versionName: String
)

data class SmartDeviceListBean(
    val list: List<AiDeviceStateBean>?
)

//data class AiDeviceStateBean(
//    val id:Int,
//    val name:String,
//    val pid:String,
//    val online:Int
//)

data class SeatMemoryBean(
    val position: Int,
    val mode: Int
)

data class RequestSeatMemoryMode(
    val position: Int
)

data class SmartDeviceStateBean(
    val list: List<AiDeviceStateBean>?
)

data class AiDeviceStateBean(
    val id: Int,
    val name: String,
    val pid: String,
    val online: Int,
    val result: Int?
)

data class QueryFragranceSwitch(
    val deviceId: Int,
    val actionId: Int,
    val state: Int
)

data class ResultState(
    val result: Int
)