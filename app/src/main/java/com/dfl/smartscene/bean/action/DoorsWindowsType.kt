package com.dfl.smartscene.bean.action

/**
 * author:zhus<PERSON><PERSON>
 * e-mail:zhushi<PERSON>@dfl.com.cn
 * time: 2022/09/13
 * desc: 门窗显示枚举类
 * version:1.0
 */

enum class DoorsWindowsType {
    /**
     * 标题-车门
     */
    DOOR_TITLE,

    /**
     * 后背门
     */
    TRUNK,

    /**
     * 所有车窗
     */
    ALL_WINDOW,

    /**
     * 标题-车窗
     */
    CAR_WINDOW,

    /**
     * 车窗透气
     */
    WINDOW_VENTILATE,

    /**
     * 车窗通风
     */
    WINDOW_IN_FRESH_AIR,

    /**
     * 主驾车窗
     */
    WINDOW_DRIVER_MAIN,

    /**
     * 副驾车窗
     */
    WINDOW_DRIVER_CHIEF,

    /**
     * 左后车窗
     */
    WINDOW_LEFT_BACK,

    /**
     * 右后车窗
     */
    WINDOW_RIGHT_BACK,

    /**
     * 全车门锁
     */
    ALL_DOOR_LOCK,

    /**
     * 左侧儿童锁
     */
    LEFT_CHILD_LOCK,

    /**
     * 右侧儿童锁
     */
    RIGHT_CHILD_LOCK,

    //itemActionType   标题+能力类别

    /**
     * 属于 标题-车窗
     */
    CAR_WINDOW_TITLE_TYPE,

    /**
     * 属于 标题-车门
     */
    CAR_DOOR_TITLE_TYPE,

    /**
     * 后背门
     */
    DOOR_BACK,

    /**天窗*/
    WINDOW_SKYLIGHT,

    /**电动遮阳帘*/
    WINDOW_ELECTRIC_SUNSHADE_CURTAIN
}