package com.dfl.smartscene.bean.communication;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <pre>
 * author : ly-louhyu
 * e-mail : <EMAIL>
 * time   : 2022/12/08
 * desc   :
 * version: 1.0
 * </pre>
 */
public class KtvRecordFileInfo implements Parcelable {
    private String filePath = "";
    private String fileName;
    private String accId;
    private int score;
    private int averageScore;
    private String scoreLevel;
    private long opusDurationMs;
    private String songName;
    private String singerName;
    private boolean isUpload;
    private boolean isShare;
    private int mOpusId;
    private String mOpusHash;
    private String position;
    private String imgUrl;
    private String krcId;
    private String songId;
    private String date;


    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public KtvRecordFileInfo(){}

    protected KtvRecordFileInfo(Parcel in) {
        date = in.readString();
        filePath = in.readString();
        fileName = in.readString();
        accId = in.readString();
        score = in.readInt();
        averageScore = in.readInt();
        scoreLevel = in.readString();
        opusDurationMs = in.readLong();
        songName = in.readString();
        singerName = in.readString();
        isUpload = in.readByte() != 0;
        isShare = in.readByte() != 0;
        mOpusId = in.readInt();
        mOpusHash = in.readString();
        position = in.readString();
        imgUrl = in.readString();
        krcId = in.readString();
        songId = in.readString();
    }

    public static final Creator<KtvRecordFileInfo> CREATOR = new Creator<KtvRecordFileInfo>() {
        @Override
        public KtvRecordFileInfo createFromParcel(Parcel in) {
            return new KtvRecordFileInfo(in);
        }

        @Override
        public KtvRecordFileInfo[] newArray(int size) {
            return new KtvRecordFileInfo[size];
        }
    };

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getAccId() {
        return accId;
    }

    public void setAccId(String accId) {
        this.accId = accId;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getAverageScore() {
        return averageScore;
    }

    public void setAverageScore(int averageScore) {
        this.averageScore = averageScore;
    }

    public String getScoreLevel() {
        return scoreLevel;
    }

    public void setScoreLevel(String scoreLevel) {
        this.scoreLevel = scoreLevel;
    }

    public long getOpusDurationMs() {
        return opusDurationMs;
    }

    public void setOpusDurationMs(long opusDurationMs) {
        this.opusDurationMs = opusDurationMs;
    }

    public String getSongName() {
        return songName;
    }

    public void setSongName(String songName) {
        this.songName = songName;
    }

    public boolean isUpload() {
        return isUpload;
    }

    public void setUpload(boolean upload) {
        isUpload = upload;
    }

    public boolean isShare() {
        return isShare;
    }

    public void setShare(boolean share) {
        isShare = share;
    }

    public String getSingerName() {
        return singerName;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public int getOpusId() {
        return mOpusId;
    }

    public void setOpusId(int mOpusId) {
        this.mOpusId = mOpusId;
    }

    public String getOpusHash() {
        return mOpusHash;
    }

    public void setOpusHash(String mOpusHash) {
        this.mOpusHash = mOpusHash;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getKrcId() {
        return krcId;
    }

    public void setKrcId(String krcId) {
        this.krcId = krcId;
    }

    public String getSongId() {
        return songId;
    }

    public void setSongId(String songId) {
        this.songId = songId;
    }

    @Override
    public String toString() {
        return "KtvRecordFileInfo{" +
                "filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                ", accId='" + accId + '\'' +
                ", score=" + score +
                ", averageScore=" + averageScore +
                ", scoreLevel='" + scoreLevel + '\'' +
                ", opusDurationMs=" + opusDurationMs +
                ", songName='" + songName + '\'' +
                ", singerName='" + singerName + '\'' +
                ", isUpload=" + isUpload +
                ", isShare=" + isShare +
                ", mOpusId=" + mOpusId +
                ", mOpusHash='" + mOpusHash + '\'' +
                ", position='" + position + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", krcId='" + krcId + '\'' +
                ", songId='" + songId + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(date);
        dest.writeString(filePath);
        dest.writeString(fileName);
        dest.writeString(accId);
        dest.writeInt(score);
        dest.writeInt(averageScore);
        dest.writeString(scoreLevel);
        dest.writeLong(opusDurationMs);
        dest.writeString(songName);
        dest.writeString(singerName);
        dest.writeByte((byte) (isUpload ? 1 : 0));
        dest.writeByte((byte) (isShare ? 1 : 0));
        dest.writeInt(mOpusId);
        dest.writeString(mOpusHash);
        dest.writeString(position);
        dest.writeString(imgUrl);
        dest.writeString(krcId);
        dest.writeString(songId);
    }
}
