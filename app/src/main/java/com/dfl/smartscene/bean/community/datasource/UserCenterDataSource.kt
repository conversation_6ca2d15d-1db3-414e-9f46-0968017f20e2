package com.dfl.smartscene.bean.community.datasource

import com.dfl.smartscene.apiweb.service.CommunityService
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.dfl.smartscene.bean.mechanism.BaseDataSource
import com.dfl.smartscene.bean.mechanism.IBaseDataSourceCallBack
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.dao.CommunityUserInfoDao

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/05/10
 * desc : 账号页 数据来源
 * version: 1.0
 */
class UserCenterDataSource(
	private val mPageNum: Int,
	private val mIsOfficial: Int,
	private val mVisitedUserId: String
) : BaseDataSource(), IBaseDataSourceCallBack<CommunityUserInfo> {

	/**
	 * 账号信息数据库dao
	 */
	private val mDao: CommunityUserInfoDao? = DbManager.getDBMaster()?.CommunityUserInfoDao()

	/**
	 * 获取后台账号信息
	 */
	override fun loadDataFromWeb(callBack: ILoadDataCallBack<CommunityUserInfo>) {
		subscribeGoData(CommunityService.getCommunityUserInfo(mIsOfficial, mPageNum, mVisitedUserId), callBack)
	}

	/**
	 * 通过uuid加载本地账号信息
	 */
	override fun loadDataFromLocal(): CommunityUserInfo? {
		return mDao?.queryByUuid(mVisitedUserId)
	}

	/**
	 * 删除过时本地数据
	 */
	override fun deleteAll2Local(): Int {
		return mDao?.deleteByUuid(mVisitedUserId) ?: 0
	}

	/**
	 * 本地缓存最新数据
	 */
	override fun add2Local(values: CommunityUserInfo): MutableList<Long> {
		mDao?.insert(values)
		return arrayListOf()
	}

	/**
	 * 根据需要修改返回来的业务值
	 */
	override fun <T : Any?> modifyValue(value: T) {
		(value as CommunityUserInfo).sceneList.forEach { communitySceneInfo ->
			communitySceneInfo.convert()
			communitySceneInfo.isOfficial = mIsOfficial
		}
	}

	fun update(communityUserInfo: CommunityUserInfo): Int? {
		return mDao?.update(communityUserInfo)
	}

	fun deleteAll(): Int {
		return mDao?.deleteAll() ?: 0
	}

	fun queryByUuid(uuid: String?): CommunityUserInfo? {
		if (uuid.isNullOrEmpty() || mDao == null) {
			return null
		}
		return mDao.queryByUuid(uuid)
	}
}