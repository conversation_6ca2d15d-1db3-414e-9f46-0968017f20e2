package com.dfl.smartscene.bean.mechanism;


import com.dfl.smartscene.apiweb.utils.ApiException;

/**
 * Created by 钟文祥 on 2023/6/28.
 * Describer:BaseRepository 加载结果回调接口
 */
public interface ILoadDataCallBack<T> {


    /**
     * 加载远程开始
     */
    void onLoadRemoteStart();

    /**
     * 获取到数据
     * DataSourceType=0:内存缓存数据，1：web数据，2：本地数据
     */
    void onDataLoaded(T values, BaseDataSource.DataSourceType type);

    /**
     * 获取不到数据
     * DataSourceType=0:内存缓存数据，1：web数据，2：本地数据
     */
    void onDataIsNullOrError(BaseDataSource.DataSourceType type);

    void onApiError(ApiException ex);
}
