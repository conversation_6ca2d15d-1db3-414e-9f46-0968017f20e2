package com.dfl.smartscene.ui.edit.action.apply.adapter

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.entity.SongSingerInfo

/**
 *Created by 钟文祥 on 2024/01/18.
 *Describer: 动作->搜索歌手或者搜索歌曲展示的列表适配器
 */
class SearchSongDataAdapter(val type: Int?) : BaseQuickAdapter<SongSingerInfo, BaseViewHolder>(
    R.layout.scene_recycle_item_search_song_musician
) {
    private var mKeyWord = ""

    override fun convert(holder: BaseViewHolder, item: SongSingerInfo) {
        val content = if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG) {
            "${item.song_name}"
        } else {
            "${item.singer_name}" //歌手搜索只需要歌手名。歌曲搜索由于有翻唱后面需要显示歌手名
        }
        val tvSong = holder.getView<TextView>(R.id.tv_song_or_musician_name)
        val tvDetail = holder.getView<TextView>(R.id.tv_song_detail_item)

        tvSong.text = content
        tvDetail.text = if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG) item.singer_name else ""

        UIUtils.stringInterceptionChangeColor(
            context, tvSong, mKeyWord, tvSong.text.toString(), R.color.scene_primary_color
        )
    }

    fun setKeyWord(key: String) {
        this.mKeyWord = key
    }


}