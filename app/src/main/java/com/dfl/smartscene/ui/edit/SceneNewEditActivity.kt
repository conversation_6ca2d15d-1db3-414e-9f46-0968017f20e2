package com.dfl.smartscene.ui.edit

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.animationlib.TabSwitchView
import com.dfl.android.common.base.MVVMActivity
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.customapi.DataEventSceneBean
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneActivityNewEditBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.adapter.SceneEditActionAdapter
import com.dfl.smartscene.ui.edit.adapter.SceneEditConditionAdapter
import com.dfl.smartscene.ui.edit.adapter.SceneEditSkillIconAdapter
import com.dfl.smartscene.ui.edit.container.SceneEditContainerDialogFragment
import com.dfl.smartscene.ui.edit.container.SceneNewEditType
import com.dfl.smartscene.ui.edit.manager.SceneDataEventManager
import com.dfl.smartscene.ui.main.me.rename.RenameDialogFragment
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.TrackUtils
import com.dfl.smartscene.widget.recycleview.ItemHolderMoveCallback
import com.dfl.smartscene.widget.recycleview.MyItemTouchHelperCallback
import com.jeremyliao.liveeventbus.LiveEventBus
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.*

/**
 *Created by 钟文祥 on 2024/10/17.
 *Describer: 添加与编辑场景界面
 */
class SceneNewEditActivity : MVVMActivity<SceneActivityNewEditBinding, SceneEditViewModel>(), View.OnClickListener,
                             SceneEditContainerDialogFragment.OnSceneEditFragmentExitListener {

    private val TAG = GlobalConstant.GLOBAL_TAG + "SceneNewEditActivity"

    private var mEditSkillIconAdapter: SceneEditSkillIconAdapter? = null    //能力图标
    private var mEditConditionAdapter: SceneEditConditionAdapter? = null    // 条件
    private var mEditActionAdapter: SceneEditActionAdapter? = null          // 动作
    private var myItemTouchHelperCallback: MyItemTouchHelperCallback? = null //recycleView拖拽效果回调


    private var mDataEventAddSceneBean: DataEventSceneBean? = null          //数据事件

    override fun getLayoutId(): Int {
        return R.layout.scene_activity_new_edit
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneEditViewModel> {
        return SceneEditViewModel::class.java
    }

    companion object {
        private const val EXTRA_KEY_BEAN = "EXTRA_KEY_BEAN"
        private const val EXTRA_KEY_ENTER_TYPE = "EXTRA_KEY_ENTER_TYPE"
        private const val EXTRA_KEY_L3ID = "EXTRA_KEY_L3ID"

        private const val ACTION_CANCEL_ASK_BEFORE_START = "action_cancel_ask_before_start"
        private const val ACTION_SAVE = "action_save"
        private const val ACTION_RESET = "action_reset"


        //进入编辑界面
        //注意：3我的场景（编辑场景） 进来时如果是自动执行开启的已经把自动执行禁止了，退出时若没修改则需要打开自动执行
        fun launchSceneEditWithMyScene(
            context: Context, //
            enterBean: MySceneBean?, // 进来的原始数据
            enterType: SceneNewEditEnterType, //(1下载（编辑并创建场景），2发现详情，用模板进入（编辑并创建场景），3我的场景（编辑场景）,4创建场景)
            scenario_L3id: String = "" //社区id
        ) {
            val intent = Intent(context, SceneNewEditActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(EXTRA_KEY_BEAN, enterBean)
            intent.putExtra(EXTRA_KEY_ENTER_TYPE, enterType.value)
            intent.putExtra(EXTRA_KEY_L3ID, scenario_L3id)
            context.startActivity(intent)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        initData2UI(intent!!)
    }

    //1.
    override fun onCreate(savedInstanceState: Bundle?) {
        val enterType = SceneNewEditEnterType.values()[intent.getIntExtra(EXTRA_KEY_ENTER_TYPE, 0)]
        if (enterType == SceneNewEditEnterType.Download) {
            setTheme(R.style.Theme_activity_edit_down)
        } else {
            setTheme(R.style.Theme_SceneControl)
        }
        savedInstanceState?.remove("android:support:fragments")
        super.onCreate(savedInstanceState)
        transparentStatusBar()
    }

    //    //下载进来的 由下向上升起
    //    override fun startTransition() {
    //        val enterType = SceneNewEditEnterType.values()[intent.getIntExtra(EXTRA_KEY_ENTER_TYPE, 0)]
    //        if (enterType == SceneNewEditEnterType.Download) {
    //            overridePendingTransition(R.anim.scene_fragment_enter_ani2, R.anim.scene_fragment_exit_ani)
    //        } else {
    //            super.startTransition()
    //        }
    //    }

    //2. initview才生成viewmodel和ciewbind
    override fun initView() {
        super.initView()

        initListener()
        initLiveDataObserve()
        initLiveEventListener()

        //        initUI()
        initTab()
        initRecyclerView()
        initTryStartSceneButton()
    }

    private fun initTab() {
        //tabSwitch执行频率初始化
        val items: MutableList<Any> = ArrayList()
        items.add(resources.getString(R.string.scene_text_edit_scene_execute_frequency_infinite))
        items.add(resources.getString(R.string.scene_text_edit_scene_execute_frequency_daily))
        items.add(resources.getString(R.string.scene_text_edit_scene_execute_frequency_start_once))
        mViewDataBinding.tabSwitchViewExecuteFrequency.generateViews(items)
        mViewDataBinding.tabSwitchViewExecuteFrequency.setOnTabSelectedListener(object :
            TabSwitchView.OnTabSelectedListener {
            override fun onTabSelected(position: Int, isManual: Boolean?) {
                when (position) {
                    0 -> {
                        mDataEventAddSceneBean?.execute_frequency = 0
                        mViewModel.curSceneEditBean?.scenario?.executeFrequency = 0
                    }

                    1 -> {
                        mDataEventAddSceneBean?.execute_frequency = 1
                        mViewModel.curSceneEditBean?.scenario?.executeFrequency = 1
                    }

                    2 -> {
                        mDataEventAddSceneBean?.execute_frequency = 2
                        mViewModel.curSceneEditBean?.scenario?.executeFrequency = 2
                    }
                }
                mViewModel?.notifyExeFrequencyStateChanged(mViewModel.convertFrequencyCode(position))
            }

            override fun onTabUnSelected(lastPosition: Int) {
            }

            override fun onInitViewComplete(isComplete: Boolean?) {
            }

        })
    }

    //3.
    override fun initData() {
        super.initData()
        initData2UI(intent)
        if (mViewModel.mEnterType == SceneNewEditEnterType.Download) {
            downloadStartAnimation()
        }
    }

    private fun downloadStartAnimation() {
        //主题初始化
        val windowLayoutParams = window.attributes
        windowLayoutParams.gravity = Gravity.TOP
        windowLayoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        windowLayoutParams.height = resources.displayMetrics.heightPixels
        window.attributes = windowLayoutParams

        //顶部要有点距离
        val layoutParams = mViewDataBinding.clContent.layoutParams as FrameLayout.LayoutParams
        layoutParams.topMargin = AutoSizeUtils.dp2px(this@SceneNewEditActivity, 253f) //原本258，减少5 因为露出一点文字边缘不好看
        mViewDataBinding.clContent.layoutParams = layoutParams
        mViewDataBinding.clContent.setPadding(0, 0, 0, 0)
        mViewDataBinding.clContent.setBackgroundResource(R.drawable.scene_shape_community_select_scene_bg_20)

        //用的是空调升起的动画
        val hyperspaceJumpAnimation =
            AnimationUtils.loadAnimation(this, com.dfl.android.common.R.anim.scene_fragment_enter_ani)
        mViewDataBinding.clContent.startAnimation(hyperspaceJumpAnimation)
    }

    private fun initData2UI(mIntent: Intent) {
        //初始化进入来的数据
        val enterBean = mIntent.getParcelableExtra<MySceneBean?>(EXTRA_KEY_BEAN)
        val enterType = SceneNewEditEnterType.values()[mIntent.getIntExtra(EXTRA_KEY_ENTER_TYPE, 0)]
        val enterScenarioL3id = mIntent.getStringExtra(EXTRA_KEY_L3ID)
        mViewModel.setEnterData(enterBean, enterType, enterScenarioL3id)
        //注意：enterBean为传进来数据，并不是初始化时需要显示的数据，因为有可能被草稿覆盖

        if (mViewModel.mEnterBean == null) {
            finish()
            return
        }
        mDataEventAddSceneBean = DataEventSceneBean()
        if (mViewModel.mEnterType == SceneNewEditEnterType.Download) { //将slEditContent设为不可见，是为了解决升起的时候有白色一闪
            mViewDataBinding.slEditContent.visibility = View.INVISIBLE
        }
        mViewModel.updateCurSceneAndUI(mViewModel.mEnterBean)
    }

    //给ui控件设默认值
    private fun setUItDefaultValue() {
        val enterType = mViewModel.mEnterType
        mViewModel.initClearBtn()

        //小图标列表
        if (enterType == SceneNewEditEnterType.AddNew) { //判断是否是我的->添加场景
            mViewDataBinding.rvActionIconEdit.visibility = View.GONE
        }
        //下载进来的
        if (enterType == SceneNewEditEnterType.Download) {


            mViewDataBinding.tvBackEdit.text = getString(R.string.scene_text_edit_name)
            mViewModel.showDownLoadView()
            //将slEditContent设为不可见，是为了解决升起的时候有白色一闪
            mViewDataBinding.slEditContent.visibility = View.VISIBLE
        } else { //不是下载进来的
            mViewDataBinding.tvBackEdit.text = if (isAddNewSave2Me()) {
                getString(R.string.scene_button_me_add_scene)
            } else {
                getString(R.string.scene_text_edit_name)
            }
        }


        val enterBean = mViewModel.curSceneEditBean
        mViewDataBinding.cbAutoRun.isChecked = enterBean?.scenario?.isAutoRun == true
        mViewDataBinding.cbAskBeforeRun.isChecked = enterBean?.scenario?.isAskBeforeAutoRun == true
        //注意：是否显示出来交由sceneConditionLiveData监听处理，那边能更好地获取条件个数
        //标题
        mViewDataBinding.tvSceneName.text = enterBean?.scenario?.scenarioInfo?.scenarioName
        //switch
        mViewDataBinding.tabSwitchViewExecuteFrequency.selectedIndex = when (enterBean?.scenario?.executeFrequency) {
            0 -> 0
            1 -> 1
            else -> 2
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initListener() {
        mViewDataBinding.ivBack.setOnClickListener(this) //返回
        mViewDataBinding.btnReset.setOnClickListener(this)  //重置
        mViewDataBinding.tvEditName.setOnClickListener(this) //场景名称
        mViewDataBinding.btnSave.setOnClickListener(this)   //完成保存
        mViewDataBinding.btnTryRun.setOnClickListener(this) //试用
        mViewDataBinding.btnSave2.setOnClickListener(this)   //完成保存2
        mViewDataBinding.btnTryRun2.setOnClickListener(this) //试用2

        //        mViewDataBinding.ckAutoRun.setOnClickListener(this) //自动执行开关
        //        mViewDataBinding.cbAskBeforeRun.setOnClickListener(this) //执行前询问开关
        mViewDataBinding.clEditAskHotArea.setOnClickListener(this)
        mViewDataBinding.clEditAutoRunHotArea.setOnClickListener(this)
    }

    //private class MyOnTouchListener(context: SceneNewEditActivity) : OnTouchListener {
    //    private var downState: Boolean = false
    //    private var reference: WeakReference<SceneNewEditActivity>? = null
    //
    //    init {
    //        reference = WeakReference<SceneNewEditActivity>(context)
    //    }
    //
    //    @SuppressLint("CheckResult", "ClickableViewAccessibility")
    //    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
    //        v as ScaleSwitchCompat
    //        if (event?.action == MotionEvent.ACTION_DOWN) {
    //            downState = v.isChecked
    //        } else if (event?.action == MotionEvent.ACTION_UP) {
    //            Observable.just(1).delay(100, TimeUnit.MILLISECONDS).subscribeOn(Schedulers.io())
    //                .observeOn(AndroidSchedulers.mainThread()).subscribe { integer: Int? ->
    //                    val upState = v.isChecked
    //                    if (downState == upState) return@subscribe
    //                    if (v.id == R.id.cb_auto_run) {
    //                        reference?.get()?.mDataEventAddSceneBean?.scenario_type_edit_time =
    //                            System.currentTimeMillis() //记录自动执行开关的打开时间
    //                        reference?.get()?.mViewModel?.notifyAutoRunStateChanged(upState)
    //                    } else {
    //                        reference?.get()?.askBeforeRunEvent()
    //                    }
    //                }
    //        }
    //        return false //返回false事件往下传递
    //    }
    //}


    private fun initLiveDataObserve() {
        mViewModel.tryStartSceneReadyLiveData.observe(this) {}
        mViewModel.sceneEditReadyLiveData.observe(this) {}
        mViewModel.scenarioBeanLiveData.observe(this) {
            if (mViewModel.isFirstState == 0) {
                setUItDefaultValue()
            }
        }
        mViewModel.btnReSetIsEnAbleLiveData.observe(this) {
            mViewDataBinding.btnReset.alpha = if (it) 1f else 0.5f
            mViewDataBinding.btnReset.isEnabled = it
        }
    }

    private fun initLiveEventListener() {
        //场景添加成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).observe(this) {
            //添加成功，退出界面
            if (it != null) {
                handleSceneChange()
            }
        }
        //场景修改成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_MODIFY_SUCCESS, MySceneBean::class.java).observe(this) {
            if (it != null) {
                //修改成功
                handleSceneChange()
            }
        }
        //试用结束执行的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_TRY_SCENE_END, String::class.java).observe(this) {
            //试用结束
            mViewModel.handleTrySceneEnd(it)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initRecyclerView() {
        //------条件---------
        mEditConditionAdapter = SceneEditConditionAdapter()
        mViewDataBinding.rvEditSceneCondition.addItemDecoration(
            SceneEditConditionAdapter.MyGridItemDecoration()
        )
        //条件列表数据回调
        mViewModel.sceneConditionLiveData.observe(this) {
            mEditConditionAdapter?.data = it
            mEditConditionAdapter?.notifyDataSetChanged()

            if (mViewModel.isFirstState == 0) { //数据初始化时，不做处理，做界面初始化
                if (it.size > 1) {
                    mViewDataBinding.llEditSceneAutoExecuteSet.visibility = View.VISIBLE
                    mViewDataBinding.llEditSceneAutoExecuteFrequency.visibility = View.VISIBLE
                } else {
                    mViewDataBinding.llEditSceneAutoExecuteSet.visibility = View.GONE
                    mViewDataBinding.llEditSceneAutoExecuteFrequency.visibility = View.GONE
                }
                mViewModel.isFirstState++ //isFirstState=1 进入初始页
                mViewModel.updateBtnState()
                return@observe
            }

            //初始页添加条件后，两个开关自动开启
            if (mViewModel.isFirstState == 1 && !mViewDataBinding.llEditSceneAutoExecuteSet.isVisible && it.size > 1) {
                mViewDataBinding.cbAutoRun.isChecked = true
                mViewModel.curSceneEditBean?.scenario?.isAutoRun = true

                mViewDataBinding.cbAskBeforeRun.isChecked = true
                mViewModel.curSceneEditBean?.scenario?.isAskBeforeAutoRun = true

                mViewDataBinding.tabSwitchViewExecuteFrequency.selectedIndex = 0
            }
            mViewModel.isFirstState++ //20241114 解决先加动作后加条件时，isFirstState连续+1，导致执行前询问没有赋值

            //区域本来是隐藏的，自动执行
            if (!mViewDataBinding.llEditSceneAutoExecuteSet.isVisible && it.size > 1) { //无条件-》有条件，显示自动执行
                mViewDataBinding.llEditSceneAutoExecuteSet.visibility = View.VISIBLE
                mViewDataBinding.llEditSceneAutoExecuteFrequency.visibility = View.VISIBLE
                mViewModel.updateBtnState()
                return@observe
            }
            if (mViewDataBinding.llEditSceneAutoExecuteSet.isVisible && it.size == 1) { //有条件-》无条件
                mViewDataBinding.llEditSceneAutoExecuteSet.visibility = View.GONE
                mViewDataBinding.llEditSceneAutoExecuteFrequency.visibility = View.GONE
                mViewModel.updateBtnState()
                return@observe
            }

            mViewModel.updateBtnState()
        }
        //点击条件删除事件
        mEditConditionAdapter?.addChildClickViewIds(R.id.iv_condition_item_delete)
        mEditConditionAdapter?.setOnItemChildClickListener { _, _, position ->
            if (mEditConditionAdapter?.data?.get(position)?.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.edgeCondition = null
            } else {
                mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.conditions?.let {
                    var index0 = 0
                    val condition2 =
                        mEditConditionAdapter?.data?.get(position)?.condition ?: return@setOnItemChildClickListener

                    it.forEachIndexed { index, condition ->
                        if (condition.skillId == condition2.skillId) {
                            index0 = index
                        }
                    }
                    it.removeAt(index0)
                }
            }

            mViewModel.updateConditions()
            mViewModel.updateIcons()
        }
        mEditConditionAdapter?.setOnItemClickListener { _, view, position ->
            //注意这里添加需要判断是状态条件还是触发条件
            val bean = mEditConditionAdapter?.data?.get(position)
            if (bean?.itemType == SceneConditionBean.ITEM_TITLE) return@setOnItemClickListener
            if (DebouncingUtils.isValid(view, 1000)) {
                when {
                    //有4种状态，1：没有触发条件状态下添加触发条件，2.有触发条件状态下编辑触发条件
                    // 3、添加状态条件。4、编辑状态条件
                    bean?.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION -> { //编辑触发条件
                        showConditionOrActionDialog(
                            SceneNewEditType.TRIGGER_CONDITION,
                            bean.conditionType != SceneConditionBean.ADD_CONDITION_ITEM,
                            position
                        )
                    }

                    bean?.type == SceneConditionBean.ConditionType.STATUS_CONDITION -> { //编辑状态条件
                        showConditionOrActionDialog(
                            SceneNewEditType.STATUS_CONDITION,
                            bean.conditionType != SceneConditionBean.ADD_CONDITION_ITEM,
                            position
                        )
                    }

                    mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.edgeCondition == null -> { //添加触发条件
                        showConditionOrActionDialog(
                            SceneNewEditType.TRIGGER_CONDITION,
                            bean?.conditionType != SceneConditionBean.ADD_CONDITION_ITEM,
                            position
                        )
                    }

                    else -> { //添加状态条件
                        showConditionOrActionDialog(
                            SceneNewEditType.STATUS_CONDITION,
                            bean?.conditionType != SceneConditionBean.ADD_CONDITION_ITEM,
                            position
                        )
                    }
                }
            }
        }
        val manager = GridLayoutManager(this, 3)
        manager.spanSizeLookup = mEditConditionAdapter?.getLayoutManager()
        mViewDataBinding.rvEditSceneCondition.layoutManager = manager
        mViewDataBinding.rvEditSceneCondition.adapter = mEditConditionAdapter
        mViewDataBinding.rvEditSceneCondition.itemAnimator = null

        //--------动作-----------------
        myItemTouchHelperCallback = MyItemTouchHelperCallback()
        val itemTouchHelper = ItemTouchHelper(myItemTouchHelperCallback!!)
        itemTouchHelper.attachToRecyclerView(mViewDataBinding.rvEditSceneAction)
        myItemTouchHelperCallback?.setItemMoveCallBack(object : ItemHolderMoveCallback {
            override fun onItemHolderMoveStart(fromPosition: Int?) {
            }

            override fun onItemHolderMoveEnd(toPosition: Int?) {
                mEditActionAdapter?.notifyDataSetChanged()
            }
        })
        mEditActionAdapter = SceneEditActionAdapter()
        mViewDataBinding.rvEditSceneAction.addItemDecoration(
            SceneEditActionAdapter.MyGridItemDecoration()
        )
        mViewDataBinding.rvEditSceneAction.adapter = mEditActionAdapter
        mViewModel.sceneActionLiveData.observe(this) {
            mEditActionAdapter?.data = it
            mEditActionAdapter?.notifyDataSetChanged()
        }
        mEditActionAdapter?.setOnActionItemClickListener(object : SceneEditActionAdapter.OnActionItemClickListener {
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                if (position == -1) return
                if (view.id == R.id.btn_action_item_remove) { //动作删除按钮
                    mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.sequence?.removeAt(position)
                    mEditActionAdapter?.data?.removeAt(position)
                    mEditActionAdapter?.notifyItemRemoved(position)
                    mEditActionAdapter?.data?.size?.let {
                        if (it == 1) {
                            mEditActionAdapter?.notifyItemChanged(0)
                        } else {
                            mEditActionAdapter?.notifyItemRangeChanged(position, it.minus(1))
                        }
                    }
                    checkLastItemDelete()
                    mViewModel.updateBtnState()
                    mViewModel.updateIcons()
                    return
                }
                if (DebouncingUtils.isValid(view, 1000)) { //添加/编辑动作
                    //动作个数不能大于30，如果当前已是30，则所有动作都是编辑状态
                    //                    val isEditActionItem: Boolean = if(mViewModel.getActionSize() >=GlobalConfig.SCENE_ACTION_COUNT_MAX){
                    //                        true
                    //                    }else{
                    //                        position != adapter.data.size - 1
                    //                    }
                    //使用itemType来作为判断
                    val isEditActionItem: Boolean
                    val defItemViewType = mEditActionAdapter?.getDefItemViewType(position)
                    isEditActionItem = defItemViewType != SceneActionBean.ADD_ACTION_ITEM
                    showConditionOrActionDialog(
                        SceneNewEditType.ACTION, isEditItem = isEditActionItem, position
                    )
                }
            }

            override fun onItemMove(fromPosition: Int, toPosition: Int) {
                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.sequence?.let {
                            Collections.swap(
                                it, i, i + 1
                            )
                        }
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.sequence?.let {
                            Collections.swap(
                                it, i, i - 1
                            )
                        }
                    }
                }
                mViewModel.updateBtnState()
                mViewModel.updateIcons()
            }
        })

        //-----------条件动作映射图标----------------------
        mEditSkillIconAdapter = SceneEditSkillIconAdapter()

        mViewDataBinding.rvActionIconEdit.adapter = mEditSkillIconAdapter
        mViewModel.mSceneSkillInfoLiveData.observe(this) {
            mViewDataBinding.rvActionIconEdit.visibility = if (it.size == 0) {
                View.GONE
            } else {
                View.VISIBLE
            }
            mViewDataBinding.slEditContent.clearFocus()
            mEditSkillIconAdapter?.data = it
            mEditSkillIconAdapter?.notifyDataSetChanged()
        }
        mViewDataBinding.rvActionIconEdit.addItemDecoration(EquallySpaceDecoration(0, 10))

    }


    override fun onClick(v: View?) {
        val id = v?.id ?: return
        if (id == R.id.cl_edit_auto_run_hot_area) { //自动执行热区
            mViewDataBinding.cbAutoRun.isChecked = !mViewDataBinding.cbAutoRun.isChecked
            val isChecked = mViewDataBinding.cbAutoRun.isChecked
            mDataEventAddSceneBean?.let {
                it.scenario_type = isChecked
                it.scenario_type_edit_time = System.currentTimeMillis()
            }
            mViewModel?.notifyAutoRunStateChanged(isChecked)
        } else if (id == R.id.cl_edit_ask_hot_area) { //自动执行前询问热区
            mViewDataBinding.cbAskBeforeRun.isChecked = !mViewDataBinding.cbAskBeforeRun.isChecked
            val state = mViewDataBinding.cbAskBeforeRun.isChecked //拿到的原本的状态，onclick拿到的是改变后的状态
            if (state) {
                mViewModel.notifyAskBeforeStateChanged(true)
            } else { //执行前询问点击关闭时弹窗提示
                //            mViewDataBinding.cbAskBeforeRun.isChecked = false //会瞬间有个点击效果，但不切到关闭
                DefaultDialogFragment.showDialog(
                    manager = supportFragmentManager,
                    action = ACTION_CANCEL_ASK_BEFORE_START,
                    title = getString(R.string.scene_text_title_ask_before_start_scene),
                    content = getString(R.string.scene_text_content_ask_before_start_scene),
                    leftButtonText = getString(R.string.scene_text_close_ask_before_start_scene),
                    rightButtonText = getString(R.string.scene_text_common_cancel),
                    onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                        override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                            mViewDataBinding.cbAskBeforeRun.isChecked = false
                            mViewModel.notifyAskBeforeStateChanged(false)
                            dialog.dismissWithAnimate()
                        }

                        override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                            mViewDataBinding.cbAskBeforeRun.isChecked = true
                            dialog.dismissWithAnimate()
                        }

                        override fun onTouchOutside(dialog: DefaultDialogFragment) {
                            mViewDataBinding.cbAskBeforeRun.isChecked = true
                            dialog.dismissWithAnimate()
                        }
                    },
                    isSoundToast = true,
                    showLeftBtnWarningStyle = true
                )
            }
        } else if (!DebouncingUtils.isValid(v)) return // 防止快速点击

        if (id == R.id.iv_back) { //点击返回按钮
            onBackPressed()
        } else if (id == R.id.btn_save || id == R.id.btn_save2) { //点击保存按钮
            toSaveScene(isFromClickDialog = false)
        } else if (id == R.id.btn_try_run || id == R.id.btn_try_run2) { //点击试用按钮
            mDataEventAddSceneBean?.try_click = true
            mDataEventAddSceneBean?.try_click_time = System.currentTimeMillis()
            if (mViewModel.onTryStartSceneLiveData.value == false) {
                mViewModel.tryStartScene()
            } else {
                mViewModel.stopTryScene()
            }
        } else if (id == R.id.tv_edit_name) { //点击修改场景名称
            mDataEventAddSceneBean?.scenario_name_edit_time = System.currentTimeMillis()
            showRenameDialog()
        } else if (id == R.id.btn_reset) {
            DefaultDialogFragment.showDialog(
                manager = supportFragmentManager,
                action = ACTION_RESET,
                content = getString(R.string.scene_text_alert_content_cleanup),
                leftButtonText = getString(R.string.scene_text_common_clean_up),
                rightButtonText = getString(R.string.scene_text_common_cancel),
                onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                    override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                        mViewModel.initClearBtn()
                        mViewModel.reset()
                        SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                        dialog.dismissWithAnimate()
                    }

                    override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                        dialog.dismissWithAnimate()
                    }
                },
                isCloseVisible = false,
                isSoundToast = true
            )
        }
    }

    //    //注意：自动执行开关必须用这个
    //    override fun onCheckedChanged(v: CompoundButton?, isChecked: Boolean) {
    //        if (v?.id == R.id.ck_auto_run) { //点击自动执行开关
    //            mDataEventAddSceneBean?.scenario_type_edit_time = System.currentTimeMillis() //记录自动执行开关的打开时间
    //            mViewModel.notifyAutoRunStateChanged(isChecked)
    //        }
    //    }

    /**执行前询问点击滑动事件*/
    private fun askBeforeRunEvent() {
        mDataEventAddSceneBean?.inquiry_switch_edit_time = System.currentTimeMillis()
        val state = mViewDataBinding.cbAskBeforeRun.isChecked //onTouchAction拿到的是原本的状态，onclick拿到的是改变后的状态
        if (state) {
            mViewModel.notifyAskBeforeStateChanged(true)
        } else { //执行前询问点击关闭时弹窗提示
            //            mViewDataBinding.cbAskBeforeRun.isChecked = false //会瞬间有个点击效果，但不切到关闭
            DefaultDialogFragment.showDialog(
                manager = supportFragmentManager,
                action = ACTION_CANCEL_ASK_BEFORE_START,
                title = getString(R.string.scene_text_title_ask_before_start_scene),
                content = getString(R.string.scene_text_content_ask_before_start_scene),
                leftButtonText = getString(R.string.scene_text_close_ask_before_start_scene),
                rightButtonText = getString(R.string.scene_text_common_cancel),
                onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                    override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                        mViewDataBinding.cbAskBeforeRun.isChecked = false
                        mViewModel.notifyAskBeforeStateChanged(false)
                        dialog.dismissWithAnimate()
                    }

                    override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                        mViewDataBinding.cbAskBeforeRun.isChecked = true
                        dialog.dismissWithAnimate()
                    }

                    override fun onTouchOutside(dialog: DefaultDialogFragment) {
                        mViewDataBinding.cbAskBeforeRun.isChecked = true
                        dialog.dismissWithAnimate()
                    }
                },
                isSoundToast = true,
                showLeftBtnWarningStyle = true
            )
        }
    }

    /**
     * 显示添加条件动作弹窗
     * @param type 区分是触发条件、状态条件、动作
     * @param isEditItem 区分是编辑状态还是添加状态
     * @param position 集合下标，注意动作下标没问题，但是条件由于是触发条件和状态条件整合起来的，后续如果发生更改的话，需要注意一下条件的下标问题
     */
    private fun showConditionOrActionDialog(
        type: SceneNewEditType, isEditItem: Boolean = false, position: Int = 0
    ) {
        val bean = mViewModel.curSceneEditBean
        bean ?: return
        when (type) {
            SceneNewEditType.TRIGGER_CONDITION -> { //触发条件
                mDataEventAddSceneBean?.trigger_condition_edit_time = System.currentTimeMillis()
            }

            SceneNewEditType.STATUS_CONDITION -> { //状态条件
                mDataEventAddSceneBean?.status_condition_edit_time = System.currentTimeMillis()
            }

            else -> { //动作
                mDataEventAddSceneBean?.action_edit_time = System.currentTimeMillis()
            }
        }
        val dialog = SceneEditContainerDialogFragment(type, bean, isEditItem, position)
        dialog.setOnSceneEditFragmentExitListener(this) //onSceneEditFragmentExit
        dialog.show(supportFragmentManager, "fragment_tag_add_skill")
    }

    /**3级弹窗的回调 ，上面SceneEditContainerDialogFragment的回调*/
    override fun onSceneEditFragmentExit(sceneEditBean: MySceneBean) {
        mViewModel.updateCurSceneAndUI(sceneEditBean)
    }

    /** 检查是否为最后一个(位置30)的item删除后总的数量是否为29，如是29则需要把+号给添加上去*/
    private fun checkLastItemDelete() {
        val size = mViewModel.getActionSize()
        CommonLogUtils.logD(TAG, "删除动作item后的数量:$size")
        if (size == GlobalConfig.SCENE_ACTION_COUNT_MAX - 1) {
            mEditActionAdapter?.addData(
                SceneActionBean(
                    null, R.drawable.scene_selector_edit_action_item_bg, SceneActionBean.ADD_ACTION_ITEM
                )
            )
        }
    }

    /**处理场景变更后的逻辑*/
    private fun handleSceneChange() {
        mViewModel.stopTryScene()
        //只finish当前页面，其他activity监听添加成功liveEventBus
        finish()
    }

    private fun initTryStartSceneButton() {
        mViewModel.onTryStartSceneLiveData.observe(this) { onTry ->
            if (mViewModel.mEnterType == SceneNewEditEnterType.Download) { //是否下载进来
                if (onTry) {
                    mViewDataBinding.btnTryRun2.text = ""
                    mViewDataBinding.lottieTryRun2.setAnimation(R.raw.scene_try_run)
                    mViewDataBinding.lottieTryRun2.playAnimation()
                } else {
                    mViewDataBinding.lottieTryRun2.cancelAnimation()
                    mViewDataBinding.lottieTryRun2.clearAnimation()
                    mViewDataBinding.btnTryRun2.text = getString(R.string.scene_text_scene_detail_try)
                }
                mViewDataBinding.lottieTryRun2.isVisible = onTry
            } else {
                if (onTry) {
                    mViewDataBinding.btnTryRun.text = ""
                    mViewDataBinding.lottieTryRun.setAnimation(R.raw.scene_try_run)
                    mViewDataBinding.lottieTryRun.playAnimation()
                } else {
                    mViewDataBinding.lottieTryRun.cancelAnimation()
                    mViewDataBinding.lottieTryRun.clearAnimation()
                    mViewDataBinding.btnTryRun.text = getString(R.string.scene_text_scene_detail_try)
                }
                mViewDataBinding.lottieTryRun.isVisible = onTry
            }

        }
    }

    /**是否需要新增并保存到我的*/
    private fun isAddNewSave2Me(): Boolean {
        return mViewModel.mEnterType != SceneNewEditEnterType.Me
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        mViewModel.stopTryScene()

        if (mViewModel.mEnterType == SceneNewEditEnterType.AddNew //创建
            || mViewModel.mEnterType == SceneNewEditEnterType.Draft //草稿
        ) {
            if (!mViewModel.btnReSetIsEnAbleLiveData.value!!) {
                finish()
            } else {
                DefaultDialogFragment.showDialog(
                    manager = supportFragmentManager,
                    action = ACTION_SAVE,
                    title = "",
                    content = getString(R.string.scene_text_save_current_modify),
                    leftButtonText = getString(R.string.scene_text_common_reserve),
                    rightButtonText = getString(R.string.scene_text_common_reserve_no),
                    onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                        override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                            toSaveScene(isFromClickDialog = true)
                            dialog.dismissWithAnimate()
                            finish()
                        }

                        override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                            dialog.dismissWithAnimate()
                            finish()
                        }
                    },
                    isCloseVisible = false,
                    isSoundToast = true
                )
            }
        } else if (mViewModel.mEnterType == SceneNewEditEnterType.Me) {
            //场景内容未做修改或不满足保存条件，不保存此次编辑内容，点击后返回直接上一级场景详情页面
            if (!mViewModel.btnReSetIsEnAbleLiveData.value!! || !mViewModel.sceneEditReadyLiveData.value!!) {
                finish()
            } else {
                DefaultDialogFragment.showDialog(
                    manager = supportFragmentManager,
                    action = ACTION_SAVE,
                    title = "",
                    content = getString(R.string.scene_text_save_current_modify),
                    leftButtonText = getString(R.string.scene_text_common_reserve),
                    rightButtonText = getString(R.string.scene_text_common_reserve_no),
                    onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                        override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                            toSaveScene(isFromClickDialog = true)
                            dialog.dismissWithAnimate()
                            finish()
                        }

                        override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                            if (mViewModel.mEnterBean?.scenario?.isAutoRun == true) {
                                //如果编辑场景进来的时候场景参数为自动执行打开状态
                                // 那么进入编辑页面时候会调用中止自动执行接口
                                // 编辑页面点击返回需要再次调用开启自动执行接口
                                mViewModel.autoExecuteScenario()
                            }
                            dialog.dismissWithAnimate()
                            finish()
                        }
                    },
                    isCloseVisible = false,
                    isSoundToast = true
                )
            }
        } else if (mViewModel.mEnterType == SceneNewEditEnterType.Download) { //下载
            //场景内容未做修改或不满足保存条件，不保存此次编辑内容，点击后返回直接上一级场景详情页面
            if (!mViewModel.btnReSetIsEnAbleLiveData.value!! || !mViewModel.sceneEditReadyLiveData.value!!) {
                finish()
            } else {
                DefaultDialogFragment.showDialog(
                    manager = supportFragmentManager,
                    action = ACTION_SAVE,
                    title = "",
                    content = getString(R.string.scene_text_exit_edit_scene_save_tome),
                    leftButtonText = getString(R.string.scene_text_common_save),
                    rightButtonText = getString(R.string.scene_text_common_save_no),
                    onDialogButtonClickListener = object : DefaultDialogFragment.OnDialogButtonClickListener {
                        override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                            toSaveScene(isFromClickDialog = true)
                            dialog.dismissWithAnimate()
                            finish()
                        }

                        override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                            dialog.dismissWithAnimate()
                            finish()
                        }
                    },
                    isCloseVisible = false,
                    isSoundToast = true
                )
            }
        }

    }

    /**场景数据的保存  创建场景+弹窗是保存草稿 ，点完成按钮还是点弹窗的保存 都是执行下面方法*/
    private fun toSaveScene(isFromClickDialog: Boolean) {
        mDataEventAddSceneBean?.save_click = true
        mDataEventAddSceneBean?.save_click_time = System.currentTimeMillis()

        mViewModel.stopTryScene()
        if (mViewModel.checkSceneName()) {
            CommonToastUtils.show(getString(R.string.scene_text_common_scene_name_repeat))
            return
        }

        //自动执行和执行前询问,执行频率，在保存时一定要用界面上呈现的状态
        if (!mViewDataBinding.llEditSceneAutoExecuteSet.isVisible) {
            mViewModel.curSceneEditBean?.scenario?.isAutoRun = false
            mViewModel.curSceneEditBean?.scenario?.isAskBeforeAutoRun = false
            mViewModel.curSceneEditBean?.scenario?.executeFrequency = 0
        } else {
            mViewModel.curSceneEditBean?.scenario?.isAutoRun = mViewDataBinding.cbAutoRun.isChecked
            mViewModel.curSceneEditBean?.scenario?.isAskBeforeAutoRun = mViewDataBinding.cbAskBeforeRun.isChecked
            mViewModel.curSceneEditBean?.scenario?.executeFrequency =
                mViewDataBinding.tabSwitchViewExecuteFrequency.selectedIndex
        }

        if (isFromClickDialog) {
            if (mViewModel.mEnterType == SceneNewEditEnterType.AddNew // 创建场景 （new和草稿）点返回的都保存草稿
                || mViewModel.mEnterType == SceneNewEditEnterType.Draft
            ) {
                setDataEvent(true)
                mViewModel.saveCacheEditData()
                return
            }
        }
        if (mViewModel.mEnterType == SceneNewEditEnterType.Download) {
            TrackUtils.clickDownSave(
                mViewModel.curSceneEditBean?.scenario?.scenarioInfo, mViewModel.mEnterScenarioL3id
            ) //埋点
        }
        val isSuccess = mViewModel.saveScene() //保存并删除草稿
        if (mViewModel.mEnterType == SceneNewEditEnterType.Download) {
            if (!isSuccess) {
                CommonToastUtils.show(R.string.scene_toast_scene_add_fail)
            }
            TrackUtils.resultClickDownSave(
                mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.scenarioId,
                mViewModel.mEnterScenarioL3id,
                isSuccess
            )
        } else {
            setDataEvent(isFromClickDialog)
        }
    }

    /**重命名场景名称 弹窗*/
    private fun showRenameDialog() {
        val mRenameDialog = RenameDialogFragment()
        mRenameDialog.scene = mViewModel.curSceneEditBean?.scenario
        mRenameDialog.onConfirmListener = object : RenameDialogFragment.OnConfirmListener {
            override fun onConfirm() {
                mViewModel.updateBtnState()
                mViewDataBinding.tvSceneName.text = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.scenarioName
            }
        }
        mRenameDialog.mDataList = SceneManager.getSceneInfoList().toMutableList()
        mRenameDialog.showNow(supportFragmentManager, "FRAGMENT_TAG_RENAME_DIALOG")
    }


    private fun setDataEvent(isClickDialog: Boolean) {
        if (mViewModel.mEnterType == SceneNewEditEnterType.AddNew //
            || mViewModel.mEnterType == SceneNewEditEnterType.Draft //
            || mViewModel.mEnterType == SceneNewEditEnterType.Me
        ) {

            //            if (mViewModel.enterType == SceneNewEditEnterType.Recommend) {
            //                TrackUtils.clickSaveRecommendNewScene(
            //                    scenario = mViewModel.curSceneEditBean?.scenario,
            //                    execution_switch_isVisible = mViewDataBinding.cbAskBeforeRun.isVisible,
            //                    inquiry_switch_isVisible = mViewDataBinding.ckAutoRun.isVisible,
            //                    scenario_originalname = mOriginalSceneName
            //                )
            //            } else {
            //
            //            }

            mDataEventAddSceneBean?.scenario_id = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.scenarioId
            mDataEventAddSceneBean?.scenario_name = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.scenarioName
            mDataEventAddSceneBean?.scenario_type = mViewModel.curSceneEditBean?.scenario?.isAutoRun
            val triggerCon = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.edgeCondition
            triggerCon?.let {
                val skillIds = arrayListOf<Int>(it.skillId)
                mDataEventAddSceneBean?.trigger_condition_type =
                    SceneDataEventManager.getSkillIdFirstMenuName(skillIds, SceneNewEditType.TRIGGER_CONDITION)
                mDataEventAddSceneBean?.trigger_condition_detail = SceneDataEventManager.getTriggerConditionDetail(it)
            }
            val statusCon = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.conditions
            statusCon?.let {
                val skillIds = it.map { item -> item.skillId }
                mDataEventAddSceneBean?.status_condition_type =
                    SceneDataEventManager.getSkillIdFirstMenuName(skillIds, SceneNewEditType.STATUS_CONDITION)
                mDataEventAddSceneBean?.status_condition_detail = SceneDataEventManager.getStatusConditionDetail(it)
            }
            val actionData = mViewModel.curSceneEditBean?.scenario?.scenarioInfo?.sequence
            actionData?.let {
                val skillIds = it.map { item -> item.action.skillId }
                mDataEventAddSceneBean?.action_type =
                    SceneDataEventManager.getSkillIdFirstMenuName(skillIds, SceneNewEditType.ACTION)
                mDataEventAddSceneBean?.action_detail = SceneDataEventManager.getActionDetail(it)
            }

            mDataEventAddSceneBean?.inquiry_switch = if (mViewDataBinding.cbAskBeforeRun.isVisible) {
                if (mViewModel?.curSceneEditBean?.scenario?.isAskBeforeAutoRun == true) "开" else "关"
            } else {
                "无"
            }

            mDataEventAddSceneBean?.execution_switch = if (mViewDataBinding.cbAutoRun.isVisible) {
                if (mViewModel?.curSceneEditBean?.scenario?.isAutoRun == true) "开" else "关"
            } else {
                "无"
            }

            val type = if (mViewModel.mEnterType == SceneNewEditEnterType.AddNew  //新建
                || mViewModel.mEnterType == SceneNewEditEnterType.Draft
            ) { //草稿
                if (!isClickDialog) 0 else 1
            } else {  //我的编辑
                if (!isClickDialog) 2 else 3
            }
            TrackUtils.clickSaveNewScene(mDataEventAddSceneBean, type)
        }

    }

    override fun onDestroy() {
        myItemTouchHelperCallback?.destroyItemMoveCallBack()
        SceneConditionManager.clearSkillIdData()

        if (mViewDataBinding.lottieTryRun.isVisible) {
            mViewDataBinding.lottieTryRun.cancelAnimation()
            mViewDataBinding.lottieTryRun.clearAnimation()
        }
        if (mViewDataBinding.lottieTryRun2.isVisible) {
            mViewDataBinding.lottieTryRun2.cancelAnimation()
            mViewDataBinding.lottieTryRun2.clearAnimation()
        }
        super.onDestroy()
    }

    override fun onStop() {
        super.onStop()
        if (mViewModel.btnReSetIsEnAbleLiveData.value == false && //没有进行编辑修改过
            mViewModel.curSceneEditBean?.scenario?.isAutoRun == true //
            && mViewModel.mEnterType == SceneNewEditEnterType.Me //只有我的场景 编辑才能执行下面设置
        ) {
            //这里需要判断如果什么都没有修改并且不是添加场景的状态返回
            mViewModel.autoExecuteScenario()
        }
    }


    override fun finish() {
        if (mViewModel.mEnterType == SceneNewEditEnterType.Download) {
            window.setBackgroundDrawableResource(R.color.transparent)
        }
        super.finish()
    }
}