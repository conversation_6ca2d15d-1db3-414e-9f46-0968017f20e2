package com.dfl.smartscene.ui.overlay.time.date;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.android.animationlib.TabSwitchView;
import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.global.GlobalConstant;
import com.dfl.android.common.global.SkillsListConstant;
import com.dfl.android.common.util.TimeUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.condition.WhichDayBean;
import com.dfl.smartscene.databinding.SceneDialogDateSettingBinding;
import com.dfl.smartscene.ui.overlay.time.date.adapter.WhichDayAdapter;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.wheel.WheelView;
import com.iauto.scenarioadapter.ArgType;
import com.iauto.scenarioadapter.InputArgInfo;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :状态-周期，lk2a弹框组件变更
 * version: 1.0
 */
@Deprecated
public class DateSettingDialog extends BaseDialog implements View.OnClickListener {
    private static final String TAG = GlobalConstant.GLOBAL_TAG + "DateSettingDialog";
    private final String title;
    private final Context mContext;
    private SceneDialogDateSettingBinding mVB;
    private RadioButton mEveryDay;
    private RadioButton mWorkDay;
    private RadioButton mHoliday;
    private LinearLayout mLlTopSetting;
    private LinearLayout mLLContentSet;
    private View mDateWheelMask;
    private RecyclerView mRecycleView;
    //标题
    private TextView mTitleView;
    private View vLineBg;
    private LinearLayout mLlAppointHour;
    private ArrayList<WhichDayBean> mWhichList;
    private int mWitchDaySkillId = 0;

    private List<String> mYearList;
    private List<String> mDayList;
    private List<String> monthList;
    private int selectYear = 2023;
    private int selectMonth = 1;
    private int mDay = 0;
    private WheelView mYearWheelView;
    private WheelView mMonthWheelView;
    private WheelView mDayWheelView;
    private boolean mIsFirst = true;
    private ScenarioInfo.Condition preCondition;
    private WhichDayAdapter dayAdapter;
    private boolean everyDayChecked = false;

    public DateSettingDialog(@NonNull Context context, @Nullable ScenarioInfo.Condition preCondition
            , String title) {
        super(context);
        this.mContext = context;
        this.title = title;
        this.preCondition = preCondition;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogDateSettingBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        initClick();
        initRecycleViewData();
        changeCheckedState(0);
        setInitData();
    }

    private void setInitData() {
        if (preCondition == null) return;
        List<InputArgInfo> inputs = preCondition.getInput();
        int skillId = preCondition.getSkillId();
        //1. 第一排tab选中
        int position = 1;
        switch (skillId) {
            //每天和每周内的某天
            case SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID:
                if (inputs.size() == 1) {
                    String value = inputs.get(0).getValue();
                    int setValue = Integer.valueOf(value);
                    //推荐-每天
                    if (isSelectedDay(setValue, SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID)) {
                        position = 0;
                        checkEveryWeekAllItem();
                    } else { //每周几
                        position = 2;
                        checkInitDays(setValue);
                    }
                }
                break;
            //指定时间
            case SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID:
                position = -3;
                checkPointerDay(inputs);
                break;
            //推荐 - 工作日， 推荐 - 节假日
            case SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID:
                position = 4;
                break;
            case SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID:
                position = 5;
                break;
        }
        changeRadioButtonState(position);
        changeCheckedState(position);
    }

    /**
     * 推荐选中每周
     */
    private void checkEveryWeekAllItem() {
        if (mWhichList != null && !mWhichList.isEmpty()) {
            for (WhichDayBean dayBean : mWhichList) {
                dayBean.setChecked(true);
            }
            dayAdapter.setList(mWhichList);
        }
    }

    private void checkEveryWeekDefaultDay() {
        if (mWhichList != null && !mWhichList.isEmpty()) {
            for (WhichDayBean dayBean : mWhichList) {
                dayBean.setChecked(false);
            }
            mWhichList.get(0).setChecked(true);
            dayAdapter.setList(mWhichList);
        }
    }

    private void checkPointerDay(List<InputArgInfo> inputs) {
        String year = findPointDayValue(inputs, "year");
        String month = findPointDayValue(inputs, "month");
        String day = findPointDayValue(inputs, "day");
        if (year == null || month == null || day == null) {
            CommonLogUtils.logE(TAG, "checkPointerDay error!");
            return;
        }
        //添加判断后台返回月份和日期为个位数情况，加上0，防止选中之前值失败
        month = TimeUtils.singleTimeAddZero(month);
        day = TimeUtils.singleTimeAddZero(day);
        int yearIndex = mYearList.indexOf(year);
        if (yearIndex < 0) {
            yearIndex = 0;
        }
        int monthIndex = monthList.indexOf(month);
        selectYear = Integer.parseInt(year);
        selectMonth = Integer.parseInt(month);
        initDayWheelViewData();
        int dayIndex = mDayList.indexOf(day);

        //select them
        mYearWheelView.setCurrentItem(yearIndex);
        mMonthWheelView.setCurrentItem(monthIndex);
        mDayWheelView.setCurrentItem(dayIndex);
    }

    @Nullable
    private String findPointDayValue(List<InputArgInfo> inputs, String name) {
        for (InputArgInfo input : inputs) {
            if (Objects.equals(input.getName(), name)) {
                if (input.getValue() == null || "".equals(input.getValue())) {
                    return null;
                }
                return input.getValue();
            }
        }
        return null;
    }

    private void checkInitDays(int value) {
        List<Integer> list = new ArrayList<>();
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID);
        }
        if (isSelectedDay(value, SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID)) {
            list.add(SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID);
        }
        if (!list.isEmpty()) {
            for (WhichDayBean dayBean : mWhichList) {
                dayBean.setChecked(list.contains(dayBean.getSkillId()));
            }
            WhichDayAdapter adapter = (WhichDayAdapter) mRecycleView.getAdapter();
            adapter.setList(mWhichList);
        }
    }

    private void changeRadioButtonState(int position) {
        switch (position) {
            case 0:
            case 1:
            case 4:
            case 5:
                mVB.tabSwitchViewCondition.setSelectedIndex(0);
                break;
            case -3:
                mVB.tabSwitchViewCondition.setSelectedIndex(2);
                break;
            case 2:
                mVB.tabSwitchViewCondition.setSelectedIndex(1);
                break;
        }
    }

    private boolean isSelectedDay(int value, int dest) {
        return (value & dest) == dest;
    }

    //提交
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {//此处将处理数据的回调
            int skillId;
            List<InputArgInfo> list = new ArrayList<>();
            String desc;
            int tabSelectedIndex = mVB.tabSwitchViewCondition.getSelectedIndex();
            if (tabSelectedIndex == 1) {//自定义
                //周几
                desc = getWhichDaySelect();
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID;//0xFF02
                list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_DAY, ArgType.UINT32,
                        String.valueOf(mWitchDaySkillId)));
            } else if (tabSelectedIndex == 2) {//指定时间
                String year = mYearList.get(mYearWheelView.getCurrentItem());
                String month = monthList.get(mMonthWheelView.getCurrentItem());
                String day = mDayList.get(mDayWheelView.getCurrentItem());
                desc = year + mContext.getString(R.string.scene_text_common_year).concat(month)
                        + mContext.getString(R.string.scene_text_common_month).concat(day)
                        + mContext.getString(R.string.scene_text_common_day);
                //如果是01保存到ccm为1
                month = TimeUtils.singleTimeRemoveZero(month);
                day = TimeUtils.singleTimeRemoveZero(day);
                list.add(new InputArgInfo("year", ArgType.UINT32, year));
                list.add(new InputArgInfo("month", ArgType.UINT32, month));
                list.add(new InputArgInfo("day", ArgType.UINT32, day));
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID;

            } else if (mHoliday.isChecked()) {
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID;
                desc = mHoliday.getText().toString();
            } else if (mWorkDay.isChecked()) {
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID;
                desc = mWorkDay.getText().toString();
            } else { //每天
                //0xFF02---注意每天的设置能力ID和周几一样，只是value属性值变了
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID;
                desc = mEveryDay.getText().toString();
                list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_DAY, ArgType.UINT32,
                        String.valueOf(SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID)));
            }

            ScenarioInfo.Condition condition = new ScenarioInfo.Condition(
                    1, desc, skillId, title, list
            );
            EventBus.getDefault().post(condition);
            dismissWithAnimate();
            return;
        }
        if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
            return;
        }

        int position = -1;
        if (v.getId() == R.id.ctv_legal_working_day) {
            position = 4;
        } else if (v.getId() == R.id.ctv_statutory_holidays) {
            position = 5;
        } else if (v.getId() == R.id.ctv_every_day) {
            position = 1;
        }
        changeCheckedState(position);
    }

    private void changeCheckedState(int position) {
        if (position != 2 && position != 0 && position != -3 && position != -1) {
            mEveryDay.setChecked(position == 1);
            mWorkDay.setChecked(position == 4);
            mHoliday.setChecked(position == 5);
        }

        mLlAppointHour.setVisibility(position == -3 ? View.VISIBLE : View.GONE);
        vLineBg.setVisibility(position == -3 ? View.VISIBLE : View.GONE);
        mLlTopSetting.setVisibility(position != 2 ? View.VISIBLE : View.GONE);
        mRecycleView.setVisibility(position == 2 ? View.VISIBLE : View.GONE);
        mDateWheelMask.setVisibility(position == -3 ? View.VISIBLE : View.GONE);
        mLLContentSet.setVisibility(position != -3 ? View.VISIBLE : View.GONE);

        if (position == 1) {
            everyDayChecked = true;
            checkEveryWeekAllItem();
        } else if (position == 5 || position == 4) {
            if (everyDayChecked) {
                everyDayChecked = false;
                checkEveryWeekDefaultDay();
            }
        }
    }


    private void initView() {
        mLLContentSet = findViewById(R.id.ll_content_recommend_week);
        mEveryDay = findViewById(R.id.ctv_every_day);
        mWorkDay = findViewById(R.id.ctv_legal_working_day);
        mHoliday = findViewById(R.id.ctv_statutory_holidays);
        mRecycleView = findViewById(R.id.rv_date_which_day);
        mTitleView = findViewById(R.id.tv_date_title);
        mLlTopSetting = findViewById(R.id.ll_center_date_setting);

        mLlAppointHour = findViewById(R.id.ll_appoint_hour);
        vLineBg = findViewById(R.id.v_appoint_hour);
        mYearWheelView = findViewById(R.id.wheel_date_year);
        mMonthWheelView = findViewById(R.id.wheel_date_month);
        mDayWheelView = findViewById(R.id.wheel_date_day);
        mDateWheelMask = findViewById(R.id.date_wheel_mask);

        mTitleView.setText(title.isEmpty() ? "时间点 08:00" : title);
        //初始化多段选择器
        mVB.tabSwitchViewCondition.generateViews(Arrays.asList(getContext().getString(R.string.scene_text_edit_add_action_recommend), getContext().getString(R.string.scene_text_condition_weekly), getContext().getString(R.string.scene_text_edit_condition_time_someday)));
        mVB.tabSwitchViewCondition.setSelectedIndex(0);
        mVB.tabSwitchViewCondition.setOnTabSelectedListener(new TabSwitchView.OnTabSelectedListener() {
            @Override
            public void onTabSelected(int selectedPosition, Boolean isManual) {
                int position = -1;
                if (selectedPosition == 0) {
                    //选择推荐
                    position = 0;
                } else if (selectedPosition == 1) {
                    //选中每周
                    position = 2;
                } else {
                    //选中指定时间
                    position = -3;
                }
                changeCheckedState(position);
            }

            @Override
            public void onTabUnSelected(int lastPosition) {
            }

            @Override
            public void onInitViewComplete(Boolean isComplete) {
            }
        });
        initWheelData();
    }

    private void initClick() {
        mEveryDay.setOnClickListener(this);
        mWorkDay.setOnClickListener(this);
        mHoliday.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
    }

    private void initRecycleViewData() {
        mWhichList = new ArrayList<>();
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_monday),
                true, mContext.getString(R.string.scene_text_common_monday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_tuesday),
                false, mContext.getString(R.string.scene_text_common_tuesday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_wednesday),
                false, mContext.getString(R.string.scene_text_common_wednesday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_thursday),
                false, mContext.getString(R.string.scene_text_common_thursday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_friday),
                false, mContext.getString(R.string.scene_text_common_friday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_saturday),
                false, mContext.getString(R.string.scene_text_common_saturday_simple),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID));
        mWhichList.add(new WhichDayBean(mContext.getString(R.string.scene_text_common_sunday),
                false, mContext.getString(R.string.scene_text_common_day),
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID));

        dayAdapter = new WhichDayAdapter(mWhichList);
        dayAdapter.addChildClickViewIds(R.id.ctv_which_day_item);
        dayAdapter.setOnItemChildClickListener((adapter1, view, position) -> {
            //需要判断如果只有一个选中的情况下，是不可以取消选中的
            var index = 0;
            for (int i = 0; i < mWhichList.size(); i++) {
                if (mWhichList.get(i).getChecked()) {
                    index++;
                }
            }
            everyDayChecked = false;
            if (index == 1 && position < mWhichList.size() && mWhichList.get(position).getChecked())
                return;
            mWhichList.get(position).setChecked(!mWhichList.get(position).getChecked());
            dayAdapter.notifyItemChanged(position);
        });
        GridLayoutManager layoutManager = new GridLayoutManager(mContext, 3) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
        mRecycleView.setLayoutManager(layoutManager);
        mRecycleView.setAdapter(dayAdapter);
    }

    private String getWhichDaySelect() {
        int index = 0;
        StringBuilder buffer = new StringBuilder();
        buffer.append(mContext.getString(R.string.scene_text_condition_weekly));
        for (int i = 0; i < mWhichList.size(); i++) {
            if (mWhichList.get(i).getChecked()) {
                index++;
                mWitchDaySkillId = mWitchDaySkillId | mWhichList.get(i).getSkillId();
            }
        }
        int position = 0;
        for (WhichDayBean bean : mWhichList) {
            if (bean.getChecked()) {
                buffer.append(bean.getDesc());
                if (index != (position + 1)) {
                    buffer.append("/");
                }
                position++;
            }
        }
        if (position == 7) {
            buffer.delete(0, buffer.length());
            buffer.append(mContext.getString(R.string.scene_text_edit_condition_time_every_day));
        }
        return buffer.toString();
    }

    private void initWheelData() {
        mYearList = new ArrayList<>();
        mDayList = new ArrayList<>();
        int year = Calendar.getInstance().get(Calendar.YEAR);
        int month = Calendar.getInstance().get(Calendar.MONTH);
        mDay = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        selectYear = year;
        selectMonth = month + 1;
        mYearList.clear();
        for (int i = year; i < year + 100; i++) {
            mYearList.add(String.format(Locale.getDefault(), "%02d", i));
        }
        initWheelView(mYearWheelView, mYearList, 1);
        int defaultYearIndex = mYearList.indexOf(String.valueOf(year));
        mYearWheelView.setCurrentItem(defaultYearIndex);
        monthList = new ArrayList<>();
        for (int i = 1; i < 13; i++) {
            monthList.add(String.format(Locale.getDefault(), "%02d", i));
        }
        initWheelView(mMonthWheelView, monthList, 2);
        mMonthWheelView.setCurrentItem(8);
        initDayWheelViewData();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initWheelView(WheelView wheelView, List<String> list, int type) {
        UIUtils.initWheelView(mContext, wheelView, list, "", 0, index -> {
            if (type == 1) {
                String value = mYearList.get(index);
                if (value == null || "".equals(value)) {
                    value = "0";
                }
                selectYear = Integer.parseInt(value);
                initDayWheelViewData();
            } else if (type == 2) {
                String value = monthList.get(index);
                if (value == null || "".equals(value)) {
                    value = "0";
                }
                selectMonth = Integer.parseInt(value);
                initDayWheelViewData();
            }
        });
    }


    private void initDayWheelViewData() {
        mDayList.clear();
        int size = 30;
        if (selectMonth == 1 || selectMonth == 3
                || selectMonth == 5 || selectMonth == 7
                || selectMonth == 8 || selectMonth == 10
                || selectMonth == 12
        ) {
            size = 31;
        }
        if (selectMonth == 2 && isRunYear(selectYear)) {
            size = 29;
        } else if (selectMonth == 2) {
            size = 28;
        }
        for (int i = 1; i <= size; i++) {
            mDayList.add(String.format(Locale.getDefault(), "%02d", i));
        }
        initWheelView(mDayWheelView, mDayList, 3);
        if (mIsFirst) {
            mDayWheelView.setCurrentItem(6);
            mIsFirst = false;
        }
    }

    /**
     * 是否闰年
     *
     * @param year
     * @return
     */
    private boolean isRunYear(int year) {
        if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {
            return true;
        } else {
            return false;
        }
    }

}
