package com.dfl.smartscene.ui.overlay.media

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.databinding.SceneRecycelItemQqCheck6Binding

/**
 *Created by 钟文祥 on 2025/2/26.
 *Describer: lk2a 播放qq item。
 */
class QQCheck6Adapter() :
    BaseQuickAdapter<CheckListBean, BaseDataBindingHolder<SceneRecycelItemQqCheck6Binding>>(
        R.layout.scene_recycel_item_qq_check_6
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycelItemQqCheck6Binding>, item: CheckListBean
    ) {
        holder.dataBinding?.bean = item
    }


}