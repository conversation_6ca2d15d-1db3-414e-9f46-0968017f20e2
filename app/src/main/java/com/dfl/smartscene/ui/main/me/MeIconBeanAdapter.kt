package com.dfl.smartscene.ui.main.me

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MeIconBean
import com.dfl.smartscene.databinding.SceneRecycelItemMeIconBeanBinding

/**
 *Created by 钟文祥 on 2024/7/1.
 *Describer: 我的场景卡片的icon组
 */
class MeIconBeanAdapter : BaseQuickAdapter<MeIconBean, BaseDataBindingHolder<SceneRecycelItemMeIconBeanBinding>>(
    R.layout.scene_recycel_item_me_icon_bean
) {

    override fun convert(holder: BaseDataBindingHolder<SceneRecycelItemMeIconBeanBinding>, item: MeIconBean) {

        holder.dataBinding?.ivMeIconBean?.setImageResource(item.icon!!)
        holder.dataBinding?.ivMeIconBean?.setBackgroundResource(
            if (item.type < 0) {
                R.drawable.scene_shape_icon_bg_tr_r16
            } else if (item.type == 0) { //条件
                R.drawable.scene_shape_icon_bg_fang_r16
            } else { //动作
                R.drawable.scene_shape_icon_bg_yuan_r16
            }
        )


//        val v72 = AutoSizeUtils.dp2px(context, 72F)
//        val v48 = AutoSizeUtils.dp2px(context, 48F)
//        val v40 = AutoSizeUtils.dp2px(context, 40F)
//        holder.dataBinding?.ivMeIconBean?.setImageResource(item.icon!!)
//
//        when (item.type) {
//            -1 -> { //更多
//                holder.dataBinding?.ivMeIconBean?.layoutParams = LinearLayout.LayoutParams(v40, v72)
//                holder.dataBinding?.ivMeIconBean?.setPadding(0, 0, 0, 0)
//                holder.dataBinding?.ivMeIconBean?.setBackgroundResource(R.drawable.scene_shape_bg_me_action_null)
//            }
//            -2 -> { //锁链
//                holder.dataBinding?.ivMeIconBean?.layoutParams = LinearLayout.LayoutParams(v48, v72)
//                holder.dataBinding?.ivMeIconBean?.setPadding(0, 0, 0, 0)
//                holder.dataBinding?.ivMeIconBean?.setBackgroundResource(R.drawable.scene_shape_bg_me_action_null)
//            }
//            0 -> { //触发条件+状态条件
//                holder.dataBinding?.ivMeIconBean?.layoutParams = LinearLayout.LayoutParams(v72, v72)
//                holder.dataBinding?.ivMeIconBean?.setPadding(5, 5, 5, 5)
//                holder.dataBinding?.ivMeIconBean?.setBackgroundResource(R.drawable.scene_shape_bg_me_action_yuan)
//            }
//            1 -> { //动作
//                holder.dataBinding?.ivMeIconBean?.layoutParams = LinearLayout.LayoutParams(v72, v72)
//                holder.dataBinding?.ivMeIconBean?.setPadding(5, 5, 5, 5)
//                holder.dataBinding?.ivMeIconBean?.setBackgroundResource(R.drawable.scene_shape_bg_me_action_fang)
//            }
//        }


    }
}