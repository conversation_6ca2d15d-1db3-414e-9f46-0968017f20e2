package com.dfl.smartscene.ui.edit.condition.status.seat

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusSeat
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->座椅
 * version: 1.0
 */
class SeatStatusFragment(private val skillId: Int, private val condition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<SeatStatusAdapter, SceneFragmentEditConditionBinding, SeatStatusViewModel>() {

    private var mAdapter: SeatStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SeatStatusViewModel> {
        return SeatStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId)

        mViewModel.mSeatLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(condition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): SeatStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SeatStatusAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.isEnable == false || it.conditionType == null) {
                    return@let
                }
                showRadioListDialog(it.conditionName, it.conditionType)
            }
        }
    }

    private fun showRadioListDialog(title: String, type: StatusSeat) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getRadioList(resources, type),
            mViewModel.getStatusConditionSeatData(type)
        )
        if (type == StatusSeat.LEFT_BELT_STATE || type == StatusSeat.RIGHT_BELT_STATE) {
            dialog.desc = getString(R.string.scene_status_condition_right_seat_tips)
        }
        dialog.preSetCondition = condition
        dialog.inputArgValueList = mViewModel.getInputArgInfoValueData(type)
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}