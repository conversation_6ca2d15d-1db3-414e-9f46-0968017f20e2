package com.dfl.smartscene.ui.main.me.scenerecord

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.dfl.android.common.base.MVVMDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneRecordBean
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneDialogFragmentMeSceneRecordBinding
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.TrackUtils
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   : 执行日志记录
 * version: 1.0
 */
class SceneRecordDialogFragment : MVVMDialogFragment<SceneDialogFragmentMeSceneRecordBinding, SceneRecordViewModel>(),
                                  View.OnClickListener {

    private var adapter: SceneRecordItemAdapter? = null
    private var page = 1

    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneRecordDialogFragment")
        fun showSceneRecordDialog(fragmentManager: FragmentManager) {
            val mRecordDialog = SceneRecordDialogFragment()
            mRecordDialog.showNow(fragmentManager, "FRAGMENT_TAG_SCENE_RECORD_DIALOG")
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_me_scene_record
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneRecordViewModel> {
        return SceneRecordViewModel::class.java
    }

    override fun initData() {
        super.initData()
        mViewModel?.initData(page)
    }

    override fun initView(view: View) {
        super.initView(view)
        setCanceledOnTouchOutside(true)
        initRecycleView(view.context)
        initListener()
        TrackUtils.viewStart(TrackUtils.PageNameType.MyLog)
        mViewDataBinding.includeDialogBottom.btnNormal.text = getString(R.string.scene_text_common_close)
        mViewDataBinding.includeDialogBottom.btnPrimary.text = getString(R.string.scene_text_record_clean_up_log)
        mViewDataBinding.nsvRecord.isVerticalScrollBarEnabled = true
    }

    private fun initRecycleView(context: Context) {
        adapter = SceneRecordItemAdapter()
        mViewDataBinding.rvRecord.adapter = adapter
        mViewDataBinding.rvRecord.layoutManager = LinearLayoutManager(context)
        //        val itemDecoration=SceneRecordItemDecoration(context, R.color.recycle_divider_line)
        //        itemDecoration.setShowBottomLine(true)
        //        mViewDataBinding?.rvRecord?.addItemDecoration(itemDecoration)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initListener() {
        mViewDataBinding.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mViewDataBinding.includeDialogBottom.btnNormal.setOnClickListener(this)

        mViewDataBinding.slRecord.setEnableRefresh(false)
        mViewDataBinding.slRecord.setOnLoadMoreListener {
            page++
            mViewModel?.initData(page)
        }
        LiveEventBus.get<ArrayList<SceneRecordBean>>(GlobalLiveEventConstants.KEY_SCENE_RUN_RECORD)
            .observe(viewLifecycleOwner) { recordList ->
                run {
                    CommonLogUtils.logD(TAG, "page:${page}触发数据加载回调${recordList.size}")
                    if (page == 1) {
                        if (recordList.isEmpty()) {
                            //mViewDataBinding.includeDialogBottom.btnPrimary.alpha = 0.5f
                            mViewDataBinding.tvEmptyMsg.visibility = View.VISIBLE
                            mViewDataBinding.tvRecordTips.visibility = View.GONE
                            mViewDataBinding.slRecord.setEnableLoadMore(false)
                            mViewDataBinding.rvRecord.visibility = View.GONE
                        } else {
                            //mViewDataBinding.includeDialogBottom.btnPrimary.alpha = 1f
                            mViewDataBinding.tvEmptyMsg.visibility = View.GONE
                        }
                        mViewDataBinding.includeDialogBottom.btnPrimary.isEnabled = recordList.isNotEmpty()
                        if (recordList.size != 0) adapter?.setNewInstance(recordList)
                    } else {
                        if (recordList.size != 0) {
                            //adapter?.addData(recordList)
                            val list = adapter?.data
                            list?.addAll(recordList)
                            adapter?.setList(list)
                        }
                    }
                    if ((recordList.size in 0..9 && page > 1) || (page == 1 && recordList.size in 1..9)) {
                        mViewDataBinding.tvRecordTips.visibility = View.VISIBLE
                        mViewDataBinding.slRecord.finishLoadMoreWithNoMoreData()
                        mViewDataBinding.slRecord.setEnableLoadMore(false)
                    } else if (recordList.size == 10 && page > 1) {
                        mViewDataBinding.slRecord.finishLoadMore()
                    }
                }
            }
    }


    override fun onClick(v: View?) {
        v ?: return
        when (v.id) {
            R.id.btn_primary -> {
                if (DebouncingUtils.isValid(v)) {
                    DefaultDialogFragment.showDialog(
                        parentFragmentManager,
                        "",
                        "",
                        getString(R.string.scene_text_record_delete_all_record),
                        getString(R.string.scene_button_common_clean_up),
                        getString(R.string.scene_text_common_cancel),
                        object : DefaultDialogFragment.OnDialogButtonClickListener {
                            override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                                dialog.dismissWithAnimate()
                            }

                            override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                                page = 1
                                mViewModel?.cleanUpSceneRecordData()
                                //mViewDataBinding.includeDialogBottom.btnPrimary.alpha = 0.5f
                                //mViewDataBinding.includeDialogBottom.btnPrimary.isEnabled = false
                                SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                                dialog.dismissWithAnimate()
                            }
                        }
                    )
                }
            }
            R.id.btn_normal -> {
                dismissWithAnimate()
            }
        }
    }

    override fun dismissWithAnimate() {
        super.dismissWithAnimate()
        page = 1
        TrackUtils.viewEnd(TrackUtils.PageNameType.MyLog)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIUtils.setSmartRefreshOnConfigurationChanged(null, mViewDataBinding.classicsFooter)
    }

}