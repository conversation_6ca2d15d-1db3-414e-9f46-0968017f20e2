package com.dfl.smartscene.ui.edit.action.fridge

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.FridgeType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2025/04/18
 * desc : 动作->冰箱
 * version: 1.0
 */
class FridgeActionViewModel : BaseViewModel() {
    val mFridgeActionLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<FridgeType>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<FridgeType>>()
//        val type = SceneEditManager.ActionType.FRIDGE
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_switch),
                R.drawable.scene_icon_me_action_icebox_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_SWITCH)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_mode),
                R.drawable.scene_icon_me_action_air_cool_heat,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_MODE)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_coding_temp),
                R.drawable.scene_icon_me_action_icebox_cold,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_CODING_TEMP,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_COOLING_TEMP)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_heating_temp),
                R.drawable.scene_icon_me_action_icebox_hot,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_HEATING_TEMP,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_HEATING_TEMP)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_convenience_mode),
                R.drawable.scene_icon_me_action_icebox_quick,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_CONVENIENT_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_departure_working_time),
                R.drawable.scene_icon_me_action_icebox_open,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_DEPARTURE_WORKING_TIME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_fridge_departure_working_off),
                R.drawable.scene_icon_me_action_icebox_close,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_DEPARTURE_WORKING_OFF,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_air_energy_conservation),
                R.drawable.scene_icon_me_action_air_energy_saving_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                FridgeType.FRIDGE_ECO_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_FRIDGE_ECO_MODE)
            )
        )
//        val result = SceneEditManager.checkActionItemIsActive(type, list)
//        mFridgeActionLiveData.postValue(result)
    }

    fun getSequenceList(type: FridgeType): List<ScenarioInfo.Sequence> {
        val list = ArrayList<ScenarioInfo.Sequence>()
        when (type) {
            FridgeType.FRIDGE_SWITCH -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_SWITCH,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_FRIDGE_SWITCH, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_MODE -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_MODE,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_FRIDGE_MODE, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_CODING_TEMP -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_COOLING_TEMP,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_TEMP, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_HEATING_TEMP -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_HEATING_TEMP,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_TEMP, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_CONVENIENT_MODE -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_DEPARTURE_WORKING_TIME -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_TIME, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_DEPARTURE_WORKING_OFF -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_SWITCH, ArgType.INT32, "0"))
                        )
                    )
                )
            }
            FridgeType.FRIDGE_ECO_MODE -> {
                list.add(
                    ScenarioInfo.Sequence(
                        1,
                        1,
                        ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_FRIDGE_ECO_MODE,
                            "",
                            listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "0"))
                        )
                    )
                )
            }
        }
        return list
    }

    fun getCheckList(actionType: FridgeType): List<CheckListBean> {
        return when (actionType) {
            FridgeType.FRIDGE_SWITCH, FridgeType.FRIDGE_ECO_MODE -> {
                listOf(
                    CheckListBean(CommonUtils.getString(R.string.scene_text_common_open), true),
                    CheckListBean(CommonUtils.getString(R.string.scene_text_common_close), false)
                )
            }
            FridgeType.FRIDGE_MODE -> {
                listOf(
                    CheckListBean(CommonUtils.getString(R.string.scene_text_me_action_fridge_mode_cold), true),
                    CheckListBean(CommonUtils.getString(R.string.scene_text_me_action_fridge_mode_heat), false)
                )
            }
            FridgeType.FRIDGE_CONVENIENT_MODE -> {
                listOf(
                    CheckListBean(CommonUtils.getString(R.string.scene_text_common_close), true),
                    CheckListBean(
                        CommonUtils.getString(R.string.scene_text_me_action_fridge_convenience_mode_2),
                        false
                    ),
                    CheckListBean(
                        CommonUtils.getString(R.string.scene_text_me_action_fridge_convenience_mode_1),
                        false
                    ),
                    CheckListBean(
                        CommonUtils.getString(R.string.scene_text_me_action_fridge_convenience_mode_3),
                        false
                    ),
                    CheckListBean(
                        CommonUtils.getString(R.string.scene_text_me_action_fridge_convenience_mode_5),
                        false
                    ),
                    CheckListBean(
                        CommonUtils.getString(R.string.scene_text_me_action_fridge_convenience_mode_4),
                        false
                    ),
                )
            }
            else -> {
                listOf(CheckListBean(CommonUtils.getString(R.string.scene_text_common_close), true))
            }
        }
    }

    fun getInputArgList(actionType: FridgeType): List<String> {
        return when (actionType) {
            FridgeType.FRIDGE_SWITCH, FridgeType.FRIDGE_ECO_MODE -> {
                listOf("0", "1")
            }
            FridgeType.FRIDGE_MODE -> {
                listOf("1", "2")
            }
            FridgeType.FRIDGE_CONVENIENT_MODE -> {
                listOf("0", "2", "1", "3", "5", "4")
            }
            else -> {
                listOf("1")
            }
        }
    }

    fun getWheelData(actionType: FridgeType): List<String> {
        return when (actionType) {
            FridgeType.FRIDGE_CODING_TEMP -> {
                SingleWheelDialog.getWheelData(-6, 10, 1)
            }
            FridgeType.FRIDGE_HEATING_TEMP -> {
                SingleWheelDialog.getWheelData(35, 55, 1)
            }
            FridgeType.FRIDGE_DEPARTURE_WORKING_TIME -> {
                SingleWheelDialog.getWheelData(1, 24, 1)
            }
            else -> {
                emptyList()
            }
        }
    }

}