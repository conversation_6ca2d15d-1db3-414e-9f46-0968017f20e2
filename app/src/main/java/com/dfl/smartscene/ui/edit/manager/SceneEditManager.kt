package com.dfl.smartscene.ui.edit.manager

import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.edit.EditConditionAndActionItemBean
import com.dfl.smartscene.ui.edit.container.SceneNewEditType
import com.iauto.scenarioadapter.SkillInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/09
 * desc :处理条件动作跳转至指定页面
 * version: 1.0
 */
object SceneEditManager {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneEditManager")

    /**
     * 触发条件一级目录,每个item 包含 二级列表
     */
    val mTriggerItemDataList = ArrayList<SceneEditSkillBean<TriggerType>>()

    /**
     *状态条件一级目录,每个item 包含 二级列表
     */
    val mStatusItemDataList = ArrayList<SceneEditSkillBean<StatusType>>()

    /**
     *动作一级目录,每个item 包含 二级列表
     */
    val mActionItemDataList = ArrayList<SceneEditSkillBean<ActionType>>()

    /**本车配置对应config,用于选中对应能力*/
    val skillConfigList = ArrayList<String>()

    /**
     * CCM返回过来的所有能力id （数量大于自己自定义的）
     */
    val mSkillIdList = ArrayList<Int>()

    const val mViewClickDuration = 1000L

    /***
     * 加载ccm返回的能力id列表
     * skillData:CCM返回过来的所有能力
     */
    fun loadAllSkillIdData(skillData: ArrayList<SkillInfo>?) {
        CoroutineScope(Dispatchers.IO).launch {
            mSkillIdList.clear()
            skillData?.forEach {
                //过滤权限为oem能力
                if (it.permission == 0) {
                    mSkillIdList.add(it.skillId)
                    val config = it.modeConfig.toString()
                    if (!skillConfigList.contains(config)) {
                        skillConfigList.add(config)
                    }
                }
            }
            CommonLogUtils.logE(TAG, "获取ccm能力:$mSkillIdList")
            // 如果ccm连接成功发生场景变化，请求场景列表ID获取为空的时候，
            // 防止重复设置这里判断不为空才能执行更新缓存能力列表ID状态
            if (mSkillIdList.size != 0) {
                initSceneSkillCacheIdData()
            }
        }
    }

    /**
     * 初始化 触发,状态,动作能力id列表
     * 这里检测 本app提供的能力与ccm提供的能力 做比较，得出app提供的能力 是否启动与禁用
     */
    fun initSceneSkillCacheIdData() {
        CoroutineScope(Dispatchers.IO).launch {
            checkTriggerConditionShowState()
            checkStatusConditionShowState()
            checkActionShowState()
        }
    }

    /**
     * app自定义的UI触发条件与ccm提供的能力做对比
     * 1：通过接口获取ccm对应的能力列表ID,为空则用app定义能力列表id初始化
     * 2：判断所有能力是否有对应显示
     * 注意:
     * 当能力列表id变化时要注释对应id,防止新能力id与旧能力id相同导致错误
     * 若tab顺序改变也需要改变list和TriggerType顺序,防止打开之前索引错误
     *------------------触发条件-----------
     */
    private fun checkTriggerConditionShowState() {
        // 如果没有获取到ccm的能力列表,使用app定义的
        if (mTriggerItemDataList.size == 0) {
            // 时间
            val time = arrayListOf(
                SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT,
                SkillsListConstant.SKILL_ID_TRIGGER_TIME_DAY_STATUS
            )
            // 环境
            val environment = arrayListOf(
                SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_HIGHER,
                SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_INNER_TEMPERATURE_LOW,
                SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_HIGHER,
                SkillsListConstant.SKILLS_ID_TRIGGER_ENVIRONMENT_OUTSIDE_TEMPERATURE_LOW,
            )
            // 位置
            val location = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL,
                SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE
            )
            // 驾驶
            val drive = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_MORE,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_LESS,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_MORE,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_LESS,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_MORE,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_LESS,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_MORE,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_LESS,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_MORE,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_LESS,
                SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_GEAR
            )
            // 门窗
            val door = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_DOOR_LOCK,
                SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE,
                SkillsListConstant.SKILL_ID_TRIGGER_DOOR_LEFT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_TRIGGER_DOOR_RIGHT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ANY_OPEN,
            )
            // 座椅
            val seat = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_STATE,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_STATE,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_SAFE_BELT,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_SAFE_BELT,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_LEFT_SAFE_BELT,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_MIDDLE_SAFE_BELT,
                SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_RIGHT_SAFE_BELT,
            )
            //灯光
            val lighting = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_FAR_LIGHT,
                SkillsListConstant.SKILL_ID_TRIGGER_NEAR_LIGHT,
                SkillsListConstant.SKILL_ID_TRIGGER_FOG_LIGHT,
            )
            //充放电
            val charge = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_CHARGE,
                SkillsListConstant.SKILL_ID_TRIGGER_CHARGE_END_TIME,
                SkillsListConstant.SKILL_ID_TRIGGER_DISCHARGE,
            )
            //oms
            val oms = arrayListOf(
                SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_CHILDREN,
                SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_SOMEONE,
                SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_GENDER,
                SkillsListConstant.SKILL_ID_TRIGGER_OMS_POSITION_BEHAVIOR,
            )
            // 触发条件在列表中的顺序要与ux一致,不然定位错误
            val triggerIdList = arrayListOf(time, environment, location, drive, door, seat, lighting, charge, oms)
            // 循环判断tab栏及其子能力是否可见
            var index = 0
            triggerIdList.forEach {
                // tab栏对应的子能力列表
                val list = ArrayList<SkillItemBean>()
                // 临时用于区分左边tab状态是否显示
                var itemIsActive = false
                it.forEach { id ->
                    // 判断自定义子能力是否可见,未连上ccm默认可见
                    val isActive = checkIdIsActive(id)
                    if (isActive) {
                        // 只要有一个子能力可见,tab栏可见
                        itemIsActive = true
                    }
                    list.add(SkillItemBean(id, isActive))
                }
                // 最后添加左边的tab栏对象
                mTriggerItemDataList.add(
                    SceneEditSkillBean(
                        list, itemIsActive, TriggerType.toEnumType(index)
                    )
                )
                index++
            }
        } else {
            // 获取到ccm能力列表,判断子能力是否可见
            mTriggerItemDataList.forEach {
                // 左边tab栏是否可见
                var itemIsActive = false
                it.skillIdList.forEach { item ->
                    // 判断子能力是否可见
                    item.isActive = checkIdIsActive(item.skillId)
                    if (item.isActive) {
                        // 只要有一个子能力可见tab栏可见
                        itemIsActive = true
                    }
                }
                it.itemIsActive = itemIsActive
            }
        }
    }

    /**
     * app自定义的UI状态条件与ccm提供的能力做对比
     * 1：通过接口获取ccm对应的能力列表ID,为空则用app定义能力列表id初始化
     * 2：判断所有能力是否有对应显示
     * 注意:
     * 当能力列表id变化时要注释对应id,防止新能力id与旧能力id相同导致错误
     * 若tab顺序改变也需要改变list和TriggerType顺序,防止打开之前索引错误
     *-----------------初始化状态条件ID------------
     */
    private fun checkStatusConditionShowState() {
        // 未连接ccm
        if (mStatusItemDataList.size == 0) {
            // 时间
            val time = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_TIME_INTERVAL_ID,
                SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID,
                SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID,
                SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID,
                SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID
            )
            // 环境
            val environment = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY,
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_WEATHER,
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_RAIN,
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_CAR_OUTSIDE_WEATHER_NO_RAIN,
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_MORE,
                SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_HUMIDITY_IN_CAR_LESS
            )

            // 位置
            val location = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT,
                SkillsListConstant.SKILL_ID_STATUS_LOCATION_LOCATED_AT_NOT
            )
            // 导航 v1.1将触发条件移动状态条件
            val navigation = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME,
                SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_DISTANCE
            )
            // 门窗
            val door = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_DOOR_ALL_DOOR_LOCK,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_DOOR_BACK,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_LEFT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_RIGHT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_ELECTRIC_DOOR_BACK,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_MAIN,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_CHIEF,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_LEFT,
                SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_RIGHT
            )

            // 座椅
            val seat = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_STATE,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_COPILOT_STATUS_STATE,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_MAIN_DRIVER_STATUS_SAFE_BELT,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_COPILOT_STATUS_SAFE_BELT,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_LEFT_STATUS_SAFE_BELT,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_MIDDLE_STATUS_SAFE_BELT,
                SkillsListConstant.SKILL_ID_STATUS_SEAT_BACK_RIGHT_STATUS_SAFE_BELT,
            )
            //灯光
            val lighting = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_FAR_LIGHT,
                SkillsListConstant.SKILL_ID_STATUS_NEAR_LIGHT,
                SkillsListConstant.SKILL_ID_STATUS_FOG_LIGHT,
            )
            // 座舱感知
            val oms = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_CHILDREN,
                SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_SOMEONE,
                SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_GENDER,
                SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_BEHAVIOR,
            )
            // 连接
            val link = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_CONNECTION,
                SkillsListConstant.SKILL_ID_STATUS_LINK_BLUETOOTH_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_CONNECTION,
                SkillsListConstant.SKILL_ID_STATUS_LINK_HOTSPOT_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_CONNECTION,
                SkillsListConstant.SKILL_ID_STATUS_LINK_WLAN_SWITCH,
                SkillsListConstant.SKILL_ID_STATUS_LINK_MOBILE_SWITCH, // 移动网络打开/关闭状态
                //				SkillsListConstant.SKILL_ID_LINK_MOBILE_CONNECTION //移动网络连接状态
            )
            // 智能设备
            val smartDevice = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY,
                SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_RIDING_STATUS
            )

            // 胎压
            val tire = arrayListOf(
                SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_FRONT_WHEEL,
                SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_FRONT_WHEEL,
                SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_LEFT_AFTER_WHEEL,
                SkillsListConstant.SKILL_ID_STATUS_OTHER_TIRE_PRESSURE_RIGHT_AFTER_WHEEL
            )
            val statusId = arrayListOf(
                time, environment, location, navigation, door, seat, lighting, oms, link, smartDevice, tire
            )
            // 循环判断tab栏及其子能力是否可见
            var index = 0
            statusId.forEach {
                // tab栏对应的子能力列表
                val list = ArrayList<SkillItemBean>()
                // 临时用于区分左边tab状态是否显示
                var itemIsActive = false
                it.forEach { id ->
                    // 判断子能力是否可见
                    val isActive = checkIdIsActive(id)
                    if (isActive) {
                        // 只要有一个子能力可见tab栏可见
                        itemIsActive = true
                    }
                    list.add(SkillItemBean(id, isActive))
                }
                // 最后添加左边的tab栏对象
                mStatusItemDataList.add(
                    SceneEditSkillBean(
                        list, itemIsActive, StatusType.toEnumType(index)
                    )
                )
                index++
            }
        } else {
            // 获取到ccm能力列表,判断子能力是否可见
            mStatusItemDataList.forEach {
                // 临时用于区分左边tab状态是否显示
                var itemIsActive = false
                it.skillIdList.forEach continuing@{ item ->
                    // 判断子能力是否可见
                    item.isActive = checkIdIsActive(item.skillId)
                    if (item.isActive) {
                        // 只要有一个子能力可见tab栏可见
                        itemIsActive = true
                    }
                }
                it.itemIsActive = itemIsActive
            }
        }

    }

    /**
     * 检测app自定义的动作与ccm提供的能力做对比
     * 1：通过接口获取ccm对应的能力列表ID,为空则用app定义能力列表id初始化
     * 2：判断所有能力是否有对应显示
     * 注意:
     * 当能力列表id变化时要注释对应id,防止新能力id与旧能力id相同导致错误
     * 若tab顺序改变也需要改变list和TriggerType顺序,防止打开之前索引错误
     *-----------初始化添加所有的动作ID-----------
     */
    private fun checkActionShowState() {
        // ccm未连接
        if (mActionItemDataList.size == 0) {
            // 推荐
            val recommend = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID, // 延时
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN, // 空调开关
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE, // 空调温度 （单区）
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT, // 空调温度 （双区主驾）
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT, // 空调温度 （双区副驾）
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME, // 空调风量
                SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY, // 回家 回公司
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE, // 去充电
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE, // 搜索地址+添加途经点
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID, //选回家公司充电+途经点
                //            SkillsListConstant.ID_NAV_PASS_SOME_WHERE,
                SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA, // 打开多媒体
                //            SkillsListConstant.ID_MEDIA_OPEN_SING,
                //            SkillsListConstant.ID_MEDIA_OPEN_HIMALAYAN,
                //            SkillsListConstant.ID_MEDIA_OPEN_BLUETOOTH,
                //            SkillsListConstant.ID_MEDIA_OPEN_LOCAL,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE, // 关闭多媒体
                SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY,  // 小尼回复
                SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY, // 消息通知
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN, // 所有车窗
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID
            )
            // 空调
            val air = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOW_MODE,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_INNER_LOOP,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AUTO_MODE_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_COOLING_HEATING_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_BLOWER_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_FRONT_DEFOG_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AFTER_DEFOG_OPEN,
                SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_COOL, // 极速制冷
                SkillsListConstant.SKILL_ID_ACTION_AIR_MAX_HEAT, // 极速制热
                SkillsListConstant.SKILL_ID_ACTION_AIR_SYNC, //同步
                SkillsListConstant.SKILL_ID_ACTION_AIR_ENERGY_CONSERVATION, //节能模式
                SkillsListConstant.SKILL_ID_ACTION_AIR_SELF_DESICCATION, //自干燥
            )
            // 门窗
            val door = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_NORMAL_LOCK, //普通后背门,行李箱解锁
                SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK, //电动后背门开闭
                SkillsListConstant.SKILL_ID_ACTION_DOOR_LEFT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_ACTION_DOOR_RIGHT_CHILD_LOCK,
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN, // 所有车窗打开,所有车窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT, // 左前车窗开度,主驾车窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT, // 右前车窗开度 ,副驾车窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT, // 左后车窗开度,左后车窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT, // 右后车窗开度,右后车窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN, // 车窗透气模式打开,车窗透气
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR, // 车窗通风,车窗通风
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT, //天窗
                SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN, //电动遮阳帘
            )
            // 灯光
            val lamp = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID, // 大灯灯语
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT, // 大灯场景灯效
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_SOA, // 大灯场景灯效
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID, // 雾灯
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS, // 星环灯
                //				CarLightConstant.LIGHT_READ_LIGHT_MODE_ID,
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID, // 氛围灯开关
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID, // 氛围灯模式
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID,
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID,
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID,
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID,    // 场景灯语
                //产品1.5能力清单屏蔽
                //SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_SOA, // 氛围灯SOA灯语
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_DIY, // 尾灯自定义等于
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH, //室内灯 开关
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE, //室内灯 模式
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM, //室内灯 （阅读灯） 自定义
                SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT, // 设置大灯音乐律动
                SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT, //大灯高度
                SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL, //大灯模式控制
            )
            // 座椅
            val seat = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_SEAT,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE,
            )
            //冰箱
            val fridge = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_COOLING_TEMP,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_HEATING_TEMP,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_CONVENIENCE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_TIME,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_DEPARTURE_WORKING_OFF,
                SkillsListConstant.SKILL_ID_ACTION_FRIDGE_ECO_MODE,
            )
            //驾驶
            val drive = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_DRIVE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_WIPER_SENSITIVITY,
            )
            // 导航
            val navigation = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY,
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE,
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE,
                SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID,
                SkillsListConstant.SKILL_ID_ACTION_NAV_ROUTE
            )

            // 播报
            val broadcast = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY, // 小尼回复
                SkillsListConstant.SKILL_ID_ACTION_APP_WEATHER_REPORT, // 天气播报
                SkillsListConstant.SKILL_ID_ACTION_APP_TRAVEL_REPORT // 行程播报
            )
            // 多媒体
            val media = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA, //-多媒体
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_MODE,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL,
                SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2
            )


            // 应用
            val apply = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY,
                SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC,
                // v1.1删除播放车外音
                // SkillsListConstant.SKILL_ID_APP_PLAY_CAR_OUT_SOUND,
                SkillsListConstant.SKILL_ID_ACTION_APP_SOUND_EFFECT,
                SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_VOLUME,
                SkillsListConstant.SKILL_ID_ACTION_APP_USE_VOICE,
                SkillsListConstant.SKILL_ID_ACTION_APP_OUT_SPEAKER,
                SkillsListConstant.SKILL_ID_ACTION_APP_PREHEAT_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_APP_DISCHARGE_SWITCH,
                SkillsListConstant.SKILL_ID_ACTION_APP_ENDURANCE_MODE,
                SkillsListConstant.SKILL_ID_ACTION_APP_OPEN_APP,
                SkillsListConstant.SKILL_ID_ACTION_APP_CLOSE_APP

            )
            apply.addAll(navigation)
            apply.addAll(broadcast)
            apply.addAll(media)
            // 系统设置
            val system = arrayListOf(
                // 连接
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_WLAN_ID,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MOBILE,
                // 声音
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME, // 车外扬声器
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_RING_VOLUME,
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE, // 静音
                // 显示
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS, // 控屏幕亮度
                SkillsListConstant.SKILL_ID_ACTION_SYSTEM_SCREEN_MODE, // 日夜模式
            )
            // 智能设备
            val smart = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_SMART_AROMA_DIFFUSER_SWITCH_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_STALL_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_DURATION_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_FRAGRANCE_MODE_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_PURIFIER_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_AIR_QUALITY_SYNC_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_BLOW_ID,
                SkillsListConstant.SKILL_ID_ACTION_SMART_CHILD_SEAT_HEAT_ID,

                //                //颈枕
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_SWITCH_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_NECK_PILLOW_HEAT_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_STRENGTH_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_TIME_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_NECK_PILLOW_MASSAGE_MODE_ID,
                //                //腰枕
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_SWITCH_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_HEAT_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_PART_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_TIME_ID,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_MASSAGE_CUSHION_STRENGTH_ID,
                //                //炫彩氛围灯
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_SWITCH,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_BRIGHTNESS,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_MODE,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE,
                //                //等离子香氛
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_SWITCH,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_CONCENTRATION,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_MODE,
                //                SkillsListConstant.SKILL_ID_ACTION_SMART_PLASMA_FRAGRANCE_FLAVOR,
            )

            // 其他
            val other = arrayListOf(
                SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_SWITCH_ID,
                SkillsListConstant.SKILL_ID_ACTION_OTHER_REARVIEW_MIRROR_HEATING_OPEN_ID,
                //				OtherConstant.OTHER_IN_ROAD_PREHEATING_ID,
                //				SkillsListConstant.SKILL_ID_APPc_NOTIFY, //消息通知
                SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID,
                SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_OFF,
                SkillsListConstant.SKILL_ID_ACTION_OTHER_SCREEN_PROTECT,
                SkillsListConstant.SKILL_ID_ACTION_OTHER_WIRELESS_CHARGER
            )
            val actionId = arrayListOf(
                recommend, air, door, lamp, seat, fridge, drive, apply, system, smart, other
            )


            // 循环判断tab栏及子能力是否可见
            var index = 0
            actionId.forEach {
                // tab栏目录对应的子能力列表
                val list = ArrayList<SkillItemBean>()
                // 临时用于区分左边tab状态是否显示
                var itemIsActive = false
                it.forEach { id ->
                    // 判断子能力是否可见
                    val isActive = checkIdIsActive(id)
                    if (isActive) {
                        // 只要有一个子能力可见tab栏可见
                        itemIsActive = true
                    }
                    list.add(SkillItemBean(id, isActive))
                }
                // 最后添加左边的tab栏目录对象
                mActionItemDataList.add(
                    SceneEditSkillBean(
                        list, itemIsActive, ActionType.toEnumType(index)
                    )
                )
                index++
            }

        } else {
            // 获取到ccm能力列表,判断子能力是否可见
            mActionItemDataList.forEach {
                // 临时用于区分左边tab状态是否显示
                var itemIsActive = false
                it.skillIdList.forEach { item ->
                    // 判断子能力是否可见
                    item.isActive = checkIdIsActive(item.skillId)
                    if (item.isActive) {
                        // 只要有一个子能力可见tab栏可见
                        itemIsActive = true
                    }
                }
                it.itemIsActive = itemIsActive
            }
        }

    }

    /**
     * 判断能力id是否存在
     * 校验app里面的能力ID是否存在于ccm
     * 未连接ccm默认存在
     */
    fun checkIdIsActive(id: Int): Boolean {
        if (mSkillIdList.size == 0) return true
        if (mSkillIdList.contains(id)) {
            return true
        }
        return false
    }

    /***
     * 判断能力id是否存在
     * @param id:能力id
     * @param type：0动作，1触发，2状态
     */
    fun checkIdIsActive(id: Int, type: Int): Boolean {
        when (type) {
            1 -> {
                mTriggerItemDataList.forEachIndexed { _, sceneEditSkillBean ->
                    sceneEditSkillBean.skillIdList.forEachIndexed { _, skillItemBean ->
                        if (skillItemBean.skillId == id && skillItemBean.isActive) {
                            return true
                        }
                    }
                }
                return false
            }

            2 -> {
                mStatusItemDataList.forEachIndexed { _, sceneEditSkillBean ->
                    sceneEditSkillBean.skillIdList.forEachIndexed { _, skillItemBean ->
                        if (skillItemBean.skillId == id && skillItemBean.isActive) {
                            return true
                        }
                    }
                }
                return false
            }

            0 -> {
                mActionItemDataList.forEachIndexed { _, sceneEditSkillBean ->
                    sceneEditSkillBean.skillIdList.forEachIndexed { _, skillItemBean ->
                        if (skillItemBean.skillId == id && skillItemBean.isActive) {
                            return true
                        }
                    }
                }
                return false
            }
        }
        return false
    }

    /**
     * 左边的tab栏目录对象
     * @property T ActionType,StatusType,TriggerType
     * @property skillIdList 目录下包含的能力
     * @property itemIsActive 目录是否被屏蔽
     * @property type T对应的枚举类型
     */
    class SceneEditSkillBean<T>(
        val skillIdList: ArrayList<SkillItemBean>, //
        var itemIsActive: Boolean, //
        val type: T
    )

    /**
     * 单个能力对象
     * @param skillId 能力Id
     * @param isActive 能力是否可用
     * */
    class SkillItemBean(
        val skillId: Int, var isActive: Boolean
    )

    /**
     * 一级item
     * 判断该item目录是否所有的id全部禁用
     * 根据list的indices检索已有的触发,状态,动作列表对应的itemIsActive,判断并过滤禁用
     * @param list EditActionAndConditionBean列表
     * @param type 触发,状态,动作
     * @param defaultIndex 默认选择的索引
     * @return 过滤后list
     */
    fun checkEditItem(
        list: ArrayList<EditConditionAndActionItemBean>, type: SceneNewEditType, defaultIndex: Int
    ): ArrayList<EditConditionAndActionItemBean> {
        //缓存的tab目录列表为空,直接返回点击默认索引的参数list
        if ((type == SceneNewEditType.TRIGGER_CONDITION && mTriggerItemDataList.size == 0) || (type == SceneNewEditType.STATUS_CONDITION && mStatusItemDataList.size == 0) || (type == SceneNewEditType.ACTION && mActionItemDataList.size == 0)) {
            list[defaultIndex].isClicked = true
            return list
        }
        // 左边tab目录rv adapter数据列表
        val cacheList = ArrayList<EditConditionAndActionItemBean>()
        for (i in list.indices) {
            val itemIsActive = when (type) {
                SceneNewEditType.TRIGGER_CONDITION -> {
                    mTriggerItemDataList[i].itemIsActive
                }

                SceneNewEditType.STATUS_CONDITION -> {
                    mStatusItemDataList[i].itemIsActive
                }

                else -> {
                    // 注：如果动作需要区分门窗、车窗的文字释义的话需要取消下面的代码注释即可
                    //                    if (list[i].eventCode== ActionEventCode.DoorWindowCode){
                    //                        var result=false
                    //                        mActionItemDataList[i].skillIdList.forEach {
                    //                            if (it.skillId==SkillsListConstant.BACK_DOOR_NORMAL_LOCK||
                    //                                    it.skillId==SkillsListConstant.BACK_DOOR_ELECTRIC_LOCK){
                    //                                if (it.isActive)
                    //                                    result=true
                    //                            }
                    //                        }
                    //                        if (!result)
                    //                            list[i].name= CommonUtils.getString(R.string.scene_text_edit_add_action_window)
                    //                    }
                    mActionItemDataList[i].itemIsActive
                }
            }
            if (itemIsActive) {
                cacheList.add(list[i])
            }
        }
        cacheList[defaultIndex].isClicked = true
        return cacheList
    }

    /**
     * 二级item
     * 判断所有条件ID是否存在禁用情况。
     * 此处逻辑同动作ID判断类似
     */
    fun <T, K> checkChildItemIsActive(
        type: T, itemSkillIdList: ArrayList<ConditionItemBean<K>>
    ): ArrayList<ConditionItemBean<K>> {
        val itemList = ArrayList<ConditionItemBean<K>>()
        itemSkillIdList.let {
            it.forEach itemSkillIdListForEach@{ bean ->
                when (type) {
                    is TriggerType -> {
                        if (bean.item == ConditionItemBean.SKILL_TITLE || mTriggerItemDataList.isEmpty()) {
                            itemList.add(bean)
                        } else {
                            val list = mTriggerItemDataList.filter { t -> type == t.type }
                            if (bean.conditionSkillId!!.size == 1) {
                                list[0].skillIdList.forEach { item ->
                                    if (bean.conditionSkillId[0] == item.skillId && item.isActive) {
                                        itemList.add(bean)
                                        //skillId存在且可用,跳出当前循环判断下一个skillId
                                        return@itemSkillIdListForEach
                                    }
                                }
                            } else { // 多个ID的情况
                                bean.conditionSkillId.removeIf { id ->
                                    // 如果多个ID的情况某个ID不存在就删除这个ID
                                    var index = 0
                                    list[0].skillIdList.forEach { item ->
                                        if (id == item.skillId && item.isActive) {
                                            index++
                                        }
                                    }
                                    index == 0
                                }
                                if (bean.conditionSkillId.size != 0) { // 存在某一个ID未被禁用的情况就必须添加进去
                                    itemList.add(bean)
                                }
                            }
                        }
                    }

                    is StatusType -> {
                        if (bean.item == ConditionItemBean.SKILL_TITLE || mStatusItemDataList.isEmpty()) {
                            itemList.add(bean)
                        } else {
                            val list = mStatusItemDataList.filter { t -> type == t.type }
                            //单个ID情况
                            if (bean.conditionSkillId!!.size == 1) {
                                list[0].skillIdList.forEach { item ->
                                    if (bean.conditionSkillId[0] == item.skillId && item.isActive) {
                                        itemList.add(bean)
                                        //skillId存在且可用,跳出当前循环判断下一个skillId
                                        return@itemSkillIdListForEach
                                    }
                                }
                            } else { // 多个ID的情况
                                bean.conditionSkillId.removeIf { id ->
                                    // 如果多个ID的情况某个ID不存在就删除这个ID
                                    var index = 0
                                    list[0].skillIdList.forEach { item ->
                                        if (id == item.skillId && item.isActive) {
                                            index++
                                        }
                                    }
                                    index == 0
                                }
                                if (bean.conditionSkillId.size != 0) { // 存在某一个ID未被禁用的情况就必须添加进去
                                    itemList.add(bean)
                                }
                            }
                        }
                    }

                }
            }
        }
        filterNoContentConditionData(itemList)
        return itemList
    }

    /**
     * 由于条件中的能力大部分都是以标题加对应的内容形式显示，所以如果对应所有能力被禁用的情况，需要考虑将标题一起隐藏
     * 此处逻辑同动作一样
     */
    private fun <T> filterNoContentConditionData(list: ArrayList<ConditionItemBean<T>>): ArrayList<ConditionItemBean<T>> {
        val titleList = list.filter { it.item == ConditionItemBean.SKILL_TITLE }
        if (titleList.isNotEmpty()) {
            titleList.forEach {
                val sameItemActionType = it.conditionItemType ?: return@forEach
                val sameList = list.filter { bean ->
                    bean.conditionItemType == sameItemActionType
                }
                val result = sameList.size == 1
                list.removeIf { type ->
                    type.conditionItemType == sameItemActionType && result
                }
            }
        }
        return list
    }

    /**
     * 检测动作能力是否被禁用
     * 逻辑如下：
     * 1、先把标题过滤出去直接添加
     * 2、区分多个ID和一个ID的情况
     * 3、一个ID的情况下如果被禁用就不添加到itemActionList
     * 4、多个ID的情况下，如果是全部被禁用，那么就不添加此item.如果禁用一部分就remove那部分被禁用的ID，然后添加到itemActionList。
     * 过滤只有标题没有对应动作内容的情况
     * 剩下的相关联的一些ID被禁用UI上显示的问题，直接具体到对应的UI上面去处理
     */
    fun <T> checkActionItemIsActive(
        type: ActionType, list: ArrayList<ActionSkillItemBean<T>>
    ): ArrayList<ActionSkillItemBean<T>> {
        val itemActionList = ArrayList<ActionSkillItemBean<T>>()
        list.forEach itemActionList@{
            if (it.type == ActionSkillItemBean.SKILL_TITLE || mActionItemDataList.isEmpty()) {
                itemActionList.add(it)
            }
            if (it.type == ActionSkillItemBean.SKILL_CONTENT && mActionItemDataList.isNotEmpty()) {
                // 分1个ID和多个ID的情况,1:先把数据处理成单个ITEM集合，然后判断ID是1个还是多个来处理
                val actionItemList = mActionItemDataList.filter { t -> type == t.type }
                if (it.actionSkillId?.size == 1) { // 1个ID的情况
                    actionItemList[0].skillIdList.forEach { itemAction ->
                        // 判断ID是否相同，并且是否存在
                        if (it.actionSkillId[0] == itemAction.skillId && itemAction.isActive) {
                            itemActionList.add(it)
                            //skillId存在且可用,跳出当前循环判断下一个skillId
                            return@itemActionList
                        }
                    }
                } else { // 多个ID的情况
                    it.actionSkillId?.removeIf { id ->
                        // 如果多个ID的情况某个ID不存在就删除这个ID
                        var itemIndex = 0
                        actionItemList[0].skillIdList.forEach { item ->
                            if (id == item.skillId && item.isActive) {
                                itemIndex++
                            }
                        }
                        itemIndex == 0
                    }
                    if (it.actionSkillId?.size != 0) { // 存在某一个ID未被禁用的情况就必须添加进去
                        itemActionList.add(it)
                    }
                }
            }
        }
        filterNoContentActionData(itemActionList)
        return itemActionList
    }

    /**
     * 由于动作中的能力大部分都是以标题加对应的内容形式显示，所以如果对应所有能力被禁用的情况，需要考虑将标题一起隐藏
     * 逻辑如下：
     * 1、先把标题类别筛选出来
     * 2、如果是actionItemType为null的就是不需要处理这种问题的，直接跳出筛选
     * 3、判断此标题类型的actionItemType在list中是否仅只有一条，如果只有一条那么就是满足判断直接remove
     */
    private fun <T> filterNoContentActionData(list: ArrayList<ActionSkillItemBean<T>>): ArrayList<ActionSkillItemBean<T>> {
        val titleList = list.filter { it.type == ActionSkillItemBean.SKILL_TITLE }
        if (titleList.isNotEmpty()) {
            titleList.forEach {
                val sameItemActionType = it.actionItemType ?: return@forEach
                val sameList = list.filter { bean ->
                    bean.actionItemType == sameItemActionType
                }
                val result = sameList.size == 1
                list.removeIf { type ->
                    type.actionItemType == sameItemActionType && result
                }
            }
        }
        return list
    }


    /**
     * 根据技能ID去定位此技能属于左边菜单列表种的哪一个选项里面的能力
     * @param skillId 能力ID
     * 步骤：1、先筛选出属于非禁用的对应的条件动作集合
     * 2、根据对应的集合去和能力ID比较得出对应的下标，如果此ID在当前状态被禁用则返回0
     */
    fun getSkillIdPosition(skillId: Int, type: SceneNewEditType): Int {
        val list = when (type) {
            SceneNewEditType.TRIGGER_CONDITION -> {
                mTriggerItemDataList.filter { it.itemIsActive }
            }

            SceneNewEditType.STATUS_CONDITION -> {
                mStatusItemDataList.filter { it.itemIsActive }
            }

            else -> {
                mActionItemDataList.filter { it.itemIsActive }
            }
        }
        CommonLogUtils.logI("SceneEditManager", "getSkillIdPosition====start=====>${skillId}")
        var index = 0
        if (list.isNotEmpty()) {
            for (i in list.indices) {
                CommonLogUtils.logI("SceneEditManager", "item====for=====>${list[i].type}")
                list[i].skillIdList.forEach {
                    if (it.skillId == skillId && it.isActive) { // 被禁用的情况下就返回第一页
                        index = i
                    }
                }
            }
        }
        CommonLogUtils.logE("SceneEditManager", "currentItem==>$index")
        return index
    }


    enum class TriggerType {
        TIME, ENVIRONMENT, LOCATION, DRIVE, DOOR, SEAT, LIGHTING, CHARGE, OMS;

        companion object {
            /**
             * 转换触发条件为enum
             * 顺序要与ux一致,不然skillId错误
             */
            fun toEnumType(type: Int): TriggerType {
                return when (type) {
                    0 -> TIME
                    1 -> ENVIRONMENT
                    2 -> LOCATION
                    3 -> DRIVE
                    4 -> DOOR
                    5 -> SEAT
                    6 -> LIGHTING
                    7 -> CHARGE
                    else -> OMS
                }
            }
        }
    }

    enum class StatusType {
        TIME, ENVIRONMENT, LOCATION, NAVIGATION, DOOR, SEAT, LIGHTING, OMS, LINK, SMARTDEVICES, OTHER;

        companion object {
            fun toEnumType(type: Int): StatusType {
                return when (type) {
                    0 -> TIME
                    1 -> ENVIRONMENT
                    2 -> LOCATION
                    3 -> NAVIGATION
                    4 -> DOOR
                    5 -> SEAT
                    6 -> LIGHTING
                    7 -> OMS
                    8 -> LINK
                    9 -> SMARTDEVICES
                    else -> OTHER
                }
            }
        }
    }

    enum class ActionType {
        RECOMMEND, AIR, DOOR, LAMP, SEAT, FRIDGE, DRIVE, APP, SYSTEM, SMART, OTHER;

        companion object {
            /**注意顺序要与添加skill list的顺序一致，否则索引错误*/
            fun toEnumType(type: Int): ActionType {
                return when (type) {
                    0 -> RECOMMEND
                    1 -> AIR
                    2 -> DOOR
                    3 -> LAMP
                    4 -> SEAT
                    5 -> FRIDGE
                    6 -> DRIVE
                    7 -> APP
                    8 -> SYSTEM
                    9 -> SMART
                    else -> OTHER
                }
            }
        }
    }
}