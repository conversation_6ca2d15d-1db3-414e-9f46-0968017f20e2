package com.dfl.smartscene.ui.edit.condition.status.other

import android.annotation.SuppressLint
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusOther
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->胎压
 * version: 1.0
 */
class OtherStatusFragment(private val skillId: Int, private val condition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<OtherStatusAdapter, SceneFragmentEditConditionBinding, OtherStatusViewModel>() {

    private var mAdapter: OtherStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<OtherStatusViewModel> {
        return OtherStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId)

        mViewModel.mTirePressureLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            // 添加安全检查，避免在编辑状态下自动点击导致异常
            if (condition != null && skillId != -1) {
                try {
                    val position = getSimulateClickPosition(condition, null)
                    if (position != -1 && position < (mAdapter?.data?.size ?: 0)) {
                        // 延迟执行，确保UI完全初始化
                        view?.post {
                            view?.let { it1 -> handleItemClick(it1, position) }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun getAdapter(): OtherStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = OtherStatusAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        val manager = GridLayoutManager(context, 2)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ConditionItemBean.SKILL_TITLE) return 2
                return 1
            }
        }
        mViewDataBinding.rvCondition.layoutManager = manager
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        mAdapter?.data?.let {
            // 添加边界检查
            if (position < 0 || position >= it.size) {
                return
            }
            
            val item = it[position]
            if (item.itemType == ConditionItemBean.SKILL_TITLE || 
                item.isEnable == false || 
                item.conditionType == null) {
                return
            }
            
            // 添加防重复点击检查
            if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
                try {
                    showRadioListDialog(item.conditionName, item.conditionType!!)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun showRadioListDialog(title: String, type: StatusOther) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.DEFAULT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getOtherStatusData(type)
        )
        dialog.preSetCondition = condition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}