package com.dfl.smartscene.ui.overlay.common.checklist

import android.view.View
import android.widget.RadioButton
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/09/10
 * desc : ui2.0带背景的单选框
 * version: 1.0
 */
interface OnRadioItemClickListener {
    fun onRadioItemClick(position: Int)
}

class RadioCheckAdapter(
    private var listener: OnRadioItemClickListener?
) : BaseQuickAdapter<CheckListBean, BaseViewHolder>(
    R.layout.scene_recycle_item_radio_check
) {

    override fun convert(holder: BaseViewHolder, item: CheckListBean) {
        val radio = holder.getView<RadioButton>(R.id.rb_radio)
        radio.text = item.content
        radio.setButtonDrawable(if (item.isSingleSelectIcon) R.drawable.scene_selector_me_radio_check_style else R.drawable.scene_selector_me_radio_multiple_check_style)
        radio.isChecked = item.isCheck
        //更多
        holder.getView<View>(R.id.view_more).visibility = if (item.isItemShowMoreIcon) View.VISIBLE else View.GONE
        if (item.isItemShowMoreIcon) {
            holder.itemView.setOnClickListener {
                listener?.onRadioItemClick(holder.position)
            }
        }
    }

    // 提供解绑监听器的方法
    fun detachListener() {
        listener = null
    }
}