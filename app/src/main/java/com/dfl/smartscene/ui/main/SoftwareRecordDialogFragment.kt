package com.dfl.smartscene.ui.main.help

import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.smartscene.R

class SoftwareRecordDialogFragment : BaseDialogFragment() {

    private var mBtnExit: AppCompatImageView? = null

    override fun initView(view: View) {
        mBtnExit = view.findViewById(R.id.btn_help_record_close)
        setListener()
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_main_software_record
    }

    private fun setListener() {
        mBtnExit?.setOnClickListener {
            dismissWithAnimate()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setCanceledOnTouchOutside(true)
        super.onCreate(savedInstanceState)
    }

}
