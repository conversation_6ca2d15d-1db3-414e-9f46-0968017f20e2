package com.dfl.smartscene.ui.main.discover.card

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.MVVMFragment
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.customapi.DataEventSceneDetailBean
import com.dfl.smartscene.databinding.SceneFragmentDiscoverSceneCardBinding
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.manager.SceneDataEventManager
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity
import com.dfl.smartscene.util.TrackUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :发现
 * version: 1.0
 */
class SceneCardFragment : MVVMFragment<SceneFragmentDiscoverSceneCardBinding, SceneCardViewModel>() {

    private var mAdapter: SceneCardItemAdapter? = null


    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_discover_scene_card
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneCardViewModel> {
        return SceneCardViewModel::class.java
    }


    @SuppressLint("NotifyDataSetChanged", "InflateParams")
    override fun initData() {
        super.initData()
        //在这里去查找发现场景的数据，不在应用起来时就去查找
        initLiveEventListener()

    }

    @SuppressLint("InflateParams", "NotifyDataSetChanged")
    private fun initLiveEventListener() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_DISCOVER_LIST, Int::class.java).observe(
            viewLifecycleOwner
        ) {
            if (it == 0) {
                mViewModel.getCacheDiscoverSceneData()
                if (mViewDataBinding.srlDiscover.isRefreshing) {
                    mViewDataBinding.srlDiscover.isRefreshing = false
                }
                mAdapter?.notifyDataSetChanged()
                if (mAdapter?.data?.size == 0) { //数据为空直接隐藏footerView
                    mAdapter?.removeAllFooterView()
                } else {
                    if (mAdapter?.hasFooterLayout() == true) //已经添加footerView就不再次添加
                        return@observe
                    mAdapter?.addFooterView(
                        LayoutInflater.from(context).inflate(R.layout.scene_recycle_item_discover_scene_foot_view, null)
                    )
                }
            } else {
                if (mViewDataBinding.srlDiscover.isRefreshing) {
                    mViewDataBinding.srlDiscover.isRefreshing = false
                }
            }
        }
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SceneCardItemAdapter()
        //        val itemTouchHelper = ItemTouchHelper(MyItemTouchHelperCallback())
        //        itemTouchHelper.attachToRecyclerView(mViewDataBinding.rvCardList)
        mViewDataBinding.srlDiscover.setColorSchemeColors(
            ContextCompat.getColor(
                context, R.color.scene_primary_color
            )
        )
        mViewDataBinding.srlDiscover.setOnRefreshListener {
            mViewDataBinding.srlDiscover.isRefreshing = true
            SceneManager.requestDiscoverInfoList()
        }

        mAdapter?.setOnItemClickListener { _, _, position ->
            mAdapter?.data?.get(position)?.let {
                SceneDetailActivity.startSceneDetailActivity(
                    context, Gson().toJson(it.scenario)
                )
            }
        }
        mViewDataBinding.rvCardList.adapter = mAdapter
        val manager = GridLayoutManager(getContext(), 3)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.footerViewPosition == position) return 3
                return 1
            }
        }
        mViewDataBinding.rvCardList.layoutManager = manager
        mViewDataBinding.rvCardList.addItemDecoration(SceneCardItemDecoration())
        mAdapter?.data = SceneManager.getDiscoverInfoList()
    }


    private val handler = Handler(Looper.getMainLooper())

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
    }

    private class SceneCardItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            outRect.set(16, 0, 16, 32)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden) //tab切换用
        if (!hidden) {
            setDiscoverSceneDetailDataEvent()
        }
    }


    private fun setDiscoverSceneDetailDataEvent() {
        mAdapter?.data?.let {
            val list = ArrayList<DataEventSceneDetailBean>()
            var triggerDetail = ""
            var statusDetail = ""
            var actionDetail = ""
            for (i in it.indices) {
                val triggerCon = it[i].scenario.scenarioInfo?.edgeCondition
                triggerCon?.let { tri ->
                    triggerDetail = "触发条件-" + tri.category + "-" + tri.desc + "-" + tri.skillId
                }
                val statusCon = it[i].scenario.scenarioInfo?.conditions
                statusCon?.let { status ->
                    statusDetail = SceneDataEventManager.getStatusConditionDetail(status)
                }
                val actionData = it[i].scenario.scenarioInfo?.sequence
                actionData?.let { action ->
                    actionDetail = SceneDataEventManager.getActionDetail(action)
                }
                list.add(
                    DataEventSceneDetailBean(
                        it[i].scenario.scenarioInfo?.scenarioName ?: "",
                        it[i].scenario.scenarioInfo?.scenarioId ?: "",
                        (i + 1).toString(),
                        it[i].scenario.scenarioInfo?.autoExeFlag == 0,
                        triggerDetail,
                        statusDetail,
                        actionDetail,
                        if (it[i].scenario.scenarioInfo?.secondAskeFlag == 0) "开" else "关"
                    )
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()
        TrackUtils.viewStart(TrackUtils.PageNameType.Discover)
    }

    override fun onPause() {
        super.onPause()
        TrackUtils.viewEnd(TrackUtils.PageNameType.Discover)
    }
}