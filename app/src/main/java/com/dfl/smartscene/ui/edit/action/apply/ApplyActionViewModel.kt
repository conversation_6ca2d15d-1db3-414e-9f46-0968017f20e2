package com.dfl.smartscene.ui.edit.action.apply

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.ApplicationActionType
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import com.iauto.scenarioadapter.ScenarioInfo.Action
import com.iauto.scenarioadapter.ScenarioInfo.Sequence

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->应用view model
 * version: 1.0
 */
class ApplyActionViewModel : BaseViewModel() {

    /**
     * 动作-应用adapter数据
     */
    val applyActionLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<ApplicationActionType>>>()
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("ApplyActionViewModel")

    /**
     * 打开应用inputArg和应用名
     * 打开应用滚轮值
     * 车主指南:1 , 蓝牙电话:2 ,   天气:3,  消息中心:4, 能源中心:5,  小尼空间:6, 车辆控制:7 ,空调:8,  导航:9 ,
     * 在线音乐（QQ音乐）:10,   酷狗唱唱:11,  蓝牙音乐:12,  USB音视频:13,   系统设置:14, 声音空间:15,  应用中心:16,
     * 爱奇艺:17,  优酷:18,  哔哩哔哩:19,  云听:20,  喜马拉雅:21,  宝宝巴士:22,  口袋故事 :23 , 凤凰FM:24,   乐听头条:25
     */
    private val appValueNameMap: Map<Int, String> = mapOf(
        13 to CommonUtils.getString(R.string.scene_text_app_open_1),
        6 to CommonUtils.getString(R.string.scene_text_app_open_2),
        3 to CommonUtils.getString(R.string.scene_text_app_open_3),
        7 to CommonUtils.getString(R.string.scene_text_app_open_4),
        8 to CommonUtils.getString(R.string.scene_text_app_open_5),
        5 to CommonUtils.getString(R.string.scene_text_app_open_6),
        2 to CommonUtils.getString(R.string.scene_text_app_open_7),
        14 to CommonUtils.getString(R.string.scene_text_app_open_8),
        1 to CommonUtils.getString(R.string.scene_text_app_open_9),
        16 to CommonUtils.getString(R.string.scene_text_app_open_10),
        4 to CommonUtils.getString(R.string.scene_text_app_open_11),
        15 to CommonUtils.getString(R.string.scene_text_app_open_12),
    )

    /**
     * 初始化adapter数据
     */
    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<ApplicationActionType>>()
        //导航
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_action_navigation),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.NAVIGATION,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_NAVIGATION
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_initiate_navigation),
                R.drawable.scene_icon_me_action_navigation_initiate_navigation,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.START_NAVI,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY,
                    SkillsListConstant.SKILL_ID_ACTION_NAV_GO_CHARGE,
                    SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE,
                    SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SELECT_AND_MID,
                ),
                ApplicationActionType.ADAPTER_ITEM_TYPE_NAVIGATION
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_navigation_route_preference),
                R.drawable.scene_icon_me_action_navigation_route_preference,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.NAVIGATION_ROUTE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_NAV_ROUTE),
                ApplicationActionType.ADAPTER_ITEM_TYPE_NAVIGATION
            )
        )

        //多媒体
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_action_multimedia),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.MEDIA,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_open_app),
                R.drawable.scene_icon_me_action_media_open_media,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.OPEN_MEDIA,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA,
                ),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_close_app),
                R.drawable.scene_icon_me_action_media_close_media,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.CLOSE_MEDIA,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_playback_mode),
                R.drawable.scene_icon_me_action_media_play_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.MEDIA_PLAY_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_MODE),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_play_qq),
                R.drawable.scene_icon_me_action_media_open_qq_media,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.PLAY_QQ,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE,
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST,
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD,
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS,
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER,
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT
                ),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_play_control),
                R.drawable.scene_icon_me_action_media_play_control,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.MEDIA_PLAY_CONTROL,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL, SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2
                ),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MEDIA
            )
        )

        //语音播报
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_broadcast),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.BROADCAST,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_BROADCAST
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_xiao_ni_reply),
                R.drawable.scene_icon_me_action_apps_xiaoni_reply,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.VPA_REPLY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY),
                ApplicationActionType.ADAPTER_ITEM_TYPE_BROADCAST
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_weather_broadcast),
                R.drawable.scene_icon_me_action_xiaochen_weather,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.WEATHER_BROADCAST,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY),
                ApplicationActionType.ADAPTER_ITEM_TYPE_BROADCAST
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_trip_broadcast),
                R.drawable.scene_icon_me_action_trip_broadcast,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.TRIP_BROADCAST,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY),
                ApplicationActionType.ADAPTER_ITEM_TYPE_BROADCAST
            )
        )


        //消息中心
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_app_open_11),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.MESSAGE_CENTER,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_MESSAGE_CENTER
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_short_prompt),
                R.drawable.scene_icon_me_action_msg_text_short_prompt,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.APP_NOTIFY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY),
                ApplicationActionType.ADAPTER_ITEM_TYPE_MESSAGE_CENTER
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_app_open_12),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.SOUND_SPACE,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_sound_space_play_prank_music),
                R.drawable.scene_icon_me_action_soundspace_play_prank_music,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.PLAY_PRANK_MUSIC,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_sound_effect),
                R.drawable.scene_icon_me_action_soundspace_sound_effect,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.SOUND_EFFECT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_SOUND_EFFECT),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_voice_switch),
                R.drawable.scene_icon_me_action_soundspace_acoustic_choser,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.VOICE_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_SWITCH),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_voice_volume),
                R.drawable.scene_icon_me_action_soundspace_voice_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.VOICE_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_VOLUME),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_use_voice),
                R.drawable.scene_icon_me_action_soundspace_acoustic_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.USE_VOICE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_USE_VOICE),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_sound_outer_speaker),
                R.drawable.scene_icon_me_action_apps_loudspeaker_shouting,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.OUTER_SPEAKER,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_OUT_SPEAKER),
                ApplicationActionType.ADAPTER_ITEM_TYPE_SOUND_SPACE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_app_open_6),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.ENERGY_CENTER,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_ENERGY_CENTER
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_energy_center_preheat_switch),
                R.drawable.scene_icon_me_action_energycenter_preheat_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.PREHEAT_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_PREHEAT_SWITCH),
                ApplicationActionType.ADAPTER_ITEM_TYPE_ENERGY_CENTER
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_energy_center_discharge_switch),
                R.drawable.scene_icon_me_action_energycenter_discharge_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.DISCHARGE_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_DISCHARGE_SWITCH),
                ApplicationActionType.ADAPTER_ITEM_TYPE_ENERGY_CENTER
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_energy_center_endurance_mode),
                R.drawable.scene_icon_me_action_apps_endurance_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.ENDURANCE_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_ENDURANCE_MODE),
                ApplicationActionType.ADAPTER_ITEM_TYPE_ENERGY_CENTER
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_control_app),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                ApplicationActionType.CONTROL_APP,
                null,
                ApplicationActionType.ADAPTER_ITEM_TYPE_CONTROL_APP
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_control_app_open),
                R.drawable.scene_icon_me_action_apply_open_apply,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.APP_OPEN,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_OPEN_APP),
                ApplicationActionType.ADAPTER_ITEM_TYPE_CONTROL_APP
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_control_app_close),
                R.drawable.scene_icon_me_action_apply_close_apply,
                ActionSkillItemBean.SKILL_CONTENT,
                ApplicationActionType.APP_CLOSE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_CLOSE_APP),
                ApplicationActionType.ADAPTER_ITEM_TYPE_CONTROL_APP
            )
        )


        val type = SceneEditManager.ActionType.APP
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        applyActionLiveData.postValue(result)
        CommonLogUtils.logD(TAG, "post了一次")
    }

    /**
     * 根据动作类型获取对应的Sequence实例
     */
    fun getActionList(
        title: String, actionType: ApplicationActionType
    ): ArrayList<ScenarioInfo.Sequence> {
        val actionList = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()

        when (actionType) {
            ApplicationActionType.NAVIGATION_ROUTE -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_NAV_ROUTE, ArgType.INT32, "0"))
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_NAV_ROUTE, title, args
                        )
                    )
                )
            }

            ApplicationActionType.OPEN_MEDIA -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_APP_OPEN_MEDIA, ArgType.INT32, "1"))
                //打开多媒体
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA, title, args
                        )
                    )
                )
            }

            ApplicationActionType.CLOSE_MEDIA -> { //关闭多媒体
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE, title, args
                        )
                    )
                )
            }

            ApplicationActionType.MEDIA_PLAY_MODE -> { //播放模式
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "0"
                    )
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_MODE, title, args
                        )
                    )
                )
            }

            ApplicationActionType.MEDIA_PLAY_CONTROL -> { //播放控制
                //暂停播放
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL,
                            CommonUtils.getString(R.string.scene_text_edit_add_action_multimedia) + title,
                            listOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                                )
                            )
                        )
                    )
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL,
                            CommonUtils.getString(R.string.scene_text_edit_add_action_multimedia) + title,
                            listOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
                //上一首下一首
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2,
                            CommonUtils.getString(R.string.scene_text_edit_add_action_multimedia) + title,
                            listOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2,
                            CommonUtils.getString(R.string.scene_text_edit_add_action_multimedia) + title,
                            listOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "2"
                                )
                            )
                        )
                    )
                )
            }

            ApplicationActionType.WEATHER_BROADCAST -> {
                //				args.add(
                //					InputArgInfo(
                //						BroadcastActionConstant.ARG_NAME_WEATHER_REPORT, ArgType.STRING, "1"
                //					)
                //				)
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_APP_WEATHER_REPORT, title, args
                        )
                    )
                )
            }

            ApplicationActionType.TRIP_BROADCAST -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_APP_TRAVEL_REPORT_JOURNEY_INFO, ArgType.STRING, "1"
                    )
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_APP_TRAVEL_REPORT, title, args
                        )
                    )
                )
            }

            ApplicationActionType.PLAY_PRANK_MUSIC -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_APP_POSITION, ArgType.INT32, "2"))
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_APP_SOUND, ArgType.INT32, "1"))
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_PLAY_PRANK_MUSIC,
                            CommonUtils.getString(R.string.scene_text_action_text_sound_space_play_prank_music),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.SOUND_EFFECT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_SOUND_EFFECT,
                            CommonUtils.getString(R.string.scene_text_action_sound_effect),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.VOICE_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_SWITCH,
                            CommonUtils.getString(R.string.scene_text_action_voice_switch),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.VOICE_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "2"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_VOICE_VOLUME,
                            CommonUtils.getString(R.string.scene_text_action_voice_volume),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.USE_VOICE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_APP_USE_VOICE, ArgType.INT32, "0"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_APP_USE_VOICE, "", args
                        )
                    )
                )
            }

            ApplicationActionType.OUTER_SPEAKER -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_APP_OUT_SPEAKER, "", args
                        )
                    )
                )
            }

            ApplicationActionType.PREHEAT_SWITCH -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"))
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_PREHEAT_SWITCH,
                            CommonUtils.getString(R.string.scene_text_action_text_energy_center_preheat_switch),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.DISCHARGE_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_DISCHARGE_SWITCH,
                            CommonUtils.getString(R.string.scene_text_action_text_energy_center_discharge_switch),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.ENDURANCE_MODE -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_MODE, ArgType.UINT8, "1"))
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_ENDURANCE_MODE,
                            CommonUtils.getString(R.string.scene_text_action_text_energy_center_discharge_switch),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.APP_OPEN -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "13"
                    )
                )
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_OPEN_APP,
                            CommonUtils.getString(R.string.scene_text_action_text_control_app_open),
                            args
                        )
                    )
                )
            }

            ApplicationActionType.APP_CLOSE -> {
                actionList.add(
                    Sequence(
                        1, 1, Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_APP_CLOSE_APP,
                            CommonUtils.getString(R.string.scene_text_action_text_control_app_close),
                            args
                        )
                    )
                )

            }


            else -> {
                CommonLogUtils.logE(TAG, "错误的ApplicationActionType:$actionType")
            }
        }

        return actionList
    }


    /**
     * 获取CheckList对话框对应icon列表
     */
    fun getListData(
        resources: Resources, actionType: ApplicationActionType, title: String = ""
    ): ArrayList<CheckListBean> {
        return when (actionType) {
            ApplicationActionType.MEDIA_PLAY_CONTROL -> { //播放控制
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_pause), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_play), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_last), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_next), false)
                )
            }

            ApplicationActionType.CLOSE_MEDIA, //关闭多媒体
            ApplicationActionType.APP_CLOSE, //关闭应用
            ApplicationActionType.WEATHER_BROADCAST, //天气播报
            ApplicationActionType.TRIP_BROADCAST -> {
                arrayListOf(
                    CheckListBean(title, true)
                )
            }

            ApplicationActionType.OPEN_MEDIA -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_qq), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_bluetooth_music), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_local_music), false),
                )
            }

            ApplicationActionType.MEDIA_PLAY_MODE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_list_loop), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_single_tune_circulation), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_shuffle), false),
                )
            }

            ApplicationActionType.VOICE_VOLUME -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_high), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_low), false),
                )
            }

            ApplicationActionType.USE_VOICE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_use_voice_1), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_use_voice_2), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_use_voice_3), false),
                )
            }

            ApplicationActionType.SOUND_EFFECT -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_nature), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_dynamic), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_environment), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_relax), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_origin), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_effect_movie), false),
                )
            }

            ApplicationActionType.NAVIGATION_ROUTE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_intelligent_recommend), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_avoid_congestion), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_charge_less), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_no_high_speed), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_high_speed_priority), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_fastest), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_road_priority), false),
                )
            }

            ApplicationActionType.APP_OPEN -> {
                val list = arrayListOf<CheckListBean>()
                appValueNameMap.values.forEachIndexed { index, value ->
                    if (index == 0) {
                        list.add(CheckListBean(value, true))
                    } else {
                        list.add(CheckListBean(value, false))
                    }
                }
                list
            }

            ApplicationActionType.ENDURANCE_MODE -> {
                arrayListOf(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_text_energy_center_endurance_mode_1), true
                    ),
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_text_energy_center_endurance_mode_2), false
                    ),
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

    /**
     * 根据动作类型获取对应InputArg value值
     */
    fun getInputArgsList(actionType: ApplicationActionType): List<Int> {
        return when (actionType) {
            ApplicationActionType.MEDIA_PLAY_MODE -> { //播放模式
                arrayListOf(1, 3, 2)
            }

            ApplicationActionType.APP_OPEN -> {
                appValueNameMap.keys.toList()
            }

            ApplicationActionType.SOUND_EFFECT -> {
                listOf(1, 2, 3, 4, 5, 6)
            }

            ApplicationActionType.OPEN_MEDIA, ApplicationActionType.USE_VOICE -> {
                listOf(1, 2, 3)
            }

            ApplicationActionType.VOICE_VOLUME -> {
                listOf(3, 2, 1)
            }

            ApplicationActionType.OUTER_SPEAKER -> {
                listOf(1, 2, 3, 4, 5, 6, 0)
            }

            ApplicationActionType.NAVIGATION_ROUTE -> {
                listOf(0, 2, 4, 8, 0x10, 0x20, 0x40)
            }

            ApplicationActionType.ENDURANCE_MODE -> {
                listOf(1, 2)
            }

            else -> {
                listOf(1, 0)
            }
        }
    }

    /**
     * 单个wheel滚动条显示文本
     * 获取沉浸音效文本,声浪音量
     */
    fun getSingleWheelData(resources: Resources, type: ApplicationActionType): List<String> {
        return when (type) {
            ApplicationActionType.SOUND_EFFECT -> {
                listOf(
                    resources.getString(R.string.scene_text_action_effect_nature),
                    resources.getString(R.string.scene_text_action_effect_dynamic),
                    resources.getString(R.string.scene_text_action_effect_environment),
                    resources.getString(R.string.scene_text_action_effect_relax),
                    resources.getString(R.string.scene_text_action_effect_origin),
                    resources.getString(R.string.scene_text_action_effect_movie),
                )
            }

            ApplicationActionType.OUTER_SPEAKER -> {
                listOf(
                    resources.getString(R.string.scene_text_common_10s),
                    resources.getString(R.string.scene_text_common_20s),
                    resources.getString(R.string.scene_text_common_30s),
                    resources.getString(R.string.scene_text_common_40s),
                    resources.getString(R.string.scene_text_common_50s),
                    resources.getString(R.string.scene_text_common_60s),
                    resources.getString(R.string.scene_text_common_close),
                )
            }

            ApplicationActionType.APP_OPEN -> {
                appValueNameMap.values.toList()
            }

            ApplicationActionType.NAVIGATION_ROUTE -> {
                listOf(
                    resources.getString(R.string.scene_text_action_intelligent_recommend),
                    resources.getString(R.string.scene_text_action_avoid_congestion),
                    resources.getString(R.string.scene_text_action_charge_less),
                    resources.getString(R.string.scene_text_action_no_high_speed),
                    resources.getString(R.string.scene_text_action_high_speed_priority),
                    resources.getString(R.string.scene_text_action_fastest),
                    resources.getString(R.string.scene_text_action_road_priority)
                )
            }

            else -> {
                emptyList()
            }
        }

    }

    fun getLeftWheelMap(): Map<String, String> {
        return mapOf(
            Pair(CommonUtils.getString(R.string.scene_text_oms_main_driver), "1"),
            Pair(CommonUtils.getString(R.string.scene_text_oms_copilot), "2"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_position_left_rear), "3"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_position_right_rear), "4"),
        )
    }

    fun getRightWheelMap(): Map<String, String> {
        return mapOf(
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_0), "1"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_1), "2"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_2), "3"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_3), "4"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_4), "5"),
            Pair(CommonUtils.getString(R.string.scene_text_edit_action_apply_sound_play_sound_5), "6"),
        )
    }
}