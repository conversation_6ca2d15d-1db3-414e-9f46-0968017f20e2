package com.dfl.smartscene.ui.edit.action.recommend

import android.annotation.SuppressLint
import android.view.View
import androidx.core.text.isDigitsOnly
import com.dfl.android.common.base.MVVMFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.RecommendType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.soa.getSequenceValueByName
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.apply.NavigationActionDialogFragment
import com.dfl.smartscene.ui.overlay.apply.VpaReplyDialogFragment
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus
import java.util.*

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->推荐页面
 * version: 1.0
 */
class RecommendActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMFragment<SceneFragmentEditActionBinding, RecommendActionViewModel>() {

    private var mAdapter: RecommendActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<RecommendActionViewModel> {
        return RecommendActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = RecommendActionAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            if (!DebouncingUtils.isValid(
                    v, SceneEditManager.mViewClickDuration
                )
            ) return@setOnItemClickListener

            mAdapter?.data?.let {
                if (it[position].actionType == null) return@let
                when (it[position].actionType) {
                    RecommendType.DELAY -> showTimePickerDialog()
                    RecommendType.AIR_TEMP, RecommendType.AIR_TEMP_TWO -> {
                        showAirTempDialog(it[position].actionType!!)
                    }

                    RecommendType.AIR_VOLUME -> showAirVolumeDialog(it[position].actionType!!)
                    RecommendType.START_NAVI -> showStartNaviDialog(it[position])
                    RecommendType.VPA_REPLY -> showVpaReplyDialog(it[position].content)
                    RecommendType.SHORT_TEXT_TIP -> showShortTextTipsDialog(it[position].content)
                    else -> {
                        showRadioListDialog(it[position].content, it[position].actionType!!)
                    }
                }
            }
        }
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)
        mViewModel.mRecommendLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
        }
    }

    /**
     * 延时
     */
    private fun showTimePickerDialog() {
        val msg = getSequenceValueByName(sequence, SkillsListConstant.INPUT_ARG_TRIGGER_TIME_SECOND_NAME)
        var hms: TimeUtils.HMS? = null
        if (msg != null && msg.isDigitsOnly()) {
            val s = msg.toInt()
            hms = TimeUtils.secondToHMS(s)
        }
        var hStr: String? = null //默认值
        var mStr: String? = null
        var sStr: String? = null
        hms?.let {
            hStr = String.format(Locale.getDefault(), "%02d", it.h)
            mStr = String.format(Locale.getDefault(), "%02d", it.m)
            sStr = String.format(Locale.getDefault(), "%02d", it.s)
        }

        val hour: MutableList<String> = ArrayList()
        val minutes: MutableList<String> = ArrayList()
        val seconds: MutableList<String> = ArrayList()
        for (i in 0..23) {
            hour.add(String.format(Locale.getDefault(), "%02d", i))
        }
        for (i in 0..59) {
            minutes.add(String.format(Locale.getDefault(), "%02d", i))
            seconds.add(String.format(Locale.getDefault(), "%02d", i))
        }
        val singlePickerDialog = TimePickerDialog(
            context,
            hour,
            minutes,
            seconds,
            context.getString(R.string.scene_text_common_action_delay),
            SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID,
            hStr,
            mStr,
            sStr
        )
        singlePickerDialog.setWheelUnit(
            getString(R.string.scene_text_common_hour),
            getString(R.string.scene_text_common_minute),
            getString(R.string.scene_text_common_second)
        )
        singlePickerDialog.showWithAnimate()
    }

    private fun showRadioListDialog(title: String, type: RecommendType) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int>
        when (type) {
            RecommendType.AIR_SWITCH, RecommendType.ALL_WINDOW, RecommendType.AMBIENT_LAMP_SWITCH, RecommendType.OPEN_MEDIA -> {
                //一个sequence根据inputArg选项
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.INPUT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getActionData(type, resources),
                )
                dialog.inputArgValueList = mViewModel.getInputArgIntValue(type)
            }

            else -> {
                //单选,播放多媒体等不需要判断inputArg,只需要选中对应sequence
                dialog = RadioListDialogFragment(
                    title,
                    CheckListType.DEFAULT_CHECK_MODE,
                    mViewModel.getListData(resources, type, title),
                    mViewModel.getActionData(type, resources),
                )
            }
        }

        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    /**
     * 空调温度
     */
    private fun showAirTempDialog(type: RecommendType) {
        if (type == RecommendType.AIR_TEMP_TWO) {
            //显示双区空调
            val dialog = DoubleWheelDialogFragment(
                resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                mViewModel.getRadioWheelDialogRbMap(),
                mViewModel.getRadioWheelDialogWheelMap(),
                mViewModel.getActionData(type, resources),
                DoubleWheelType.DOUBLE_SKILL
            )
            dialog.setupPreAction(sequence)
            dialog.setUnit("", getString(R.string.scene_text_common_temperature_unit))
            dialog.setRightWheelLoop(false)
            dialog.setWheelDefaultPosition(0, 8)
            dialog.show(parentFragmentManager, "showAirTempDialog")
        } else {
            val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Double>(
                context,
                mViewModel.initAirTemperatureWheelData(),
                resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                mViewModel.getActionData(type, resources),
                SingleWheelType.SINGLE_WHEEL_INPUT_ARG
            )
            dialog.setUnit(getString(R.string.scene_text_common_temperature_unit))
            dialog.isLoop = false
            dialog.setupPreAction(sequence)
            dialog.setWheelDefaultPosition(8)
            dialog.setInputArgValueList(mViewModel.getAirTempInputArgInfoList())
            dialog.showWithAnimate()
        }
    }

    /**
     * 空调风量
     */
    private fun showAirVolumeDialog(type: RecommendType) {
        val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Int>(
            context,
            mViewModel.initAirConditionerVolumeWheelData(),
            resources.getString(R.string.scene_text_common_action_air_conditioner_air_volume),
            mViewModel.getActionData(type, resources),
            SingleWheelType.SINGLE_WHEEL_INPUT_ARG
        )
        dialog.setUnit(getString(R.string.scene_text_indicator_unit_air_volume))
        dialog.setupPreAction(sequence)
        dialog.setWheelDefaultPosition(1)
        dialog.setInputArgValueList(mViewModel.getAirConditionerVolumeArgInfoList())
        dialog.showWithAnimate()

    }

    /**
     * 发起导航
     */
    private fun showStartNaviDialog(bean: ActionSkillItemBean<RecommendType>) {
        NavigationActionDialogFragment.show(childFragmentManager, bean.actionSkillId, sequence)
    }

    /**
     * 小尼回复
     */
    private fun showVpaReplyDialog(content: String) {
        val replayMsg = getSequenceValueByName(sequence, SkillsListConstant.INPUT_ARG_ACTION_APP_VPA_REPLY)
        val vpaReplyDialogFragment = VpaReplyDialogFragment.getInstance()
        vpaReplyDialogFragment.setReplayContent(replayMsg)
        vpaReplyDialogFragment.title = content
        vpaReplyDialogFragment.type = VpaReplyDialogFragment.EditInputType.VPA_REPLY
        vpaReplyDialogFragment.setEditHint(resources.getString(R.string.scene_edit_input_hint_vpa_reply_please))
        vpaReplyDialogFragment.onInputListener = object : VpaReplyDialogFragment.OnInputListener {
            override fun onInput(inputData: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArg = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_APP_VPA_REPLY, ArgType.STRING, inputData
                )
                inputArgList.add(inputArg)
                val action = ScenarioInfo.Action(
                    inputData, SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY, content, inputArgList
                )
                val sequence = ScenarioInfo.Sequence(1, 1, action)
                EventBus.getDefault().post(sequence)
            }
        }
        vpaReplyDialogFragment.show(
            childFragmentManager, VpaReplyDialogFragment.DIALOG_FRAGMENT_TAG
        )
    }

    /**
     * 文字短提示
     */
    private fun showShortTextTipsDialog(title: String) {
        val replayMsg = getSequenceValueByName(sequence, SkillsListConstant.INPUT_ARG_ACTION_APP_NOTIFY)
        val vpaReplyDialogFragment = VpaReplyDialogFragment.getInstance()
        vpaReplyDialogFragment.type = VpaReplyDialogFragment.EditInputType.SHORT_TEXT_PROMPT
        vpaReplyDialogFragment.title = title
        vpaReplyDialogFragment.setEditHint(getString(R.string.scene_edit_input_hint_short_text_tips_please))
        vpaReplyDialogFragment.setReplayContent(replayMsg)
        vpaReplyDialogFragment.onInputListener = object : VpaReplyDialogFragment.OnInputListener {
            override fun onInput(inputData: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArg = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_APP_NOTIFY, ArgType.STRING, inputData
                )
                inputArgList.add(inputArg)
                val action = ScenarioInfo.Action(
                    inputData, SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY, title, inputArgList
                )
                val sequence = ScenarioInfo.Sequence(1, 1, action)
                EventBus.getDefault().post(sequence)
            }
        }
        vpaReplyDialogFragment.show(
            childFragmentManager, "vpaReplyDialogFragment"
        )
    }

}