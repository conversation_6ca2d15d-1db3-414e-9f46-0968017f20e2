package com.dfl.smartscene.ui.overlay.media

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.MediaType
import com.dfl.smartscene.databinding.SceneDialogFragmentRadioListForBigBinding
import com.dfl.smartscene.ui.overlay.apply.EnterTextDialogFragment
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 *Created by 钟文祥 on 2025/3/4.
 *Describer: 播放qq音乐专用 dialog (lk2a新的)
 */
class QQCheck6DialogFragment2a(private var preSequence: ScenarioInfo.Sequence?) : BaseDialogFragment(),
                                                                                  View.OnClickListener {

    private var _binding: SceneDialogFragmentRadioListForBigBinding? = null
    private val viewBinging get() = _binding!!

    private var mAdapter: QQCheck6Adapter? = null
    private var mList: ArrayList<CheckListBean> = arrayListOf()

    companion object {
        fun showFragment(manager: FragmentManager, sequence: ScenarioInfo.Sequence?) {
            val dialog = QQCheck6DialogFragment2a(sequence)
            dialog.show(manager, QQCheck6DialogFragment2a::javaClass.name)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = SceneDialogFragmentRadioListForBigBinding.inflate(inflater, container, false)
        return viewBinging.root
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_radio_list_for_big
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }


    override fun initView(view: View) {
        viewBinging.tvTitle.text = getString(R.string.scene_text_action_play_qq)

        initListener()
        initRecycleView()
    }

    private fun initListener() {
        viewBinging.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnNormal.setOnClickListener(this)
    }

    private fun initRecycleView() {
        UIUtils.initRecyclerView(
            activity, viewBinging.rvCheckList, GridLayoutManager(activity, 2), false
        )
        mAdapter = QQCheck6Adapter()
        mList.addAll(getListData())
        mAdapter?.addData(mList)
        mAdapter?.setOnItemClickListener { adapter, view, position ->
            openItem(mList.get(position).content, position)
        }
        viewBinging.rvCheckList.adapter = mAdapter
    }

    private fun openItem(content: String, position: Int) {
        if (position in 0..3) {
            showRadioListDialog(content, position)
        }
        if (position == 4) {
            openSearchSingerDialog(getString(R.string.scene_text_action_singer_search), position)
        }
        if (position == 5) {
            openSearchSongsDialog(getString(R.string.scene_text_action_songs_search), position)
        }
    }

    private fun openSearchSingerDialog(title: String, position: Int) {
        val enterTextDialogFragment = EnterTextDialogFragment.getInstance()
        enterTextDialogFragment.isLevel4OrAbove = true
        enterTextDialogFragment.title = title
        enterTextDialogFragment.type = SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SINGER
        enterTextDialogFragment.onInputListener = object : EnterTextDialogFragment.OnInputListener {
            override fun onInput(id: String, name: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val arg1 = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_MEDIA_SINGER, ArgType.STRING, id //歌手id
                )
                inputArgList.add(arg1)
                val action = ScenarioInfo.Action(
                    name, SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER, title, inputArgList
                )
                preSequence = ScenarioInfo.Sequence(1, 1, action)
                handleConfirm()
            }

            override fun cancel() {
                //                dismiss()
            }
        }
        enterTextDialogFragment.show(
            childFragmentManager, EnterTextDialogFragment.TAG_DIALOG_ENTER
        )
    }

    private fun openSearchSongsDialog(title: String, position: Int) {
        val enterTextDialogFragment = EnterTextDialogFragment.getInstance()
        enterTextDialogFragment.isLevel4OrAbove = true
        enterTextDialogFragment.title = title
        enterTextDialogFragment.type = SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG
        enterTextDialogFragment.onInputListener = object : EnterTextDialogFragment.OnInputListener {
            override fun onInput(id: String, name: String) {
                val inputArgList = ArrayList<InputArgInfo>()
                val inputArg = InputArgInfo(
                    SkillsListConstant.INPUT_ARG_ACTION_MEDIA_NAME, ArgType.STRING, id //歌曲id
                )
                inputArgList.add(inputArg)
                val action = ScenarioInfo.Action(
                    name, SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT, title, inputArgList
                )
                preSequence = ScenarioInfo.Sequence(1, 1, action)
                handleConfirm()
            }

            override fun cancel() {
                //dismiss()
            }
        }
        enterTextDialogFragment.show(
            childFragmentManager, EnterTextDialogFragment.TAG_DIALOG_ENTER
        )
    }

    private fun showRadioListDialog(title: String, position: Int) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int> = RadioListDialogFragment(
            title,
            CheckListType.QQ_MUSIC_MODE,
            initCheckList(getCheckList(mActionTypeList[position])),
            getActionList(title, mActionTypeList[position]),
        )
        dialog.isLevel4OrAbove = true
        dialog.inputArgValueList = getInputArgsList(mActionTypeList[position])
        dialog.preSetSequence = preSequence
        dialog.onPrimaryBtnListener = DialogInterface.OnDismissListener { dismissWithAnimate() }
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    private fun initCheckList(list: ArrayList<String>): ArrayList<CheckListBean> {
        val checkList = arrayListOf<CheckListBean>()
        list.forEachIndexed { index, s ->
            checkList.add(CheckListBean(s, index == 0))
        }
        return checkList
    }

    private fun openSingleWheelDialog(title: String, position: Int) {
        val dialog = context?.let {
            SingleWheelDialog<ScenarioInfo.Sequence, Int>(
                it,
                getCheckList(mActionTypeList[position]),
                title,
                getActionList(title, mActionTypeList[position]),
                SingleWheelType.SINGLE_WHEEL_INPUT_ARG
            )
        }

        run breaking@{
            getCheckList(mActionTypeList[position]).forEachIndexed { index, s ->
                if (s == mList[position].desc) {
                    dialog?.setWheelDefaultPosition(index)
                    return@breaking
                }
            }
        }

        //        dialog?.setupPreAction(preSequence)
        dialog?.setInputArgValueList(getInputArgsList(mActionTypeList[position]))
        dialog?.setOnSceneActionListener(object : SingleWheelDialog.OnSceneActionListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun selectAction(sequence: ScenarioInfo.Sequence?) {
                preSequence = sequence
                updateRv(position)
            }

            override fun cancel() {

            }

            override fun selectValue(value: String?, index: Int) {
            }
        })
        dialog?.show()
    }

    private fun updateRv(position: Int) {
        if (mList[position].isCheck && mList[position].desc == preSequence?.action?.desc!!) {
            return
        }

        run breaking@{
            mList.forEachIndexed { index, checkListBean ->
                if (checkListBean.isCheck) {
                    checkListBean.isCheck = false
                    mAdapter?.notifyItemChanged(index)
                    return@breaking
                }
            }
        }
        run breaking@{
            mList.forEachIndexed { index, checkListBean ->
                if (index == position) {
                    checkListBean.isCheck = true
                    checkListBean.desc = preSequence?.action?.desc!!
                    mAdapter?.notifyItemChanged(index)
                    return@breaking
                }
            }
        }
        updateTvConfirm()
    }

    private fun getActionList(
        title: String, actionType: MediaType
    ): ArrayList<ScenarioInfo.Sequence> {
        val actionList = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()

        //Sequence.type 1-6分别代表
        when (actionType) {
            MediaType.QQ_SONG_STYLE -> { //曲风
                //曲风
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_MEDIA_GENRE, ArgType.INT32, "1"
                    ) //enum:流行:1,中国风:2,民谣:3,摇滚:4,轻音乐:5,电子:6,爵士:7,乡村:8
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "流行", SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE, title, args
                        )
                    )
                )
            }

            MediaType.QQ_SONG_LIST -> { //歌单
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "1"
                    ) //enum:最近播放(歌曲歌单):1,我喜欢(歌曲):2,下载(歌曲):3,每日推荐:4,新歌速递:5,热门儿歌:6
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST, title, args
                        )
                    )
                )
            }

            MediaType.QQ_SONG_MOOD -> { //心情
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_MEDIA_MOOD, ArgType.INT32, "1"
                    ) //enum: 快乐:1,伤感 : 2,安静 : 3,励志 : 4,治愈 : 5,思念 : 6,甜蜜 : 7,寂寞 : 8 , 宣泄 : 9
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD, title, args
                        )
                    )
                )
            }

            MediaType.QQ_SONG_YEAR -> { //年代
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_MEDIA_ERA, ArgType.INT32, "1"
                    ) //enum: 00年代:1, 90年代 : 2, 80年代 : 3, 70年代 : 4
                )
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS, title, args
                        )
                    )
                )
            }

            else -> {}
        }

        return actionList
    }

    private val mActionTypeList = mutableListOf<MediaType>(
        MediaType.QQ_SONG_STYLE, //曲风
        MediaType.QQ_SONG_LIST, //歌单
        MediaType.QQ_SONG_MOOD, //心情
        MediaType.QQ_SONG_YEAR, //年代
        MediaType.QQ_SONG_SINGER, //歌手
        MediaType.QQ_SONG_APPOINT, //歌曲
    )
    private val mSkillList = mutableListOf<Int>(
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE,
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST,
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD,
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_YEARS,
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER,
        SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT
    )

    private fun getInputArgsList(actionType: MediaType): ArrayList<Int> {
        return when (actionType) {
            MediaType.QQ_SONG_STYLE -> arrayListOf(1, 2, 3, 4, 5, 6, 7, 8) //曲风
            MediaType.QQ_SONG_LIST -> arrayListOf(1, 2, 3, 4, 5, 6) //歌单
            MediaType.QQ_SONG_MOOD -> arrayListOf(1, 2, 3, 4, 5, 6, 7, 8, 9) //心情
            MediaType.QQ_SONG_YEAR -> arrayListOf(1, 2, 3, 4) //年代
            else -> arrayListOf()
        }
    }

    private fun getDesc(actionType: MediaType): String {
        preSequence?.let {
            if (it.action.skillId == mSkillList[mActionTypeList.indexOf(actionType)]) {

                if (it.action.skillId == SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SINGER || it.action.skillId == SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_SONG_APPOINT) {
                    return it.action.desc
                } else {
                    val value = it.action.input[0].value.toInt() - 1
                    if (value >= 0) {
                        return getCheckList(actionType)[value]
                    }
                }
            }
        }
        if (actionType == MediaType.QQ_SONG_SINGER || actionType == MediaType.QQ_SONG_APPOINT) {
            return ""
        }
        return getCheckList(actionType)[0] //默认的desc
    }

    private fun getCheckList(actionType: MediaType): ArrayList<String> {
        return when (actionType) {
            MediaType.QQ_SONG_STYLE -> { //曲风
                return arrayListOf(
                    resources.getString(R.string.scene_text_qq_song_style_1),
                    resources.getString(R.string.scene_text_qq_song_style_2),
                    resources.getString(R.string.scene_text_qq_song_style_3),
                    resources.getString(R.string.scene_text_qq_song_style_4),
                    resources.getString(R.string.scene_text_qq_song_style_5),
                    resources.getString(R.string.scene_text_qq_song_style_6),
                    resources.getString(R.string.scene_text_qq_song_style_7),
                    resources.getString(R.string.scene_text_qq_song_style_8),
                )
            }

            MediaType.QQ_SONG_LIST -> { //歌单
                return arrayListOf(
                    resources.getString(R.string.scene_text_qq_song_list_1),
                    resources.getString(R.string.scene_text_qq_song_list_2),
                    resources.getString(R.string.scene_text_qq_song_list_3),
                    resources.getString(R.string.scene_text_qq_song_list_4),
                    resources.getString(R.string.scene_text_qq_song_list_5),
                    resources.getString(R.string.scene_text_qq_song_list_6),
                )
            }

            MediaType.QQ_SONG_MOOD -> { //心情
                return arrayListOf(
                    resources.getString(R.string.scene_text_qq_song_mood_1),
                    resources.getString(R.string.scene_text_qq_song_mood_2),
                    resources.getString(R.string.scene_text_qq_song_mood_3),
                    resources.getString(R.string.scene_text_qq_song_mood_4),
                    resources.getString(R.string.scene_text_qq_song_mood_5),
                    resources.getString(R.string.scene_text_qq_song_mood_6),
                    resources.getString(R.string.scene_text_qq_song_mood_7),
                    resources.getString(R.string.scene_text_qq_song_mood_8),
                    resources.getString(R.string.scene_text_qq_song_mood_9),
                )
            }

            MediaType.QQ_SONG_YEAR -> { //年代
                return arrayListOf(
                    resources.getString(R.string.scene_text_qq_song_year_1),
                    resources.getString(R.string.scene_text_qq_song_year_2),
                    resources.getString(R.string.scene_text_qq_song_year_3),
                    resources.getString(R.string.scene_text_qq_song_year_4)
                )
            }

            else -> {
                arrayListOf()
            }
        }
    }


    private fun getListData(): ArrayList<CheckListBean> {

        val list = arrayListOf(
            CheckListBean(
                getString(R.string.scene_text_action_song_style), false, desc = getDesc(mActionTypeList[0])
            ),
            CheckListBean(
                getString(R.string.scene_text_action_song_list), false, desc = getDesc(mActionTypeList[1])
            ),
            CheckListBean(
                getString(R.string.scene_text_action_song_mood), false, desc = getDesc(mActionTypeList[2])
            ),
            CheckListBean(
                getString(R.string.scene_text_action_song_year), false, desc = getDesc(mActionTypeList[3])
            ),
            CheckListBean(getString(R.string.scene_text_action_singer), false, desc = getDesc(mActionTypeList[4])),
            CheckListBean(getString(R.string.scene_text_action_songs), false, desc = getDesc(mActionTypeList[5])),
        )

        var selectedIndex = -1
        viewBinging.includeDialogBottom.btnPrimary.isEnable = false

        preSequence?.let {
            if (mSkillList.contains(it.action.skillId)) {
                selectedIndex = mSkillList.indexOf(it.action.skillId)
                viewBinging.includeDialogBottom.btnPrimary.isEnable = true
            }
        }
        if (selectedIndex != -1) {
            list.get(selectedIndex).isCheck = true
        }
        return list
    }

    private fun updateTvConfirm() {
        preSequence?.let {
            if (mSkillList.contains(it.action.skillId)) {
                viewBinging.includeDialogBottom.btnPrimary.isEnable = true
                return
            }
        }
        viewBinging.includeDialogBottom.btnPrimary.isEnable = false
    }

    //回调 按钮事件 setOnClickListener
    @SuppressLint("NotifyDataSetChanged")
    override fun onClick(v: View?) {
        if (!DebouncingUtils.isValid(v!!)) return
        when (v.id) {
            R.id.btn_normal -> { //取消
                dismissWithAnimate()
            }

            R.id.btn_primary -> { //确定
                handleConfirm()
            }
        }
    }

    /**提交 -总*/
    private fun handleConfirm() {
        preSequence?.action?.category = getString(R.string.scene_text_action_play_qq)
        preSequence?.action?.desc = when (preSequence?.action?.skillId) {
            SkillsListConstant.SKILL_ID_ACTION_MEDIA_QQ_SONG_STYLE -> preSequence?.action?.desc + getString(R.string.scene_text_media_song_style)
            SkillsListConstant.SKILL_ID_ACTION_MEDIA_PLAY_LIST -> preSequence?.action?.desc + getString(R.string.scene_text_media_play_list)
            SkillsListConstant.SKILL_ID_ACTION_MEDIA_MOOD -> preSequence?.action?.desc + getString(R.string.scene_text_media_mood)
            else -> preSequence?.action?.desc
        }
        EventBus.getDefault().post(preSequence)
        dismissWithAnimate()
    }
}