package com.dfl.smartscene.ui.edit.condition.trigger.time

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerTime
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog
import com.iauto.scenarioadapter.ScenarioInfo.Condition
import com.jeremyliao.liveeventbus.LiveEventBus
import java.util.*

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   :条件->时间页面（包含触发条件和状态条件）
 * version: 1.0
 */
class TimeTriggerFragment(private val skillId: Int, private val condition: Condition?) :
    MVVMAdapterFragment<TimeTriggerAdapter, SceneFragmentEditConditionBinding, TimeTriggerViewModel>() {

    private var mAdapter: TimeTriggerAdapter? = null


    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<TimeTriggerViewModel> {
        return TimeTriggerViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {

        mViewModel.initData(resources)

        mViewModel.mTimeNameLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()

            if (condition == null) return@observe

            if (SceneEditManager.checkIdIsActive(condition.skillId, 1)) {
                //isHaveTimeSkillId 的作用解决2级菜单viewpager初始化时会自动先生成第一个时间fragment，此时会发送一个没用参考数
                var isHaveTimeSkillId = false

                it.forEach { conditionItemBean ->
                    if (conditionItemBean.conditionSkillId?.contains(condition.skillId) == true) {
                        isHaveTimeSkillId = true
                    }
                }
                if (isHaveTimeSkillId) {
                    val position = getSimulateClickPosition(condition, null)
                    if (position != -1) {
                        view?.let { it1 -> handleItemClick(it1, position) }
                    }
                }
            } else { //没有这个能力
                //发消息通知上一级菜单 还原回去
                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(-1)
            }
        }
    }

    override fun getAdapter(): TimeTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = TimeTriggerAdapter()

        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(v: View, position: Int = 0) {
        if (DebouncingUtils.isValid(v, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.conditionType == null) return@let
                when (it.conditionType) {
                    TriggerTime.TIME_POINT -> {
                        showTimeSelectDialog()
                    }

                    else -> {
                        showRadioListDialog(it.conditionName, it.conditionType)
                    }
                }
            }
        }
    }

    /**日出日落 RadioListDialog*/
    private fun showRadioListDialog(title: String, type: TriggerTime) {
        val dialog = RadioListDialogFragment<Condition, String>(
            title, CheckListType.INPUT_CHECK_MODE, mViewModel.getCheckList(type), mViewModel.getConditionList(type)
        )
        dialog.inputArgValueList = mViewModel.getInputArgList(type)
        dialog.preSetCondition = condition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    private fun showTimeSelectDialog() {
        val hStr = condition?.input?.find { it.name == SkillsListConstant.INPUT_ARG_TRIGGER_TIME_HOUR_NAME }?.value
        val mStr = condition?.input?.find { it.name == SkillsListConstant.INPUT_ARG_TRIGGER_TIME_MINUTE_NAME }?.value
        val context = getContext() ?: return
        val hour: MutableList<String> = ArrayList()
        val minutes: MutableList<String> = ArrayList()
        for (i in 0..23) {
            hour.add(String.format(Locale.getDefault(), "%02d", i))
        }
        for (i in 0..59) {
            minutes.add(String.format(Locale.getDefault(), "%02d", i))
        }
        //ccm储存单位数不会添加0前缀，要手动添加
        val singlePickerDialog = TimePickerDialog(
            context,
            hour,
            minutes,
            context.getString(R.string.scene_text_common_condition_time_point),
            SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT,
            TimeUtils.singleTimeAddZero(hStr),
            TimeUtils.singleTimeAddZero(mStr),
            null
        )

        singlePickerDialog.showWithAnimate()
    }
}