package com.dfl.smartscene.ui.overlay.ambientlight

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.StringUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CarLightType
import com.dfl.smartscene.databinding.SceneDialogAmbientLightOptionalColorBinding
import com.dfl.smartscene.soa.getInputByName
import com.dfl.smartscene.ui.edit.action.light.CarLightActionViewModel
import com.dfl.smartscene.widget.SwatchesView
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

class OptionAmbientLightColorDialog(
    context: Context, var mTitle: String
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogAmbientLightOptionalColorBinding
    private var mSwatchesView: SwatchesView? = null
    private var preSetSequence: ScenarioInfo.Sequence? = null
    private val carLightActionViewModel: CarLightActionViewModel = CarLightActionViewModel()
    lateinit var list: ArrayList<ScenarioInfo.Sequence>

    /**用于监听确认按钮的dismiss事件,实现关闭父弹框*/
    var onPrimaryBtnListener: DialogInterface.OnDismissListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogAmbientLightOptionalColorBinding.inflate(LayoutInflater.from(context))
        setContentView(mVB.root)
        initView()
        setupInitData()
    }

    private fun initView() {
        mVB.tvLightColorOptionalTitle.text = context.getString(R.string.scene_text_action_optional)
        mSwatchesView = findViewById(R.id.swatches_view)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)

        mSwatchesView?.initView(64, 43)
    }

    private fun setupInitData() {
        preSetSequence?.let {
            when (it.action.skillId) {
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID, //氛围灯颜色自定义
                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE //炫彩氛围灯自定义
                -> {
                    val inputArgInfo = getInputByName(
                        it, when (it.action.skillId) {
                            SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID -> SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_NAME
                            SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE -> SkillsListConstant.INPUT_ARG_ACTION_COLORFUL_AMBIENT_LIGHT_COLOR_DETAILED_COLOR_NAME
                            else -> ""
                        }
                    )
                    val customColor = inputArgInfo?.value
                    if (customColor != null && StringUtils.isNumeric(customColor, false, true)) {
                        if (!TextUtils.isEmpty(inputArgInfo.desc)) {
                            mSwatchesView?.initView(
                                inputArgInfo.desc.split(",")[0].toInt(), inputArgInfo.desc.split(",")[1].toInt()
                            )
                        }
                    }
                }
            }
        }
    }

    fun setPreSetAmbientAction(sequence: ScenarioInfo.Sequence?) {
        this.preSetSequence = sequence
    }

    override fun onClick(v: View?) {
        v?.id?.let {
            when (it) {
                R.id.btn_normal -> {
                    dismissWithAnimate()
                }

                R.id.btn_primary -> {
                    val colorValue = mSwatchesView?.getSelectColorInt()
                    list = carLightActionViewModel.getActionData(CarLightType.CAR_AMBIENT_LAMP_COLOR)
                    val optionalAmbientLightAction = list[1]
                    optionalAmbientLightAction.action.input[0].value = colorValue.toString()
                    optionalAmbientLightAction.action.input[0].desc = mSwatchesView?.getSelectColorPosition()
                    optionalAmbientLightAction.action.category = mTitle
                    optionalAmbientLightAction.action.desc = context.getString(R.string.scene_text_action_optional)
                    EventBus.getDefault().post(optionalAmbientLightAction)
                    dismissWithAnimate()
                    onPrimaryBtnListener?.onDismiss(this)
                }

                else -> {}
            }
        }
    }

}