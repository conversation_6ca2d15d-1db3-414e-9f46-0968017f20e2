package com.dfl.smartscene.ui.main.help

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneFragmentHelpIntroBinding

class HelpDialogIntroFragment : Fragment() {
    private var binding: SceneFragmentHelpIntroBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 使用DataBindingUtil生成绑定类实例
        binding = DataBindingUtil.inflate(inflater, R.layout.scene_fragment_help_intro, container, false)
        // 返回绑定的根视图
        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }
}