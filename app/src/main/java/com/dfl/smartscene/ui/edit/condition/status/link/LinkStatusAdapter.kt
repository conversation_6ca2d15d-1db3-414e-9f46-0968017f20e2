package com.dfl.smartscene.ui.edit.condition.status.link

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusLink

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/09/20
 * desc: 连接状态条件的适配器
 * version:1.0
 */
class LinkStatusAdapter : BaseQuickAdapter<ConditionItemBean<StatusLink>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<StatusLink>> {
    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<StatusLink>) {
        holder.setText(R.id.tv_condition_reflex_name, item.conditionName)
        holder.setImageResource(R.id.iv_condition_reflex_icon, item.conditionIcon)
        if (item.isEnable == false) {
            holder.itemView.alpha = 0.5f
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override val adapterData: List<ConditionItemBean<StatusLink>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }

}