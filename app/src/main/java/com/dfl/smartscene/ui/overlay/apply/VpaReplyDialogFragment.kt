package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogFragmentVpaReplyBinding
import com.dfl.smartscene.util.UIUtils
import java.lang.ref.WeakReference

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/07/05
 * desc : 小尼回复 +消息通知
 * version: 1.0
 */
class VpaReplyDialogFragment : BaseDialogFragment() {
    private var _binding: SceneDialogFragmentVpaReplyBinding? = null
    private val viewBinging get() = _binding!!
    var type: EditInputType? = null
    var onInputListener: OnInputListener? = null
    private var hint: String? = null
    private var contentText: String? = null
    var title: String? = null
    private val imeOptions: Int = EditorInfo.IME_ACTION_DONE
    private val minNum = 1

    companion object {
        const val DIALOG_FRAGMENT_TAG = "vpa_reply_dialog_fragment_tag"
        fun getInstance(): VpaReplyDialogFragment {
            return VpaReplyDialogFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = SceneDialogFragmentVpaReplyBinding.inflate(inflater, container, false)
        return viewBinging.root
    }

    fun setEditHint(hint: String) {
        this.hint = hint
    }

    fun setReplayContent(content: String?) {
        this.contentText = content
    }

    @SuppressLint("ClickableViewAccessibility", "SetTextI18n")
    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        viewBinging.tvTitle.text = title
        if (!hint.isNullOrEmpty()) {
            viewBinging.etEditInput.hint = hint
        }
        if (!contentText.isNullOrEmpty()) {
            viewBinging.etEditInput.setText(contentText)
            viewBinging.etEditInput.setSelection(contentText?.length ?: 0)
        }
        viewBinging.etEditInput.imeOptions = imeOptions
        UIUtils.setKeyBroadInputMinIfNotEnable(viewBinging.etEditInput, minNum)
        setListener()
        onTextChanged(viewBinging.etEditInput.text.toString())
        KeyboardUtils.showSoftInput(viewBinging.etEditInput)
    }

    private fun cancel() {
        KeyboardUtils.hideSoftInput(viewBinging.etEditInput)
        onInputListener?.cancel()
        dismissWithAnimate()
    }

    private fun setListener() {
        viewBinging.ivClose.setOnClickListener {
            cancel()
        }
        viewBinging.etEditInput.setOnEditorActionListener(MyOnEditorActionListener(this@VpaReplyDialogFragment))
        viewBinging.etEditInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            @SuppressLint("SetTextI18n")
            override fun onTextChanged(inputStr: CharSequence?, start: Int, before: Int, count: Int) {
                onTextChanged(inputStr.toString())
            }

            @SuppressLint("NotifyDataSetChanged")
            override fun afterTextChanged(s: Editable?) {

            }
        })
    }

    class MyOnEditorActionListener(dialogFragment: VpaReplyDialogFragment) : TextView.OnEditorActionListener {
        private var reference: WeakReference<VpaReplyDialogFragment> =
            WeakReference<VpaReplyDialogFragment>(dialogFragment)

        override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            reference.get()?.let {
                if (actionId == it.imeOptions || (event?.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                    KeyboardUtils.hideSoftInput(it.viewBinging.etEditInput)
                    if (it.onInputListener != null) {
                        it.checkInputText()
                    }
                }
            }
            return true
        }
    }


    @SuppressLint("SetTextI18n")
    private fun onTextChanged(content: String) {

        val maxNum = if (type == EditInputType.VPA_REPLY) 50 else 20

        if (content.length > maxNum) {
            viewBinging.etEditInput.setText(content.substring(0, maxNum))
            viewBinging.etEditInput.setSelection(maxNum)
        }

        if (type == EditInputType.VPA_REPLY) {
            viewBinging.tvCheckMsg.text =
                "${getString(R.string.scene_edit_input_hint_vpa_reply_50)} (${viewBinging.etEditInput.text.toString().length}/${maxNum})"
        } else {
            viewBinging.tvCheckMsg.text =
                "${getString(R.string.scene_edit_input_hint_vpa_reply_20)} (${viewBinging.etEditInput.text.toString().length}/${maxNum})"
        }

    }

    private fun checkInputText() {
        val inputText = viewBinging.etEditInput.text.toString()
        onInputListener?.onInput(inputText)
        dismissWithAnimate()
    }


    enum class EditInputType {
        VPA_REPLY, SHORT_TEXT_PROMPT
    }


    interface OnInputListener {
        fun onInput(inputData: String)
        fun cancel() {}
    }

    override fun onResume() {
        super.onResume()
        viewBinging.etEditInput.post {
            KeyboardUtils.showSoftInput(viewBinging.etEditInput)
        }
        KeyboardUtils.registerSoftInputChangedListener(dialog?.window!!) {
            if (it == 0) {
                viewBinging.etEditInput.clearFocus()
            } else {
                viewBinging.etEditInput.requestFocus()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun getLayoutId(): Int {
        return -1
    }

    override fun onTouchOutside() {
        super.onTouchOutside()
        cancel()
    }
}