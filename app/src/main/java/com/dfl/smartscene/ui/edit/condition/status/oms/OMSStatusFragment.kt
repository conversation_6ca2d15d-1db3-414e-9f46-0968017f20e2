package com.dfl.smartscene.ui.edit.condition.status.oms

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusOMS
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/1/2.
 *Describer:
 */
class OMSStatusFragment(private val skillId: Int, private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<OMSStatusAdapter, SceneFragmentEditConditionBinding, OMSStatusViewModel>() {

    private var mAdapter: OMSStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<OMSStatusViewModel> {
        return OMSStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        mViewModel.initData(resources, skillId)

        mViewModel.liveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.isEnable == false) {
            return
        }
        if (!DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) return

        mAdapter?.data?.get(position)?.let {
            showOMSDialog(it.conditionType as StatusOMS, it.conditionName)
        }
    }

    override fun getAdapter(): OMSStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = OMSStatusAdapter()
        mAdapter?.setOnItemClickListener { _, view, position -> handleItemClick(view, position) }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }


    private fun showOMSDialog(type: StatusOMS, title: String) {
        val dialog = DoubleWheelDialogFragment<ScenarioInfo.Condition>(
            title,
            mViewModel.initLeftWheelMap(resources, type),
            mViewModel.initRightWheelMap(resources, type),
            mViewModel.getConditionData(type),
            DoubleWheelType.SINGLE_SKILL
        )
        dialog.setupPreCondition(preCondition)
        dialog.setWheelDefaultPosition(0, 0)
        dialog.show(parentFragmentManager, "showOMSDialog")
    }
}