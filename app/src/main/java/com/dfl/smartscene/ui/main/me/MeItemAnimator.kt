package com.dfl.smartscene.ui.main.me

import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import jp.wasabeef.recyclerview.animators.ScaleInAnimator

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/12/13
 * desc : 为了解决添加item时footer也有动画的问题
 * version: 1.0
 */
class MeItemAnimator : ScaleInAnimator() {
    /**添加item动画之前*/
    override fun preAnimateAddImpl(holder: RecyclerView.ViewHolder) {
        if (holder.itemViewType != BaseQuickAdapter.FOOTER_VIEW) {
            super.preAnimateAddImpl(holder)
        }
    }
}