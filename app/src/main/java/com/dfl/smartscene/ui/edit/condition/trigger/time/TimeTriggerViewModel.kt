package com.dfl.smartscene.ui.edit.condition.trigger.time

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerTime
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo.Condition

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   :
 * version: 1.0
 */
class TimeTriggerViewModel : BaseViewModel() {
    val mTimeNameLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerTime>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerTime>>()
        val type = SceneEditManager.TriggerType.TIME
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_common_condition_time_point),
                R.drawable.scene_icon_time_system_clock,
                arrayListOf(SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT),
                TriggerTime.TIME_POINT,
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_me_condition_time_day_status),
                R.drawable.scene_icon_me_trigger_time_sunrise_sunset,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_TIME_DAY_STATUS),
                TriggerTime.DAY_STATUS,
            )
        )
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        mTimeNameLiveData.postValue(result)
    }

    fun getCheckList(type: TriggerTime): List<CheckListBean> {
        return when (type) {
            TriggerTime.DAY_STATUS -> {
                listOf(
                    CheckListBean(CommonUtils.getString(R.string.scene_text_me_trigger_time_day_status), true),
                    CheckListBean("日落", false),
                )
            }
            else -> {
                emptyList()
            }
        }
    }

    fun getConditionList(type: TriggerTime): List<Condition> {
        val conditionList = mutableListOf<Condition>()
        when (type) {
            TriggerTime.TIME_POINT -> {
                //TimePickerDialog内部赋值
            }
            TriggerTime.DAY_STATUS -> {
                conditionList.add(
                    Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_TIME_DAY_STATUS,
                        "",
                        listOf(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"))
                    )
                )
            }
        }
        return conditionList
    }

    fun getInputArgList(type: TriggerTime): List<String>? {
        return when (type) {
            TriggerTime.DAY_STATUS -> {
                listOf("1", "4")
            }
            else -> null
        }
    }
}