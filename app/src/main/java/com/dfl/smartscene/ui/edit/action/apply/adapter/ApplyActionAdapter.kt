package com.dfl.smartscene.ui.edit.action.apply.adapter

import android.view.ViewGroup
import android.widget.TextView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.ApplicationActionType
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/09/21
 * desc:应用动作的适配器
 * version:1.0
 */
class ApplyActionAdapter : BaseMultiItemQuickAdapter<ActionSkillItemBean<ApplicationActionType>, BaseViewHolder>(),
    MatchableAdapter<ActionSkillItemBean<ApplicationActionType>> {
    init {
        addItemType(ActionSkillItemBean.SKILL_CONTENT, R.layout.scene_recycle_item_edit_action_dialog_item)
        addItemType(ActionSkillItemBean.SKILL_TITLE, R.layout.scene_recycle_item_edit_action_dialog_title)
    }

    override fun convert(holder: BaseViewHolder, item: ActionSkillItemBean<ApplicationActionType>) {
        if (item.itemType == ActionSkillItemBean.SKILL_TITLE) {
            val txtTitle = holder.getView<TextView>(R.id.tv_edit_scene_action_name_item_dialog)
            val params = txtTitle.layoutParams as ViewGroup.MarginLayoutParams
            txtTitle.text = item.content
            if (holder.layoutPosition == 0) {
                params.topMargin = AutoSizeUtils.dp2px(context, 16.0f)
            } else {
                params.topMargin = AutoSizeUtils.dp2px(context, 40.0f)
            }
            txtTitle.layoutParams = params
        } else {
            holder.setText(R.id.tv_action_reflex_name, item.content)
            holder.setImageResource(R.id.iv_action_reflex_icon, item.iconId)
        }
    }

    override val adapterData: List<ActionSkillItemBean<ApplicationActionType>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.content.replace("\n", "") == title && !it.actionSkillId.isNullOrEmpty() }
    }
}