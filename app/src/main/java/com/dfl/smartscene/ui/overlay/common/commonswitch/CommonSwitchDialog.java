package com.dfl.smartscene.ui.overlay.common.commonswitch;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckedTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.databinding.SceneDialogCommonSwitchBinding;
import com.iauto.scenarioadapter.InputArgInfo;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/08
 * desc :针对动作条件项只有开关的能力  二项选择,ui2.0后已被废弃,使用RadioListDialogFragment
 * version: 1.0
 * update: 钟文祥 (2个选项) （通用）
 */
@Deprecated
public class CommonSwitchDialog<T> extends BaseDialog implements View.OnClickListener {
    private SceneDialogCommonSwitchBinding mVB;
    private String mTitle;//标题
    private Context mContext;
    private TextView mTitleDescTv;
    private String mTitleDesc;
    private ScenarioInfo.Condition preSetCondition = null;
    private ScenarioInfo.Sequence preSetAction = null;

    private TextView mTitleTv;
    private CheckedTextView mOpenCtv;
    private ImageView mOpenImv;
    private LinearLayout mllOpen;
    private CheckedTextView mCloseCtv;
    private ImageView mCloseImv;
    private LinearLayout mllClose;

    private List<ScenarioInfo.Condition> mConditionList;
    private List<ScenarioInfo.Sequence> mSequenceList;

    private String mOpenTxt;
    private String mCloseTxt;
    private CommonSwitchType commonSwitchType;
    private List<Integer> mInputArgsList;
    private String mCategory;//显示的类别
    private boolean mIsOpen = true;
    private boolean mIsOnlyOneChecked = false;//判断是否只有一个选择项的情况
    @DrawableRes
    private int mLeftTvIcon = -1;
    @DrawableRes
    private int mRightTvIcon = -1;

    public CommonSwitchDialog(@NonNull Context context,
                              String title,
                              List<T> list,
                              CommonSwitchType commonSwitchType) {
        super(context);
        this.mContext = context;
        this.mTitle = title;
        if (list != null && list.size() > 0) {
            if (list.get(0) instanceof ScenarioInfo.Condition) {
                this.mConditionList = (List<ScenarioInfo.Condition>) list;
            } else {
                this.mSequenceList = (List<ScenarioInfo.Sequence>) list;
            }
        }
        this.commonSwitchType = commonSwitchType;
    }

    public void setIsOnlyOneChecked(boolean isOnlyOneChecked) {
        this.mIsOnlyOneChecked = isOnlyOneChecked;
    }

    public void setOpenTxt(String mOpenTxt) {
        this.mOpenTxt = mOpenTxt;
    }

    public void setCloseTxt(String mCloseTxt) {
        this.mCloseTxt = mCloseTxt;
    }

    public void setDefaultIsOpen(boolean isOpen) {
        this.mIsOpen = isOpen;
    }

    public void setTitleDesc(String desc) {
        this.mTitleDesc = desc;
    }

    public void setupPreCondition(ScenarioInfo.Condition condition) {
        this.preSetCondition = condition;
    }

    public void setupPreAction(ScenarioInfo.Sequence action) {
        this.preSetAction = action;
    }

    public void setTextDrawableLeftIcon(@DrawableRes int leftIcon, @DrawableRes int rightIcon) {
        this.mLeftTvIcon = leftIcon;
        this.mRightTvIcon = rightIcon;
    }

    /**
     * 当对应能力需要设置InputArgInfo的时候需要传递对应value集合
     *
     * @param list 能力列表中提供的InputArgInfo里面value内容
     */
    public void setInputArgValueData(List<Integer> list) {
        this.mInputArgsList = list;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogCommonSwitchBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        initData();
        if (preSetCondition != null) {
            setInitData(preSetCondition);
        }

        if (preSetAction != null) {
            setInitData(preSetAction);
        }
    }

    private void setInitData(ScenarioInfo.Condition preSetCondition) {
        int index = -1;
        if (commonSwitchType == CommonSwitchType.COMMON_DEFAULT) {
            index = ObjectUtils.findPreIndex(preSetCondition, mConditionList);
        } else if (preSetCondition.getInput().size() == 1) {
            index = ObjectUtils.findPreIndexWithInput(preSetCondition, mConditionList, mInputArgsList);
        } else {
            //非空判断
            if (ObjectUtils.checkScenarioDataIsEmpty(preSetCondition)
                    || ObjectUtils.checkScenarioDataIsEmpty(mConditionList)) {
                return;
            }
            //在有两个参数的情况下，一般第一个参数是位置，第二个参数是具体值。具体值才是UI还原所需要的值。
            //需要在第一个值 位置 一致情况下才去找对应的值。比如车门条件
            if (preSetCondition.getInput().get(0).getValue().equals(mConditionList.get(0).getInput().get(0).getValue())) {
                index = ObjectUtils.findPreIndexWithMultiInput(preSetCondition, mConditionList, mInputArgsList, 1);
            }
        }
        if (index != -1) {
            checkItemWithIndex(index);
        }
    }

    private void setInitData(ScenarioInfo.Sequence action) {
        int index = -1;
        if (commonSwitchType == CommonSwitchType.COMMON_DEFAULT) {
            index = ObjectUtils.findPreIndex(action, mSequenceList);
        } else {
            index = ObjectUtils.findPreIndexWithInput(action, mSequenceList, mInputArgsList);
        }
        if (index != -1) {
            checkItemWithIndex(index);
        }
    }

    private void checkItemWithIndex(int index) {
        if (index == 0) {
            checkOpenLayout();
        } else if (index == 1) {
            checkCloseLayout();
        }
    }

    private void initView() {
        mTitleTv = findViewById(R.id.tv_common_switch_title);
        mTitleDescTv = findViewById(R.id.tv_common_switch_title_desc);
        if (ObjectUtils.isNotEmpty(mTitleDesc)) {
            mTitleDescTv.setText(mTitleDesc);
            mTitleDescTv.setVisibility(View.VISIBLE);
        }

        mOpenCtv = findViewById(R.id.ctv_open);
        mOpenImv = findViewById(R.id.imv_open);
        mllOpen = findViewById(R.id.ll_open);
        mCloseCtv = findViewById(R.id.ctv_close);
        mCloseImv = findViewById(R.id.imv_close);
        mllClose = findViewById(R.id.ll_close);


        mllOpen.setOnClickListener(this);
        mllClose.setOnClickListener(this);
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);

        mOpenCtv.setChecked(mIsOpen);
        mllOpen.setSelected(mIsOpen);
        mCloseCtv.setChecked(!mIsOpen);
        mllClose.setSelected(!mIsOpen);
        if (mIsOnlyOneChecked) {
            mCloseCtv.setVisibility(View.GONE);
        }
        if (mLeftTvIcon != -1) {
            mOpenImv.setImageResource(mLeftTvIcon);
            mOpenImv.setVisibility(View.VISIBLE);
        }
        if (mRightTvIcon != -1) {
            mCloseImv.setImageResource(mRightTvIcon);
            mCloseImv.setVisibility(View.VISIBLE);
        }
    }

    private void initData() {
        mTitleTv.setText(mTitle.isEmpty() ? "请选择" : mTitle);
        if (mOpenTxt != null && !mOpenTxt.isEmpty()) {
            mOpenCtv.setText(mOpenTxt);
        }
        if (mCloseTxt != null && !mCloseTxt.isEmpty()) {
            mCloseCtv.setText(mCloseTxt);
        }
        if (mOpenCtv.getText().toString().length() == 3) {
            mllOpen.setPadding(46, 0, 46, 0);
        }
        if (mCloseCtv.getText().toString().length() == 3) {
            mllClose.setPadding(46, 0, 46, 0);
        }
    }

    public void setCategory(String category) {
        this.mCategory = category;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
        } else if (v.getId() == R.id.ll_open) {
            checkOpenLayout();
        } else if (v.getId() == R.id.ll_close) {
            checkCloseLayout();
        } else if (v.getId() == R.id.btn_primary) {
            int index = mOpenCtv.isChecked() ? 0 : 1;
            String desc = mOpenCtv.isChecked() ? mOpenCtv.getText().toString() :
                    mCloseCtv.getText().toString();
            if (mSequenceList != null) {
                if (commonSwitchType == CommonSwitchType.COMMON_DEFAULT) {
                    ScenarioInfo.Sequence action = mSequenceList.get(index);
                    action.getAction().setDesc(desc);
                    action.getAction().setCategory(TextUtils.isEmpty(mCategory) ? mTitle : mCategory);
                    EventBus.getDefault().post(action);
                } else {
                    if (mInputArgsList != null && !mInputArgsList.isEmpty()) {
                        ScenarioInfo.Sequence action = mSequenceList.get(0);
                        action.getAction().setDesc(desc);
                        action.getAction().getInput().get(0).setValue(String.valueOf(mInputArgsList.get(index)));
                        if (action.getAction().getCategory().isEmpty())
                            action.getAction().setCategory(TextUtils.isEmpty(mCategory) ? mTitle : mCategory);
                        EventBus.getDefault().post(action);
                    }
                }
            } else {
                if (mConditionList != null) {
                    if (commonSwitchType == CommonSwitchType.COMMON_DEFAULT) {
                        ScenarioInfo.Condition condition = mConditionList.get(index);
                        condition.setCategory(TextUtils.isEmpty(mCategory) ? mTitle : mCategory);
                        condition.setDesc(desc);
                        EventBus.getDefault().post(condition);
                    } else {
                        if (mInputArgsList != null && !mInputArgsList.isEmpty()) {
                            ScenarioInfo.Condition condition = mConditionList.get(0);
                            List<InputArgInfo> infoList = condition.getInput();
                            int position = 0;
                            if (infoList.size() > 1) {
                                position = 1;
                            }
                            condition.getInput().get(position).setValue(String.valueOf(mInputArgsList.get(index)));
                            condition.setDesc(desc);
                            condition.setCategory(TextUtils.isEmpty(mCategory) ? mTitle : mCategory);
                            EventBus.getDefault().post(condition);
                        }
                    }
                }
            }
            dismissWithAnimate();
        }
    }

    private void checkCloseLayout() {
        mOpenCtv.setChecked(false);
        mOpenImv.setSelected(false);
        mllOpen.setSelected(false);

        mCloseCtv.setChecked(true);
        mCloseImv.setSelected(true);
        mllClose.setSelected(true);
    }

    private void checkOpenLayout() {
        mCloseCtv.setChecked(false);
        mCloseImv.setSelected(false);
        mllClose.setSelected(false);

        mOpenCtv.setChecked(true);
        mOpenImv.setSelected(true);
        mllOpen.setSelected(true);
    }
}
