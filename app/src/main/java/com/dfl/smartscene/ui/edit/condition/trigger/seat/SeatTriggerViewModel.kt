package com.dfl.smartscene.ui.edit.condition.trigger.seat

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerSeat
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->座椅view model
 * version: 1.0
 */
class SeatTriggerViewModel : BaseViewModel() {
    val mSeatLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerSeat>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerSeat>>()
        val type = SceneEditManager.TriggerType.SEAT
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_main_driver_seat),
                R.drawable.scene_icon_me_trigger_seat_main_seat,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_STATE),
                TriggerSeat.LEFT_SEAT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_copilot_seat),
                R.drawable.scene_icon_me_trigger_seat_copilot_seat,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_STATE),
                TriggerSeat.RIGHT_SEAT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_main_driver_seat_belt),
                R.drawable.scene_icon_me_trigger_seat_main_safe_belt,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_SAFE_BELT),
                TriggerSeat.LEFT_BELT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_copilot_seat_belt),
                R.drawable.scene_icon_me_trigger_seat_copilot_safe_belt,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_SAFE_BELT),
                TriggerSeat.RIGHT_BELT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_back_left_seat_belt),
                R.drawable.scene_icon_me_trigger_seat_left_seat_belt,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_LEFT_SAFE_BELT),
                TriggerSeat.BACK_LEFT_BELT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_back_middle_seat_belt),
                R.drawable.scene_icon_me_trigger_seat_middle_seat_belt,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_MIDDLE_SAFE_BELT),
                TriggerSeat.BACK_MIDDLE_BELT_STATE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_back_right_seat_belt),
                R.drawable.scene_icon_me_trigger_seat_right_safe_belt,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_RIGHT_SAFE_BELT),
                TriggerSeat.BACK_RIGHT_BELT_STATE
            )
        )
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        mSeatLiveData.postValue(result)
    }

    fun getTriggerConditionSeatData(type: TriggerSeat, resources: Resources):
            ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()

        var skillId = 0
        val desc = ""
        when (type) {
            TriggerSeat.LEFT_SEAT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_STATE
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_SEAT_IS_OCCUPIED,
                        ArgType.INT32,
                        "0"
                    )
                )
            }
            TriggerSeat.RIGHT_SEAT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_STATE
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TRIGGER_SEAT_IS_OCCUPIED,
                        ArgType.INT32,
                        "0"
                    )
                )
            }
            TriggerSeat.LEFT_BELT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_MAIN_DRIVER_TRIGGER_SAFE_BELT
                inputArgList.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_SEAT_BELT_STATE, ArgType.INT32, "0"))
            }
            TriggerSeat.RIGHT_BELT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_COPILOT_TRIGGER_SAFE_BELT
                inputArgList.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_SEAT_BELT_STATE, ArgType.INT32, "0"))
            }
            TriggerSeat.BACK_LEFT_BELT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_LEFT_SAFE_BELT
                inputArgList.add(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"))
            }
            TriggerSeat.BACK_MIDDLE_BELT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_MIDDLE_SAFE_BELT
                inputArgList.add(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"))
            }
            TriggerSeat.BACK_RIGHT_BELT_STATE -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_SEAT_BACK_RIGHT_SAFE_BELT
                inputArgList.add(InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"))
            }
        }
        conditionList.add(
            ScenarioInfo.Condition(
                1,
                desc,
                skillId,
                "",
                inputArgList
            )
        )

        return conditionList
    }


    fun getInputArgInfoValueData(position: TriggerSeat): ArrayList<Int> {
        val list = ArrayList<Int>()
        if (position == TriggerSeat.LEFT_SEAT_STATE || position == TriggerSeat.RIGHT_SEAT_STATE) {
            list.add(1)
            list.add(0)
        } else {
            list.add(0)
            list.add(2)
        }
        return list
    }

    fun getRadioList(resources: Resources, type: TriggerSeat): List<CheckListBean> {
        return when (type) {
            TriggerSeat.LEFT_SEAT_STATE, TriggerSeat.RIGHT_SEAT_STATE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_seat_someone), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_seat_no_one), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_safe_belt_tie_up), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_safe_belt_untie), false),
                )
            }
        }
    }
}