package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.*
import android.view.inputmethod.EditorInfo
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.communication.MediaLocalManager
import com.dfl.smartscene.databinding.SceneDialogFragmentEnterTextBinding
import com.dfl.smartscene.ui.edit.action.apply.adapter.SearchSongDataAdapter
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.entity.SongSingerInfo
import kotlinx.coroutines.*

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/09/23
 * desc: 需要应用间通讯的文本输入的对话框 ,歌手歌曲 搜索框
 * version:1.0
 */
class EnterTextDialogFragment : BaseDialogFragment(), View.OnClickListener {
    private lateinit var viewBinging: SceneDialogFragmentEnterTextBinding

    var title: String? = null
    var type: Int? = SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG
    var onInputListener: OnInputListener? = null

    private var mAdapter: SearchSongDataAdapter? = null
    private var mIsSelectInputEditText = false //是否已选择列表填写进输入框

    private var mSelectItem: SongSingerInfo? = null

    /**确定按钮是否允许点击*/
    private var isAllow: Boolean = true
    private val imeOptions: Int = EditorInfo.IME_ACTION_SEARCH

    private lateinit var textWatcher: TextWatcher

    interface OnInputListener {
        fun onInput(id: String, name: String) //id:歌曲id或歌手id
        fun cancel() {}
    }

    companion object {
        const val TAG = GlobalConstant.GLOBAL_TAG + "EnterTextDialogFragment"
        const val TAG_DIALOG_ENTER = "tag_dialog_enter"
        fun getInstance(): EnterTextDialogFragment {
            return EnterTextDialogFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        viewBinging = SceneDialogFragmentEnterTextBinding.inflate(inflater, container, false)
        return viewBinging.root
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_enter_text
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        updateTvConfirm(false)
        viewBinging.tvTitle.text = title
        viewBinging.etEditInput.imeOptions = imeOptions
        viewBinging.etEditInput.hint =
            if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG) getString(R.string.scene_edit_action_input_song_name) else getString(
                R.string.scene_edit_action_input_artist_name
            )
        UIUtils.setKeyBroadInputMinIfNotEnable(viewBinging.etEditInput, 1)

        initListener()
        initRecycleView()

    }

    private fun initListener() {
        viewBinging.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinging.includeDialogBottom.btnNormal.setOnClickListener(this)
        textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            private var curTime1 = System.currentTimeMillis()
            private val antiShakeTime: Long = 400 //防抖动时间 毫秒

            @SuppressLint("NotifyDataSetChanged")
            override fun afterTextChanged(s: Editable?) {
                if (!mIsSelectInputEditText) {
                    val curTime2 = System.currentTimeMillis()

                    val job = lifecycleScope.launch(Dispatchers.IO) {
                        delay(antiShakeTime)
                        withContext(Dispatchers.Main) {
                            updateTvConfirm(false)
                            handleSearch()
                        }
                    }
                    if (curTime2 - curTime1 < antiShakeTime) {
                        job.cancel()
                    }
                    curTime1 = System.currentTimeMillis()
                } else { //点击事件
                    mIsSelectInputEditText = false
                }

            }
        }
        viewBinging.etEditInput.addTextChangedListener(textWatcher)
        viewBinging.etEditInput.setOnEditorActionListener { v, actionId, event ->
            if (actionId == imeOptions) { //键盘右下角按钮点击退出键盘
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
            true
        }

    }


    private fun initRecycleView() {
        UIUtils.initRecyclerView(context, viewBinging.rvActionSong, LinearLayoutManager(activity), false)

        mAdapter = SearchSongDataAdapter(type)
        viewBinging.rvActionSong.adapter = mAdapter
        viewBinging.rvActionSong.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        })

        mAdapter?.setOnItemClickListener { _, _, position ->
            mAdapter?.data?.let {
                viewBinging.rvActionSong.visibility = View.GONE

                mSelectItem = it[position]
                mIsSelectInputEditText = true

                val content = if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SINGER) {
                    it[position].singer_name
                } else {
                    it[position].song_name
                }

                viewBinging.etEditInput.setText(content)
                viewBinging.etEditInput.setSelection(viewBinging.etEditInput.length())

                if (content?.isNotEmpty() == true) {
                    updateTvConfirm(true)
                }
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        }
    }

    //回调 按钮事件 setOnClickListener
    @SuppressLint("NotifyDataSetChanged")
    override fun onClick(v: View?) {
        if (!DebouncingUtils.isValid(v!!)) return
        when (v.id) {
            R.id.btn_normal -> { //取消
                cancel()
            }

            R.id.btn_primary -> { //确定
                handleConfirm()
            }
        }
    }

    private fun cancel() {
        KeyboardUtils.hideSoftInput(viewBinging.etEditInput)
        onInputListener?.cancel()
        dismissWithAnimate()
    }

    /** 校验输入的数据*/
    private fun handleConfirm() {
        if (TextUtils.isEmpty(viewBinging.etEditInput.text.toString())) {
            if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SINGER) {
                CommonToastUtils.show(getString(R.string.scene_edit_action_input_artist_name))
            } else {
                CommonToastUtils.show(getString(R.string.scene_edit_action_input_song_name))
            }
            return
        }
        KeyboardUtils.hideSoftInput(viewBinging.etEditInput)
        mSelectItem?.let {
            if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SINGER) {
                onInputListener?.onInput(it.singer_id.toString(), it.singer_name ?: "")
            } else {
                onInputListener?.onInput(it.song_id.toString(), it.song_name ?: "")
            }
        }
        dismissWithAnimate()
    }

    private fun updateTvConfirm(isAllow1: Boolean) {
        if (this.isAllow != isAllow1) {

            this.isAllow = isAllow1
            viewBinging.includeDialogBottom.btnPrimary.isEnabled = isAllow
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun handleSearch() {
        val key = viewBinging.etEditInput.text.toString()
        if (key.isNotEmpty()) {
            mAdapter?.setKeyWord(key)
            lifecycleScope.launch(Dispatchers.IO) {
                val data = async {
                    if (type == SkillsListConstant.INPUT_ARG_ACTION_APP_TYPE_SONG) {
                        MediaLocalManager.searchSong(key)
                    } else {
                        MediaLocalManager.searchSinger(key)
                    }
                }.await()
                withContext(Dispatchers.Main) {
                    if (data == null) {
                        CommonToastUtils.show(R.string.scene_toast_common_get_data_error)
                        return@withContext
                    }
                    if (viewBinging.etEditInput.text.toString().isEmpty() || data.size == 0) return@withContext

                    data.let {
                        mAdapter?.setList(it)
                        viewBinging.rvActionSong.visibility = View.VISIBLE
                    }
                }
            }
        } else {
            viewBinging.rvActionSong.visibility = View.GONE
            mAdapter?.data?.clear()
            mAdapter?.notifyDataSetChanged()
        }
    }

    override fun onTouchOutside() {
        super.onTouchOutside()
        cancel()
    }
}