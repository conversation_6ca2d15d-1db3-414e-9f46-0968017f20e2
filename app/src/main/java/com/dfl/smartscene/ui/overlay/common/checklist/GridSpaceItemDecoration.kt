package com.dfl.smartscene.ui.overlay.common.checklist

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/12/20
 * desc :RecycleView Grid平均分
 * version: 1.0
 */
class GridSpaceItemDecoration(
    private val spaceCount: Int,
    private val rowSpace: Float,
    private val columnSpacing: Float
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view) // 获取view 在adapter中的位置。

        val column = position % spaceCount // view 所在的列


        outRect.left = (column * columnSpacing / spaceCount).toInt() // column * (列间距 * (1f / 列数))

        outRect.right =
            (columnSpacing - (column + 1) * columnSpacing / spaceCount).toInt() // 列间距 - (column + 1) * (列间距 * (1f /列数))

        // 如果position > 行数，说明不是在第一行，则不指定行高，其他行的上间距为 top=rowSpace
        if (position >= spaceCount) {
            outRect.top = rowSpace.toInt() // item top
        }
    }
}