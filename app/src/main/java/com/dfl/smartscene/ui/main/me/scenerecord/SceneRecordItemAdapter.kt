package com.dfl.smartscene.ui.main.me.scenerecord

import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RoundRectShape
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneRecordBean
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.smartscene.databinding.SceneRecycleItemMeSceneRecordBinding
import com.dfl.smartscene.databinding.SceneRecycleItemMeSceneRecordDateBinding
import com.dfl.smartscene.room.SceneRecordTable
import me.jessyan.autosize.utils.AutoSizeUtils
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// 在文件顶部添加导入
import androidx.core.content.ContextCompat
import com.dfl.android.common.util.StringUtils.getString

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   : 多布局场景记录适配器，支持日期头部和执行记录项
 * version: 1.0
 */
class SceneRecordItemAdapter : BaseMultiItemQuickAdapter<SceneRecordBean, BaseDataBindingHolder<*>>() {
    
    companion object {
        const val TYPE_DATE = 1  // 日期类型
        const val TYPE_RECORD = 2  // 记录类型
    }
    
    init {
        // 添加多布局支持
        addItemType(TYPE_DATE, R.layout.scene_recycle_item_me_scene_record_date)
        addItemType(TYPE_RECORD, R.layout.scene_recycle_item_me_scene_record)
    }
    
    /**
     * 转换数据，根据不同类型显示不同布局
     */
    override fun convert(holder: BaseDataBindingHolder<*>, item: SceneRecordBean) {
        when (holder.itemViewType) {
            TYPE_DATE -> convertDateItem(holder as BaseDataBindingHolder<SceneRecycleItemMeSceneRecordDateBinding>, item)
            TYPE_RECORD -> convertRecordItem(holder as BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>, item)
        }
    }
    
    /**
     * 处理日期类型的item
     */
    private fun convertDateItem(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordDateBinding>,
        item: SceneRecordBean
    ) {
        holder.dataBinding?.bean = item
        holder.dataBinding?.executePendingBindings()
    }
    
    /**
     * 处理记录类型的item（一行两列）
     */
    private fun convertRecordItem(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>,
        item: SceneRecordBean
    ) {
        holder.dataBinding?.bean = item
        
        // 处理第一个记录
        setupRecordView(
            holder.dataBinding?.flRecordFirst,
            holder.dataBinding?.ivIconFirst,
            holder.dataBinding?.tvSceneNameFirst,
            holder.dataBinding?.tvTimeFirst,
            holder.dataBinding?.tvStatusFirst,
            item.record,
            item.title
        )
        
        // 处理第二个记录
        if (item.secondRecord != null) {
            holder.dataBinding?.flRecordSecond?.visibility = View.VISIBLE
            setupRecordView(
                holder.dataBinding?.flRecordSecond,
                holder.dataBinding?.ivIconSecond,
                holder.dataBinding?.tvSceneNameSecond,
                holder.dataBinding?.tvTimeSecond,
                holder.dataBinding?.tvStatusSecond,
                item.secondRecord!!,
                "" // 第二个记录的title可以从record中获取
            )
        } else {
            // 如果没有第二个记录，隐藏第二个位置
            holder.dataBinding?.flRecordSecond?.visibility = View.INVISIBLE
        }
        
        holder.dataBinding?.executePendingBindings()
    }
    
    /**
     * 设置单个记录视图
     */
    private fun setupRecordView(
        frameLayout: FrameLayout?,
        iconView: ImageView?,
        nameView: TextView?,
        timeView: TextView?,
        statusView: TextView?, // 新增状态文本视图参数
        record: SceneRecordEntity,
        title: String
    ) {
        frameLayout?.setBackgroundResource(R.drawable.scene_shape_common_bg_day_bg2_night_medal_component)
        
        // 根据执行结果设置图标和状态文本
        when (record.startSceneResult) {
            SceneRecordTable.VALUE_START_SCENE_RESULT_SUCCESS -> {
                iconView?.setImageResource(R.drawable.scene_icon_me_log_run_ok)
                statusView?.text = getString(R.string.scene_text_execution_succeed)
            }
            SceneRecordTable.VALUE_START_SCENE_RESULT_FAIL -> {
                iconView?.setImageResource(R.drawable.scene_icon_me_log_run_fail)
                statusView?.text = getString(R.string.scene_text_execution_fail)
            }
        }
        
        // 设置场景名称
        nameView?.text = record.sceneName
        
        // 设置时间
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        timeView?.text = timeFormat.format(Date(record.finishSceneTime))
        
        // 设置圆角
        val floatRadius = AutoSizeUtils.dp2px(context, 12F).toFloat()
        setRadius(frameLayout, floatRadius, floatRadius, floatRadius, floatRadius)
    }
    
    /**
     * 设置圆角背景
     */
    private fun setRadius(
        frameLayout: FrameLayout?,
        floatUpperLeft: Float,
        floatUpperRight: Float,
        floatLowerLeft: Float,
        floatLowerRight: Float,
    ) {
        frameLayout?.let { fl ->
            val radii = floatArrayOf(
                floatUpperLeft, floatUpperLeft,
                floatUpperRight, floatUpperRight,
                floatLowerLeft, floatLowerLeft,
                floatLowerRight, floatLowerRight
            )
            val shape = RoundRectShape(radii, null, null)
            val shapeDrawable = ShapeDrawable(shape)
            shapeDrawable.paint.color = context.getColor(R.color.scene_color_day_bg2_night_medal_component)
            fl.background = shapeDrawable
        }
    }
}
