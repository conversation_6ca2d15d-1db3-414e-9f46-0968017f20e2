package com.dfl.smartscene.ui.main.me.scenerecord

import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RoundRectShape
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneRecordBean
import com.dfl.smartscene.databinding.SceneRecycleItemMeSceneRecordBinding
import com.dfl.smartscene.room.SceneRecordTable
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class SceneRecordItemAdapter : BaseQuickAdapter<SceneRecordBean,
        BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>>
    (R.layout.scene_recycle_item_me_scene_record) {
    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>,
        item: SceneRecordBean
    ) {
        holder.dataBinding?.bean = item
        holder.dataBinding?.executePendingBindings()
        holder.dataBinding?.flRecordOfOneDay?.setBackgroundResource(R.drawable.scene_shape_common_bg_day_bg2_night_medal_component)
        when (item.record.startSceneResult) {
            SceneRecordTable.VALUE_START_SCENE_RESULT_SUCCESS -> holder.dataBinding?.ivIcon?.setImageResource(
                R.drawable.scene_icon_me_log_run_ok
            )
            SceneRecordTable.VALUE_START_SCENE_RESULT_FAIL -> holder.dataBinding?.ivIcon?.setImageResource(
                R.drawable.scene_icon_me_log_run_fail
            )
        }
        if (holder.absoluteAdapterPosition == 0) {
            holder.dataBinding?.tvDateRecordItem?.visibility = View.VISIBLE
        } else if (holder.absoluteAdapterPosition <= data.size - 1) {
            if (item.title == data[holder.absoluteAdapterPosition - 1].title) {
                holder.dataBinding?.tvDateRecordItem?.visibility = View.GONE
            } else {
                val lp = holder.dataBinding?.tvDateRecordItem?.layoutParams as ViewGroup.MarginLayoutParams
                lp?.topMargin = AutoSizeUtils.dp2px(context, 60F)
                holder.dataBinding?.tvDateRecordItem?.visibility = View.VISIBLE
            }
        }
        val floatRadius = AutoSizeUtils.dp2px(context, 24F).toFloat()
        //每个日期最后一项没有分割线
        //每个日期最后一项设置左下和右下有圆角
        //每个日期第一项设置左上和右上有圆角
        if (checkIsRelativeFirstItem(holder, item) && checkIsRelativeLastItem(holder, item)) {
            holder.dataBinding?.llLine?.visibility = View.INVISIBLE
            setRadius(holder, item, floatRadius, floatRadius, floatRadius, floatRadius)
        } else if (checkIsRelativeFirstItem(holder, item)) {
            val layoutParams = holder?.dataBinding?.tvDateRecordItem?.layoutParams as ViewGroup.MarginLayoutParams
            if (item.title == data[0].title) {
                layoutParams.topMargin = AutoSizeUtils.dp2px(context, 16F)
            } else {
                layoutParams.topMargin = AutoSizeUtils.dp2px(context, 60F)
            }
            setRadius(holder, item, floatRadius, floatRadius, 0f, 0f)
        } else if (checkIsRelativeLastItem(holder, item)) {
            holder.dataBinding?.llLine?.visibility = View.INVISIBLE
            setRadius(holder, item, 0F, 0F, floatRadius, floatRadius)
        } else {
            holder.dataBinding?.llLine?.visibility = View.VISIBLE
        }
    }

    private fun checkIsRelativeFirstItem(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>,
        item: SceneRecordBean
    ): Boolean {
        return holder.absoluteAdapterPosition == 0 || item.title != data[holder.absoluteAdapterPosition - 1].title
    }

    private fun checkIsRelativeLastItem(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>,
        item: SceneRecordBean
    ): Boolean {
        return holder.absoluteAdapterPosition == data.size - 1 || item.title != data[holder.absoluteAdapterPosition + 1].title
    }

    private fun setRadius(
        holder: BaseDataBindingHolder<SceneRecycleItemMeSceneRecordBinding>,
        item: SceneRecordBean,
        floatUpperLeft: Float,
        floatUpperRight: Float,
        floatLowerLeft: Float,
        floatLowerRight: Float,
    ) {
        val frameLayoutRecord: FrameLayout = holder.dataBinding?.flRecordOfOneDay as FrameLayout
        //定义形状的圆角
        val radii = floatArrayOf(
            floatUpperLeft, floatUpperLeft, // 左上角的圆角半径
            floatUpperRight, floatUpperRight, // 右上角的圆角半径
            floatLowerLeft, floatLowerLeft, // 左下角的圆角半径
            floatLowerRight, floatLowerRight // 右下角的圆角半径
        )
        // 创建一个RoundRectShape
        val shape = RoundRectShape(radii, null, null)
        // 创建一个ShapeDrawable
        val shapeDrawable = ShapeDrawable(shape)
        shapeDrawable.paint.color = context.getColor(R.color.scene_color_day_bg2_night_medal_component)
        //shapeDrawable.paint.pathEffect = CornerPathEffect(floatUpperLeft)
        frameLayoutRecord.background = shapeDrawable
    }
}
