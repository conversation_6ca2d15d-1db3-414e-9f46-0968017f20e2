package com.dfl.smartscene.ui.main.discover.detail.adapter

import android.graphics.Rect
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.StringUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.iauto.scenarioadapter.ArgType

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :场景详情页面动作适配器
 * version: 1.0
 */
class SceneDetailActionAdapter :
    BaseQuickAdapter<SceneActionBean, BaseViewHolder>(R.layout.scene_recycle_item_detail_action) {

    override fun convert(holder: BaseViewHolder, item: SceneActionBean) {
        val view = holder.getView<View>(R.id.v_ambient_light_bg)
        view.visibility = View.GONE
        if (item.action?.action?.skillId == SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID //
                || item.action?.action?.skillId == SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE
        ) {
            //如果动作ID为氛围灯ID并且input的长度为3即为氛围灯颜色，这里需要判断究竟是主题颜色还是自选颜色
            if (item.action.action.desc.contains(context.getString(R.string.scene_text_action_optional)) && item.action.action.input[0].type == ArgType.INT32) {
                //防止错误数据导致闪退
                if (StringUtils.isNumeric(item.action.action.input[0].value, true, true)) {
                    view.visibility = View.VISIBLE
                    val color = item.action.action.input[0].value.toInt()
                    view.setBackgroundColor(color)
                }
            }
        }

        holder.getView<TextView>(R.id.tv_detail_action_item_serial_no).text =
            (holder.absoluteAdapterPosition.plus(1)).toString()
        holder.getView<TextView>(R.id.tv_detail_action_item_name).text = item.action?.action?.category
        holder.getView<TextView>(R.id.tv_detail_action_item_desc).text = item.action?.action?.desc

        val ivItemBg = holder.getView<AppCompatImageView>(R.id.iv_detail_action_item)
        ivItemBg.setImageResource(item.itemBg)

        if (!item.isActive) {
            holder.itemView.alpha = 0.5f
            holder.getView<TextView>(R.id.iv_condition_action_no_active).visibility = View.GONE
        } else {
            holder.itemView.alpha = 1.0f
            holder.getView<TextView>(R.id.iv_condition_action_no_active).visibility = View.GONE
        }

    }

    class MyGridItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            outRect.bottom = 24
            outRect.left = 0
            outRect.right = 24
        }
    }
}