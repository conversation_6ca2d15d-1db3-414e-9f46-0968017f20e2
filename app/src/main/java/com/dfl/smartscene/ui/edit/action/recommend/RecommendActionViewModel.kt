package com.dfl.smartscene.ui.edit.action.recommend

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.RecommendType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->推荐view model
 * version: 1.0
 */
class RecommendActionViewModel : BaseViewModel() {
    var mRecommendLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<RecommendType>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<RecommendType>>()
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_delay),
                R.drawable.scene_icon_me_action_delay,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.DELAY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_air_conditioner_switch),
                R.drawable.scene_icon_me_action_air_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.AIR_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                R.drawable.scene_icon_me_action_air_temperature,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.AIR_TEMP,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_air_conditioner_temperature),
                R.drawable.scene_icon_me_action_air_temperature,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.AIR_TEMP_TWO,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT,
                    SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT
                )
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_common_action_air_conditioner_air_volume),
                R.drawable.scene_icon_me_action_air_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.AIR_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_initiate_navigation),
                R.drawable.scene_icon_me_action_navigation_initiate_navigation,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.START_NAVI,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_NAV_HOME_AND_COMPANY,
                    SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE,
                )
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_open_app),
                R.drawable.scene_icon_me_action_media_open_media,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.OPEN_MEDIA,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA,
                )
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_close_app),
                R.drawable.scene_icon_me_action_media_close_media,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.CLOSE_MEDIA,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_xiao_ni_reply),
                R.drawable.scene_icon_me_action_apps_xiaoni_reply,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.VPA_REPLY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_VPA_REPLY)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_text_short_prompt),
                R.drawable.scene_icon_me_action_msg_text_short_prompt,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.SHORT_TEXT_TIP,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_APP_NOTIFY)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_all_windows),
                R.drawable.scene_icon_me_action_door_window_all,
                ActionSkillItemBean.SKILL_CONTENT,
                RecommendType.ALL_WINDOW,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN)
            )
        )
        //		if (isHighConfig) {
//        list.add(
//            ActionSkillItemBean(
//                resources.getString(R.string.scene_text_action_ambient_light_switch),
//                R.drawable.scene_icon_me_action_light_atmosphere_switch,
//                ActionSkillItemBean.SKILL_CONTENT,
//                RecommendType.AMBIENT_LAMP_SWITCH,
//                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID)
//            )
//        )
        //		}
        val type = SceneEditManager.ActionType.RECOMMEND
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mRecommendLiveData.postValue(result)
    }

    fun getActionData(type: RecommendType, resources: Resources): ArrayList<ScenarioInfo.Sequence> {
        val skillId: Int
        val args = ArrayList<InputArgInfo>()
        val list = ArrayList<ScenarioInfo.Sequence>()
        when (type) {
            RecommendType.DELAY -> {
                //TimePickerDialog里面设置
            }

            RecommendType.AIR_SWITCH -> {
                skillId = SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_OPEN
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_SWITCH_NAME,
                        ArgType.INT32,
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_DEFAULT_VALUE
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", skillId, "", args
                        )
                    )
                )
            }

            RecommendType.AIR_TEMP -> { //空调温度
                //单区
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_NAME,
                        ArgType.DOUBLE,
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_DEFAULT_VALUE
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE, "", args
                        )
                    )
                )
            }

            RecommendType.AIR_TEMP_TWO -> {
                //双区
                //主驾驶
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_NAME,
                        ArgType.DOUBLE,
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_DEFAULT_VALUE
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_LEFT, "", args
                        )
                    )
                )
                //副驾驶
                val arg2 = ArrayList<InputArgInfo>()
                arg2.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_NAME,
                        ArgType.DOUBLE,
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_TEMPERATURE_DEFAULT_VALUE
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_TEMPERATURE_TWO_RIGHT, "", arg2
                        )
                    )
                )
            }

            RecommendType.AIR_VOLUME -> {
                skillId = SkillsListConstant.SKILL_ID_ACTION_AIR_CONDITIONER_AIR_VOLUME
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_AIR_VOLUME_NAME,
                        ArgType.INT32,
                        SkillsListConstant.INPUT_ARG_ACTION_AIR_CONDITIONER_AIR_VOLUME_DEFAULT_VALUE
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_air_volume_gear_setting), skillId, "", args
                        )
                    )
                )
            }

            RecommendType.START_NAVI -> {
                //发起导航不再这个方法里面配置,在NavigationActionSelectDialogFragment里面已经配好
            }

            RecommendType.OPEN_MEDIA -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_APP_OPEN_MEDIA, ArgType.INT32, "1"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_OPEN_MEDIA, "", args
                        )
                    )
                )
            }

            RecommendType.CLOSE_MEDIA -> { //关闭多媒体
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_MEDIA_CLOSE, "", args
                        )
                    )
                )
            }

            RecommendType.VPA_REPLY, RecommendType.SHORT_TEXT_TIP -> {
                //VpaReplyDialogFragment里面设置
            }

            RecommendType.ALL_WINDOW -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN, "", args
                        )
                    )
                )
            }

            RecommendType.AMBIENT_LAMP_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID, "", args
                        )
                    )
                )
            }
        }
        return list
    }

    fun getInputArgIntValue(type: RecommendType): ArrayList<Int> {
        return when (type) {
            RecommendType.AIR_SWITCH -> {
                arrayListOf(0, 1)
            }

            RecommendType.AMBIENT_LAMP_SWITCH -> {
                arrayListOf(1, 0)
            }

            RecommendType.ALL_WINDOW -> {
                arrayListOf(0, 0xa)
            }

            RecommendType.OPEN_MEDIA -> {
                arrayListOf(1, 2, 3)
            }

            else -> {
                arrayListOf()
            }
        }
    }

    /**
     * 初始化空调温度的范围值
     */
    fun initAirTemperatureWheelData(): ArrayList<String> {
        val list = ArrayList<String>()
        var index = 16.0
        while (index <= 32) {
            if ((index.toInt() - index) == 0.0) {
                list.add(index.toInt().toString())
            } else {
                list.add(index.toString())
            }
            index += 0.5
        }
        return list
    }

    /**
     * 初始化空调温度inputArgs的值
     */
    fun getAirTempInputArgInfoList(): ArrayList<Double> {
        val list = ArrayList<Double>()
        var index = 16.0
        while (index <= 32) {
            if ((index.toInt() - index) == 0.0) {
                list.add(index)
            } else {
                list.add(index)
            }
            index += 0.5
        }
        return list
    }

    /**
     * 双区空调对话框radio值
     */
    fun getRadioWheelDialogRbMap(): MutableMap<String, String> {
        return mutableMapOf<String, String>(
            Pair(CommonUtils.getString(R.string.scene_text_oms_main_driver), "0"),
            Pair(CommonUtils.getString(R.string.scene_text_oms_copilot), "1"),
        )
    }

    /**
     * 双区空调对话框wheel值
     */
    fun getRadioWheelDialogWheelMap(): MutableMap<String, String> {
        val map: MutableMap<String, String> = mutableMapOf()
        var index = 16.0
        while (index <= 32) {
            if ((index.toInt() - index) == 0.0) {
                map[index.toInt().toString()] = index.toInt().toString()
            } else {
                map[index.toString()] = index.toString()
            }
            index += 0.5
        }
        return map
    }

    fun getListData(resources: Resources, type: RecommendType, title: String): List<CheckListBean> {
        return when (type) {
            RecommendType.CLOSE_MEDIA -> {
                arrayListOf(
                    CheckListBean(title, true)
                )
            }

            RecommendType.ALL_WINDOW -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_open), false),
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_close), true)
                )
            }

            RecommendType.OPEN_MEDIA -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_qq), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_bluetooth_music), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_local_music), false),
                )
            }

            RecommendType.AIR_SWITCH, RecommendType.AMBIENT_LAMP_SWITCH -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }

            else -> {
                arrayListOf()
            }
        }
    }

    fun initAirConditionerVolumeWheelData(): ArrayList<String> {
        val list = ArrayList<String>()
        var index = 1
        while (index <= 7) {
            list.add(index.toString())
            index += 1
        }
        return list
    }

    fun getAirConditionerVolumeArgInfoList(): ArrayList<Int> {
        val list = ArrayList<Int>()
        var index = 1
        while (index <= 7) {
            list.add(index)
            index += 1
        }
        return list
    }
}