package com.dfl.smartscene.ui.overlay.common.checklist

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.databinding.SceneRecycleItemCheckListBinding

/**
 *Created by 钟文祥 on 2023/12/21.
 *Describer:
 */
class CheckListAdapter :
    BaseQuickAdapter<CheckListBean, BaseDataBindingHolder<SceneRecycleItemCheckListBinding>>(
        R.layout.scene_recycle_item_check_list
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycleItemCheckListBinding>,
        bean: CheckListBean
    ) {
        holder.dataBinding?.bean = bean
    }
}