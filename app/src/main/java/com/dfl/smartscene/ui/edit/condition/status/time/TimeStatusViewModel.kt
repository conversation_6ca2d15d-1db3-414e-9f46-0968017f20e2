package com.dfl.smartscene.ui.edit.condition.status.time

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusTime
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   :
 * version: 1.0
 */
class TimeStatusViewModel : BaseViewModel() {
    val mTimeNameLiveData = MutableLiveData<ArrayList<ConditionItemBean<StatusTime>>>()

    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusTime>>()
        val type = SceneEditManager.StatusType.TIME
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_common_condition_time_interval),
                R.drawable.scene_icon_me_status_time_interval,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_TIME_INTERVAL_ID),
                StatusTime.TIME_INTERVAL
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_cycle),
                R.drawable.scene_icon_me_status_time_cycle,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID,
                    SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID,
                    SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID,
                    SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID
                ),
                StatusTime.TIME_CYCLE
            )
        )
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        mTimeNameLiveData.postValue(result)
    }

}