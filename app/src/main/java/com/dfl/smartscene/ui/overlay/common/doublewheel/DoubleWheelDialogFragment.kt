package com.dfl.smartscene.ui.overlay.common.doublewheel

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import com.dfl.android.common.base.BaseVBDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogDoubleWheelBinding
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.ScenarioInfo
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus

/**
 * Created by 钟文祥 on 2025/1/7.
 * Describer:双滚轮选择弹窗
 */
class DoubleWheelDialogFragment<T>(
    private var mCategory: String = "",
    /** 左滚轮的key滚轮文本，value滚轮选中值  */
    private val mLeftMap: Map<String, String>,
    /** 右滚轮的key滚轮文本，value滚轮选中值  */
    private val mRightMap: Map<String, String>,
    list: List<T>,
    private val mType: DoubleWheelType,
) : BaseVBDialogFragment<SceneDialogDoubleWheelBinding>(), View.OnClickListener {
    private var mSequenceList: List<ScenarioInfo.Sequence>? = null
    private var mConditionList: List<ScenarioInfo.Condition>? = null
    var titleDesc: String? = null
    private var mLeftWheelDefaultPosition = 0 //滚轮默认位置，以集合下标对应为准
    private var mRightWheelDefaultPosition = 0 //滚轮默认位置，以集合下标对应为准
    private var mLeftUnit = "" //单位
    private var mRightUnit = "" //单位
    private var mIsLeftLoop = true
    private var mIsRightLoop = true //是否循环
    private var mPreSetCondition: ScenarioInfo.Condition? = null
    private var mPreSequence: ScenarioInfo.Sequence? = null
    private var mListener: OnSceneActionListener? = null
    private var mLeftDesc: String = "" //关系描述
    private var mRightDesc: String = "" //关系描述
    private var mDialogTitle: String = "" //弹框标题，不一定是类别

    /**用于监听确认按钮的dismiss事件,实现关闭父弹框*/
    var onPrimaryBtnListener: DialogInterface.OnDismissListener? = null

    init {
        if (!ObjectUtils.checkScenarioDataIsEmpty(list)) {
            if (list[0] is ScenarioInfo.Condition) {
                @Suppress("UNCHECKED_CAST")
                mConditionList = list as List<ScenarioInfo.Condition>?
            } else if (list[0] is ScenarioInfo.Sequence) {
                @Suppress("UNCHECKED_CAST")
                mSequenceList = list as List<ScenarioInfo.Sequence>?
            } else {
                throw Error("错误的数据类型")
            }
        }
        //根据ux大于2个选项才循环
        mIsLeftLoop = mLeftMap.size > 2
        mIsRightLoop = mRightMap.size > 2
    }

    fun setUnit(unitLeft: String, unitRight: String) {
        mLeftUnit = unitLeft
        mRightUnit = unitRight
    }

    fun setWheelDefaultPosition(leftPosition: Int, rightPosition: Int) {
        mLeftWheelDefaultPosition = leftPosition
        mRightWheelDefaultPosition = rightPosition
    }

    override fun initView(savedInstanceState: Bundle?) {
        if (mDialogTitle.isEmpty()) {
            mVB.tvTitle.text = mCategory
        } else {
            mVB.tvTitle.text = mDialogTitle
        }
        if (ObjectUtils.isNotEmpty(titleDesc)) {
            mVB.tvDesc.text = titleDesc
            mVB.tvDesc.visibility = View.VISIBLE
            mVB.tvTitle.setPadding(0, 0, 0, AutoSizeUtils.dp2px(context, 28f))
        }
        UIUtils.initWheelViewP42R(context, mVB.wheelLeft, mLeftMap.keys.toList(), mLeftUnit, mLeftWheelDefaultPosition, null,null)
        mVB.wheelLeft.setCyclic(mIsLeftLoop)
        mVB.wheelLeft.prefix = mLeftDesc
        UIUtils.initWheelViewP42R(
            context,
            mVB.wheelRight,
            mRightMap.keys.toList(),
            mRightUnit,
            mRightWheelDefaultPosition,
            null,
            null
        )
        mVB.wheelRight.setCyclic(mIsRightLoop)
        mVB.wheelRight.prefix = mRightDesc
        initClick()
        initPreData()
        //setLeftUpInitData()
        //setRightUpInitData()
        setCanceledOnTouchOutside(true)
    }

    private fun initPreData() {
        when (mType) {
            DoubleWheelType.SINGLE_SKILL -> {
                updateViewByPreSetForOneSkill(0, 1)
            }
            DoubleWheelType.DOUBLE_SKILL -> {
                updateViewByPreSetForTwoSkill()
            }
            DoubleWheelType.SINGLE_SKILL_LEFT_1 -> {
                updateViewByPreSetForOneSkill(1, 0)
            }
        }
    }

    private fun initClick() {
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
    }

    /**
     * 针对 动作或条件列表是复数,子项InputArgList只有一个
     * 根据之前值在动作或条件列表选中radio,故要注意radio文本位置要对应列表中位置
     * 根据之前值InputArg.value对应mWheelMap值列表位置选中wheel
     * 例如 主驾 副驾 空调温度
     */
    private fun updateViewByPreSetForTwoSkill() {
        var indexRadio = -1
        var indexWheel = -1
        //选中之前的值
        mPreSequence?.let {
            indexRadio = ObjectUtils.findPreIndex(it, mSequenceList)
            //不符合当前对话框的Sequence直接返回防止indexWheel越界
            if (indexRadio != -1) {
                indexWheel = mRightMap.values.toList().indexOf(it.action.input[0].value)
            }
        }
        mPreSetCondition?.let {
            indexRadio = ObjectUtils.findPreIndex(it, mConditionList)
            if (indexRadio != -1) {
                indexWheel = mRightMap.values.toList().indexOf(it.input[0].value)
            }
        }
        //indexWheel不为-1,indexRadio肯定不为-1
        if (indexWheel != -1) {
            mVB.wheelLeft.currentItem = indexRadio
            mVB.wheelRight.currentItem = indexWheel
        }
    }

    /**
     * 针对 动作或条件列表值只有一个,其InputArg列表复数
     * 根据 mRbMap,mWheelMap的值列表比对之前数据值
     * 没有之前数据使用传入动作或条件InputArg定位默认值
     * 例如 打开 关闭 场景灯效
     * @param radioInputIndex radio值在InputArg列表索引
     * @param wheelInputIndex wheel值在InputArg列表索引
     */
    private fun updateViewByPreSetForOneSkill(
        radioInputIndex: Int, wheelInputIndex: Int
    ) {
        var indexRadio = -1
        var indexWheel = -1
        mPreSequence?.let {
            indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                it, mSequenceList, mLeftMap.values.toList(), radioInputIndex
            )
            indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                it, mSequenceList, mRightMap.values.toList(), wheelInputIndex
            )
        }
        mPreSetCondition?.let {
            indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                it, mConditionList, mLeftMap.values.toList(), radioInputIndex
            )
            indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                it, mConditionList, mRightMap.values.toList(), wheelInputIndex
            )
        }
        //indexWheel不为-1,indexRadio肯定不为-1
        if (indexRadio != -1 || indexWheel != -1) {
            mVB.wheelLeft.currentItem = indexRadio
            mVB.wheelRight.currentItem = indexWheel
        }
    }

    private fun setLeftUpInitData() {
        var indexLeft = -1
        //如果是动作
        if (mPreSequence != null) {
            indexLeft = ObjectUtils.findPreIndexWithInput(mPreSequence, mSequenceList, mLeftMap.values.toList())
        } else if (mPreSetCondition != null) {
            //如果选项值对应 mDataList
            indexLeft = ObjectUtils.findPreIndexWithInput(mPreSetCondition, mConditionList, mLeftMap.values.toList())
        }
        if (indexLeft != -1) {
            mVB.wheelLeft.currentItem = indexLeft
        }
        mVB.wheelLeft.setCyclic(mIsLeftLoop)
    }

    private fun setRightUpInitData() {
        var indexRight = -1
        //如果是动作
        if (mPreSequence != null) {
            indexRight = ObjectUtils.findPreIndexWithInput(mPreSequence, mSequenceList, mRightMap.values.toList())
        } else if (mPreSetCondition != null) {
            //如果选项值对应 mDataList
            indexRight = ObjectUtils.findPreIndexWithMultiInput(
                mPreSetCondition,
                mConditionList,
                mRightMap.values.toList(),
                1
            )
        }
        if (indexRight != -1) {
            mVB.wheelRight.currentItem = indexRight
        }
        mVB.wheelRight.setCyclic(mIsRightLoop)
    }

    fun setupPreCondition(preSetCondition: ScenarioInfo.Condition?) {
        this.mPreSetCondition = preSetCondition
    }

    fun setupPreAction(sequence: ScenarioInfo.Sequence?) {
        mPreSequence = sequence
    }

    fun setOnSceneActionListener(listener: OnSceneActionListener?) {
        this.mListener = listener
    }

    override fun onClick(v: View) {
        if (v.id == R.id.btn_normal) {
            if (mListener != null) mListener!!.cancel()
            dismissWithAnimate()
        } else if (v.id == R.id.btn_primary) {
            if (!DebouncingUtils.isValid(v)) return
            val desc = if (mLeftDesc.isEmpty() && mRightDesc.isEmpty()) {
                mLeftMap.keys.toList()[mVB.wheelLeft.currentItem] + mLeftUnit + " " + mRightMap.keys.toList()[mVB.wheelRight.currentItem] + mRightUnit
            } else {
                mLeftDesc + " " + mLeftMap.keys.toList()[mVB.wheelLeft.currentItem] + mLeftUnit + " " + mRightDesc + " " + mRightMap.keys.toList()[mVB.wheelRight.currentItem] + mRightUnit
            }

            /**
             * 对SINGLE_SKILL_LEFT_1特殊处理，保持和ccm参数顺序一致
             */
            val reverseDesc = if (mLeftDesc.isEmpty() && mRightDesc.isEmpty()) {
                mRightMap.keys.toList()[mVB.wheelRight.currentItem] + mRightUnit + " " + mLeftMap.keys.toList()[mVB.wheelLeft.currentItem] + mLeftUnit
            } else {
                mRightDesc + " " + mRightMap.keys.toList()[mVB.wheelRight.currentItem] + mRightUnit + " " + mLeftDesc + " " + mLeftMap.keys.toList()[mVB.wheelLeft.currentItem] + mLeftUnit
            }
            val category = mCategory
            when (mType) {
                DoubleWheelType.SINGLE_SKILL -> {
                    mSequenceList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || it[0].action.input.size < 2) return
                        val action = it[0]
                        action.action.input[0].value = mLeftMap.values.toList()[mVB.wheelLeft.currentItem]
                        action.action.input[1].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        action.action.desc = desc
                        action.action.category = category
                        if (mListener != null) {
                            mListener!!.selectAction(action)
                        } else {
                            EventBus.getDefault().post(action)
                        }
                    }
                    mConditionList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || it[0].input.size < 2) return
                        val condition = it[0]
                        condition.input[0].value = mLeftMap.values.toList()[mVB.wheelLeft.currentItem]
                        condition.input[1].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        condition.category = category
                        condition.desc = desc
                        EventBus.getDefault().post(condition)
                    }
                }
                DoubleWheelType.SINGLE_SKILL_LEFT_1 -> {
                    mSequenceList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || it[0].action.input.size < 2) return
                        val action = it[0]
                        action.action.input[1].value = mLeftMap.values.toList()[mVB.wheelLeft.currentItem]
                        action.action.input[0].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        action.action.desc = reverseDesc
                        action.action.category = category
                        if (mListener != null) {
                            mListener!!.selectAction(action)
                        } else {
                            EventBus.getDefault().post(action)
                        }
                    }
                    mConditionList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || it[0].input.size < 2) return
                        val condition = it[0]
                        condition.input[1].value = mLeftMap.values.toList()[mVB.wheelLeft.currentItem]
                        condition.input[0].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        condition.category = category
                        condition.desc = reverseDesc
                        EventBus.getDefault().post(condition)
                    }
                }
                DoubleWheelType.DOUBLE_SKILL -> {
                    mSequenceList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || mLeftMap.size > it.size) return
                        val postSequence = it[mVB.wheelLeft.currentItem]
                        postSequence.action.desc = desc
                        postSequence.action.category = category
                        postSequence.action.input[0].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        EventBus.getDefault().post(postSequence)
                    }
                    mConditionList?.let {
                        if (ObjectUtils.checkScenarioDataIsEmpty(it) || mLeftMap.size > it.size) return
                        val postCondition: ScenarioInfo.Condition = it[mVB.wheelLeft.currentItem]
                        postCondition.desc = desc
                        postCondition.category = category
                        postCondition.input[0].value = mRightMap.values.toList()[mVB.wheelRight.currentItem]
                        EventBus.getDefault().post(postCondition)
                    }
                }
            }
            dismissWithAnimate()
            onPrimaryBtnListener?.onDismiss(dialog)
        }
    }

    interface OnSceneActionListener {
        fun selectAction(sequence: ScenarioInfo.Sequence?)
        fun cancel()
    }

    fun setRightWheelLoop(right: Boolean) {
        mIsRightLoop = right
    }

    fun setPrefixText(left: String, right: String) {
        mLeftDesc = left
        mRightDesc = right
    }

    fun setDialogTitle(title: String) {
        mDialogTitle = title
    }
}