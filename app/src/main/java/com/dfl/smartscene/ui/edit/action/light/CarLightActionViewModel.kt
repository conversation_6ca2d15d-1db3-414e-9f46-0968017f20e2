package com.dfl.smartscene.ui.edit.action.light

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CarLightType
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->车灯view model
 * version: 1.0
 */
class CarLightActionViewModel : BaseViewModel() {

    val mCarLightLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<CarLightType>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<CarLightType>>()
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_car_outside_lamp),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                null,
                null,
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_lamp_language_mode),
                R.drawable.scene_icon_me_action_light_language_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_lamp_soa_language_mode),
                R.drawable.scene_icon_me_action_light_soa_language_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_SOA),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_lamp_scene_lighting_effect),
                R.drawable.scene_icon_me_action_light_lamp_effect,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )

        //v1.1 删除 尾灯自定义灯语
        //		list.add(
        //			ActionSkillItemBean(
        //				resources.getString(R.string.scene_text_action_lamp_tail_diy_language),
        //				R.drawable.scene_icon_fog_lamp,
        //				ActionSkillItemBean.SKILL_CONTENT,
        //				CarLightType.CAR_OUTSIDE_LAMP_LIGHT_DIY,
        //				arrayListOf(SkillsListConstant.SKILL_ID_LIGHT_DIY),
        //				CarLightType.CAR_OUTSIDE_LAMP_TYPE
        //			)
        //		)

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_fog_lights),
                R.drawable.scene_icon_me_action_light_fog_lamp,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_FOG_LAMP_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_star_light),
                R.drawable.scene_icon_me_action_light_sceneslight,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_STAR_LIGHT_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_light_music_effect),
                R.drawable.scene_icon_me_action_light_headlight_music_rhythm,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_EXTERIOR_LIGHT_MUSIC_EFFECT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_light_control),
                R.drawable.scene_icon_me_action_light_headlight_control,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_EXTERIOR_LIGHT_MODE_CONTROL,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_light_height),
                R.drawable.scene_icon_me_action_light_headlight_height,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_EXTERIOR_LIGHT_HEIGHT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT),
                CarLightType.CAR_OUTSIDE_LAMP_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_action_atmosphere),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                null, //CarLightType.CAR_AMBIENT_LAMP_TITLE,
                null,
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ambient_light_switch),
                R.drawable.scene_icon_me_action_light_atmosphere_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_AMBIENT_LAMP_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ambient_light_mode),
                R.drawable.scene_icon_me_action_light_atmosphere_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_AMBIENT_LAMP_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ambient_light_brightness),
                R.drawable.scene_icon_me_action_light_atmosphere_light,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_AMBIENT_LAMP_LIGHT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ambient_light_color),
                R.drawable.scene_icon_me_action_light_atmosphere_color,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_AMBIENT_LAMP_COLOR,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID,
                    SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID
                ),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_atmosphere_light_soa),
                R.drawable.scene_icon_me_action_light_atmosphere_soa_language_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_SOA),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_scene_lighting),
                R.drawable.scene_icon_me_action_light_atmosphere_lamp_effect,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_SCENE_LAMP_EFFECT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID),
                CarLightType.CAR_AMBIENT_LAMP_TYPE
            )
        )

        list.add(
            ActionSkillItemBean.initTitle(
                resources.getString(R.string.scene_text_action_reading_light), CarLightType.CAR_INSIDE_LAMP_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_reading_light_switch),
                R.drawable.scene_icon_me_action_light_read_lamp_switch,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_READ_LAMP_SWITCH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH),
                CarLightType.CAR_INSIDE_LAMP_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_reading_light_mode),
                R.drawable.scene_icon_me_action_light_read_lamp_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                CarLightType.CAR_READ_LAMP_ADJUST,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE,
                    SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM
                ),
                CarLightType.CAR_INSIDE_LAMP_TYPE
            )
        )

        val type = SceneEditManager.ActionType.LAMP
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mCarLightLiveData.postValue(result)
    }

    fun initAtmosphereLampBrightnessWheelData(): ArrayList<String> {
        val list = ArrayList<String>()
        var index = 1
        while (index <= 8) {
            list.add(index.toString())
            index += 1
        }
        return list
    }

    fun getAtmosphereLampBrightnessArgInfoList(): ArrayList<Int> {
        val list = ArrayList<Int>()
        var index = 0
        while (index <= 7) {
            list.add(index.toInt())
            index += 1
        }
        return list
    }

    fun getLeftInputArgInfoData(type: CarLightType): ArrayList<Int> {
        return when (type) {
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA, //大灯灯光秀
                //氛围灯功能联动
            CarLightType.CAR_SCENE_LAMP_EFFECT -> {
                arrayListOf(1, 0)
            }
            //大灯功能联动
            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> {
                arrayListOf(1, 2)
            }

            else -> {
                arrayListOf()
            }
        }
    }

    fun getRightInputArgInfoData(type: CarLightType): ArrayList<Int> {
        return when (type) {
            //大灯灯光秀
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA -> {
                arrayListOf(4, 5, 2, 1, 3)
            }
            //氛围灯功能联动
            CarLightType.CAR_SCENE_LAMP_EFFECT -> {
                arrayListOf(3, 4, 5, 6)
            }
            //大灯功能联动
            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> {
                arrayListOf(3, 4, 5, 6)
            }

            else -> {
                arrayListOf(5, 1, 2, 3, 4)
            }
        }
    }

    //阅读灯亮度和色温调节
    fun initLeftWheelMap(): Map<String, String> {
        val map = mutableMapOf<String, String>()
        var index = 1
        while (index <= 100) {
            map[index.toString()] = index.toString()
            index += 1
        }
        return map
    }

    fun initRightWheelMap(): Map<String, String> {
        val map = mutableMapOf<String, String>()
        var index = 1
        while (index <= 100) {
            map[index.toString()] = index.toString()
            index += 1
        }
        return map
    }

    //开关选择器的 键值
    fun getRbMap(resources: Resources, type: CarLightType): MutableMap<String, String> {
        return when (type) {
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA, //大灯灯光秀
            CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA -> {
                //氛围灯SOA灯语
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_action_play), "1"),
                    Pair(resources.getString(R.string.scene_text_action_stop), "0"),
                )
            }

            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> { //大灯功能联动
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_common_open), "1"),
                    Pair(resources.getString(R.string.scene_text_common_close), "2"),
                )
            }

            CarLightType.CAR_SCENE_LAMP_EFFECT -> { //氛围灯功能联动
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_common_open), "1"),
                    Pair(resources.getString(R.string.scene_text_common_close), "0"),
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MUSIC_EFFECT -> {
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_common_open), "1"),
                    Pair(resources.getString(R.string.scene_text_common_close), "2"),
                )
            }

            else -> {
                mutableMapOf<String, String>()
            }
        }

    }

    //tWheel的键值
    fun getWheelMap(resources: Resources, type: CarLightType): MutableMap<String, String> {
        return when (type) {
            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> { //大灯场景灯效（文言改为大灯功能联动）
                val map = mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_action_light_short_park), "6"),
                    Pair(resources.getString(R.string.scene_text_action_light_long_park), "5"),
                    Pair(resources.getString(R.string.scene_text_action_light_unlock), "1"),
                    Pair(resources.getString(R.string.scene_text_action_light_lock), "2"),
                    Pair(resources.getString(R.string.scene_text_action_light_quick_charge), "3"),
                    Pair(resources.getString(R.string.scene_text_action_light_slow_charge), "4"),
                    Pair(resources.getString(R.string.scene_text_action_light_auto_trunk), "7"),
                )
                if (ConfigManager.isLK2A()) {
                    map.remove(resources.getString(R.string.scene_text_action_light_short_park))
                    map.remove(resources.getString(R.string.scene_text_action_light_auto_trunk))
                }
                map
            }

            CarLightType.CAR_SCENE_LAMP_EFFECT -> { //氛围灯场景灯效（文言改为氛围灯功能联动）
                mutableMapOf<String, String>(
                    //TODO：ota之后上-Pair("语音唤醒", "1"),
                    //Pair("KTV人声律动", "2"),
                    Pair(resources.getString(R.string.scene_text_action_ambient_light_drive_mode), "3"),
                    Pair(resources.getString(R.string.scene_text_action_ambient_light_air_temperature), "4"),
                    Pair(resources.getString(R.string.scene_text_action_ambient_light_door_open), "5"),
                    Pair(resources.getString(R.string.scene_text_action_ambient_light_start), "6")
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA -> { //氛围灯SOA灯语
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_action_light_theme_birthday), "3"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_nissan), "4"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_romance), "5"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_yuandan), "2"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_spring), "1"),
                )
            }

            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA -> { //大灯soa灯语（文言改为大灯灯光秀）
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_action_light_theme_birthday), "3"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_nissan), "4"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_romance), "5"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_yuandan), "2"),
                    Pair(resources.getString(R.string.scene_text_action_light_theme_spring), "1")
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MUSIC_EFFECT -> {
                mutableMapOf<String, String>(
                    Pair(resources.getString(R.string.scene_text_action_light_music_effect_drum), "1"),
                    Pair(resources.getString(R.string.scene_text_action_light_music_effect_spectrum), "2"),
                )
            }

            else -> {
                mutableMapOf<String, String>()
            }
        }
    }


    fun getActionData(type: CarLightType): ArrayList<ScenarioInfo.Sequence> {
        val list = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_DIY -> { //尾灯自定义灯语
                //这一个能力id 有 两个键值对
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_DIY_LAMPEFFECT, ArgType.INT32, "27"
                    ) //enum: 自定义1 : 27 , 自定义2 : 28
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //没意义
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_DIY, "", args
                        )
                    )
                )

            }

            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> { //大灯场景灯效
                //这一个能力id 有 两个键值对
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_SCENE_EFFECT_SCENEID, ArgType.INT32, "6"
                    ) //enum: 迎宾&解锁 : 1, 上锁 : 2,  快速充电 : 3, 慢速充电: 4, 停车等待 : 5,   短停提醒 : 8, 接近告警 : 10
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_SCENE_EFFECT_SCENEEFFECTSETSTS, ArgType.INT32, "1"
                    ) //enum: 打开 : 1 , 关闭 : 2
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //没意义
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_SCENE_LAMP_EFFECT -> { //氛围灯功能联动
                //这一个能力id 有 两个键值对
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    ) //enum:语音唤醒 : 1,KTV人声联动 : 2, 驾驶模式切换 : 3,空调升降温 : 4,  盲区预警 : 5 ， 上电启动：6
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    ) //enum:关闭 : 0,打开 : 1
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //没意义
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA -> { //大灯灯光秀
                //这一个能力id 有 一个键值对
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "4"
                    ) //enum: 春节 : 1, 元旦 : 2,  生日快乐 : 3,  Nissan主题: 4 , 烂漫：5
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    ) //enum: 打开 : 1 , 关闭 : 0
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //没意义
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_SOA, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA -> { //氛围灯SOA灯语
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_TYPE, ArgType.INT32, "4"
                    ) //enum: 春节 : 1, 元旦 : 2,  生日快乐 : 3,  Nissan主题: 4， 浪漫主题：5
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    ) //enum: 打开 : 1 , 关闭 : 0
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //必须为1
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_SOA, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE -> { //大灯打招呼灯语
                //默认
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_LAMP_LANGUAGE_MODE_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_LAMP_LANGUAGE_MODE_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_FOG_LAMP_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_FOG_LIGHT_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_STAR_LIGHT_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_STAR_LIGHT_STATUS, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_READ_LAMP_SWITCH -> { //室内灯开关
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_READ_LIGHT_MODE_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_SWITCH, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_READ_LAMP_ADJUST -> { //室内灯（阅读灯调制）
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_READ_LIGHT_THEME_NAME, ArgType.INT32, "1"
                    ) //温馨 : 1 , 冷白 : 2, 标准 : 3, 自然 : 4
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_MODE, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_READ_LAMP_ADJUST_CUSTOM -> {
                val arg = ArrayList<InputArgInfo>()
                arg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_BRIGHTNESS_NAME, ArgType.INT32, "0"
                    )
                )
                arg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_TEMPERATURE, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM, "", arg
                        )
                    )
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_SWITCH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_SWITCH_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_MODE -> { //氛围灯模式
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_MODE_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_MODE_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_LIGHT -> { //氛围灯亮度
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_BRIGHTNESS_NAME, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_LIGHT_ID, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_COLOR -> { //氛围灯颜色
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_THEME_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID, "", args
                        )
                    )
                )
                val arg = ArrayList<InputArgInfo>()
                arg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_NAME, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID, "", arg
                        )
                    )
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MUSIC_EFFECT -> { //设置大灯音乐律动
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_EXTERIOR_LIGHT_MUSIC_EFFECT_STATUS, ArgType.UINT8, "0"
                    ) //打开 : 0 , 关闭 : 1
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_EXTERIOR_LIGHT_MUSIC_EFFECT_EFFECT, ArgType.UINT8, "0"
                    ) //鼓点 : 1 , 频谱 : 2
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1,  //没意义 默认1开始
                        1,  //必须为1
                        ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MUSIC_EFFECT, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MODE_CONTROL -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_EXTERIOR_LIGHT_MODE_CONTROL, ArgType.UINT8, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_MODE_CONTROL, "", args
                        )
                    )
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_HEIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_EXTERIOR_LIGHT_MODE_HEIGHT, ArgType.UINT8, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_EXTERIOR_LIGHT_HEIGHT, "", args
                        )
                    )
                )
            }

            else -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_MODE, ArgType.INT32, "1"
                    )
                )
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_LIGHT_SCENE_EFFECT_ID, "", args
                        )
                    )
                )
            }
        }
        return list
    }

    /**
     * RadioListDialog里的数据
     */

    fun getCheckInputArgValueData(position: CarLightType): ArrayList<Int> {
        return when (position) {
            CarLightType.CAR_FOG_LAMP_SWITCH, CarLightType.CAR_STAR_LIGHT_SWITCH -> {
                arrayListOf(1, 0)
            }

            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_DIY -> { //尾灯自定义灯语
                arrayListOf(27, 28)
            }

            CarLightType.CAR_READ_LAMP_SWITCH -> {  //室内灯开关
                arrayListOf(0, 1) // 全关 : 0 ,全开 : 1
            }

            CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE -> { //大灯打招呼灯语
                arrayListOf(1, 2, 3, 4, 5)
            }

            CarLightType.CAR_AMBIENT_LAMP_MODE -> { //氛围灯模式
                arrayListOf(1, 2, 4, 3)
            }

            CarLightType.CAR_READ_LAMP_ADJUST -> { //室内灯主题
                arrayListOf(1, 2, 3, 4)
            }

            CarLightType.CAR_AMBIENT_LAMP_COLOR -> { //氛围灯颜色
                arrayListOf(1, 2, 3, 4, 5, 6)
            }

            CarLightType.CAR_EXTERIOR_LIGHT_HEIGHT -> { //大灯高度 低 : 4,中 : 3,标准 : 2,高 : 1
                arrayListOf(4, 3, 2, 1)
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MODE_CONTROL -> { //大灯控制 关闭 : 0,自动 : 1,位置灯 : 2,近光灯 : 3
                arrayListOf(0, 2, 3, 1)
            }

            else -> {
                arrayListOf(1, 0)
            }
        }
    }

    //单个Wheel时的所有显示值
    fun getSingleWheelNameList(resources: Resources, type: CarLightType): ArrayList<String> {
        return when (type) {
            CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE -> {
                arrayListOf(
                    resources.getString(R.string.scene_text_nissan),
                    resources.getString(R.string.scene_text_hi),
                    resources.getString(R.string.scene_text_dingding),
                    resources.getString(R.string.scene_text_miaomiao),
                    resources.getString(R.string.scene_text_bowl),
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_COLOR -> {
                arrayListOf(
                    resources.getString(R.string.scene_text_action_warm_sun),
                    resources.getString(R.string.scene_text_action_sea_breeze),
                    resources.getString(R.string.scene_text_action_starlight),
                    resources.getString(R.string.scene_text_action_forest),
                    resources.getString(R.string.scene_text_action_day),
                    resources.getString(R.string.scene_text_action_evening_breeze),
                    resources.getString(R.string.scene_text_action_optional)
                )
            }

            else -> {
                arrayListOf()
            }
        }
    }

    fun initLeftWheelData(resources: Resources, type: CarLightType): ArrayList<String> {
        val list = ArrayList<String>()
        when (type) {
            //大灯灯光秀
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA -> {
                list.add(resources.getString(R.string.scene_text_action_play))
                list.add(resources.getString(R.string.scene_text_action_stop))
            }

            else -> {
                list.add(resources.getString(R.string.scene_text_common_open))
                list.add(resources.getString(R.string.scene_text_common_close))
            }
        }

        return list
    }

    fun initRightWheelData(resources: Resources, type: CarLightType): ArrayList<String> {
        val list = ArrayList<String>()
        when (type) {
            //大灯灯光秀
            CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA -> {
                list.add(resources.getString(R.string.scene_text_action_light_theme_nissan))
                list.add(resources.getString(R.string.scene_text_action_light_theme_romance))
                list.add(resources.getString(R.string.scene_text_action_light_theme_yuandan))
                list.add(resources.getString(R.string.scene_text_action_light_theme_spring))
                list.add(resources.getString(R.string.scene_text_action_light_theme_birthday))
            }
            // 氛围灯功能联动
            CarLightType.CAR_SCENE_LAMP_EFFECT -> {
                list.add(resources.getString(R.string.scene_text_action_ambient_light_drive_mode))
                list.add(resources.getString(R.string.scene_text_action_ambient_light_air_temperature))
                list.add(resources.getString(R.string.scene_text_action_ambient_light_door_open))
                list.add(resources.getString(R.string.scene_text_action_ambient_light_start))
            }
            //大灯功能联动
            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT -> {
                list.add(resources.getString(R.string.scene_text_action_light_long_park))
                list.add(resources.getString(R.string.scene_text_action_light_unlock))
                list.add(resources.getString(R.string.scene_text_action_light_lock))
                list.add(resources.getString(R.string.scene_text_action_light_quick_charge))
                list.add(resources.getString(R.string.scene_text_action_light_slow_charge))
            }

            else -> {}
        }
        return list
    }

    fun getListData(resources: Resources, type: CarLightType): List<CheckListBean> {
        return when (type) {
            CarLightType.CAR_AMBIENT_LAMP_MODE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_always_bright), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_breathing), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_sound_wave), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_musical_rhythm), false)
                )
            }

            CarLightType.CAR_READ_LAMP_ADJUST -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_warm), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_coolwhite), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_standard), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_nature), false),
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_optional),
                        isCheck = false,
                        isItemShowMoreIcon = true
                    )
                )
            }

            CarLightType.CAR_READ_LAMP_SWITCH -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_close), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_open), false)
                )
            }

            CarLightType.CAR_AMBIENT_LAMP_COLOR -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_warm_sun), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_sea_breeze), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_starlight), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_forest), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_day), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_evening_breeze), false),
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_optional),
                        isCheck = false,
                        isItemShowMoreIcon = true
                    )
                )
            }

            CarLightType.CAR_HEAD_LIGHT_LAMP_LANGUAGE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_nissan), true),
                    CheckListBean(resources.getString(R.string.scene_text_hi), false),
                    CheckListBean(resources.getString(R.string.scene_text_dingding), false),
                    CheckListBean(resources.getString(R.string.scene_text_miaomiao), false),
                    CheckListBean(resources.getString(R.string.scene_text_bowl), false),
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_MODE_CONTROL -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_light_position), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_add_near_light), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_auto), true)
                )
            }

            CarLightType.CAR_EXTERIOR_LIGHT_HEIGHT -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_low), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), false),
                    CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_standard), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_high), false)
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

}