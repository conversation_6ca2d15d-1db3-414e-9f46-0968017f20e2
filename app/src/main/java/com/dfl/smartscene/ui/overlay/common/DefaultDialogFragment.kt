package com.dfl.smartscene.ui.overlay.common

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.smartscene.R
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneDialogFragmentDefaultBinding
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/24
 * desc   :
 * version: 1.0
 */
class DefaultDialogFragment() : BaseDialogFragment(), View.OnClickListener {
    private var onDialogButtonClickListener: OnDialogButtonClickListener? = null
    private lateinit var mViewDataBinding: SceneDialogFragmentDefaultBinding
    private var content = ""
    private var subContent = ""
    private var title = ""
    private var leftButtonText: String? = null
    private var rightButtonText: String? = null
    var action: String? = null
    private var isCloseVisible = false

    /**左边按钮样式*/
    private var showLeftBtnWarningStyle = false

    constructor(action: String) : this() {
        this.action = action
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        mViewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false)
        return mViewDataBinding.root
    }

    override fun initView(view: View) {
        mViewDataBinding.tvContent.text = content
        mViewDataBinding.ivClose.visibility = if (isCloseVisible) View.VISIBLE else View.GONE
        mViewDataBinding.tvSubContent.text = subContent
        //底部按钮文言
        if (!leftButtonText.isNullOrEmpty()) {
            mViewDataBinding.includeDialogBottom.btnPrimary.text = leftButtonText
        }
        if (!rightButtonText.isNullOrEmpty()) {
            mViewDataBinding.includeDialogBottom.btnNormal.text = rightButtonText
        }
        //如果没有标题,修改内容顶部间距
        if (title.isNotEmpty()) {
            mViewDataBinding.tvTitle.text = title
        } else {
            mViewDataBinding.tvTitle.isVisible = false
            val lp = mViewDataBinding.tvContent.layoutParams as MarginLayoutParams
            lp.topMargin = AutoSizeUtils.dp2px(context, 72f)
            mViewDataBinding.tvContent.layoutParams = lp
        }
        /**左侧按钮样式*/
        mViewDataBinding.includeDialogBottom.btnPrimary.isVisible = !showLeftBtnWarningStyle
        mViewDataBinding.includeDialogBottom.btnWarning.isVisible = showLeftBtnWarningStyle

        mViewDataBinding.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mViewDataBinding.includeDialogBottom.btnNormal.setOnClickListener(this)
        mViewDataBinding.includeDialogBottom.btnWarning.setOnClickListener(this)
        mViewDataBinding.ivClose.setOnClickListener(this)
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_default
    }

    override fun initData() {
        setTitle(title)
        setContent(content)
        setSubContent(subContent)
        setButtonText(leftButtonText, rightButtonText)
        setCloseVisible(isCloseVisible)
    }

    override fun onTouchOutside() {
        onDialogButtonClickListener?.onTouchOutside(this)
        super.onTouchOutside()

    }

    fun setTitle(str: String) {
        title = str
    }

    fun setContent(str: String) {
        content = str
    }

    fun setCloseVisible(isCloseVisible: Boolean) {
        this.isCloseVisible = isCloseVisible
    }

    fun setSubContent(str: String) {
        subContent = str
    }

    fun setButtonText(left: String?, right: String?) {
        if (left != null) {
            leftButtonText = left
        }
        if (right != null) {
            rightButtonText = right
        }
    }

    companion object {
        private const val FRAGMENT_TAG_DEFAULT_DIALOG = "FRAGMENT_TAG_DEFAULT_DIALOG"

        fun showDialog(
            manager: FragmentManager,
            action: String = "",
            title: String = "",
            content: String = "",
            leftButtonText: String? = null,
            rightButtonText: String? = null,
            onDialogButtonClickListener: OnDialogButtonClickListener? = null,
            isCloseVisible: Boolean = false, //左上角的叉是否显示
            cancelTouchOutside: Boolean = true,
            isSoundToast: Boolean = false,
            showLeftBtnWarningStyle: Boolean = false
        ): DefaultDialogFragment {
            val dialog = DefaultDialogFragment(action)
            dialog.setTitle(title)
            dialog.setContent(content)
            dialog.setButtonText(leftButtonText, rightButtonText)
            dialog.setCloseVisible(isCloseVisible)
            dialog.setCanceledOnTouchOutside(cancelTouchOutside)
            dialog.show(manager, FRAGMENT_TAG_DEFAULT_DIALOG)
            dialog.onDialogButtonClickListener = onDialogButtonClickListener
            if (isSoundToast) {
                SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
            }
            dialog.showLeftBtnWarningStyle = showLeftBtnWarningStyle
            return dialog
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_primary, R.id.btn_warning -> {
                notifyClickCallback(true)
            }

            R.id.btn_normal -> {
                notifyClickCallback(false)
            }

            R.id.iv_close -> {
                dismissWithAnimate()
            }
        }
    }

    private fun notifyClickCallback(isConfirm: Boolean) {
        if (isConfirm) {
            onDialogButtonClickListener?.onLeftButtonClick(this)
        } else {
            onDialogButtonClickListener?.onRightButtonClick(this)
        }
    }

    interface OnDialogButtonClickListener {
        /**
         * 左边的按钮,有主色颜色
         */
        fun onLeftButtonClick(dialog: DefaultDialogFragment)

        /**
         * 右边的按钮,没有主色颜色
         */
        fun onRightButtonClick(dialog: DefaultDialogFragment)


        fun onTouchOutside(dialog: DefaultDialogFragment) {

        }
    }
}