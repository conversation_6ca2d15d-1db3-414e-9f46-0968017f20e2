package com.dfl.smartscene.ui.main.me.scenerecord

import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.bean.main.SceneRecordBean
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.soa.DataManager
import com.dfl.smartscene.soa.SceneRecordManager
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.Date

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class SceneRecordViewModel : BaseViewModel() {
    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneRecordModel")
    }

    private val mSceneRecordLock = Mutex()
    fun initData(page: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            DbManager.db { db ->
                val dao = db.SceneRecordDao()
                return@db dao.queryAllData(10, (page - 1) * 10)
            }.flowOn(Dispatchers.IO).map { list ->
                addRecordData(list as ArrayList<SceneRecordEntity>)
                CommonLogUtils.logD(GlobalConstant.GLOBAL_TAG.plus("SceneRecordViewModel"), "addRecord")
                return@map list
            }.catch { cause ->
                CommonLogUtils.logW(DataManager.TAG, cause.message)
            }.flowOn(Dispatchers.IO).collect {}
        }
    }


    fun cleanUpSceneRecordData() {
        CoroutineScope(Dispatchers.IO).launch {
            mSceneRecordLock.withLock {
                DbManager.db { db ->
                    val dao = db.SceneRecordDao()
                    dao.deleteAllData()
                }.flowOn(Dispatchers.IO).catch { e ->
                    CommonLogUtils.logW(TAG, e.message)
                }.onCompletion {

                }.flowOn(Dispatchers.Main).collect {
                    LiveEventBus.get<ArrayList<SceneRecordBean>>(GlobalLiveEventConstants.KEY_SCENE_RUN_RECORD)
                        .post(ArrayList())
                }
            }
        }
    }

    private fun addRecordData(recordList: ArrayList<SceneRecordEntity>) {
        val list = ArrayList<SceneRecordBean>()
        recordList.sortByDescending {
            it.finishSceneTime
        }
        recordList.forEach {
            val title = SceneRecordManager.dateStampFormat.format(Date(it.finishSceneTime))
            list.add(SceneRecordBean(title, it))
        }
        LiveEventBus.get<ArrayList<SceneRecordBean>>(GlobalLiveEventConstants.KEY_SCENE_RUN_RECORD).post(list)
    }
}