package com.dfl.smartscene.ui.main.discover.detail.adapter

import android.graphics.Rect
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.util.UIUtils

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :场景详情页面条件适配器
 * version: 1.0
 */
class SceneDetailConditionAdapter : BaseMultiItemQuickAdapter<SceneConditionBean, BaseViewHolder>() {
    init {
        addItemType(SceneConditionBean.ITEM_TITLE, R.layout.scene_recycle_item_edit_condition_title)
        addItemType(SceneConditionBean.ITEM_CONTENT, R.layout.scene_recycle_item_detail_conditions)
    }

    override fun convert(holder: BaseViewHolder, item: SceneConditionBean) =
        if (item.itemType == SceneConditionBean.ITEM_TITLE) {
            holder.getView<TextView>(R.id.tv_edit_scene_condition_name_item).text = item.title
            if (item.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                holder.getView<AppCompatImageView>(R.id.iv_condition_item_drawable).visibility = View.VISIBLE
            } else {
                holder.getView<AppCompatImageView>(R.id.iv_condition_item_drawable).visibility = View.GONE
            }
        } else {
            val conditionIcon = holder.getView<AppCompatImageView>(R.id.iv_detail_condition_item_icon)

            holder.getView<TextView>(R.id.tv_detail_condition_item_name).text =
                UIUtils.getConditionHtmlStr(context, item.condition?.category, item.condition?.desc)
            holder.getView<AppCompatImageView>(R.id.iv_detail_condition_item_icon).visibility = View.VISIBLE
            val signView = holder.getView<View>(R.id.v_trigger_condition_sign_detail_item)
            if (item.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                signView.visibility = View.VISIBLE
            } else {
                signView.visibility = View.GONE
            }
            conditionIcon.setImageResource(item.conditionIcon)

            if (!item.isActive) {
                holder.itemView.alpha = 0.5f
                holder.getView<TextView>(R.id.iv_condition_action_no_active).visibility = View.GONE
            } else {
                holder.itemView.alpha = 1.0f
                holder.getView<TextView>(R.id.iv_condition_action_no_active).visibility = View.GONE
            }
        }

    fun getLayoutManager(): GridLayoutManager.SpanSizeLookup {
        return object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                //返回类型为标题类型时候占2格，返回条件类型占1格
                return if (getDefItemViewType(position) == SceneConditionBean.ITEM_TITLE) 3
                else 1
            }
        }
    }

    class MyGridItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            outRect.bottom = 24
            outRect.left = 0
            outRect.right = 32
        }
    }
}