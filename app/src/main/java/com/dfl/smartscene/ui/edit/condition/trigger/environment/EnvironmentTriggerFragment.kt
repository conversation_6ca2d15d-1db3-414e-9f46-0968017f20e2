package com.dfl.smartscene.ui.edit.condition.trigger.environment

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerEnvironment
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->环境
 * version: 1.0
 */
class EnvironmentTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<EnvironmentTriggerAdapter, SceneFragmentEditConditionBinding, EnvironmentTriggerViewModel>() {
    /**
     * 条件->环境 二级目录适配器
     */
    private var mAdapter: EnvironmentTriggerAdapter? = null
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("EnvironmentTriggerFragment")
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<EnvironmentTriggerViewModel> {
        return EnvironmentTriggerViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = EnvironmentTriggerAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                when (it[position].conditionType) {
                    TriggerEnvironment.CAR_INNER_TEMP, TriggerEnvironment.CAR_OUTSIDE_TEMP -> {
                        showTemperatureDialog(
                            it[position].conditionType!!, it[position].conditionName
                        )

                    }

                    null -> {
                        CommonLogUtils.logE(TAG, "传入的Condition参数conditionType为空!")
                    }
                }
            }
        }
    }
    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.environmentLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): EnvironmentTriggerAdapter? {
        return mAdapter
    }


    private fun showTemperatureDialog(position: TriggerEnvironment, title: String?) {
        title?.let {
            val dialog = DoubleWheelDialogFragment(
                title,
                mViewModel.getRbMap(),
                mViewModel.getWheelMap(),
                mViewModel.getTemperature(position == TriggerEnvironment.CAR_INNER_TEMP, resources),
                DoubleWheelType.DOUBLE_SKILL
            )
            dialog.setupPreCondition(preCondition)
            dialog.setUnit("", getString(R.string.scene_text_common_temperature_unit))
            dialog.setRightWheelLoop(false)
            dialog.setWheelDefaultPosition(0, 70)
            dialog.show(parentFragmentManager, "showTemperatureDialog")
        }
    }

}