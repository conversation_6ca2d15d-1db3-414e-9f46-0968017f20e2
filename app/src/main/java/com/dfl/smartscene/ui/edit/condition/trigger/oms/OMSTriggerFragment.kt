package com.dfl.smartscene.ui.edit.condition.trigger.oms

import android.annotation.SuppressLint
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerOMS
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2024/12/31.
 *Describer:
 */
class OMSTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<OMSTriggerAdapter, SceneFragmentEditConditionBinding, OMSTriggerViewModel>() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("OMSTriggerFragment")
    private var mAdapter: OMSTriggerAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<OMSTriggerViewModel> {
        return OMSTriggerViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.liveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): OMSTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = OMSTriggerAdapter()
        mAdapter?.setOnItemClickListener(object : OnItemClickListener {
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                handleItemClick(view, position)
            }
        })
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.isEnable == false) {
            return
        }
        if (!DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) return

        mAdapter?.data?.get(position)?.let {
            showOMSDialog(it.conditionType as TriggerOMS, it.conditionName)
        }
    }

    private fun showOMSDialog(type: TriggerOMS, title: String) {
        val dialog = DoubleWheelDialogFragment<ScenarioInfo.Condition>(
            title,
            mViewModel.initLeftWheelMap(resources, type),
            mViewModel.initRightWheelMap(resources, type),
            mViewModel.getConditionData(type),
            DoubleWheelType.SINGLE_SKILL
        )
        dialog.setupPreCondition(preCondition)
        dialog.setWheelDefaultPosition(0, 0)
        dialog.show(parentFragmentManager, "showOMSDialog")
    }

}