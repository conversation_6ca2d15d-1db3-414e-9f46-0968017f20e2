package com.dfl.smartscene.ui.edit.condition.trigger.charge

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.TriggerCharger
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/4/17.
 *Describer: 充放电
 */
class ChargeTriggerFragment(private val preCondition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<ChargeTriggerAdapter, SceneFragmentEditConditionBinding, ChargeTriggerViewModel>() {

    private var mAdapter: ChargeTriggerAdapter? = null
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.chargeWindowsLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources)
    }

    override fun getAdapter(): ChargeTriggerAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        mAdapter = ChargeTriggerAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            run {
                handleItemClick(v, position)
            }
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].conditionType == null) return@let
                showRadioListDialog(it[position].conditionName, it[position].conditionType!!)

            }
        }
    }

    override fun getViewModelClass(): Class<ChargeTriggerViewModel> {
        return ChargeTriggerViewModel::class.java
    }

    private fun showRadioListDialog(title: String, type: TriggerCharger) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getTriggerConditionData(type)
        )
        dialog.inputArgValueList = mViewModel.getInputArg(type)
        dialog.preSetCondition = preCondition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}