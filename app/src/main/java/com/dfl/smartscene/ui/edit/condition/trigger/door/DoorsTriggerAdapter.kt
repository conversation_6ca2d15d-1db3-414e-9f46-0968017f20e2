package com.dfl.smartscene.ui.edit.condition.trigger.door

import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerDoor
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author:zhushiwei
 * e-mail:<EMAIL>
 * time: 2022/09/13
 * desc: 车窗选框的适配器
 * version:1.0
 */
class DoorsTriggerAdapter : BaseMultiItemQuickAdapter<ConditionItemBean<TriggerDoor>, BaseViewHolder>(),
                            MatchableAdapter<ConditionItemBean<TriggerDoor>> {
    init {
        addItemType(ConditionItemBean.SKILL_TITLE, R.layout.scene_recycle_item_edit_action_dialog_title)
        addItemType(ConditionItemBean.SKILL_CONTENT, R.layout.scene_recycle_item_edit_condition_dialog_item)
    }

    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<TriggerDoor>) {
        if (item.itemType == ConditionItemBean.SKILL_CONTENT) {
            holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
            holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
        } else {
            val txtTitle = holder.getView<TextView>(R.id.tv_edit_scene_action_name_item_dialog)
            val params = txtTitle.layoutParams as ViewGroup.MarginLayoutParams
            txtTitle.text = item.conditionName
            if (holder.layoutPosition == 0) {
                params.topMargin = AutoSizeUtils.dp2px(context, 0.0f)
            } else {
                params.topMargin = AutoSizeUtils.dp2px(context, 20.0f)
            }
            txtTitle.layoutParams = params
        }
    }

    override val adapterData: List<ConditionItemBean<TriggerDoor>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}