package com.dfl.smartscene.ui.overlay.common.checklist;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.action.CheckListBean;
import com.dfl.smartscene.databinding.SceneDialogCheckListBinding;
import com.dfl.android.common.global.SkillsListConstant;
import com.dfl.smartscene.util.UIUtils;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/09
 * desc :公用-复选框列表弹窗  多项选择 四项;ui2.0后被废弃使用RadioListDialogFragment
 * version: 1.0
 * update :钟文祥
 */
@Deprecated
public class CheckListDialog<T, K> extends BaseDialog implements View.OnClickListener {
    private SceneDialogCheckListBinding mVB;

    private String mTitle;
    private TextView mTitleTv;
    private String mDesc;
    private TextView mDescTv;
    private RecyclerView mRecycleView;
    /**
     * 选项对象,当前选项的数量，比如挡位就是4个
     */
    private List<CheckListBean> mDataList;

    private CheckListAdapter mAdapter;

    private List<ScenarioInfo.Condition> mConditionList;
    private List<ScenarioInfo.Sequence> mSequenceList;
    private ScenarioInfo.Condition preSetCondition;
    private ScenarioInfo.Sequence preSetSequence;
    private CheckListType type;
    /**
     * 选项对应的InputArg值
     */
    private List<K> mValueList;
    private boolean mIsVertical = true;
    private Context mContext;
    private int mLineCount;

    public CheckListDialog(@NonNull Context context, List<CheckListBean> list,
                           String title, List<T> dataList, CheckListType type) {
        super(context);
        this.mContext = context;
        this.mDataList = list;
        this.mTitle = title;
        this.type = type;
        if (dataList != null && dataList.size() > 0) {
            if (dataList.get(0) instanceof ScenarioInfo.Sequence) {
                this.mSequenceList = (List<ScenarioInfo.Sequence>) dataList;
            } else {
                this.mConditionList = (List<ScenarioInfo.Condition>) dataList;
            }
        }
    }

    public void setInputArgValues(List<K> list) {
        this.mValueList = list;
    }

    public void setRecycleViewVertical(boolean isVertical, int count) {
        mIsVertical = isVertical;
        mLineCount = count;
    }

    public void setPreSetAction(ScenarioInfo.Sequence sequence) {
        this.preSetSequence = sequence;
    }

    public void setPreSetCondition(ScenarioInfo.Condition condition) {
        this.preSetCondition = condition;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogCheckListBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        setCanceledOnTouchOutside(true);
        initView();
        initData();
        setupInitData();
    }

    private void setupInitData() {
        if (preSetCondition != null) {
            setupPreSetCondition(preSetCondition);
        }
        if (preSetSequence != null) {
            setupPreSetSequence(preSetSequence);
        }
    }

    private void setupPreSetCondition(ScenarioInfo.Condition condition) {
        int index = -1;
        if (type == CheckListType.INPUT_CHECK_MODE) {
            index = ObjectUtils.findPreIndexWithInput(condition, mConditionList, mValueList);

        } else if (type == CheckListType.DEFAULT_CHECK_MODE) {
            index = ObjectUtils.findPreIndex(condition, mConditionList);
        }
        if (index != -1) {
            onItemClick(index);
        }
    }

    private void setupPreSetSequence(ScenarioInfo.Sequence sequence) {
        int index = ObjectUtils.findPreIndexWithInput(sequence, mSequenceList, mValueList);
        if (index != -1) {
            String value = sequence.getAction().getInput().get(0).getValue();
            if (sequence.getAction().getSkillId() == SkillsListConstant.SKILL_ID_ACTION_MEDIA_CONTROL2) {
                index = Integer.parseInt(value) + 1;
            }
            onItemClick(index);
        }
    }

    private void initView() {
        mTitleTv = findViewById(R.id.tv_check_list_title);
        mDescTv = findViewById(R.id.tv_desc);
        mRecycleView = findViewById(R.id.rv_check_list);
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
    }

    public void setDesc(String desc) {
        this.mDesc = desc;
    }

    private void initData() {
        mTitleTv.setText(mTitle.isEmpty() ? "" : mTitle);
        if (mDesc != null) {
            mDescTv.setText(mDesc.isEmpty() ? "" : mDesc);
            mDescTv.setVisibility(mDesc.isEmpty() ? View.GONE : View.VISIBLE);
        }

        mAdapter = new CheckListAdapter();
        mAdapter.addData(mDataList);
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            onItemClick(position);
        });

        UIUtils.initRecyclerView(
                mContext,
                mRecycleView,
                new GridLayoutManager(mContext, 2),
                false
        );
        mRecycleView.setAdapter(mAdapter);
    }

    private void onItemClick(int position) {
        if (position == -1) return;
        //避免下标越界
        if (position >= mDataList.size()) {
            return;
        }
        int index = -1;
        for (int i = 0; i < mDataList.size(); i++) {
            if (mDataList.get(i).isCheck()) {
                mDataList.get(i).setCheck(false);
                index = i;
            }
        }

        if (index != -1) mAdapter.notifyItemChanged(index);
        mDataList.get(position).setCheck(true);
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
        } else if (v.getId() == R.id.btn_primary) {
            int index = 0;
            for (int i = 0; i < mDataList.size(); i++) {
                if (mDataList.get(i).isCheck()) {
                    index = i;
                    break;
                }
            }

            if (type == CheckListType.INPUT_CHECK_MODE) {
                if (!mValueList.isEmpty()) {
                    if (mSequenceList != null) {
                        ScenarioInfo.Sequence action = mSequenceList.get(0);
                        action.getAction().getInput().get(0).setValue(String.valueOf(mValueList.get(index)));
                        action.getAction().setDesc(mDataList.get(index).getContent());
                        action.getAction().setCategory(mTitle);
                        EventBus.getDefault().post(action);
                    } else if (mConditionList != null) {
                        ScenarioInfo.Condition condition = mConditionList.get(0);
                        condition.getInput().get(0).setValue(String.valueOf(mValueList.get(index)));
                        condition.setDesc(mDataList.get(index).getContent());
                        condition.setCategory(mTitle);
                        EventBus.getDefault().post(condition);
                    }
                }
            } else if (type == CheckListType.DEFAULT_CHECK_MODE) {
                if (mSequenceList != null) {
                    ScenarioInfo.Sequence action = mSequenceList.get(index);
                    action.getAction().setDesc(mDataList.get(index).getContent());
                    action.getAction().setCategory(mTitle);
                    EventBus.getDefault().post(action);
                } else if (mConditionList != null) {
                    ScenarioInfo.Condition condition = mConditionList.get(index);
                    condition.setDesc(mDataList.get(index).getContent());
                    condition.setCategory(mTitle);
                    EventBus.getDefault().post(condition);
                }
            } else if (type == CheckListType.CHECK_SCENE_LIGHT_EFFECT_MODE) {
                if (mSequenceList != null) {
                    ScenarioInfo.Sequence action = mSequenceList.get(0);
                    int lightEffectMode = 0;
                    int lightState = index + 2;
                    if (index > 1) {
                        lightEffectMode = 1;
                        lightState = index;
                    }
                    action.getAction().setDesc(mDataList.get(index).getContent());
                    action.getAction().setCategory(mTitle);
                    action.getAction().getInput().get(0).setValue(String.valueOf(mValueList.get(lightEffectMode)));
                    action.getAction().getInput().get(1).setValue(String.valueOf(mValueList.get(lightState)));
                    EventBus.getDefault().post(action);
                }
            } else if (type == CheckListType.MEDIA_CONTROL) { //播放控制
                if (!mValueList.isEmpty()) {
                    if (mSequenceList != null) {
                        ScenarioInfo.Sequence action = mSequenceList.get(index < 2 ? 0 : 1);
                        action.getAction().getInput().get(0).setValue(String.valueOf(mValueList.get(index)));
                        action.getAction().setDesc(mDataList.get(index).getContent());
                        action.getAction().setCategory(mTitle);
                        EventBus.getDefault().post(action);
                    }
                }
            } else if (type == CheckListType.SEAT_MASSAGE) {
                if (!mValueList.isEmpty()) {
                    if (mSequenceList != null) {
                        ScenarioInfo.Sequence action;
                        if (index == 0) {
                            action = mSequenceList.get(0);
                        } else {
                            action = mSequenceList.get(1);
                        }
                        action.getAction().getInput().get(0).setValue(String.valueOf(mValueList.get(index)));
                        action.getAction().setDesc(mDataList.get(index).getContent());
                        action.getAction().setCategory(mTitle);
                        EventBus.getDefault().post(action);
                    }
                }
            }
            dismissWithAnimate();
        }
    }
}
