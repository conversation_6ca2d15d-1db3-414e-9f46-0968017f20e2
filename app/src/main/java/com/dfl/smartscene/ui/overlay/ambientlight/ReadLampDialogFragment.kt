package com.dfl.smartscene.ui.overlay.ambientlight

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.animationlib.TabSwitchView
import com.dfl.android.common.base.BaseVBDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.edit.CustomSeekBarBean
import com.dfl.smartscene.databinding.SceneDialogReadLambBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.OnRadioItemClickListener
import com.dfl.smartscene.ui.overlay.common.checklist.RadioCheckAdapter
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.ScenarioInfo
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus

/**
 *Created by 钟文祥 on 2025/1/2.
 *Describer: 室内灯 阅读灯 调节
 */
open class ReadLampDialogFragment(
    var mTitle: String,  //标题
    var mCheckListType: CheckListType, //
    var mCheckList: List<CheckListBean>, //list文本显示
    var mSequenceList: List<ScenarioInfo.Sequence>,   //list对应的动作
    private val customSeekBarBean1: CustomSeekBarBean, //滑条1设置
    private val customSeekBarBean2: CustomSeekBarBean, //滑条2设置
) : BaseVBDialogFragment<SceneDialogReadLambBinding>(), View.OnClickListener, OnRadioItemClickListener {

    var preSetSequence: ScenarioInfo.Sequence? = null

    /**传入的InputArgValue值*/
    var inputArgValueList: List<Int>? = null //文本对应的值
    var mAdapter: RadioCheckAdapter? = null

    override fun initView(savedInstanceState: Bundle?) {
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.tvLightTitle.text = mTitle

        //初始化rv
        mAdapter = RadioCheckAdapter(this)
        mAdapter?.addData(mCheckList)
        mVB.rvCheckList.adapter = mAdapter
        mAdapter?.setOnItemClickListener { _: BaseQuickAdapter<*, *>, _: View, position: Int ->
            onItemClick(position)
        }
        UIUtils.initRecyclerView(
            context, mVB.rvCheckList, LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false), false
        )
        if (mCheckList.size > 4) {
            val lp = mVB.rvCheckList.layoutParams
            lp.height = AutoSizeUtils.dp2px(context, 626.0f)
            mVB.rvCheckList.layoutParams = lp
        }
        mVB.rvCheckList.addItemDecoration(EquallySpaceDecoration(AutoSizeUtils.dp2px(context, 32F), 0))
        setCanceledOnTouchOutside(true)


        mVB.tabSwitchViewCondition.generateViews(
            listOf(
                context?.getString(R.string.scene_text_action_theme),
                context?.getString(R.string.scene_text_action_optional),
            )
        )
        mVB.tabSwitchViewCondition.setOnTabSelectedListener(object : TabSwitchView.OnTabSelectedListener {
            override fun onTabSelected(position: Int, isManual: Boolean?) {
                if (position == 0) {
                    showCheckListView()
                } else {
                    showCsbView()
                }
            }

            override fun onTabUnSelected(lastPosition: Int) {
            }

            override fun onInitViewComplete(isComplete: Boolean?) {
            }
        })

        setupInitData()
    }

    private fun setupInitData() {
        UIUtils.initSeekBarDefaultWidth(context, mVB.csbBright, customSeekBarBean1)
        UIUtils.initSeekBarDefaultWidth(context, mVB.csbColorTemp, customSeekBarBean2)


        if (ObjectUtils.checkScenarioDataIsEmpty(preSetSequence) || ObjectUtils.checkScenarioDataIsEmpty(mSequenceList)) {
            showCheckListView()
            return
        }
        preSetSequence?.let {
            if (it.action.skillId == mSequenceList[0].action.skillId) {
                setupPreSetSequence(it)
                showCheckListView()
            } else if (it.action.skillId == mSequenceList[1].action.skillId) {
                val value = it.action.input[0].value
                if (value.toInt() in customSeekBarBean1.min..customSeekBarBean1.max) {
                    mVB.csbBright.progress = value.toInt()
                }
                val value1 = it.action.input[1].value
                if (value1.toInt() in customSeekBarBean2.min..customSeekBarBean2.max) {
                    mVB.csbColorTemp.progress = value1.toInt()
                }
                showCsbView()
            } else {
                showCheckListView()
            }
        }

    }

    //显示主题
    private fun showCheckListView() {
        mVB.tabSwitchViewCondition.selectedIndex = 0
        mVB.rvCheckList.visibility = View.VISIBLE
        mVB.llCsb.visibility = View.GONE
    }

    //显示自定义
    private fun showCsbView() {
        mVB.tabSwitchViewCondition.selectedIndex = 1
        mVB.rvCheckList.visibility = View.GONE
        mVB.llCsb.visibility = View.VISIBLE
    }

    open fun onItemClick(position: Int) {
        if (position == -1 || position >= mCheckList.size) {
            return
        }
        //原来选中位置
        var index = -1
        for (i in mCheckList.indices) {
            if (mCheckList[i].isCheck) {
                index = i
                break
            }
        }
        //选中的和原来位置一样,直接返回
        if (index == position) return
        if (index != -1) {
            mCheckList[index].isCheck = false
            mAdapter?.notifyItemChanged(index)
        }
        mCheckList[position].isCheck = true
        mAdapter?.notifyItemChanged(position)
    }

    private fun setupPreSetSequence(sequence: ScenarioInfo.Sequence) {
        var index = -1
        when (mCheckListType) {
            CheckListType.DEFAULT_CHECK_MODE -> {
                index = ObjectUtils.findPreIndex(sequence, mSequenceList)
            }

            CheckListType.INPUT_CHECK_MODE -> {
                index = ObjectUtils.findPreIndexWithInput(sequence, mSequenceList, inputArgValueList)
            }

            CheckListType.DOOR_SWITCH_TYPE -> {
                index = ObjectUtils.findPreIndexWithMultiInput(sequence, mSequenceList, inputArgValueList, 1)
            }

            else -> {}
        }
        if (index != -1) {
            onItemClick(index)
        }
    }

    override fun onClick(v: View) {
        if (!DebouncingUtils.isValid(v, SceneEditManager.mViewClickDuration)) return
        if (v.id == R.id.btn_normal) {
            dismissWithAnimate()
        } else if (v.id == R.id.btn_primary) {

            val tabSelectedIndex = mVB.tabSwitchViewCondition.selectedIndex
            val selectedText = (mVB.tabSwitchViewCondition.getTabView(tabSelectedIndex) as TextView).text.toString()
            if (tabSelectedIndex == 0) { //主题
                //先获取选中位置
                var index = 0
                for (i in mCheckList.indices) {
                    if (mCheckList[i].isCheck) {
                        index = i
                        break
                    }
                }
                //非空越界判断
                if (!ObjectUtils.isEmpty(inputArgValueList) && inputArgValueList!!.size > index) {
                    if (!ObjectUtils.isEmpty(mSequenceList)) {
                        val action = mSequenceList[0]
                        action.action.input[0].value = inputArgValueList!![index].toString()
                        action.action.desc = selectedText.plus(" ").plus(mCheckList[index].content)
                        action.action.category = mTitle
                        EventBus.getDefault().post(action)
                    }
                }
            } else { //自定义
                val action = mSequenceList[1]
                action.action.input[0].value = mVB.csbBright.progress.toString()
                action.action.input[1].value = mVB.csbColorTemp.progress.toString()
                action.action.desc =
                    "亮度 " + action.action.input[0].value + "% 色温 " + action.action.input[1].value + "%"
                action.action.category = mTitle
                EventBus.getDefault().post(action)
            }


        }


        dismissWithAnimate()
    }

    override fun onRadioItemClick(position: Int) {
    }
}