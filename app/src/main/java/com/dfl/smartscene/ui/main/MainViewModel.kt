package com.dfl.smartscene.ui.main

import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.soa.SceneManager.isCCMConnect
import com.dfl.smartscene.soa.SceneManager.requestDiscoverInfoList
import com.dfl.smartscene.soa.SceneManager.requestSkillInfoList
import com.dfl.smartscene.soa.SceneRecordManager.deleteTimeOutSceneRecordList

/**
 *Created by 钟文祥 on 2024/2/27.
 *Describer:
 */
class MainViewModel : BaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("MainActivity")

    /**初始化一些应用数据 */
    fun initData() {
        if (isCCMConnect()) { //连接上ccm
            CommonLogUtils.logI(TAG, "ccm已连接,获取发现和能力列表数据")
            requestDiscoverInfoList() //发现列表
            requestSkillInfoList() //预加载所有 能力
        }
    }

    fun deleteTimeOutSceneRecord() {
        deleteTimeOutSceneRecordList()
    }

}