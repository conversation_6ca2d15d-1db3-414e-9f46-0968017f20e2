package com.dfl.smartscene.ui.edit.action.apply.adapter

import android.widget.CheckedTextView
import android.widget.ImageView
import android.widget.LinearLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.PlaySongBean

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2023/03/13
 * desc :
 * version: 1.0
 */
@Deprecated("跟随PlaySongDialogFragment废弃")
class PlaySongAdapter : BaseQuickAdapter<PlaySongBean, BaseViewHolder>(
    R.layout.scene_recycle_item_play_song
) {
    override fun convert(holder: BaseViewHolder, item: PlaySongBean) {
        val content = holder.getView<CheckedTextView>(R.id.ctv_song_style)
        val containerCsl = holder.getView<LinearLayout>(R.id.csl_container_song_item)
        val imageView = holder.getView<ImageView>(R.id.imv_right_item_arrow)
        containerCsl.isSelected = item.isChecked
        imageView.isSelected = item.isChecked
        content.isChecked = item.isChecked
        content.text = item.content
        val params = content.layoutParams as LinearLayout.LayoutParams
        if (item.content.length == 2) {
            params.marginStart = 60
        } else if (item.content.length == 4) {
            params.marginStart = 27
        }
        content.layoutParams = params
    }
}