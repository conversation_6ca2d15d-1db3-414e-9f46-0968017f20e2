package com.dfl.smartscene.ui.edit.condition.status.door

import android.annotation.SuppressLint
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusDoors
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->门窗
 * version: 1.0
 */
class DoorsAndWindowsStatusFragment(
    private val skillId: Int, private val condition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<DoorsWindowsStatusAdapter, SceneFragmentEditConditionBinding, DoorsAndWindowsStatusViewModel>() {
    private var mAdapter: DoorsWindowsStatusAdapter? = null
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.doorsWindowsLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(condition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources, skillId)
    }

    override fun getAdapter(): DoorsWindowsStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        mAdapter = DoorsWindowsStatusAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            run {
                handleItemClick(v, position)
            }
        }

        val manager = GridLayoutManager(context, 2)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ConditionItemBean.SKILL_TITLE) return 2
                return 1
            }
        }
        mViewDataBinding.rvCondition.layoutManager = manager
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].itemType == ConditionItemBean.SKILL_TITLE || it[position].isEnable == false || it[position].conditionType == null) {
                    return
                }
                showRadioListDialog(it[position].conditionName, it[position].conditionType!!)
            }
        }
    }

    override fun getViewModelClass(): Class<DoorsAndWindowsStatusViewModel> {
        return DoorsAndWindowsStatusViewModel::class.java
    }

    private fun showRadioListDialog(title: String, type: StatusDoors) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getConditionerCondition(type, title),
        )
        dialog.inputArgValueList = mViewModel.getInputArgsValueData(type)
        dialog.preSetCondition = condition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}