package com.dfl.smartscene.ui.overlay.time.date.adapter

import android.graphics.drawable.Drawable
import android.widget.CheckedTextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.WhichDayBean

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :日期选择中周几的适配器
 * version: 1.0
 */
class WhichDayAdapter(data: ArrayList<WhichDayBean>) : BaseQuickAdapter<WhichDayBean, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_time_which_day, data
) {
    override fun convert(holder: BaseViewHolder, item: WhichDayBean) {
        val tv = holder.getView<CheckedTextView>(R.id.ctv_which_day_item)
        tv.text = item.content
        val drawable: Drawable? = if (item.checked) {
            ContextCompat.getDrawable(context, R.drawable.scene_img_me_status_time_which_day_selected)
        } else {
            ContextCompat.getDrawable(context, R.drawable.scene_img_me_status_time_which_day_unselected)
        }
        tv.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null)

    }
}