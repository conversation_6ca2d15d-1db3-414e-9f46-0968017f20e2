package com.dfl.smartscene.ui.edit.action.apply.adapter

import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneRecycleItemLocationFavoritesBinding
import com.dfl.soacenter.entity.RoutePoi

/**
 *Created by 钟文祥 on 2023/12/28.
 *Describer:
 */
class LocationFavoritesAdapter(_mSelectedIndex: Int? = -1) :
    BaseQuickAdapter<RoutePoi, BaseDataBindingHolder<SceneRecycleItemLocationFavoritesBinding>>(
        R.layout.scene_recycle_item_location_favorites
    ) {
    var mSelectedIndex = _mSelectedIndex ?: -1

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycleItemLocationFavoritesBinding>, item: RoutePoi
    ) {
        if (item != null) {
            holder.dataBinding?.bean = item

            if (item.isEnable == true) {
                holder.dataBinding?.ivLocation?.alpha = 1f
                holder.dataBinding?.tvLocationName?.alpha = 1f
                holder.dataBinding?.tvLocationAddress?.alpha = 1f
            } else {
                holder.dataBinding?.ivLocation?.alpha = 0.3f
                holder.dataBinding?.tvLocationName?.alpha = 0.3f
                holder.dataBinding?.tvLocationAddress?.alpha = 0.3f
            }
        }

        holder.dataBinding?.isSelected = mSelectedIndex == holder.layoutPosition

        holder.dataBinding?.tvLocationAddress?.visibility = if (holder.layoutPosition > 1) {
            if (!TextUtils.isEmpty(item.address)) View.VISIBLE else View.GONE
        } else View.GONE
        if (holder.layoutPosition == 0) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_navigation_back_home_no)
        } else if (holder.layoutPosition == 1) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_navigation_back_company_no)
        } else {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_location_position_no)
        }

        if (holder.layoutPosition == data.size - 1) {
            holder.dataBinding?.viewLine?.visibility = View.GONE
        } else {
            holder.dataBinding?.viewLine?.visibility = View.VISIBLE
        }
    }
}