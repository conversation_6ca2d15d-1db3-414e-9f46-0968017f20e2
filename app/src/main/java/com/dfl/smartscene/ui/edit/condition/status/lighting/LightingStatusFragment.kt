package com.dfl.smartscene.ui.edit.condition.status.lighting

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusLighting
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/4/18.
 *Describer:
 */
class LightingStatusFragment(
    private val skillId: Int, private val preCondition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<LightingStatusAdapter, SceneFragmentEditConditionBinding, LightingStatusViewModel>() {
    private var mAdapter: LightingStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<LightingStatusViewModel> {
        return LightingStatusViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = LightingStatusAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId)

        mViewModel.lightingWindowsLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].conditionType == null) return@let
                showRadioListDialog(it[position].conditionName, it[position].conditionType!!)

            }
        }
    }

    private fun showRadioListDialog(title: String, type: StatusLighting) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getConditionData(type)
        )
        dialog.inputArgValueList = mViewModel.getInputArg(type)
        dialog.preSetCondition = preCondition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    override fun getAdapter(): LightingStatusAdapter? {
        return mAdapter
    }


}