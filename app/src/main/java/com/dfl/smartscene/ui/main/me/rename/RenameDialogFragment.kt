package com.dfl.smartscene.ui.main.me.rename

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneDialogFragmentMeRenameBinding
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.ClearEditText
import com.dfl.smartscene.widget.RegexEditText


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   : 编辑场景 编辑场景名称
 * version: 1.0
 */
class RenameDialogFragment : BaseDialogFragment() {
    private lateinit var mVB: SceneDialogFragmentMeRenameBinding
    var mDataList: MutableList<MySceneBean?>? = null
    var scene: ScenarioBean? = null
    private var ivClose: ImageView? = null
    private var etInput: ClearEditText? = null
    private var tvCheckMsg: TextView? = null
    var onConfirmListener: OnConfirmListener? = null
    
    // 添加对话框容器的引用
    private var clDialogContainer: androidx.constraintlayout.widget.ConstraintLayout? = null

    private val minNum = 3
    private val maxNum = 10
    private val imeOptions = 9 //保存

    interface OnConfirmListener {
        fun onConfirm()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        mVB = SceneDialogFragmentMeRenameBinding.inflate(layoutInflater, null, false)
        return mVB.root
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_me_rename
    }

    @SuppressLint("SetTextI18n")
    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)

        ivClose = view.findViewById(R.id.iv_rename_close)
        etInput = view.findViewById(R.id.edt_input_area)
        tvCheckMsg = view.findViewById(R.id.tv_check_msg)
        // 添加对话框容器的引用
        clDialogContainer = view.findViewById(R.id.cl_dialog_container)

        //必须先初始化监听，防止非法字符输入
        setListener()
        etInput?.setText(scene?.scenarioInfo?.scenarioName)
        //将光标设置在字符最后
        etInput?.length()?.let { etInput?.setSelection(it) }
        onTextChanged(etInput?.text.toString())
        KeyboardUtils.showSoftInput(etInput!!)
    }

    private fun cancel() {
        etInput?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }

    private fun setListener() {
        ivClose?.setOnClickListener {
            cancel()
        }
        UIUtils.setKeyBroadInputMinIfNotEnable(etInput, minNum)

        etInput?.imeOptions = imeOptions
        etInput?.setOnEditorActionListener { v, actionId, event ->
            if (actionId == imeOptions) {
                if (isExistName(etInput?.text.toString())) {
                    CommonToastUtils.show(R.string.scene_text_input_repeat_name_tips)
                    SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
                }

                scene?.scenarioInfo?.scenarioName = etInput?.text.toString()
                onConfirmListener?.onConfirm()
                cancel()
            }
            true
        }
        etInput?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            @SuppressLint("SetTextI18n")
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                onTextChanged(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {

            }
        })
        etInput?.setInputRegex(RegexEditText.REGEX_NAME)
        //监听是否有输入特殊字符串
        if (etInput is RegexEditText) {
            (etInput as RegexEditText).onMatchListener = object : RegexEditText.OnMatchListener {
                @SuppressLint("SetTextI18n")
                override fun onMatchFail() {
                    CommonLogUtils.logE(TAG, "输入非法字符，不给输入")
                    //nev车型取消非法输入提示
                    //tvCheckMsg?.text = "${getString(R.string.scene_edit_input_hint_short_text_tips)} (${etInput?.text.toString().length}/${maxNum})"
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun onTextChanged(inputStr: String) {
        tvCheckMsg?.text =
            "${getString(R.string.scene_edit_input_hint_short_text_tips)} (${etInput?.text.toString().length}/${maxNum})"

    }


    /** 校验当前输入的名称是否已经存在
     * @param inputName 需要校验的名称
     */
    private fun isExistName(inputName: String): Boolean {
        val dataList = mDataList
        dataList ?: return false
        for (myScene in dataList) {
            if (myScene?.scenario?.scenarioInfo?.scenarioName == inputName && //其他的场景是否有相同的名字
                    myScene.scenario.scenarioInfo?.scenarioId != scene?.scenarioInfo?.scenarioId
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 根据键盘状态调整对话框的 marginTop
     * @param keyboardHeight 键盘高度，0表示键盘收起
     */
    private fun adjustDialogMargin(keyboardHeight: Int) {
        clDialogContainer?.let { container ->
            val layoutParams = container.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            if (keyboardHeight > 0) {
                layoutParams.topMargin = resources.getDimensionPixelSize(R.dimen.px_72)
            } else {
                layoutParams.topMargin = resources.getDimensionPixelSize(R.dimen.px_211)
            }
            container.layoutParams = layoutParams
        }
    }

    override fun onResume() {
        super.onResume()
        etInput?.post {
            etInput?.let {
                KeyboardUtils.showSoftInput(it)
            }
        }
        // 修改键盘状态监听，添加 marginTop 调整逻辑
        KeyboardUtils.registerSoftInputChangedListener(dialog?.window!!) { keyboardHeight ->
            if (keyboardHeight == 0) {
                etInput?.clearFocus()
                // 键盘收起时，调整 marginTop 为 211dp
                adjustDialogMargin(0)
            } else {
                etInput?.requestFocus()
                // 键盘显示时，调整 marginTop 为 72dp
                adjustDialogMargin(keyboardHeight)
            }
        }
    }

    override fun onTouchOutside() {
        super.onTouchOutside()
        cancel()
    }
}