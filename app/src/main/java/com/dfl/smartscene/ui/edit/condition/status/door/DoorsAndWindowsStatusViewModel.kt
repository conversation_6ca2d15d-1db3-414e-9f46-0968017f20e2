package com.dfl.smartscene.ui.edit.condition.status.door

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusDoors
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->门窗view model
 * version: 1.0
 */
class DoorsAndWindowsStatusViewModel : BaseViewModel() {

    val doorsWindowsLiveData = MutableLiveData<ArrayList<ConditionItemBean<StatusDoors>>>()


    //这里添加门窗的二级菜单rv的item
    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusDoors>>()
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_TITLE,
                resources.getString(R.string.scene_text_edit_add_action_car_door_lock),
                -1, null, null,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_all_door_locks),
                R.drawable.scene_icon_me_status_door_all_door_lock,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_ALL_DOOR_LOCK),
                StatusDoors.ALL_DOOR_LOCK,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_action_trunk),
                R.drawable.scene_icon_me_action_door_back_door,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_DOOR_BACK),
                StatusDoors.TRUNK,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )
        //电动尾门，电动行李箱不同车型不同文言
        val title = if (ConfigManager.isPK1B()) {
            resources.getString(R.string.scene_text_edit_add_action_door_back_pk1b)
        } else {
            resources.getString(R.string.scene_text_edit_add_action_door_back)
        }
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                title,
                R.drawable.scene_icon_me_action_door_back_door,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_ELECTRIC_DOOR_BACK),
                StatusDoors.DOOR_BACK,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_action_door_left_child_lock),
                R.drawable.scene_icon_me_action_door_children_lock_l,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_LEFT_CHILD_LOCK),
                StatusDoors.LEFT_CHILD_LOCK,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_action_door_right_child_lock),
                R.drawable.scene_icon_me_action_door_children_lock_r,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_RIGHT_CHILD_LOCK),
                StatusDoors.RIGHT_CHILD_LOCK,
                true, StatusDoors.CAR_DOOR_ITEM
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_TITLE,
                resources.getString(R.string.scene_text_edit_add_action_window),
                -1, null, null, true,
                StatusDoors.CAR_WINDOW_ITEM
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_action_drivers_window),
                R.drawable.scene_icon_me_status_door_main_door_window,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_MAIN),
                StatusDoors.LEFT_UP_WINDOW, true, StatusDoors.CAR_WINDOW_ITEM
            )
        )

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_action_front_passenger_window),
                R.drawable.scene_icon_me_status_door_copilot_door_window,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_CHIEF),
                StatusDoors.RIGHT_UP_WINDOW,
                true,
                StatusDoors.CAR_WINDOW_ITEM
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_action_rear_left_window),
                R.drawable.scene_icon_me_status_door_main_door_window,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_LEFT),
                StatusDoors.LEFT_LOW_WINDOW,
                true,
                StatusDoors.CAR_WINDOW_ITEM
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_action_rear_right_window),
                R.drawable.scene_icon_me_status_door_copilot_door_window,
                arrayListOf(SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_RIGHT),
                StatusDoors.RIGHT_LOW_WINDOW,
                true,
                StatusDoors.CAR_WINDOW_ITEM
            )
        )
        val type = SceneEditManager.StatusType.DOOR
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        doorsWindowsLiveData.postValue(result)
    }

    fun getConditionerCondition(
        type: StatusDoors,
        title: String
    ): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val lock = ""
        val window = ""
        val skillId: Int
        when (type) {
            StatusDoors.ALL_DOOR_LOCK -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_ALL_DOOR_LOCK
                list.add(ScenarioInfo.Condition(0, title, skillId, lock, inputArg))
            }
            StatusDoors.TRUNK -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_DOOR_BACK
                list.add(ScenarioInfo.Condition(0, title, skillId, lock, inputArg))
            }
            StatusDoors.DOOR_BACK -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_ELECTRIC_DOOR_BACK
                list.add(ScenarioInfo.Condition(0, title, skillId, lock, inputArg))
            }
            StatusDoors.LEFT_CHILD_LOCK -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_LEFT_CHILD_LOCK
                list.add(ScenarioInfo.Condition(0, title, skillId, lock, inputArg))
            }
            StatusDoors.RIGHT_CHILD_LOCK -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_RIGHT_CHILD_LOCK
                list.add(ScenarioInfo.Condition(0, title, skillId, lock, inputArg))
            }
            StatusDoors.LEFT_UP_WINDOW -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_MAIN
                list.add(ScenarioInfo.Condition(0, title, skillId, window, inputArg))
            }
            StatusDoors.RIGHT_UP_WINDOW -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_DRIVER_CHIEF
                list.add(ScenarioInfo.Condition(0, title, skillId, window, inputArg))
            }
            StatusDoors.LEFT_LOW_WINDOW -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_LEFT
                list.add(ScenarioInfo.Condition(0, title, skillId, window, inputArg))
            }
            StatusDoors.RIGHT_LOW_WINDOW -> {
                val inputArg = ArrayList<InputArgInfo>()
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL,
                        ArgType.INT32,
                        "0"
                    )
                )
                skillId = SkillsListConstant.SKILL_ID_STATUS_DOOR_WINDOW_BACK_RIGHT
                list.add(ScenarioInfo.Condition(0, title, skillId, window, inputArg))
            }
            else -> {

            }
        }
        return list
    }

    fun getInputArgsValueData(type: StatusDoors): ArrayList<Int> {
        return when (type) {
            StatusDoors.DOOR_BACK -> {
                arrayListOf(2, 1, 3)
            }
            StatusDoors.LEFT_UP_WINDOW,
            StatusDoors.RIGHT_UP_WINDOW,
            StatusDoors.LEFT_LOW_WINDOW,
            StatusDoors.RIGHT_LOW_WINDOW -> {
                arrayListOf(0, 10)
            }
            StatusDoors.TRUNK -> {
                arrayListOf(1, 0)
            }
            else -> {
                arrayListOf(0, 1)
            }
        }
    }

    fun getListData(resources: Resources, type: StatusDoors): List<CheckListBean> {
        return when (type) {
            StatusDoors.ALL_DOOR_LOCK, StatusDoors.LEFT_CHILD_LOCK, StatusDoors.RIGHT_CHILD_LOCK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_unlock), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_lock), false),
                )
            }
            StatusDoors.DOOR_BACK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_pause), false),
                )
            }
            StatusDoors.TRUNK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_close), false),
                )
            }
        }
    }
}