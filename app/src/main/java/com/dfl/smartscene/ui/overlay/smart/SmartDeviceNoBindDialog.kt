package com.dfl.smartscene.ui.overlay.smart

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.dfl.android.animationlib.ScaleImageButton
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.util.UIUtils
import com.dfl.soacenter.communication.CarControlManager
import com.dfl.soacenter.entity.ReqOpenPage

/**
 *Created by 钟文祥 on 2024/6/26.
 *Describer: 设备未连接 dialog
 */
class SmartDeviceNoBindDialog(context: Context) : BaseDialog(context) {

    private val TAG = GlobalConstant.GLOBAL_TAG + "SmartDeviceNoBindDialog"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCanceledOnTouchOutside(true)
        setContentView(R.layout.scene_dialog_smart_device_no_bind)
        initView()

    }

    private fun initView() {
        val textLeft: TextView? = findViewById(R.id.txt_left_1)
        val textRight: TextView? = findViewById(R.id.txt_right_1)

        UIUtils.stringInterceptionChangeColor(
            context,
            textLeft,
            context.getString(R.string.scene_text_device_not_connect_left_key),
            textLeft?.text.toString(),
            R.color.scene_primary_color_highlight
        )
        UIUtils.stringInterceptionChangeColor(
            context,
            textRight,
            context.getString(R.string.scene_text_device_not_connect_right_key),
            textRight?.text.toString(),
            R.color.scene_primary_color_highlight
        )

        val btnGoCarShop: View? = findViewById(R.id.txt_go_to_car_shop)
        val btnGoCarContort: View? = findViewById(R.id.txt_go_to_car_contort)
        val btnDismiss: ScaleImageButton? = findViewById(R.id.tv_dismiss)

        btnGoCarShop?.setOnClickListener {
            goCarShop()
            dismissWithAnimate()
        }
        btnGoCarContort?.setOnClickListener {
            CarControlManager.openPage(ReqOpenPage.SMART_DEVICES)
            dismissWithAnimate()
        }
        btnDismiss?.setOnClickListener {
            dismissWithAnimate()
        }

    }


    @SuppressLint("QueryPermissionsNeeded")
    private fun goCarShop() {
        try {
            val intent = Intent()
            intent.data = Uri.parse("mall://mall.dfl.com/gate?path=main&type=12")
            context.startActivity(intent)
            dismissWithAnimate()
        } catch (e: Exception) {
            CommonLogUtils.logE(TAG, "打开车联商场失败:$e.message")
        }
    }
}