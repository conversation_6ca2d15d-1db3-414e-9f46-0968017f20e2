package com.dfl.smartscene.ui.overlay.time.timelimit;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.global.SkillsListConstant;
import com.dfl.android.common.util.DebouncingUtils;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.android.common.util.TimeUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.databinding.SceneDialogTimeLimitBinding;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.wheel.WheelView;
import com.iauto.scenarioadapter.ArgType;
import com.iauto.scenarioadapter.InputArgInfo;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :时间范围选择弹窗
 * version: 1.0
 */
public class TimeLimitDialog extends BaseDialog implements View.OnClickListener {
    //上下文
    private final Context mContext;
    private final int mSkillId;//0xFF05
    private SceneDialogTimeLimitBinding mVB;
    //开始时间
    private WheelView mStartHourWheelView;
    private WheelView mStartMinuteWheelView;
    private List<String> mStartHourList;
    private List<String> mStartMinuteList;
    //结束时间
    private WheelView mEndHourWheelView;
    private WheelView mEndMinuteWheelView;
    private List<String> mEndHourList;
    private List<String> mEndMinuteList;
    private TextView mNextDayTv;
    //    //文字提示
    //    private TextView mTimeTipsTv;
    private TextView mTitleTv;
    private ScenarioInfo.Condition condition;

    public TimeLimitDialog(@NonNull Context context, @Nullable ScenarioInfo.Condition condition,
                           int skillId) {
        super(context);
        this.mContext = context;
        this.mSkillId = skillId;
        this.condition = condition;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogTimeLimitBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        initClickListener();
        initWheelViewData();
        if (!ObjectUtils.checkScenarioDataIsEmpty(condition)) {
            setInitView(condition);
        }
    }

    private void setInitView(ScenarioInfo.Condition condition) {
        List<com.iauto.scenarioadapter.InputArgInfo> argInfos = condition.getInput();
        String startHour = findMatchItem(argInfos, SkillsListConstant.INPUT_ARG_STATUS_TIME_START_HOUR);
        String startMin = findMatchItem(argInfos, SkillsListConstant.INPUT_ARG_STATUS_TIME_START_MINUTE);
        String stopHour = findMatchItem(argInfos, SkillsListConstant.INPUT_ARG_STATUS_TIME_STOP_HOUR);
        String stopMin = findMatchItem(argInfos, SkillsListConstant.INPUT_ARG_STATUS_TIME_STOP_MINUTE);

        int startHourIndex = findMatchIndex(mStartHourList, startHour, 0);
        int startMinIndex = findMatchIndex(mStartMinuteList, startMin, 0);
        int stopHourIndex = findMatchIndex(mEndHourList, stopHour, 0);
        int stopMinIndex = findMatchIndex(mEndMinuteList, stopMin, 0);
        mStartHourWheelView.setCurrentItem(startHourIndex);
        mStartMinuteWheelView.setCurrentItem(startMinIndex);
        mEndHourWheelView.setCurrentItem(stopHourIndex);
        mEndMinuteWheelView.setCurrentItem(stopMinIndex);
        checkNextDayShow();
        //        checkTipsShown();
    }

    private int findMatchIndex(List<String> list, String dest, int defaultIndex) {
        //ccm储存单位数不会添加0前缀，要手动添加
        int index = list.indexOf(TimeUtils.singleTimeAddZero(dest));
        if (index == -1) {
            index = defaultIndex;
        }
        return index;
    }

    private String findMatchItem(List<com.iauto.scenarioadapter.InputArgInfo> argInfos,
                                 String name) {
        for (InputArgInfo argInfo : argInfos) {
            if (argInfo.getName().equals(name)) {
                return argInfo.getValue();
            }
        }
        return null;
    }

    private void initView() {
        mTitleTv = findViewById(R.id.tv_limit_title);
        mTitleTv.setText(mContext.getString(R.string.scene_text_common_condition_time_interval));
        mStartHourWheelView = findViewById(R.id.wv_first_hour_options);
        mStartMinuteWheelView = findViewById(R.id.wv_first_minute_options);

        mEndHourWheelView = findViewById(R.id.wv_second_hour_options);
        mEndMinuteWheelView = findViewById(R.id.wv_second_minute_options);
        mNextDayTv = findViewById(R.id.tv_next_day);
        //        mTimeTipsTv = findViewById(R.id.tv_time_condition_tips);
    }

    private void initClickListener() {
        mVB.btnNormal.setOnClickListener(this);
        mVB.btnPrimary.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            if (!DebouncingUtils.isValid(v)) return;
            //            if (mTimeTipsTv.getVisibility() == View.VISIBLE) {
            //                mTimeTipsTv.clearAnimation();
            //                Animation animation = AnimationUtils.loadAnimation(mContext,
            //                        R.anim.scene_time_condition_tip_shake);
            //                mTimeTipsTv.startAnimation(animation);
            //                return;
            //            }
            String startHour = mStartHourList.get(mStartHourWheelView.getCurrentItem());
            String startMinute = mStartMinuteList.get(mStartMinuteWheelView.getCurrentItem());

            String endHour = mEndHourList.get(mEndHourWheelView.getCurrentItem());
            String endMinute = mEndMinuteList.get(mEndMinuteWheelView.getCurrentItem());
            //            String nextDay;
            //            if (mNextDayTv.getText().toString().contains(mContext.getString(R.string
            //                    .scene_text_condition_morrow))) {
            //                nextDay = mContext.getString(R.string.scene_text_condition_morrow);
            //            } else {
            //                nextDay = "";
            //            }
            //时间描述个位数带上0，保存到ccm去掉0
            String desc = startHour + ":" + startMinute + "~" + (mNextDayTv.getVisibility() == View.VISIBLE ?
                    mNextDayTv.getText().toString() : "") + endHour + ":" + endMinute;
            startHour = TimeUtils.singleTimeRemoveZero(startHour);
            startMinute = TimeUtils.singleTimeRemoveZero(startMinute);
            endHour = TimeUtils.singleTimeRemoveZero(endHour);
            endMinute = TimeUtils.singleTimeRemoveZero(endMinute);
            List<InputArgInfo> list = new ArrayList<>();
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_START_HOUR, ArgType.INT32,
                    startHour, ""));
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_START_MINUTE, ArgType.INT32,
                    startMinute, ""));
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_START_SECOND, ArgType.INT32,
                    "0", ""));
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_STOP_HOUR, ArgType.INT32,
                    endHour, ""));
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_STOP_MINUTE, ArgType.INT32,
                    endMinute, ""));
            list.add(new InputArgInfo(SkillsListConstant.INPUT_ARG_STATUS_TIME_STOP_SECOND, ArgType.INT32,
                    "0", ""));
            ScenarioInfo.Condition condition = new ScenarioInfo.Condition(1, desc, mSkillId,
                    mTitleTv.getText().toString(), list);
            EventBus.getDefault().post(condition);
            dismissWithAnimate();
        } else if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
        }
    }


    private void initWheelViewData() {
        mStartHourList = new ArrayList<>();
        mEndHourList = new ArrayList<>();

        mStartMinuteList = new ArrayList<>();
        mEndMinuteList = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            mStartHourList.add(String.format(Locale.getDefault(), "%02d", i));
            mEndHourList.add(String.format(Locale.getDefault(), "%02d", i));
        }
        for (int i = 0; i < 60; i++) {
            mStartMinuteList.add(String.format(Locale.getDefault(), "%02d", i));
            mEndMinuteList.add(String.format(Locale.getDefault(), "%02d", i));
        }
        initWheelView(mStartHourWheelView, mStartHourList);
        initWheelView(mStartMinuteWheelView, mStartMinuteList);
        mStartHourWheelView.setCurrentItem(0);
        mStartMinuteWheelView.setCurrentItem(0);
        initWheelView(mEndHourWheelView, mEndHourList);
        initWheelView(mEndMinuteWheelView, mEndMinuteList);
        mEndHourWheelView.setCurrentItem(23);
        mEndMinuteWheelView.setCurrentItem(59);
    }
    private void initWheelView(WheelView wheelView, List<String> list) {
        UIUtils.initWheelViewP42R(mContext, wheelView, list, "", 0, index -> {
            checkNextDayShow();
            //            checkTipsShown();
        });
    }

    //    private void checkTipsShown() {
    //        boolean isShow = false;
    //        if (mStartHourWheelView.getCurrentItem() == mEndHourWheelView.getCurrentItem()) {
    //            isShow = mStartMinuteWheelView.getCurrentItem() >= mEndMinuteWheelView
    //                    .getCurrentItem();
    //        } else if (mStartHourWheelView.getCurrentItem() > mEndHourWheelView.getCurrentItem()) {
    //            isShow = true;
    //        }
    //        mTimeTipsTv.setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
    //    }

    private void checkNextDayShow() {
        boolean isShow = false;
        if (mStartHourWheelView.getCurrentItem() == mEndHourWheelView.getCurrentItem()) {
            isShow = mStartMinuteWheelView.getCurrentItem() >= mEndMinuteWheelView
                    .getCurrentItem();
        } else if (mStartHourWheelView.getCurrentItem() > mEndHourWheelView.getCurrentItem()) {
            isShow = true;
        }
        mNextDayTv.setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
    }

}
