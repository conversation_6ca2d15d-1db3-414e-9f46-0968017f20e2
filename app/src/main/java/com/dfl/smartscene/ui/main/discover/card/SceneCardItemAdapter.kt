package com.dfl.smartscene.ui.main.discover.card

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.SceneCardBean

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class SceneCardItemAdapter : BaseQuickAdapter<SceneCardBean,
        BaseViewHolder>
                                 (R.layout.scene_recycle_item_discover_scene_card) {

    companion object {
        //        private val TAG = SceneCardItemAdapter::class.java.simpleName
    }

    override fun convert(holder: BaseViewHolder, item: SceneCardBean) {
        holder.getView<TextView>(R.id.tv_scene_icon).text = item.scenario.scenarioInfo?.scenarioName
        holder.getView<TextView>(R.id.tv_scene_desc).text = item.scenario.scenarioInfo?.scenarioDesc
        if (item.scenario.scenarioInfo?.imgPath.isNullOrEmpty()) {
            //glide存在同资源加载切换主题有问题的情况
            holder.getView<AppCompatImageView>(R.id.iv_discover_item)
                .setImageResource(R.drawable.scene_img_discover_default_bg)
        } else {
            Glide.with(context)
                .asDrawable()
                .load(item.scenario.scenarioInfo?.imgPath)
                .format(DecodeFormat.PREFER_RGB_565)
                .apply(RequestOptions.bitmapTransform(RoundedCorners(12)))
                .override(584, 282)
                .into(holder.getView<AppCompatImageView>(R.id.iv_discover_item))
        }

    }

}