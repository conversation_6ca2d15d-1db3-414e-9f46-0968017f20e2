package com.dfl.smartscene.ui.edit.action.other

import android.annotation.SuppressLint
import android.view.View
import androidx.core.text.isDigitsOnly
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.OtherType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.soa.getSequenceValueByName
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog
import com.iauto.scenarioadapter.ScenarioInfo
import java.util.*

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->其他页面
 * version: 1.0
 */
class OtherActionFragment(private val skillId: Int, private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<OtherActionAdapter, SceneFragmentEditActionBinding, OtherActionViewModel>() {

    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG + "OtherFragment"
    }

    private var mAdapter: OtherActionAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<OtherActionViewModel> {
        return OtherActionViewModel::class.java
    }


    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = OtherActionAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }

        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) {
                    return 4
                }
                return 1
            }
        }

        mViewDataBinding.rvAction.layoutManager = manager
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].type == ActionSkillItemBean.SKILL_TITLE || it[position].actionType == null) {
                    return
                }
                if (it[position].actionType == OtherType.DELAY) {
                    showDelayDialog()
                } else {
                    showRadioListDialog(it[position].content, it[position].actionType!!)
                }
                //else if (it[position].actionType == OtherType.TEXT_SHORT_PROMPT) {
                //	showSetMsgDialog(it[position].content)
                //}
            }
        }
    }

    private fun showRadioListDialog(title: String, type: OtherType) {
        //一个sequence根据inputArg选项
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getActionData(type),
        )
        dialog.inputArgValueList = mViewModel.getInputArgIntValue(type)
        if (type == OtherType.REARVIEW_MIRROR_SWITCH) {
            dialog.desc = resources.getString(R.string.scene_text_common_door_back_desc)
        }
        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    private fun showDelayDialog() {
        val msg = getSequenceValueByName(sequence, SkillsListConstant.INPUT_ARG_TRIGGER_TIME_SECOND_NAME)
        var hms: TimeUtils.HMS? = null
        if (msg != null && msg.isDigitsOnly()) {
            val s = msg.toInt()
            hms = TimeUtils.secondToHMS(s)
        }
        val hour: MutableList<String> = java.util.ArrayList()
        val minutes: MutableList<String> = java.util.ArrayList()
        val seconds: MutableList<String> = java.util.ArrayList()
        for (i in 0..23) {
            hour.add(String.format(Locale.getDefault(), "%02d", i))
        }
        for (i in 0..59) {
            minutes.add(String.format(Locale.getDefault(), "%02d", i))
            seconds.add(String.format(Locale.getDefault(), "%02d", i))
        }

        var hStr: String? = null
        var mStr: String? = null
        var sStr: String? = null

        hms?.let {
            hStr = String.format(Locale.getDefault(), "%02d", it.h)
            mStr = String.format(Locale.getDefault(), "%02d", it.m)
            sStr = String.format(Locale.getDefault(), "%02d", it.s)
        }

        val singlePickerDialog = TimePickerDialog<String>(
            context,
            hour,
            minutes,
            seconds,
            context.getString(R.string.scene_text_common_action_delay),
            SkillsListConstant.SKILL_ID_ACTION_RECOMMEND_DELAY_ID,
            hStr,
            mStr,
            sStr
        )
        singlePickerDialog.setWheelUnit(
            getString(R.string.scene_text_common_hour),
            getString(R.string.scene_text_common_minute),
            getString(R.string.scene_text_common_second)
        )
        singlePickerDialog.showWithAnimate()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mOtherLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): OtherActionAdapter? {
        return mAdapter
    }

}