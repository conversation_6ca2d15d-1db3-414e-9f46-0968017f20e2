package com.dfl.smartscene.ui.edit.action.drive

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.DriveType
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : raoyulin
 * e-mail : <EMAIL>
 * time : 2025/04/21
 * desc : 动作->驾驶fragment
 * version: 1.0
 */
class DriveActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<DriveActionAdapter, SceneFragmentEditActionBinding, DriveActionViewModel>() {
    private var mAdapter: DriveActionAdapter? = null
    var preSetSequence: ScenarioInfo.Sequence? = null
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getAdapter(): DriveActionAdapter? {
        return mAdapter
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<DriveActionViewModel> {
        return DriveActionViewModel::class.java
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = DriveActionAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvAction.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.type == ActionSkillItemBean.SKILL_TITLE) return
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.actionType == null) return@let
                val content = it.content.replace("\n", "")
                showRadioListDialog(content, it.actionType)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources)

        mViewModel.mDriveActionLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun showRadioListDialog(title: String, type: DriveType) {
        val dialog = RadioListDialogFragment<ScenarioInfo.Sequence, String>(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getCheckList(resources, type),
            mViewModel.getActionList(title, type),
        )
        dialog.inputArgValueList = mViewModel.getInputArgList(type)
        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }
}