package com.dfl.smartscene.ui.edit.adapter

import android.graphics.Rect
import android.text.Html
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.util.getItemView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.StringUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.recycleview.ItemHolderMoveCallback
import com.dfl.smartscene.widget.recycleview.ItemMoveCallback
import com.iauto.scenarioadapter.ArgType
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.Collections

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :编辑页面动作适配器
 * version: 1.0
 */
class SceneEditActionAdapter : BaseMultiItemQuickAdapter<SceneActionBean, BaseViewHolder>(), ItemMoveCallback {

    private var listener: OnActionItemClickListener? = null
    private val mImgOptions: RequestOptions = UIUtils.getGlideOptions()

    init {
        //具体的动作卡片
        addItemType(
            SceneActionBean.ADD_ACTION_CONTENT_ITEM, R.layout.scene_recycle_item_edit_new_action
        )
        //添加动作
        addItemType(
            SceneActionBean.ADD_ACTION_ITEM, R.layout.scene_recycle_item_edit_add_action
        )
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        if (viewType == SceneActionBean.ADD_ACTION_ITEM) return SceneEditActionViewHolder(
            viewType, parent.getItemView(R.layout.scene_recycle_item_edit_add_action)
        )
        return SceneEditActionViewHolder(
            viewType, parent.getItemView(R.layout.scene_recycle_item_edit_new_action)
        )
    }

    override fun convert(holder: BaseViewHolder, item: SceneActionBean) {
        when (item.itemType) {
            SceneActionBean.ADD_ACTION_CONTENT_ITEM -> {
                val view = holder.getView<View>(R.id.v_ambient_light_bg)
                view.visibility = View.GONE
                if (item.action?.action?.skillId == SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID //
                        || item.action?.action?.skillId == SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE
                ) {
                    //如果动作ID为氛围灯ID并且input的长度为3即为氛围灯颜色，这里需要判断究竟是主题颜色还是自选颜色
                    if (item.action.action.desc.contains(context.getString(R.string.scene_text_action_optional)) && item.action.action.input[0].type == ArgType.INT32) {
                        //防止错误数据导致闪退
                        if (StringUtils.isNumeric(item.action.action.input[0].value, true, true)) {
                            view.visibility = View.VISIBLE
                            val color = item.action.action.input[0].value.toInt()
                            view.setBackgroundColor(color)
                        }
                    }
                }
                holder.getView<TextView>(R.id.tv_action_item_serial_no).text =
                    (holder.absoluteAdapterPosition.plus(1)).toString()
                holder.getView<TextView>(R.id.tv_action_item_name).text = item.action?.action?.category
                holder.getView<TextView>(R.id.tv_action_item_detail).text = item.action?.action?.desc

                if (item.action?.action?.skillId == SkillsListConstant.SKILL_ID_ACTION_NAV_GO_SOME_WHERE) {
                    val arr = item.action.action?.desc?.split("+")
                    if (arr?.size == 2 && arr[0].length > 6) {
                        val name1 = arr[0].substring(0, 4) + "…+ "
                        holder.getView<TextView>(R.id.tv_action_item_detail).text = name1 + arr[1]
                    }
                }

                holder.getView<AppCompatImageView>(R.id.btn_action_item_remove).setOnClickListener {
                    listener?.onItemClick(
                        this, view = it, position = holder.absoluteAdapterPosition
                    )
                }
                holder.getView<View>(R.id.v_edit_action_item).alpha = 0f
            }

            SceneActionBean.ADD_ACTION_ITEM -> {
                val textView = holder.getView<TextView>(R.id.tv_add_action_desc_item)
                val clAddAction = holder.getView<ConstraintLayout>(R.id.cl_add_action)
                val ivBg = holder.getView<ImageView>(R.id.iv_bg)
                val ivAddAction = holder.getView<AppCompatImageView>(R.id.iv_add_action)
                
                // 根据data.size动态设置尺寸
                if (data.size == 1) {
                    // data.size == 1时的尺寸设置
                    val layoutParams1 = clAddAction.layoutParams
                    layoutParams1.width = AutoSizeUtils.dp2px(context, 282f)
                    layoutParams1.height = AutoSizeUtils.dp2px(context, 162f)
                    clAddAction.layoutParams = layoutParams1
                    
                    val layoutParams2 = ivBg.layoutParams
                    layoutParams2.width = AutoSizeUtils.dp2px(context, 282f)
                    layoutParams2.height = AutoSizeUtils.dp2px(context, 162f)
                    ivBg.layoutParams = layoutParams2
                    
                    val layoutParams3 = ivAddAction.layoutParams as ConstraintLayout.LayoutParams
                    layoutParams3.topMargin = AutoSizeUtils.dp2px(context, 10.5f)
                    ivAddAction.layoutParams = layoutParams3
                    
                    textView.text = Html.fromHtml(
                        context.getString(
                            R.string.scene_text_edit_add_action_title_tip,
                            context.getColor(R.color.scene_primary_color_highlight).toString()
                        ), Html.FROM_HTML_MODE_COMPACT
                    )
                } else {
                    // 其他情况下的尺寸设置
                    val layoutParams1 = clAddAction.layoutParams
                    layoutParams1.width = AutoSizeUtils.dp2px(context, 278f)
                    layoutParams1.height = AutoSizeUtils.dp2px(context, 162f)
                    clAddAction.layoutParams = layoutParams1
                    
                    val layoutParams2 = ivBg.layoutParams
                    layoutParams2.width = AutoSizeUtils.dp2px(context, 278f)
                    layoutParams2.height = AutoSizeUtils.dp2px(context, 162f)
                    ivBg.layoutParams = layoutParams2
                    
                    val layoutParams3 = ivAddAction.layoutParams as ConstraintLayout.LayoutParams
                    layoutParams3.topMargin = AutoSizeUtils.dp2px(context, 33f)
                    ivAddAction.layoutParams = layoutParams3
                    
                    textView.text = context.getString(R.string.scene_text_edit_add_continue_action_title)
                }
            }
        }
        holder.itemView.setOnClickListener {
            listener?.onItemClick(this, view = it, position = holder.absoluteAdapterPosition)
        }

        val ivBg = holder.getView<ImageView>(R.id.iv_bg)
        Glide.with(context).load(item.itemBg).apply(mImgOptions).into(ivBg)
        Log.d("TAGGGGGGGGGGGGGGGGGGG", "convert: "+item.itemBg)
    }

    override fun onItemMove(fromPosition: Int, toPosition: Int) {
        //如果是添加动作的类型，则不能移动
        if (getDefItemViewType(fromPosition) == SceneActionBean.ADD_ACTION_ITEM || getDefItemViewType(toPosition) == SceneActionBean.ADD_ACTION_ITEM) {
            return
        }
        //        if (fromPosition == data.size - 1 || toPosition == data.size - 1)
        //            return
        if (fromPosition < toPosition) {
            for (i in fromPosition until toPosition) {
                data.let { Collections.swap(it, i, i + 1) }
            }
        } else {
            for (i in fromPosition downTo toPosition + 1) {
                data.let { Collections.swap(it, i, i - 1) }
            }
        }
        notifyItemMoved(fromPosition, toPosition)
        listener?.onItemMove(fromPosition, toPosition)
    }

    public override fun getDefItemViewType(position: Int): Int {
        return super.getDefItemViewType(position)
    }

    private class SceneEditActionViewHolder(val type: Int, view: View) : BaseViewHolder(view), ItemHolderMoveCallback {
        var vItemBg: View? = null

        init {
            vItemBg = view.findViewById(R.id.v_edit_action_item)
        }

        override fun onItemHolderMoveStart(fromPosition: Int?) {
            if (type == SceneActionBean.ADD_ACTION_CONTENT_ITEM) {
                vItemBg?.alpha = 0.2f
            }
        }

        override fun onItemHolderMoveEnd(toPosition: Int?) {
            if (type == SceneActionBean.ADD_ACTION_CONTENT_ITEM) {
                vItemBg?.alpha = 0f
            }
        }
    }

    interface OnActionItemClickListener {
        fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int)

        fun onItemMove(fromPosition: Int, toPosition: Int)
    }

    fun setOnActionItemClickListener(listener: OnActionItemClickListener?) {
        this.listener = listener
    }

    class MyGridItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            //            val position = parent.getChildAdapterPosition(view)
            outRect.bottom = 24
            outRect.left = 0
            outRect.right = 24
        }
    }

    override fun onViewRecycled(holder: BaseViewHolder) {
        context.let {
            val ivBg = holder.getView<ImageView>(R.id.iv_bg)
            ivBg.let {
                Glide.with(context).clear(it)
            }
        }
        super.onViewRecycled(holder)
    }
}