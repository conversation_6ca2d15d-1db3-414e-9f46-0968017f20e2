package com.dfl.smartscene.ui.edit.condition.status.smartdevice

import android.annotation.SuppressLint
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusSmartDevice
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.smart.BuySmartDevice4StatusDialog
import com.dfl.smartscene.ui.overlay.smart.SmartDeviceNoBindDialog
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:
 */
class SmartDeviceStatusFragment(
    private val skillId: Int,   //用于和触发条件做比较
    private val condition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<SmartDeviceStatusAdapter, SceneFragmentEditConditionBinding, SmartDeviceStatusViewModel>() {

    private var mAdapter: SmartDeviceStatusAdapter? = null


    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SmartDeviceStatusViewModel> {
        return SmartDeviceStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId) //初始化rv的item List

        mViewModel.liveData.observe(viewLifecycleOwner) {
            mAdapter?.setList(it)
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(condition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): SmartDeviceStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SmartDeviceStatusAdapter()
        mAdapter?.setOnItemClickListener { _, view, position -> handleItemClick(view, position) }

        val manager = GridLayoutManager(context, 2)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ConditionItemBean.SKILL_TITLE) return 2
                return 1
            }
        }
        mViewDataBinding.rvCondition.layoutManager = manager
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (!DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) return
        mAdapter?.data?.get(position)?.let {
            if (it.conditionType == null) return@let
            if (it.isEnable == false) {
                //showBuyDeviceDialog(mAdapter?.data?.get(position)?.conditionItemType)
                showDeviceNoBindDialog()
                return@let
            }
            showRadioListDialog(it.conditionName, it.conditionType)
        }
    }

    private fun showDeviceNoBindDialog() {
        val dialog = SmartDeviceNoBindDialog(context)
        dialog.showWithAnimate()
    }

    /**
     * 623购买智能设备二维码
     */
    private fun showBuyDeviceDialog(type: StatusSmartDevice?) {
        type?.let {
            val dialog = BuySmartDevice4StatusDialog(context, it)
            dialog.showWithAnimate()
        }
    }

    private fun showRadioListDialog(title: String, type: StatusSmartDevice) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getSmartStatusCondition(type)
        )
        dialog.inputArgValueList = mViewModel.getInputArgInfoValueData(type)
        dialog.preSetCondition = condition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

}