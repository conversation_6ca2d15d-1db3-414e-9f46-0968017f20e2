package com.dfl.smartscene.ui.edit.container

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.EditConditionAndActionItemBean
import com.dfl.smartscene.ui.edit.manager.SceneEditManager

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   :
 * version: 1.0
 */
class SceneEditContainerViewModel : BaseViewModel() {
    // common
    companion object {
        //        private var TAG = SceneEditContainerViewModel::class.simpleName
    }

    /**
     * viewpage和rv适配器的状态/触发/动作 目录全部数据
     */
    var categoryList = MutableLiveData<ArrayList<EditConditionAndActionItemBean>>()

    /**
     * 场景条件与编辑的弹框标题
     */
    var titleName = MutableLiveData<String>()

    /**
     * 场景条件与编辑的弹框默认目录索引,第几个高亮
     */
    var mDefaultLeftIndex = 0


    /**
     * 初始化viewpage和rv的动作或条件数据
     * @param resources R
     * @param sceneEditType 区分是触发条件、状态条件、动作
     * @param isAddConditionOrAction 是否为新添加的条件或动作
     * @param statusSkillId 条件或动作能力id
     */
    fun initData(
        resources: Resources, sceneEditType: SceneNewEditType, //触发、状态、动作
        isAddConditionOrAction: Boolean, //是否添加
        statusSkillId: Int //状态条件的能力id，第一个状态条件的不为空的时候赋值
    ) {
        val list = ArrayList<EditConditionAndActionItemBean>()
        when (sceneEditType) {
            SceneNewEditType.TRIGGER_CONDITION -> {
                titleName.value = resources.getString(R.string.scene_text_edit_scene_trigger_conditions)
                list.addAll(
                    initTriggeringConditionsData(
                        resources, sceneEditType, isAddConditionOrAction
                    )
                )
            }

            SceneNewEditType.STATUS_CONDITION -> {
                titleName.value = resources.getString(R.string.scene_text_edit_scene_status_conditions)
                list.addAll(
                    initStatusConditionsData(
                        resources, sceneEditType, isAddConditionOrAction, statusSkillId
                    )
                )
            }

            SceneNewEditType.ACTION -> {
                titleName.value = resources.getString(R.string.scene_text_edit_add_action_title)
                list.addAll(initActionsData(resources, sceneEditType, isAddConditionOrAction))
            }
        }

        //        model.splitSkillByCategory(sceneEditBean, list)
        categoryList.postValue(list)
    }

    /**
     * 初始化 触发条件的一级菜单及包含子项,并判断itemIsActive
     * @param resources R
     * @param type SceneNewEditType.ACTION
     * @param isAddCondition 是否新增
     * @return EditActionAndConditionBean列表
     */
    private fun initTriggeringConditionsData(
        resources: Resources, type: SceneNewEditType, //触发、状态、动作
        isAddCondition: Boolean
    ): ArrayList<EditConditionAndActionItemBean> {
        val list = ArrayList<EditConditionAndActionItemBean>()
        list.ensureCapacity(5) //list的最小容量
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.TimeCode,
                resources.getString(R.string.scene_text_edit_add_condition_time),
                -1,
                R.drawable.scene_icon_me_add_condition_time,
                R.drawable.scene_icon_me_add_condition_time_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.EnvironmentCode,
                resources.getString(R.string.scene_text_edit_add_condition_environment),
                -1,
                R.drawable.scene_icon_me_add_condition_environment,
                R.drawable.scene_icon_me_add_condition_environment_selected,
                type,
                isAddCondition,
                false
            )
        )
        //位置
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.LocationCode,
                resources.getString(R.string.scene_text_edit_add_condition_location),
                -1,
                R.drawable.scene_icon_me_add_condition_location,
                R.drawable.scene_icon_me_add_condition_location_selected,
                type,
                isAddCondition,
                false
            )
        )
        //v1.1删除导航
        //		list.add(
        //			EditConditionAndActionItemBean(
        //				ConditionEventCode.NavigationCode,
        //				resources.getString(R.string.scene_text_edit_add_action_navigation),
        //				-1, R.drawable.scene_icon_navigation_unselected,
        //				R.drawable.scene_icon_navigation_selected, type, isAddCondition, false
        //			)
        //		)
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.DriveCode,
                resources.getString(R.string.scene_text_edit_add_condition_drive),
                -1,
                R.drawable.scene_icon_me_add_condition_drive,
                R.drawable.scene_icon_me_add_condition_drive_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.DoorCode,
                resources.getString(R.string.scene_text_edit_add_action_car_door),
                -1,
                R.drawable.scene_icon_me_add_condition_door,
                R.drawable.scene_icon_me_add_condition_door_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.SeatCode,
                resources.getString(R.string.scene_text_edit_add_action_chair),
                -1,
                R.drawable.scene_icon_me_add_condition_seat,
                R.drawable.scene_icon_me_add_condition_seat_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.LightingCode,
                resources.getString(R.string.scene_text_edit_add_action_car_light),
                -1,
                R.drawable.scene_icon_me_add_action_lamp,
                R.drawable.scene_icon_me_add_action_lamp_selected,
                type,
                isAddCondition,
                false
            )
        )
//        list.add(
//            EditConditionAndActionItemBean(
//                ConditionEventCode.ChargeCode,
//                resources.getString(R.string.scene_text_edit_add_charge_discharge),
//                -1,
//                R.drawable.scene_icon_me_add_action_charging,
//                R.drawable.scene_icon_me_add_action_charging_selected,
//                type,
//                isAddCondition,
//                false
//            )
//        )
//        list.add(
//            EditConditionAndActionItemBean(
//                ConditionEventCode.OMSCode,
//                resources.getString(R.string.scene_text_edit_add_common_oms),
//                -1,
//                R.drawable.scene_icon_me_add_condition_cabin_perception,
//                R.drawable.scene_icon_me_add_condition_cabin_perception_selected,
//                type,
//                isAddCondition,
//                false
//            )
//        )

        return SceneEditManager.checkEditItem(list, type, mDefaultLeftIndex)
    }


    /**
     * 初始化 状态条件一级菜单及包含子项,并判断itemIsActive
     * @param resources R
     * @param type SceneNewEditType.ACTION
     * @param isAddCondition 是否新增
     * @return EditActionAndConditionBean列表
     */
    private fun initStatusConditionsData(
        resources: Resources, type: SceneNewEditType, isAddCondition: Boolean, statusSkillId: Int
    ): ArrayList<EditConditionAndActionItemBean> {
        val list = ArrayList<EditConditionAndActionItemBean>()
        list.ensureCapacity(6)
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.TimeCode,
                resources.getString(R.string.scene_text_edit_add_condition_time),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_time,
                R.drawable.scene_icon_me_add_condition_time_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.EnvironmentCode,
                resources.getString(R.string.scene_text_edit_add_condition_environment),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_environment,
                R.drawable.scene_icon_me_add_condition_environment_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.LocationCode,
                resources.getString(R.string.scene_text_edit_add_condition_location),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_location,
                R.drawable.scene_icon_me_add_condition_location_selected,
                type,
                isAddCondition,
                false
            )
        )
        //v1.1 将触发条件的导航 移到 状态条件
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.NavigationCode,
                resources.getString(R.string.scene_text_edit_add_action_navigation),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_navigation,
                R.drawable.scene_icon_me_add_condition_navigation_selected,
                type,
                isAddCondition,
                false
            )
        )

        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.DoorCode,
                resources.getString(R.string.scene_text_edit_add_condition_doors_and_windows),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_door,
                R.drawable.scene_icon_me_add_condition_door_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.SeatCode,
                resources.getString(R.string.scene_text_edit_add_action_chair),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_seat,
                R.drawable.scene_icon_me_add_condition_seat_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.LightingCode,
                resources.getString(R.string.scene_text_edit_add_action_car_light),
                statusSkillId,
                R.drawable.scene_icon_me_add_action_lamp,
                R.drawable.scene_icon_me_add_action_lamp_selected,
                type,
                isAddCondition,
                false
            )
        )
//        list.add(
//            EditConditionAndActionItemBean(
//                ConditionEventCode.OMSCode,
//                resources.getString(R.string.scene_text_edit_add_common_oms),
//                statusSkillId,
//                R.drawable.scene_icon_me_add_condition_cabin_perception,
//                R.drawable.scene_icon_me_add_condition_cabin_perception_selected,
//                type,
//                isAddCondition,
//                false
//            )
//        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.LinkCode,
                resources.getString(R.string.scene_text_edit_add_condition_link),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_link,
                R.drawable.scene_icon_me_add_condition_link_selected,
                type,
                isAddCondition,
                false
            )
        )
        //智能设备
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.SmartDeviceCode,
                resources.getString(R.string.scene_text_action_smart_device),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_smartdevice,
                R.drawable.scene_icon_me_add_condition_smartdevice_selected,
                type,
                isAddCondition,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ConditionEventCode.TirePressureCode,
                resources.getString(R.string.scene_text_edit_add_action_other),
                statusSkillId,
                R.drawable.scene_icon_me_add_condition_other,
                R.drawable.scene_icon_me_add_condition_other_selected,
                type,
                isAddCondition,
                false
            )
        )
        return SceneEditManager.checkEditItem(list, type, mDefaultLeftIndex)
    }

    /**
     * 初始化 动作一级菜单及包含子项,并判断itemIsActive
     * @param resources R
     * @param type SceneNewEditType.ACTION
     * @param isAddAction 是否新增
     * @return EditActionAndConditionBean列表
     */
    private fun initActionsData(
        resources: Resources, type: SceneNewEditType, isAddAction: Boolean
    ): ArrayList<EditConditionAndActionItemBean> {
        val list = ArrayList<EditConditionAndActionItemBean>()
        list.ensureCapacity(9)
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.RecommendCode,
                resources.getString(R.string.scene_text_edit_add_action_recommend),
                -1,
                R.drawable.scene_icon_me_add_action_recommend,
                R.drawable.scene_icon_me_add_action_recommend_selected,
                type,
                isAddAction,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.AirConditionerCode,
                resources.getString(R.string.scene_text_app_open_5),
                -1,
                R.drawable.scene_icon_me_add_action_snow,
                R.drawable.scene_icon_me_add_action_snow_selected,
                type,
                isAddAction,
                false
            )
        )
        //         if (ConfigManager.isHighConfig()){
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.DoorWindowCode,
                resources.getString(R.string.scene_text_edit_add_condition_doors_and_windows),
                -1,
                R.drawable.scene_icon_me_add_condition_door,
                R.drawable.scene_icon_me_add_condition_door_selected,
                type,
                isAddAction,
                false
            )
        )
        //         }

//        list.add(
//            EditConditionAndActionItemBean(
//                ActionEventCode.CarLightCode,
//                resources.getString(R.string.scene_text_edit_add_action_car_light),
//                -1,
//                R.drawable.scene_icon_me_add_action_lamp,
//                R.drawable.scene_icon_me_add_action_lamp_selected,
//                type,
//                isAddAction,
//                false
//            )
//        )
        //        if (ConfigManager.isHighConfig()) {//高配才有座椅功能项
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.ChairCode,
                resources.getString(R.string.scene_text_edit_add_action_chair),
                -1,
                R.drawable.scene_icon_me_add_condition_seat,
                R.drawable.scene_icon_me_add_condition_seat_selected,
                type,
                isAddAction,
                false
            )
        )
        //        }

        //冰箱
//        list.add(
//            EditConditionAndActionItemBean(
//                ActionEventCode.FridgeCode,
//                resources.getString(R.string.scene_text_me_action_fridge),
//                -1,
//                R.drawable.scene_icon_me_add_action_icebox,
//                R.drawable.scene_icon_me_add_action_icebox_selected,
//                type,
//                isAddAction,
//                false
//            )
//        )

        //驾驶
//        list.add(
//            EditConditionAndActionItemBean(
//                ActionEventCode.DriveCode,
//                resources.getString(R.string.scene_text_edit_add_condition_drive),
//                -1,
//                R.drawable.scene_icon_me_add_condition_drive,
//                R.drawable.scene_icon_me_add_condition_drive_selected,
//                type,
//                isAddAction,
//                false
//            )
//        )

        //应用
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.ApplicationCode,
                resources.getString(R.string.scene_text_edit_add_action_application),
                -1,
                R.drawable.scene_icon_me_add_action_app,
                R.drawable.scene_icon_me_add_action_app_selected,
                type,
                isAddAction,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.SystemCode,
                resources.getString(R.string.scene_text_app_open_8),
                -1,
                R.drawable.scene_icon_me_add_action_system,
                R.drawable.scene_icon_me_add_action_system_selected,
                type,
                isAddAction,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.SmartDeviceCode,
                resources.getString(R.string.scene_text_action_smart_device),
                -1,
                R.drawable.scene_icon_me_add_condition_smartdevice,
                R.drawable.scene_icon_me_add_condition_smartdevice_selected,
                type,
                isAddAction,
                false
            )
        )
        list.add(
            EditConditionAndActionItemBean(
                ActionEventCode.OtherCode,
                resources.getString(R.string.scene_text_edit_add_action_other),
                -1,
                R.drawable.scene_icon_me_add_condition_other,
                R.drawable.scene_icon_me_add_condition_other_selected,
                type,
                isAddAction,
                false
            )
        )
        return SceneEditManager.checkEditItem(list, type, mDefaultLeftIndex)
    }
}