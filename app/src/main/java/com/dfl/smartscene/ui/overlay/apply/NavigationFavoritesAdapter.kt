package com.dfl.smartscene.ui.overlay.apply

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneRecycelItemNavigationFavoritesSelectBinding
import com.dfl.soacenter.entity.RoutePoi

/**
 *Created by 钟文祥 on 2025/2/26.
 *Describer: lk2a 收藏夹 item。 代替 LocationFavoritesAdapter
 */
class NavigationFavoritesAdapter(_mSelectedIndex: Int? = -1) :
    BaseQuickAdapter<RoutePoi, BaseDataBindingHolder<SceneRecycelItemNavigationFavoritesSelectBinding>>(
        R.layout.scene_recycel_item_navigation_favorites_select
    ) {
    var mSelectedIndex = _mSelectedIndex ?: -1

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycelItemNavigationFavoritesSelectBinding>, item: RoutePoi
    ) {
        val isSelected = mSelectedIndex == holder.layoutPosition
        item.let {
            holder.dataBinding?.bean = it
            if (it.isEnable == true) {
                if (isSelected) {
                    holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_primary_color_highlight))
                } else {
                    holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_color_text_1))
                }
            } else {
                holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_color_text_1))
            }
        }
        holder.dataBinding?.isSelected = isSelected


        if (holder.layoutPosition == 0) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_navigation_back_home_no)
        } else if (holder.layoutPosition == 1) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_navigation_back_company_no)
        } else if (holder.layoutPosition == 2) {
            if (item.name == context.getString(R.string.scene_text_action_go_charge)) {
                holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_navigation_go_charge)
            } else {
                holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_location_position_no)
            }
        } else {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_me_action_location_position_no)
        }

    }
}