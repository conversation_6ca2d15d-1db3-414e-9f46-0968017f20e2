package com.dfl.smartscene.ui.overlay.apply

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneRecycelItemNavigationFavoritesSelectBinding
import com.dfl.soacenter.entity.RoutePoi

/**
 *Created by 钟文祥 on 2025/2/26.
 *Describer: lk2a 收藏夹 item。 代替 LocationFavoritesAdapter
 */
class NavigationFavoritesAdapter(_mSelectedIndex: Int? = -1) :
    BaseQuickAdapter<RoutePoi, BaseDataBindingHolder<SceneRecycelItemNavigationFavoritesSelectBinding>>(
        R.layout.scene_recycel_item_navigation_favorites_select
    ) {
    var mSelectedIndex = _mSelectedIndex ?: -1

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycelItemNavigationFavoritesSelectBinding>, item: RoutePoi
    ) {
        val isSelected = mSelectedIndex == holder.layoutPosition
        item.let {
            holder.dataBinding?.bean = it
            if (it.isEnable == true) {
                if (isSelected) {
                    holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_primary_color_highlight))
                } else {
                    holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_color_text_1))
                }
            } else {
                holder.dataBinding?.tvLocationName?.setTextColor(context.getColor(R.color.scene_color_text_1))
            }
        }
        holder.dataBinding?.isSelected = isSelected


        if (holder.layoutPosition == 0) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_navigation_dock_home)
        } else if (holder.layoutPosition == 1) {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_navigation_system_company)
        }else {
            holder.dataBinding?.ivLocation?.setImageResource(R.drawable.scene_icon_navigation_system_cancel)
        }

    }
}