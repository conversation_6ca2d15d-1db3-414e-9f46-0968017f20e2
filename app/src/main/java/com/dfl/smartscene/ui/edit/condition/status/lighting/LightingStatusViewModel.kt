package com.dfl.smartscene.ui.edit.condition.status.lighting

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusLighting
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/4/18.
 *Describer:
 */
class LightingStatusViewModel : BaseViewModel() {

    val lightingWindowsLiveData = MutableLiveData<ArrayList<ConditionItemBean<StatusLighting>>>()

    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusLighting>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_far_light),
                R.drawable.scene_icon_me_action_light_highbeam,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_FAR_LIGHT,
                ),
                StatusLighting.FAR_LIGHT
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_near_light),
                R.drawable.scene_icon_me_action_light_lowbeam,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_NEAR_LIGHT,
                ),
                StatusLighting.NEAR_LIGHT
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_add_fog_light),
                R.drawable.scene_icon_me_action_light_fog,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_FOG_LIGHT,
                ),
                StatusLighting.FOG_LIGHT
            )
        )

        val type = SceneEditManager.StatusType.LIGHTING
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        lightingWindowsLiveData.postValue(result)
    }

    fun getListData(resources: Resources, type: StatusLighting): List<CheckListBean> {
        return when (type) {
            StatusLighting.FAR_LIGHT, StatusLighting.NEAR_LIGHT, StatusLighting.FOG_LIGHT -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                )
            }
        }
    }

    fun getConditionData(type: StatusLighting): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val args = ArrayList<InputArgInfo>()
        when (type) {
            StatusLighting.FAR_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_FAR_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_FAR_LIGHT, "", args
                    )
                )
            }

            StatusLighting.NEAR_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_NEAR_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_NEAR_LIGHT, "", args
                    )
                )
            }

            StatusLighting.FOG_LIGHT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_FOG_LIGHT, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_FOG_LIGHT, "", args
                    )
                )
            }
        }
        return list
    }

    fun getInputArg(type: StatusLighting): ArrayList<Int> {
        return when (type) {
            StatusLighting.FAR_LIGHT, StatusLighting.NEAR_LIGHT, StatusLighting.FOG_LIGHT -> {
                arrayListOf(1)
            }
        }
    }
}