package com.dfl.smartscene.ui.edit.condition.status.time

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusTime
import com.dfl.android.common.util.MatchableAdapter

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/26
 * desc :条件->时间适配器
 * version: 1.0
 */
class TimeStatusAdapter : BaseQuickAdapter<ConditionItemBean<StatusTime>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<StatusTime>> {
    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<StatusTime>) {
        holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
        holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
        if (item.isEnable == false) {
            holder.itemView.alpha = 0.5f
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override val adapterData: List<ConditionItemBean<StatusTime>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}