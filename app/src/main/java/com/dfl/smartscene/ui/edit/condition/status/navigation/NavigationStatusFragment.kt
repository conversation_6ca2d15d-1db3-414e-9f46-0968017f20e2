package com.dfl.smartscene.ui.edit.condition.status.navigation

import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusNavigation
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.RadioProgressDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog
import com.iauto.scenarioadapter.ScenarioInfo
import java.util.Locale

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer: 状态 - 导航
 */
class NavigationStatusFragment(private val skillId: Int, private val condition: ScenarioInfo.Condition?) :
    MVVMAdapterFragment<NavigationStatusAdapter, SceneFragmentEditConditionBinding, NavigationStatusViewModel>() {

    private var mAdapter: NavigationStatusAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<NavigationStatusViewModel> {
        return NavigationStatusViewModel::class.java
    }


    override fun initData() {
        super.initData()
        mViewModel.initData(resources, skillId)

        mViewModel.liveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(condition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    override fun getAdapter(): NavigationStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = NavigationStatusAdapter()
        mAdapter?.setOnItemClickListener { _, view, position -> handleItemClick(view, position) }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.isEnable == false) {
            return
        }
        if (!DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) return

        when (mAdapter?.data?.get(position)?.conditionType) {
            //到达时间
            StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_TIME -> {
                showTimeSelectDialog()
            }
            //到达距离
            StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_DISTANCE -> {
                //showDistanceDialog(
                //    500, 10000, 100, 1000, context.getString(R.string.scene_text_common_less_than), "m"
                //)
                showSingleWheelDialog(mAdapter?.data?.get(position)?.conditionName)
            }
            else -> {}
        }
    }

    private fun showSingleWheelDialog(title: String?) {
        val map = mViewModel.getWheelMap(500, 10000, 100)
        val dialog = SingleWheelDialog<ScenarioInfo.Condition, String>(
            context,
            map.keys.toList(),
            title,
            mViewModel.getDriveConditionData(StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_DISTANCE),
            SingleWheelType.SINGLE_WHEEL_DEFAULT
        )
        dialog.setupPreCondition(condition)
        dialog.setShowLeftDesc(true, getString(R.string.scene_text_common_less_than))
        dialog.setUnit(getString(R.string.scene_text_condition_drive_distance_mi))
        dialog.isLoop = false
        dialog.setWheelDefaultPosition(5)
        dialog.showWithAnimate()
    }

    //显示时间
    private fun showTimeSelectDialog() {
        val hStr =
            condition?.input?.find { it.name == SkillsListConstant.INPUT_ARG_STATUS_NAVIGATION_H }?.value?.toInt()
        val mStr =
            condition?.input?.find { it.name == SkillsListConstant.INPUT_ARG_STATUS_NAVIGATION_MIN }?.value?.toInt()
        val context = getContext() ?: return
        val hour: MutableList<String> = ArrayList()
        val minutes: MutableList<String> = ArrayList()
        for (i in 0..6) {
            hour.add(String.format(Locale.getDefault(), "%02d", i))
        }
        for (i in 0..59) {
            minutes.add(String.format(Locale.getDefault(), "%02d", i))
        }
        //ccm储存单位数不会添加0前缀，要手动添加
        val singlePickerDialog = TimePickerDialog(
            context,
            hour,
            minutes,
            context.getString(R.string.scene_text_edit_condition_navigation_expected_arrival_time),
            SkillsListConstant.SKILL_ID_STATUS_NAVIGATION_EXPECTED_ARRIVAL_TIME,
            TimeUtils.singleTimeAddZero(hStr?.toString()),
            TimeUtils.singleTimeAddZero(mStr?.toString()),
            null
        )
        singlePickerDialog.setWheelUnit(
            getString(R.string.scene_text_common_hour),
            getString(R.string.scene_text_common_minute),
            ""
        )
        singlePickerDialog.showWithAnimate()
    }

    //显示距离
    private fun showDistanceDialog(
        min: Int, max: Int, keli: Int, def: Int, pre: String, unit: String
    ) {
        val dialog = RadioProgressDialog(
            context,
            context.getString(R.string.scene_text_edit_condition_navigation_expected_arrival_distance),
            mViewModel.getDriveConditionData(StatusNavigation.NAVIGATION_EXPECTED_ARRIVAL_DISTANCE),
            pre
        )
        //0-500  颗粒：10  默认60
        dialog.setPreSetCondition(condition)
        dialog.setSeekBarMultiple(keli) //颗粒

        dialog.setMinNum(min)
        dialog.setMaxNum(max)
        dialog.setUnit(unit) //单位
        dialog.setDefaultUnit(def)

        dialog.setSeekBarTopTextType(2) //是否显示前缀
        dialog.showWithAnimate()
    }
}