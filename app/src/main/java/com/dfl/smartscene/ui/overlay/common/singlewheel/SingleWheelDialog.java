package com.dfl.smartscene.ui.overlay.common.singlewheel;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.databinding.SceneDialogSingleWheelBinding;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.wheel.WheelView;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :单滚轮选择弹窗
 * version: 1.0
 */
public class SingleWheelDialog<T, K> extends BaseDialog implements View.OnClickListener {
    private final Context mContext;
    /**
     * 滚轮的文本列表
     */
    private final List<String> mDataList;
    private final SingleWheelType type;
    private final String mTitle;
    /** 滚轮是否循环 */
    public Boolean isLoop = true;
    private SceneDialogSingleWheelBinding mVB;
    private WheelView mSingleWheelView;
    private List<ScenarioInfo.Sequence> mSequenceList;
    private List<ScenarioInfo.Condition> mConditionList;
    /**
     * 滚轮文本对应的InputArg
     */
    private List<K> mInputArgValueList;
    private String mTitleDesc;
    private int mWheelDefaultPosition = 0;//滚轮默认位置，以集合下标对应为准
    private String mUnit = ""; //单位
    private String mCategory = "";
    private boolean mIsShowLeftDesc = false; //左边的描述
    private String mLeftDesc = "";
    private ScenarioInfo.Condition preSetCondition;
    private ScenarioInfo.Sequence preSequence;
    private OnSceneActionListener listener;

    public SingleWheelDialog(@NonNull Context context,
                             List<String> dataList,
                             String title,
                             List<T> list,
                             SingleWheelType type) {
        super(context);
        this.mContext = context;
        this.mTitle = title;
        if (list != null && list.size() > 0) {
            if (list.get(0) instanceof ScenarioInfo.Condition) {
                this.mConditionList = (List<ScenarioInfo.Condition>) list;
            } else {
                this.mSequenceList = (List<ScenarioInfo.Sequence>) list;
            }
        }
        this.mDataList = dataList;
        this.type = type;
    }

    /**
     * @param start 开始
     * @param end 结束
     * @param multiple 颗粒度
     * @param intWithoutZero 整数是否带.0
     * @return wheel数值list
     */
    public static List<String> getWheelDoubleData(Double start, Double end, Double multiple, Boolean intWithoutZero) {
        ArrayList<String> list = new ArrayList<>();
        for (Double index = start; index <= end; index += multiple) {
            if ((index.intValue() - index) == 0.0 && intWithoutZero) {
                list.add(String.valueOf(index.intValue()));
            } else {
                list.add(index.toString());
            }
        }
        return list;
    }

    /**
     * @param start 开始
     * @param end 结束
     * @param multiple 颗粒度
     * @return wheel数值list
     */
    public static List<String> getWheelData(Integer start, Integer end, Integer multiple) {
        ArrayList<String> list = new ArrayList<>();
        for (Integer index = start; index <= end; index += multiple) {
            list.add(index.toString());
        }
        return list;
    }

    public void setCategory(String category) {
        this.mCategory = category;
    }

    public void setInputArgValueList(List<K> list) {
        this.mInputArgValueList = list;
    }

    public void setUnit(String unit) {
        this.mUnit = unit;
    }

    public void setWheelDefaultPosition(int position) {
        this.mWheelDefaultPosition = position;
    }

    public void setTitleDesc(String desc) {
        this.mTitleDesc = desc;
    }

    public void setShowLeftDesc(boolean isShowLeftDesc, String desc) {
        this.mIsShowLeftDesc = isShowLeftDesc;
        this.mLeftDesc = desc;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogSingleWheelBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        initClick();
        setupInitData();
        setCanceledOnTouchOutside(true);
    }

    private void setupInitData() {
        int index = -1;
        //如果是动作
        if (preSequence != null) {
            switch (type) {
                case APP_OPEN:
                    index = ObjectUtils.findPreIndexWithMultiInput(preSequence, mSequenceList, mInputArgValueList, 1);
                    break;
                case SINGLE_WHEEL_DOOR_WINDOWS:
                case SINGLE_WHEEL_INPUT_ARG:
                    index = ObjectUtils.findPreIndexWithInput(preSequence, mSequenceList, mInputArgValueList);
                    break;
                default:
                    index = ObjectUtils.findPreIndexWithInput(preSequence, mSequenceList, mDataList);
                    break;
            }
        } else if (preSetCondition != null) {
            //如果是条件
            //如果选项值对应 mDataList
            if (type == SingleWheelType.SINGLE_WHEEL_DEFAULT) {
                index = ObjectUtils.findPreIndexWithInput(preSetCondition, mConditionList, mDataList);
            } else {
                //如果选项值对应 InputArg
                index = ObjectUtils.findPreIndexWithInput(preSetCondition, mConditionList, mInputArgValueList);
            }
        }
        if (index != -1) {
            mSingleWheelView.setCurrentItem(index);
        }
    }

    private void initView() {
        TextView titleTv = findViewById(R.id.tv_weather_title);
        titleTv.setText(mTitle.isEmpty() ? "" : mTitle);
        mSingleWheelView = findViewById(R.id.wheel_weather);
        TextView mDescTv = findViewById(R.id.tv_single_wheel_title_desc);
        TextView mLeftDescTv = findViewById(R.id.tv_single_wheel_left_desc);
        if (ObjectUtils.isNotEmpty(mTitleDesc)) {
            mDescTv.setText(mTitleDesc);
            mDescTv.setVisibility(View.VISIBLE);
            mVB.tvWeatherTitle.setPadding(0, 0, 0, AutoSizeUtils.dp2px(getContext(), 28f));
        }

        if (mIsShowLeftDesc) {
            mLeftDescTv.setVisibility(View.VISIBLE);
            mLeftDescTv.setText(mLeftDesc);
        }
        UIUtils.initWheelViewP42R(mContext, mSingleWheelView, mDataList, mUnit, mWheelDefaultPosition, null,null);
        if (mDataList.size() <= 2) {
            isLoop = false;
        }
        mSingleWheelView.setCyclic(isLoop);
    }

    private void initClick() {
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
    }

    public void setupPreCondition(ScenarioInfo.Condition preSetCondition) {
        this.preSetCondition = preSetCondition;
    }

    public void setupPreAction(ScenarioInfo.Sequence sequence) {
        this.preSequence = sequence;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_normal) {
            if (listener != null)
                listener.cancel();
            dismissWithAnimate();
        } else if (v.getId() == R.id.btn_primary) {
            String unit = mSingleWheelView.label;
            String desc = mLeftDesc + " " + mDataList.get(mSingleWheelView.getCurrentItem()).concat(unit);
            String category = mCategory.isEmpty() ? mTitle : mCategory;
            switch (type) {
                case SINGLE_WHEEL_INPUT_ARG: {
                    if (mSequenceList != null) {
                        if (mSequenceList.size() == 1) {
                            ScenarioInfo.Sequence action = mSequenceList.get(0);
                            if (mInputArgValueList != null && !mInputArgValueList.isEmpty()) {
                                action.getAction().getInput().get(0).setValue(
                                        String.valueOf(mInputArgValueList.get(mSingleWheelView.getCurrentItem())));
                            } else {
                                action.getAction().getInput().get(0).setValue(
                                        String.valueOf(mDataList.get(mSingleWheelView.getCurrentItem()))
                                );
                            }
                            action.getAction().setDesc(desc);
                            action.getAction().setCategory(category);
                            if (listener != null) {
                                listener.selectAction(action);
                            } else {
                                EventBus.getDefault().post(action);
                            }
                        }
                    } else if (mConditionList != null) {
                        if (mConditionList.isEmpty() || mConditionList.get(0).getInput() == null || mConditionList.get(0).getInput().isEmpty())
                            return;
                        ScenarioInfo.Condition condition = mConditionList.get(0);
                        condition.getInput().get(0).setValue(
                                String.valueOf(mInputArgValueList.get(mSingleWheelView.getCurrentItem()))
                        );
                        condition.setCategory(category);
                        condition.setDesc(desc);
                        EventBus.getDefault().post(condition);
                    } else {
                        if (listener != null) {
                            listener.selectValue(mDataList.get(mSingleWheelView.getCurrentItem()),
                                    mSingleWheelView.getCurrentItem());
                        }
                    }
                    break;
                }
                case SINGLE_WHEEL_DOOR_WINDOWS: {
                    if (mSequenceList != null) {
                        if (mSequenceList.size() == 1) {
                            ScenarioInfo.Sequence action = mSequenceList.get(0);
                            if (mInputArgValueList != null && !mInputArgValueList.isEmpty()) {
                                action.getAction().getInput().get(0).setValue(
                                        String.valueOf(mInputArgValueList.get(mSingleWheelView.getCurrentItem())));
                            } else {
                                action.getAction().getInput().get(0).setValue(
                                        String.valueOf(mDataList.get(mSingleWheelView.getCurrentItem()))
                                );
                            }
                            //车窗关闭度的描述
                            if (mSingleWheelView.getCurrentItem() == 0) {
                                desc = mContext.getResources().getString(R.string.scene_text_condition_full_open);
                            } else if (mSingleWheelView.getCurrentItem() == 10) {
                                desc = mContext.getResources().getString(R.string.scene_text_condition_full_close);
                            } else {
                                desc = mContext.getResources().getString(R.string.scene_text_common_close) + " " + desc;
                            }
                            action.getAction().setDesc(desc);
                            action.getAction().setCategory(category);
                            if (listener != null) {
                                listener.selectAction(action);
                            } else {
                                EventBus.getDefault().post(action);
                            }
                        }
                    } else if (mConditionList != null) {
                        if (mConditionList.isEmpty() || mConditionList.get(0).getInput() == null || mConditionList.get(0).getInput().isEmpty())
                            return;
                        ScenarioInfo.Condition condition = mConditionList.get(0);
                        condition.getInput().get(0).setValue(
                                String.valueOf(mInputArgValueList.get(mSingleWheelView.getCurrentItem()))
                        );
                        condition.setCategory(category);
                        condition.setDesc(desc);
                        EventBus.getDefault().post(condition);
                    } else {
                        if (listener != null) {
                            listener.selectValue(mDataList.get(mSingleWheelView.getCurrentItem()),
                                    mSingleWheelView.getCurrentItem());
                        }
                    }
                    break;
                }
                case SINGLE_WHEEL_DEFAULT: {
                    if (mConditionList != null) {
                        if (mConditionList.size() == 1) {
                            ScenarioInfo.Condition condition = mConditionList.get(0);
                            condition.getInput().get(0).setValue(String.valueOf(mDataList.get(mSingleWheelView.getCurrentItem())));
                            condition.setDesc(desc);
                            condition.setCategory(category);
                            EventBus.getDefault().post(condition);
                        }
                    } else if (mSequenceList != null) {
                        if (mSequenceList.size() == 1) {
                            ScenarioInfo.Sequence action = mSequenceList.get(0);
                            action.getAction().getInput().get(0).setValue(
                                    String.valueOf(mDataList.get(mSingleWheelView.getCurrentItem()))
                            );
                            action.getAction().setDesc(desc);
                            action.getAction().setCategory(category);
                            if (listener != null) {
                                listener.selectAction(action);
                            } else {
                                EventBus.getDefault().post(action);
                            }
                        }
                    }
                    break;
                }
                case APP_OPEN: {
                    if (mSequenceList != null && !mSequenceList.isEmpty()) {
                        //打开应用InputArg为status,type
                        ScenarioInfo.Sequence action = mSequenceList.get(0);
                        action.getAction().getInput().get(1).setValue(String.valueOf(mInputArgValueList.get(mSingleWheelView.getCurrentItem())));
                        action.getAction().setDesc(desc);
                        action.getAction().setCategory(category);
                        EventBus.getDefault().post(action);
                    }
                    break;
                }
            }
            dismissWithAnimate();
        }
    }

    public void setOnSceneActionListener(OnSceneActionListener listener) {
        this.listener = listener;
    }

    public interface OnSceneActionListener {
        void selectAction(ScenarioInfo.Sequence sequence);

        void cancel();

        void selectValue(String value, int index);
    }
}
