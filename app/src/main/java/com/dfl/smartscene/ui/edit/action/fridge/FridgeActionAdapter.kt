package com.dfl.smartscene.ui.edit.action.fridge

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.FridgeType

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2025/04/18
 * desc : 动作->冰箱能力adapter
 * version: 1.0
 */
class FridgeActionAdapter : BaseQuickAdapter<ActionSkillItemBean<FridgeType>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_action_dialog_item
), MatchableAdapter<ActionSkillItemBean<FridgeType>> {
    override fun convert(holder: BaseViewHolder, item: ActionSkillItemBean<FridgeType>) {
        holder.getView<AppCompatImageView>(R.id.iv_action_reflex_icon).setImageResource(item.iconId)
        holder.getView<TextView>(R.id.tv_action_reflex_name).text = item.content
    }

    override val adapterData: List<ActionSkillItemBean<FridgeType>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.content.replace("\n", "") == title && !it.actionSkillId.isNullOrEmpty() }
    }
}