package com.dfl.smartscene.ui.overlay.ambientlight

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.dfl.android.animationlib.TabSwitchView
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.ConvertUtils
import com.dfl.android.common.util.StringUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.databinding.SceneDialogAmbientLightColorBinding
import com.dfl.smartscene.soa.getInputByName
import com.dfl.smartscene.soa.getSequenceValueByName
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.SwatchesView
import com.dfl.smartscene.widget.wheel.WheelView
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus


/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/21
 * desc :氛围灯颜色设置弹窗
 * version: 1.0
 * update ：钟文祥
 */
@Deprecated("lk2a弹框组件变更")
class AmbientLightColorDialog(
    context: Context,
    val title: String,
    val list: ArrayList<ScenarioInfo.Sequence>,
    private val wheelList: ArrayList<String>
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogAmbientLightColorBinding

    private var mTitleTv: TextView? = null
    private var mWheelView: WheelView? = null
    private var mDateWheelMask: View? = null
    private var mThemeColorView: View? = null
    private var mColorThemeList = ArrayList<Int>()

    private var mSwatchesView: SwatchesView? = null
    private var preSetSequence: ScenarioInfo.Sequence? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogAmbientLightColorBinding.inflate(layoutInflater)
        setContentView(mVB.root)
        initView()
        setupInitData()
    }

    private fun setupInitData() {
        preSetSequence?.let {
            when (it.action.skillId) {
                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_THEME_ID, //氛围灯颜色主题
                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_THEME //炫彩氛围灯颜色的主题
                -> {
                    val themeMode = getSequenceValueByName(
                        it, SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_THEME_NAME
                    )
                    setCheckMoreThanLight()
                    val find = mColorThemeList.find { theme -> theme.toString() == themeMode }
                    val index = mColorThemeList.indexOf(find)
                    if (index != -1) {
                        mWheelView?.currentItem = index
                    } else {
                        mWheelView?.currentItem = 0
                    }
                }

                SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID, //氛围灯颜色自定义
                SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE //炫彩氛围灯自定义
                -> {
                    val inputArgInfo = getInputByName(
                        it, when (it.action.skillId) {
                            SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID -> SkillsListConstant.INPUT_ARG_ACTION_LIGHT_AMBIENT_LIGHT_COLOR_NAME
                            SkillsListConstant.SKILL_ID_ACTION_SMART_COLORFUL_AMBIENT_LIGHT_COLOR_CUSTOMIZE -> SkillsListConstant.INPUT_ARG_ACTION_COLORFUL_AMBIENT_LIGHT_COLOR_DETAILED_COLOR_NAME
                            else -> ""
                        }
                    )
                    val customColor = inputArgInfo?.value
                    setCheckLessZanLight()
                    if (customColor != null && StringUtils.isNumeric(customColor, false, true)) {
                        if (!TextUtils.isEmpty(inputArgInfo.desc)) {
                            mSwatchesView?.initView(
                                inputArgInfo.desc.split(",")[0].toInt(), inputArgInfo.desc.split(",")[1].toInt()
                            )
                        }
                    }
                }

                else -> {
                    setCheckMoreThanLight()
                }
            }
        }
        if (preSetSequence == null) {
            setCheckMoreThanLight()
        }
    }

    fun setPreSetAmbientAction(sequence: ScenarioInfo.Sequence?) {
        this.preSetSequence = sequence
    }

    fun setAmbientColorThemeValueList(list: ArrayList<Int>) {
        mColorThemeList.clear()
        mColorThemeList.addAll(list)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initView() {
        mTitleTv = findViewById(R.id.tv_light_title)
        mWheelView = findViewById(R.id.wheel_ambient_light)
        mThemeColorView = findViewById(R.id.v_theme_light_bg)
        mDateWheelMask = findViewById(R.id.date_wheel_mask)
        mSwatchesView = findViewById(R.id.swatches_view)

        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mTitleTv?.text = title

        initWheelView(mWheelView!!, wheelList)
        mWheelView?.currentItem = 0

        mSwatchesView?.initView(64, 43)
        mVB.tabSwitchViewCondition.generateViews(
            listOf(
                context.getString(R.string.scene_text_action_theme),
                context.getString(R.string.scene_text_action_optional),
            )
        )
        mVB.tabSwitchViewCondition.setOnTabSelectedListener(object : TabSwitchView.OnTabSelectedListener {
            override fun onTabSelected(position: Int, isManual: Boolean?) {
                if (position == 0) {
                    setCheckMoreThanLight()
                } else {
                    setCheckLessZanLight()
                }
            }

            override fun onTabUnSelected(lastPosition: Int) {
            }

            override fun onInitViewComplete(isComplete: Boolean?) {
            }

        })

    }

    override fun onClick(v: View?) {
        v?.id?.let {
            when (it) {
                R.id.btn_normal -> {
                    dismissWithAnimate()
                }

                R.id.btn_primary -> {
                    val tabSelectedIndex = mVB.tabSwitchViewCondition.selectedIndex
                    val selectedText =
                        (mVB.tabSwitchViewCondition.getTabView(tabSelectedIndex) as TextView).text.toString()
                    val desc: String = if (tabSelectedIndex == 0) {
                        selectedText.plus(" ").plus(wheelList[mWheelView?.currentItem!!])
                    } else {
                        selectedText
                    }
                    val action = list[tabSelectedIndex]
                    val value = if (tabSelectedIndex == 0) {
                        mColorThemeList[mWheelView?.currentItem!!]
                    } else {
                        mSwatchesView?.getSelectColorInt()
                    }
                    CommonLogUtils.logD(
                        "AmbientLightColorDialog", "SelectColor=$value,转为${Integer.toHexString(value!!)}" + ",R=${
                            ConvertUtils.int2HexString(value.red)
                        }," + "G=${ConvertUtils.int2HexString(value.green)}," + "B=${
                            ConvertUtils.int2HexString(
                                value.blue
                            )
                        }"
                    )
                    action.action.input[0].value = value.toString()
                    action.action.input[0].desc = mSwatchesView?.getSelectColorPosition()
                    action.action.category = mTitleTv?.text.toString()
                    action.action.desc = desc
                    EventBus.getDefault().post(action)
                    dismissWithAnimate()
                }
            }
        }
    }

    private fun setCheckLessZanLight() {
        mVB.tabSwitchViewCondition.selectedIndex = 1
        mWheelView?.visibility = View.GONE
        mDateWheelMask?.visibility = View.GONE
        mThemeColorView?.visibility = View.GONE

        mSwatchesView?.visibility = View.VISIBLE
    }

    private fun setCheckMoreThanLight() {
        mVB.tabSwitchViewCondition.selectedIndex = 0
        mWheelView?.visibility = View.VISIBLE
        mDateWheelMask?.visibility = View.VISIBLE
        mThemeColorView?.visibility = View.VISIBLE

        mSwatchesView?.visibility = View.GONE
    }

    private fun initWheelView(wheelView: WheelView, list: List<String>) {
        UIUtils.initWheelView(context, wheelView, list, "", 0)
    }

}