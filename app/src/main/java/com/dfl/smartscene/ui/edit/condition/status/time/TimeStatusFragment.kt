package com.dfl.smartscene.ui.edit.condition.status.time

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusTime
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.time.date.DateSettingDialogFragment
import com.dfl.smartscene.ui.overlay.time.timelimit.TimeLimitDialog
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   :条件->时间页面（包含触发条件和状态条件）
 * version: 1.0
 */
class TimeStatusFragment(
    val skillId: Int, private val preCondition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<TimeStatusAdapter, SceneFragmentEditConditionBinding, TimeStatusViewModel>() {


    private var mAdapter: TimeStatusAdapter? = null


    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<TimeStatusViewModel> {
        return TimeStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        mViewModel.initData(resources, skillId)

        mViewModel.mTimeNameLiveData.observe(viewLifecycleOwner) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()

            if (preCondition == null) return@observe

            if (SceneEditManager.checkIdIsActive(preCondition.skillId, 2)) {
                //isHaveTimeSkillId 的作用解决2级菜单viewpager初始化时会自动先生成第一个时间fragment，此时会发送一个没用参考数
                var isHaveTimeSkillId = false

                it.forEach { conditionItemBean ->
                    if (conditionItemBean.conditionSkillId?.contains(preCondition?.skillId) == true) {
                        isHaveTimeSkillId = true
                    }
                }
                if (isHaveTimeSkillId) {
                    val position = getSimulateClickPosition(preCondition, null)
                    if (position != -1) {
                        view?.let { it1 -> handleItemClick(it1, position) }
                    }
                }
            } else {//没有这个能力
                //发消息通知上一级菜单 还原回去
                LiveEventBus.get<Int>(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER).post(-1)
            }
        }
    }


    override fun getAdapter(): TimeStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = TimeStatusAdapter()

        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }
        mViewDataBinding.rvCondition.adapter = mAdapter
    }

    private fun handleItemClick(v: View, position: Int) {
        mAdapter?.data?.let {
            if (it[position].isEnable == false) {
                return
            }
            if (DebouncingUtils.isValid(v, SceneEditManager.mViewClickDuration)) {
                when (it[position].conditionType) {
                    StatusTime.TIME_INTERVAL -> {
                        showTimeLimitDialog()
                    }

                    StatusTime.TIME_CYCLE -> {
                        showRepeatDialog()
                    }

                    else -> {}
                }
            }
        }
    }

    private fun showTimeLimitDialog() {
        val dialog = TimeLimitDialog(
            context, preCondition, SkillsListConstant.SKILL_ID_STATUS_TIME_INTERVAL_ID
        )
        dialog.showWithAnimate()
    }

    private fun showRepeatDialog() {
        DateSettingDialogFragment.showFragment(childFragmentManager, preCondition)
    }

}