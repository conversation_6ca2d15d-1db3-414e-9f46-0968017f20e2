package com.dfl.smartscene.ui.edit.condition.trigger.drive

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerDrive
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->驾驶view model
 * version: 1.0
 */
class DriveTriggerViewModel : BaseViewModel() {

    val mDriveLiveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerDrive>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerDrive>>()
        val type = SceneEditManager.TriggerType.DRIVE
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_battery_life),
                R.drawable.scene_icon_me_trigger_drive_mileage,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_MORE,
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_LESS
                ),
                TriggerDrive.BATTERY_LIFE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_battery_power),
                R.drawable.scene_icon_me_trigger_drive_battery,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_MORE,
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_LESS
                ),
                TriggerDrive.BATTERY_POWER
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_speed),
                R.drawable.scene_icon_me_trigger_drive_speed,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_MORE,
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_LESS
                ),
                TriggerDrive.DRIVE_SPEED
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_drive_time),
                R.drawable.scene_icon_me_trigger_drive_time,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_MORE),
                TriggerDrive.DRIVE_TIME
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_drive_mile),
                R.drawable.scene_icon_me_trigger_drive_distance,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_MORE,
                    SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_LESS
                ),
                TriggerDrive.DRIVE_DISTANCE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_condition_gear),
                R.drawable.scene_icon_me_trigger_drive_gear,
                arrayListOf(SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_GEAR),
                TriggerDrive.DRIVE_GEAR
            )
        )

        val result = SceneEditManager.checkChildItemIsActive(type, list)
        mDriveLiveData.postValue(result)
    }

    fun getDriveConditionData(
        resources: Resources,
        position: TriggerDrive
    ): ArrayList<ScenarioInfo.Condition> {
        val list = ArrayList<ScenarioInfo.Condition>()
        val skillId: Int
        val skillName: String
        val className: String
        val args = ArrayList<InputArgInfo>()
        when (position) {
            //续航里程
            TriggerDrive.BATTERY_LIFE -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_DISTANCE_TO_EMPTY, ArgType.FLOAT, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_MORE,
                        resources.getString(R.string.scene_text_condition_power_system),
                        args
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_LIFE_LESS,
                        resources.getString(R.string.scene_text_condition_power_system),
                        args
                    )
                )
            }
            //电池电量
            TriggerDrive.BATTERY_POWER -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TRAVEL_HV_BAT_SOC, ArgType.FLOAT, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_MORE,
                        resources.getString(R.string.scene_text_condition_power_system),
                        args
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_BATTERY_POWER_LESS,
                        resources.getString(R.string.scene_text_condition_power_system),
                        args
                    )
                )
            }
            //车速
            TriggerDrive.DRIVE_SPEED -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TRAVEL_VEHICLE_SPEED, ArgType.DOUBLE, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_MORE,
                        resources.getString(R.string.scene_text_edit_add_condition_drive),
                        args
                    )
                )
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_SPEED_LESS,
                        resources.getString(R.string.scene_text_edit_add_condition_drive),
                        args
                    )
                )
            }
            TriggerDrive.DRIVE_TIME -> { //驾驶时长
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TRAVEL_TIME_STATE, ArgType.FLOAT, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_TIME_MORE,
                        "",
                        args
                    )
                )
                //                list.add(
                //                    ScenarioInfo.Condition(
                //                        1,
                //                        "",
                //                        DriveConstant.DRIVE_TIME_LESS_ID,
                //                        "",
                //                        args
                //                    )
                //                )
            }
            TriggerDrive.DRIVE_DISTANCE -> { //驾驶里程（注意这里暂时能力列表没有数据）
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TRAVEL_DISTANCE_STATE, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Condition(
                        1,
                        "",
                        SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_DISTANCE_MORE,
                        "",
                        args
                    )
                )
                //                list.add(
                //                    ScenarioInfo.Condition(
                //                        1,
                //                        "",
                //                        DriveConstant.DRIVE_DISTANCE_LESS_ID,
                //                        "",
                //                        args
                //                    )
                //                )
            }
            else -> {
                skillId = SkillsListConstant.SKILL_ID_TRIGGER_DRIVE_GEAR
                skillName = ""
                className = resources.getString(R.string.scene_text_edit_add_condition_drive)
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_TRAVEL_GEAR_STATE, ArgType.INT32, "0"))
                list.add(ScenarioInfo.Condition(1, skillName, skillId, className, args))
            }
        }


        return list
    }

    fun getGearCheckData(resources: Resources): ArrayList<CheckListBean> {
        val list = ArrayList<CheckListBean>()
        list.add(
            CheckListBean(
                resources.getString(R.string.scene_text_condition_drive_gear_p),
                true,
            )
        )
        list.add(
            CheckListBean(
                resources.getString(R.string.scene_text_condition_drive_gear_n),
                false,
            )
        )
        list.add(
            CheckListBean(
                resources.getString(R.string.scene_text_condition_drive_gear_r),
                false,
            )
        )
        list.add(
            CheckListBean(
                resources.getString(R.string.scene_text_condition_drive_gear_d),
                false,
            )
        )
        return list
    }

    fun getGearInputData(): ArrayList<Int> {
        //挡位只有4个，其他都不需要，p->1,r->2,n->3,d->4
        return arrayListOf(1, 3, 2, 4)
    }

    fun initDriveTimeData(): ArrayList<String> {
        val list = ArrayList<String>()
        var index = 0.0
        while (index <= 24) {
            if ((index.toInt() - index) == 0.0) {
                list.add(index.toInt().toString())
            } else {
                list.add(index.toString())
            }
            index += 0.5
        }
        return list
    }

    /**
     * 生成wheelMap，闭区间
     * @param start 开始
     * @param end 结束
     * @param multiple 颗粒度
     */
    fun getWheelData(start: Double, end: Double, multiple: Double): List<String> {
        val list = ArrayList<String>()
        var index = start
        while (index <= end) {
            if ((index.toInt() - index) == 0.0) {
                list.add(index.toInt().toString())
            } else {
                list.add(index.toString())
            }
            index += multiple
        }
        return list.toList()
    }

    /**
     * radio 键:radio文言,值:能力对应getDriveConditionData位置
     */
    fun getRbMap(): MutableMap<String, String> {
        return mutableMapOf(
            Pair(CommonUtils.getString(R.string.scene_text_common_more_than), "0"),
            Pair(CommonUtils.getString(R.string.scene_text_common_less_than), "1"),
        )
    }

    /**
     * 键wheel的文本值,值 文本对应的InputArg.Value值
     */
    fun getWheelMap(start: Int, end: Int, multiple: Int): MutableMap<String, String> {
        val mutableList = mutableMapOf<String, String>()
        for (i in start..end step multiple) {
            mutableList[i.toString()] = i.toString()
        }
        return mutableList
    }

}