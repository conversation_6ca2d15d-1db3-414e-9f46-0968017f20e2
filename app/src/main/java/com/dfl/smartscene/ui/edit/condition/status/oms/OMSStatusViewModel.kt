package com.dfl.smartscene.ui.edit.condition.status.oms

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.StatusOMS
import com.dfl.smartscene.soa.SceneConditionManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2025/1/2.
 *Describer:
 */
class OMSStatusViewModel : BaseViewModel() {

    val liveData = MutableLiveData<ArrayList<ConditionItemBean<StatusOMS>>>()


    fun initData(resources: Resources, skillId: Int) {
        val list = ArrayList<ConditionItemBean<StatusOMS>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_oms_position_children),
                R.drawable.scene_icon_me_status_cabin_perception_child_testing,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_CHILDREN
                ),
                StatusOMS.OMS_POSITION_CHILDREN
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_oms_position_someone),
                R.drawable.scene_icon_me_status_cabin_perception_distribution_detection,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_SOMEONE
                ),
                StatusOMS.OMS_POSITION_SOMEONE
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_oms_position_gender),
                R.drawable.scene_icon_me_status_cabinperception_gender_detection,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_GENDER
                ),
                StatusOMS.OMS_POSITION_GENDER
            )
        )
        //        list.add(
        //            ConditionItemBean(
        //                ConditionItemBean.SKILL_CONTENT,
        //                resources.getString(R.string.scene_text_edit_condition_oms_position_expression),
        //                R.drawable.scene_icon_me_status_location_position,
        //                arrayListOf(
        //                    SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_EXPRESSION
        //                ),
        //                StatusOMS.OMS_POSITION_EXPRESSION
        //            )
        //        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_oms_position_behavior),
                R.drawable.scene_icon_me_status_cabin_perception_behavior_detection,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_BEHAVIOR
                ),
                StatusOMS.OMS_POSITION_BEHAVIOR
            )
        )

        val type = SceneEditManager.StatusType.OMS
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        SceneConditionManager.checkItemConditionHasAdd(skillId, result)
        liveData.postValue(result)

    }

    fun initLeftWheelMap(resources: Resources, type: StatusOMS): Map<String, String> {
        return when (type) {
            StatusOMS.OMS_POSITION_BEHAVIOR -> mapOf(
                Pair(resources.getString(R.string.scene_text_oms_main_driver), "0"),
                Pair(resources.getString(R.string.scene_text_oms_copilot), "1"),
            )

            else -> mapOf(
                Pair(resources.getString(R.string.scene_text_oms_main_driver), "0"),
                Pair(resources.getString(R.string.scene_text_oms_copilot), "1"),
                Pair(resources.getString(R.string.scene_text_oms_back_row_left), "2"),
                Pair(resources.getString(R.string.scene_text_oms_back_row_center), "3"),
                Pair(resources.getString(R.string.scene_text_oms_back_row_right), "4"),
            )
        }
    }

    fun initRightWheelMap(resources: Resources, type: StatusOMS): Map<String, String> {
        return when (type) {
            StatusOMS.OMS_POSITION_CHILDREN -> {
                mapOf(
                    Pair(resources.getString(R.string.scene_text_oms_has_children), "1"),
                    Pair(resources.getString(R.string.scene_text_oms_no_children), "0"),
                )
            }

            StatusOMS.OMS_POSITION_SOMEONE -> {
                mapOf(
                    Pair(resources.getString(R.string.scene_text_oms_has_people), "1"),
                    Pair(resources.getString(R.string.scene_text_oms_no_people), "0"),
                )
            }

            StatusOMS.OMS_POSITION_GENDER -> {
                mapOf(
                    Pair(resources.getString(R.string.scene_text_oms_man), "0"),
                    Pair(resources.getString(R.string.scene_text_oms_women), "1"),
                )
            }

            StatusOMS.OMS_POSITION_EXPRESSION -> {
                mapOf(
                    Pair(resources.getString(R.string.scene_text_oms_expression0), "0"),
                    Pair(resources.getString(R.string.scene_text_oms_expression1), "1"),
                    Pair(resources.getString(R.string.scene_text_oms_expression2), "2"),
                    Pair(resources.getString(R.string.scene_text_oms_expression3), "3"),
                    Pair(resources.getString(R.string.scene_text_oms_expression4), "4"),
                    Pair(resources.getString(R.string.scene_text_oms_expression5), "5"),
                    Pair(resources.getString(R.string.scene_text_oms_expression6), "6"),
                    Pair(resources.getString(R.string.scene_text_oms_expression7), "7"),
                )
            }

            StatusOMS.OMS_POSITION_BEHAVIOR -> {
                // 0 打电话 2 抽烟  4 喝水
                mapOf(
                    Pair(resources.getString(R.string.scene_text_oms_behavior0), "0"),
                    //Pair(resources.getString(R.string.scene_text_oms_behavior1),2),
                    //Pair(resources.getString(R.string.scene_text_oms_behavior2),4),
                )
            }
        }
    }

    fun getConditionData(type: StatusOMS): ArrayList<ScenarioInfo.Condition> {
        val conditionList = ArrayList<ScenarioInfo.Condition>()
        val inputArgList = ArrayList<InputArgInfo>()
        inputArgList.add(
            InputArgInfo(
                SkillsListConstant.INPUT_ARG_POSITION, ArgType.INT32, "0"
            ) //主驾
        )
        when (type) {
            StatusOMS.OMS_POSITION_CHILDREN -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_SCSTATUS, ArgType.INT32, "1"
                    ) //有儿童
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_CHILDREN, "", inputArgList
                    )
                )
            }

            StatusOMS.OMS_POSITION_SOMEONE -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_SCSTATUS, ArgType.INT32, "1"
                    ) //有人
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_SOMEONE, "", inputArgList
                    )
                )
            }

            StatusOMS.OMS_POSITION_GENDER -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_SCGENDER, ArgType.INT32, "0"
                    ) //男
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_GENDER, "", inputArgList
                    )
                )
            }

            StatusOMS.OMS_POSITION_BEHAVIOR -> {
                inputArgList.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_SCBEHAVIOR, ArgType.INT32, "0"
                    ) //行为 打电话
                )
                conditionList.add(
                    ScenarioInfo.Condition(
                        1, "", SkillsListConstant.SKILL_ID_STATUS_OMS_POSITION_BEHAVIOR, "", inputArgList
                    )
                )
            }
            else -> {}

        }
        return conditionList
    }
}