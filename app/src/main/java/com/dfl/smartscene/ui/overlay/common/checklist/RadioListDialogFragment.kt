package com.dfl.smartscene.ui.overlay.common.checklist

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.common.base.BaseVBDialogFragment
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CarLightType
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.databinding.SceneDialogFragmentRadioListBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.ui.edit.action.light.CarLightActionViewModel
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.ambientlight.OptionAmbientLightColorDialog
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelDialogFragment
import com.dfl.smartscene.ui.overlay.common.doublewheel.DoubleWheelType
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/09/10
 * desc : ui2.0三级对话框选择框列表,4个以内带背景raido
 * version: 1.0
 * @param mTitle 标题
 * @param mCheckListType 不同输入类型影响设置之前的值和确认的值
 * @param mCheckList 选项文本和图像值
 */
open class RadioListDialogFragment<T, K>(
    var mTitle: String,  //标题
    var mCheckListType: CheckListType, //
    var mCheckList: List<CheckListBean>, //选项文本和图像值
) : BaseVBDialogFragment<SceneDialogFragmentRadioListBinding>(), View.OnClickListener, OnRadioItemClickListener {

    @Suppress("UNCHECKED_CAST")
    constructor(
        title: String, //标题
        checkListType: CheckListType, //
        checkList: List<CheckListBean>, //选项文本和图像值
        dataList: List<T>,  //条件或动作
    ) : this(
        title, checkListType, checkList
    ) {
        if (!ObjectUtils.checkScenarioDataIsEmpty(dataList)) {
            if (dataList[0] is ScenarioInfo.Sequence) {
                mSequenceList = dataList as List<ScenarioInfo.Sequence>
            } else if (dataList[0] is ScenarioInfo.Condition) {
                mConditionList = dataList as List<ScenarioInfo.Condition>
            } else {
                throw Error("错误的数据类型")
            }
        }
        mMoreActionTitle = title
    }

    /**是否单选*/
    var isSingleSelect: Boolean = true

    /**传入的动作*/
    private var mSequenceList: List<ScenarioInfo.Sequence>? = null

    /**传入的条件*/
    private var mConditionList: List<ScenarioInfo.Condition>? = null
    var preSetCondition: ScenarioInfo.Condition? = null
    var preSetSequence: ScenarioInfo.Sequence? = null

    /**传入的InputArgValue值*/
    var inputArgValueList: List<K>? = null
    var desc: String? = null
    var mAdapter: RadioCheckAdapter? = null

    /**动作类别*/
    private var mMoreActionTitle: String = ""

    /**周期类别确认按钮事件*/
    var mListener: OnRadioListener? = null

    /**阅读灯自定义的index*/
    private val INDEX_OF_LAMP_CUSTOM: Int = 4

    /**氛围灯自定义的index*/
    private val INDEX_OF_AMBIENT_LIGHT_CUSTOM: Int = 6

    /**对于多选框，选中的有效值*/
    private var multiValidValue: Int = 6

    /**用于实现监听子弹窗确认按钮dismiss事件,实现关闭自身,动效正确*/
    private val onChildPrimaryDismissListener = DialogInterface.OnDismissListener {
        dismissWithAnimate()
    }

    /**用于实现监听确认按钮dismiss事件,实现关闭父弹框*/
    var onPrimaryBtnListener: DialogInterface.OnDismissListener? = null

    interface OnRadioListener {
        fun primarySelect(selectIndexs: ArrayList<Int>)
    }

    /**
     * 初始化视图
     * @param savedInstanceState 保存的实例状态
     */
    override fun initView(savedInstanceState: Bundle?) {
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.tvAllSelect.setOnClickListener(this)
        //标题
        mVB.tvCheckListTitle.text = mTitle
        //标题备注
        if (!ObjectUtils.isEmpty(desc)) {
            mVB.tvDesc.text = desc
            mVB.tvDesc.isVisible = true
            mVB.tvCheckListTitle.setPadding(0, 0, 0, 0)
//            mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 28f), 0,0)
            // 当tv_desc显示时，为tv_check_list_title添加底部边距28dp
//            val layoutParams = mVB.tvDesc.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
//            layoutParams.bottomMargin = AutoSizeUtils.dp2px(context, 28f)
//            mVB.tvDesc.layoutParams = layoutParams
        }
        //全选按钮
        mVB.tvAllSelect.isVisible = !isSingleSelect
        
        // 根据item数量动态调整对话框尺寸
        adjustDialogSize()
        
        //初始化rv
        mAdapter = RadioCheckAdapter(this)
        mAdapter?.addData(mCheckList)
        mVB.rvCheckList.adapter = mAdapter
        mAdapter?.setOnItemClickListener { _: BaseQuickAdapter<*, *>, _: View, position: Int ->
            onItemClick(position)
        }
        
        // 根据item数量动态设置LayoutManager
        val layoutManager = if (mCheckList.size <= 2) {
            // item数量<=2时，使用单列布局
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        } else {
            // item数量>=3时，使用双列网格布局
            GridLayoutManager(context, 2)
        }
        
        UIUtils.initRecyclerView(
            context, mVB.rvCheckList, layoutManager, false
        )
        
        // 动态设置RecyclerView高度
        adjustRecyclerViewHeight()
        
        // 根据布局类型设置不同的间距
        val spacingL = AutoSizeUtils.dp2px(context, 16F)
        val spacingG = AutoSizeUtils.dp2px(context, 20F)
        if (mCheckList.size <= 2) {
            // 单列布局：只设置垂直间距，最后一行间距为0
            mVB.rvCheckList.addItemDecoration(EquallySpaceDecoration(spacingL, 0, false))
        } else {
            // 双列布局：设置水平和垂直间距，最后一行间距为0
            mVB.rvCheckList.addItemDecoration(EquallySpaceDecoration(spacingG, spacingG, false))
        }
        
        // 当item为两列时，调整按钮尺寸
        if (mCheckList.size >= 3) {
            adjustButtonSizeForTwoColumns()
        }
        
        //选中之前的值
        preSetCondition?.let { setupPreSetCondition(it) }
        preSetSequence?.let { setupPreSetSequence(it) }
        //多选校验确认按钮
        checkPrimaryBtnEnable()
    }
    
    /**
     * 根据item数量动态调整RecyclerView高度
     */
    /**
     * 根据item数量动态调整RecyclerView高度
     */
    private fun adjustRecyclerViewHeight() {
        val lp = mVB.rvCheckList.layoutParams
        
        // 检查是否需要为include_dialog_bottom添加顶部边距
        adjustBottomDialogMargin()
        
        when {
            mCheckList.size == 1 -> {
                if (!ObjectUtils.isEmpty(desc)){
                    // 有描述文本：高度120，顶部margin28
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 28f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 120f)
                }else{
                    // 无描述文本：高度120，顶部margin8
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 8f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 120f)
                }
            }
            mCheckList.size == 2 -> {
                if (!ObjectUtils.isEmpty(desc)){
                    // 有描述文本：高度260，顶部margin28
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 28f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 260f)
                }else{
                    // 无描述文本：高度260，顶部margin8
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 8f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 260f)
                }
            }
            mCheckList.size == 3 || mCheckList.size == 4 -> {
                if (!ObjectUtils.isEmpty(desc)){
                    // 有描述文本：高度260，顶部margin28
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 28f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 260f)
                }else{
                    // 无描述文本：高度260，顶部margin8
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 8f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 260f)
                }
            }
            mCheckList.size > 4 -> {
                if (!ObjectUtils.isEmpty(desc)){
                    // 有描述文本：高度317，顶部margin28
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 28f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 317f)
                }else{
                    // 无描述文本：高度317，顶部margin8
                    mVB.rvCheckList.setPadding(0, AutoSizeUtils.dp2px(context, 8f), 0,0)
                    lp.height = AutoSizeUtils.dp2px(context, 317f)
                }
            }
        }
        
        mVB.rvCheckList.layoutParams = lp
    }
    
    /**
     * 根据条件为include_dialog_bottom添加顶部边距
     * 当item数量<5且desc为空时，添加28dp的顶部边距
     */
    private fun adjustBottomDialogMargin() {
        val includeDialogBottom = mVB.includeDialogBottom.root
        val layoutParams = includeDialogBottom.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
        
        // 检查条件：item数量<5 且 desc为空
        if (mCheckList.size < 5 && ObjectUtils.isEmpty(desc)) {
            // 添加28dp的顶部边距
            layoutParams.topMargin = AutoSizeUtils.dp2px(context, 28f)
        } else {
            // 重置顶部边距为0
            layoutParams.topMargin = 0
        }
        
        includeDialogBottom.layoutParams = layoutParams
    }
    
    /**
     * 根据item数量动态调整对话框尺寸
     */
    private fun adjustDialogSize() {
        val dialogContainer = mVB.root.findViewById<androidx.constraintlayout.widget.ConstraintLayout>(
            com.dfl.smartscene.R.id.dialog_container
        ) ?: return
        
        val layoutParams = dialogContainer.layoutParams
        
        when {
            mCheckList.size == 1 -> {
                // 1个item：对话框744*400
                layoutParams.width = AutoSizeUtils.dp2px(context, 744f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 400f)
            }
            mCheckList.size == 2 -> {
                // 2个item：对话框744*548
                layoutParams.width = AutoSizeUtils.dp2px(context, 744f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 548f)
            }
            mCheckList.size == 3 || mCheckList.size == 4 -> {
                // 3个或4个item：对话框1008*548
                layoutParams.width = AutoSizeUtils.dp2px(context, 1008f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 548f)
            }
            mCheckList.size > 4 -> {
                // 大于4个item：对话框1008*605
                layoutParams.width = AutoSizeUtils.dp2px(context, 1008f)
                layoutParams.height = AutoSizeUtils.dp2px(context, 605f)
            }
        }
        
        dialogContainer.layoutParams = layoutParams
    }
    
    /**
     * 当RecyclerView为两列布局时，调整按钮尺寸为424*76
     */
    private fun adjustButtonSizeForTwoColumns() {
        val btnPrimary = mVB.includeDialogBottom.btnPrimary
        val btnNormal = mVB.includeDialogBottom.btnNormal
        
        // 设置按钮尺寸为424*76
        val buttonWidth = AutoSizeUtils.dp2px(context, 424f)
        val buttonHeight = AutoSizeUtils.dp2px(context, 76f)
        
        // 调整主按钮尺寸
        val primaryLayoutParams = btnPrimary.layoutParams
        primaryLayoutParams.width = buttonWidth
        primaryLayoutParams.height = buttonHeight
        btnPrimary.layoutParams = primaryLayoutParams
        
        // 调整次要按钮尺寸
        val normalLayoutParams = btnNormal.layoutParams
        normalLayoutParams.width = buttonWidth
        normalLayoutParams.height = buttonHeight
        btnNormal.layoutParams = normalLayoutParams
    }
    
    /**越界判断，点击rv选项*/
    open fun onItemClick(position: Int) {
        if (position == -1 || position >= mCheckList.size) {
            return
        }
        if (isSingleSelect) { //单选
            //原来选中位置
            var index = -1
            for (i in mCheckList.indices) {
                if (mCheckList[i].isCheck) {
                    index = i
                    break
                }
            }
            //选中的和原来位置一样,直接返回
            if (index == position) return
            if (index != -1) {
                mCheckList[index].isCheck = false
                mAdapter?.notifyItemChanged(index)
            }
            mCheckList[position].isCheck = true
            mAdapter?.notifyItemChanged(position)
        } else { //多选
            mCheckList[position].isCheck = !mCheckList[position].isCheck
            mAdapter?.notifyItemChanged(position)
            checkPrimaryBtnEnable()
        }
    }

    /**检查确认按钮是否能点击，用于周期和多选*/
    private fun checkPrimaryBtnEnable() {
        if (mCheckListType == CheckListType.CONDITION_WEEKLY || mCheckListType == CheckListType.MULTI_INPUT_SINGLE_SEQUENCE_TYPE) { //周期加上多选为空时确定按钮置灰
            var btnPrimaryIsEnable = false
            run breaking@{
                mCheckList.forEach {
                    if (it.isCheck) {
                        btnPrimaryIsEnable = true
                        return@breaking
                    }
                }
            }
            mVB.includeDialogBottom.btnPrimary.isEnable = btnPrimaryIsEnable
        }
    }

    /**动作选中之前的值*/
    private fun setupPreSetCondition(condition: ScenarioInfo.Condition) {
        var index = -1
        when (mCheckListType) {
            CheckListType.DEFAULT_CHECK_MODE -> {
                index = ObjectUtils.findPreIndex(condition, mConditionList)
            }

            CheckListType.QQ_MUSIC_MODE, //
            CheckListType.INPUT_CHECK_MODE -> {
                index = ObjectUtils.findPreIndexWithInput(condition, mConditionList, inputArgValueList)
            }

            CheckListType.DOOR_SWITCH_TYPE -> {
                index = ObjectUtils.findPreIndexWithMultiInput(condition, mConditionList, inputArgValueList, 1)
            }

            CheckListType.CONDITION_WEEKLY -> {
                //输入到adapter数据已经选中好之前到值
                index = -1
            }

            CheckListType.MULTI_INPUT_SINGLE_SEQUENCE_TYPE -> {
                if (ObjectUtils.checkScenarioDataIsEmpty(condition)
                    || ObjectUtils.checkScenarioDataIsEmpty(mConditionList)
                    || ObjectUtils.checkScenarioDataIsEmpty(inputArgValueList)) {
                    return
                }
                condition.input.forEachIndexed { i: Int, input: InputArgInfo ->
                    //防止mCheckList越界，string比较
                    if (input.value == inputArgValueList!![0].toString()) {
                        onItemClick(i)
                    }
                }
            }

            else -> {}
        }
        if (index != -1) {
            if (mCheckListType == CheckListType.DOOR_SWITCH_TYPE) {
                if (mConditionList!![0].input[0].value != condition.input[0].value) {
                    return
                }
            }
            onItemClick(index)
            mVB.rvCheckList.scrollToPosition(index)
        }
    }

    private fun setupPreSetSequence(sequence: ScenarioInfo.Sequence) {
        var index = -1
        when (mCheckListType) {
            CheckListType.DEFAULT_CHECK_MODE -> {
                index = ObjectUtils.findPreIndex(sequence, mSequenceList)
            }

            CheckListType.QQ_MUSIC_MODE, //
            CheckListType.INPUT_CHECK_MODE -> {
                //初始化被选中的radio item
                index =
                        //阅读灯调节自定义
                    if (mTitle == resources.getString(R.string.scene_text_action_reading_light_mode) && sequence.action.skillId == SkillsListConstant.SKILL_ID_ACTION_LIGHT_READ_LIGHT_CUSTOM) {
                        INDEX_OF_LAMP_CUSTOM
                    }
                    //氛围灯颜色自定义
                    else if (mTitle == resources.getString(R.string.scene_text_action_ambient_light_color) && sequence.action.skillId == SkillsListConstant.SKILL_ID_ACTION_LIGHT_AMBIENT_LAMP_COLOR_ID) {
                        INDEX_OF_AMBIENT_LIGHT_CUSTOM
                    } else {
                        ObjectUtils.findPreIndexWithInput(sequence, mSequenceList, inputArgValueList)
                    }
            }

            CheckListType.DOOR_SWITCH_TYPE -> {
                index = ObjectUtils.findPreIndexWithMultiInput(sequence, mSequenceList, inputArgValueList, 1)
            }

            CheckListType.APP_OPEN -> {
                index = ObjectUtils.findPreIndexWithMultiInput(sequence, mSequenceList, inputArgValueList, 1)
            }

            CheckListType.MULTI_INPUT_SINGLE_SEQUENCE_TYPE -> {
                if (ObjectUtils.checkScenarioDataIsEmpty(sequence)
                    || ObjectUtils.checkScenarioDataIsEmpty(mConditionList)
                    || ObjectUtils.checkScenarioDataIsEmpty(inputArgValueList)) {
                    return
                }
                sequence.action.input.forEachIndexed { i: Int, input: InputArgInfo ->
                    if (input.value == inputArgValueList!![0]) {
                        onItemClick(i)
                    }
                }
            }

            else -> {}
        }
        if (index != -1) {
            onItemClick(index)
        }
    }

    override fun onClick(v: View) {
        if (!DebouncingUtils.isValid(v, SceneEditManager.mViewClickDuration)) return
        when (v.id) {
            R.id.btn_normal -> {
                dismissWithAnimate()

            }

            R.id.btn_primary -> {
                //先获取选中位置
                storeFinalIndex()
                dismissWithAnimate()
                onPrimaryBtnListener?.onDismiss(dialog)
            }

            R.id.tv_all_select -> {
                var isNeedChange = false
                mCheckList.forEach { checkListBean ->
                    if (!checkListBean.isCheck) {
                        checkListBean.isCheck = true
                        isNeedChange = true
                    }
                }
                if (isNeedChange) {
                    mAdapter?.notifyItemRangeChanged(0, mCheckList.size)
                    checkPrimaryBtnEnable()
                }
            }
        }
    }

    /**确定提交*/
    private fun storeFinalIndex() {
        var index = 0
        if (isSingleSelect) {
            for (i in mCheckList.indices) {
                if (mCheckList[i].isCheck) {
                    index = i
                    break
                }
            }
        }

        when (mCheckListType) {
            CheckListType.DEFAULT_CHECK_MODE -> {
                mSequenceList?.let {
                    //越界判断
                    if (index >= it.size) {
                        return
                    }
                    val action = it[index]
                    action.action.desc = mCheckList[index].content
                    action.action.category = mTitle
                    EventBus.getDefault().post(action)
                }
                mConditionList?.let {
                    //越界判断
                    if (index >= it.size) {
                        return
                    }
                    val condition = it[index]
                    condition.desc = mCheckList[index].content
                    condition.category = mTitle
                    EventBus.getDefault().post(condition)
                }
            }

            CheckListType.QQ_MUSIC_MODE, //
            CheckListType.INPUT_CHECK_MODE -> {
                //非空越界判断
                if (!ObjectUtils.isEmpty(inputArgValueList) && inputArgValueList!!.size > index) {
                    if (!ObjectUtils.checkScenarioDataIsEmpty(mSequenceList)) {
                        val action = mSequenceList!![0]
                        action.action.input[0].value = inputArgValueList!![index].toString()
                        action.action.desc = mCheckList[index].content
                        action.action.category = mTitle
                        if (mCheckListType == CheckListType.QQ_MUSIC_MODE) {
                            action.action.category = getString(R.string.scene_text_action_play_qq)
                        }
                        EventBus.getDefault().post(action)
                    } else if (!ObjectUtils.checkScenarioDataIsEmpty(mConditionList)) {
                        val condition = mConditionList!![0]
                        condition.input[0].value = inputArgValueList!![index].toString()
                        condition.desc = mCheckList[index].content
                        condition.category = mTitle
                        EventBus.getDefault().post(condition)
                    }
                } else if (preSetSequence != null) {
                    //灯光选中自定义后操作
                    EventBus.getDefault().post(preSetSequence)
                } else if (preSetCondition != null) {
                    EventBus.getDefault().post(preSetCondition)
                }
            }

            CheckListType.DOOR_SWITCH_TYPE -> {
                //非空越界判断
                if (!ObjectUtils.isEmpty(inputArgValueList) && inputArgValueList!!.size > index
                    && !ObjectUtils.checkScenarioDataIsEmpty(mConditionList) && mConditionList!![0].input.size > 1) {
                    val condition = mConditionList!![0]
                    //index=1设置车门开关,index=0是车门
                    condition.input[1].value = inputArgValueList!![index].toString()
                    condition.desc = mCheckList[index].content
                    condition.category = mTitle
                    EventBus.getDefault().post(condition)
                }
            }

            CheckListType.APP_OPEN -> {
                if (!ObjectUtils.isEmpty(inputArgValueList) && inputArgValueList!!.size > index
                    && !ObjectUtils.checkScenarioDataIsEmpty(mSequenceList) && mSequenceList!![0].action.input.size > 1) {
                    val sequence = mSequenceList!![0]
                    sequence.action.input[1].value = inputArgValueList!![index].toString()
                    sequence.action.desc = mCheckList[index].content
                    sequence.action.category = mTitle
                    EventBus.getDefault().post(sequence)
                }
            }

            CheckListType.CONDITION_WEEKLY -> {
                mListener?.let {
                    val list = arrayListOf<Int>()
                    mCheckList.forEachIndexed { index, checkListBean ->
                        if (checkListBean.isCheck) {
                            list.add(index)
                        }
                    }
                    it.primarySelect(list)
                }
            }

            CheckListType.MULTI_INPUT_SINGLE_SEQUENCE_TYPE -> {
                //校验inputArg数量>=2
                if (!ObjectUtils.isEmpty(inputArgValueList) && inputArgValueList!!.size >= 2) {
                    if (!ObjectUtils.checkScenarioDataIsEmpty(mSequenceList)) {
                        val action = mSequenceList!![0].action
                        mCheckList.forEachIndexed { i, checkListBean: CheckListBean ->
                            //越界判断，inputArgValueList索引0选中值，1未选中值
                            if (i < mSequenceList!![0].action.input.size) {
                                mSequenceList!![0].action.input[i].value = if (checkListBean.isCheck) {
                                    action.desc += checkListBean.content + " "
                                    inputArgValueList!![0].toString()
                                } else {
                                    inputArgValueList!![1].toString()
                                }
                            }
                        }
                        //去掉最后一个空格
                        action.desc = action.desc.substring(0, action.desc.length - 1)
                        action.category = mTitle
                        EventBus.getDefault().post(action)
                    } else if (!ObjectUtils.checkScenarioDataIsEmpty(mConditionList)) {
                        val condition = mConditionList!![0]
                        mCheckList.forEachIndexed { i, checkListBean: CheckListBean ->
                            //越界判断，inputArgValueList索引0选中值，1未选中值
                            if (i < condition.input.size) {
                                condition.input[i].value = if (checkListBean.isCheck) {
                                    condition.desc += checkListBean.content + " "
                                    inputArgValueList!![0].toString()
                                } else {
                                    inputArgValueList!![1].toString()
                                }
                            }
                        }
                        //去掉最后一个空格
                        condition.desc = condition.desc.substring(0, condition.desc.length - 1)
                        condition.category = mTitle
                        EventBus.getDefault().post(condition)
                    }
                }
            }

            else -> {
                //不在这个对话框
            }
        }
    }

    override fun onRadioItemClick(position: Int) {
        if (mMoreActionTitle == resources.getString(R.string.scene_text_action_ambient_light_color)) {
            val dialog = OptionAmbientLightColorDialog(
                requireContext(), mMoreActionTitle
            )
            dialog.isLevel4OrAbove = true
            dialog.setPreSetAmbientAction(preSetSequence)
            dialog.onPrimaryBtnListener = onChildPrimaryDismissListener
            dialog.showWithAnimate()
        } else if (mMoreActionTitle == resources.getString(R.string.scene_text_action_reading_light_mode)) {
            val mCarLightActionViewModel = CarLightActionViewModel()
            val dialog = DoubleWheelDialogFragment(
                mMoreActionTitle,
                mCarLightActionViewModel.initLeftWheelMap(),
                mCarLightActionViewModel.initRightWheelMap(),
                mCarLightActionViewModel.getActionData(CarLightType.CAR_READ_LAMP_ADJUST_CUSTOM),
                DoubleWheelType.SINGLE_SKILL,
            )
            dialog.isLevel4OrAbove = true
            dialog.setupPreAction(preSetSequence)
            dialog.setUnit(
                resources.getString(R.string.scene_text_indicator_unit_percentage),
                resources.getString(R.string.scene_text_indicator_unit_percentage)
            )
            dialog.setPrefixText(
                resources.getString(R.string.scene_text_brightness),
                resources.getString(R.string.scene_text_color_temp),
            )
            dialog.setWheelDefaultPosition(49, 49)
            dialog.setDialogTitle(resources.getString(R.string.scene_text_action_optional))
            dialog.onPrimaryBtnListener = onChildPrimaryDismissListener
            dialog.show(parentFragmentManager, "showReadLampAdjustDialog")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mAdapter?.detachListener()
    }
}