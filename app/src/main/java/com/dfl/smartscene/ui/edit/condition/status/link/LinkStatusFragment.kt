package com.dfl.smartscene.ui.edit.condition.status.link

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.StatusLink
import com.dfl.smartscene.databinding.SceneFragmentEditConditionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->连接
 * version: 1.0
 * isAddCondition:判断是添加条件还是编辑条件（如果是添加条件需要判断是否已添加此条件）
 */
class LinkStatusFragment(
    private val skillId: Int, private val preCondition: ScenarioInfo.Condition?
) : MVVMAdapterFragment<LinkStatusAdapter, SceneFragmentEditConditionBinding, LinkStatusViewModel>() {
    private var mAdapter: LinkStatusAdapter? = null
    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_condition
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<LinkStatusViewModel> {
        return LinkStatusViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        mAdapter = LinkStatusAdapter()
        mViewModel.linkConditionLiveData.observe(
            viewLifecycleOwner
        ) {
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(preCondition, null)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
        mViewModel.initData(resources, skillId)
    }

    override fun getAdapter(): LinkStatusAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        mViewDataBinding.rvCondition.adapter = mAdapter
        mAdapter?.setOnItemClickListener { _, v, position ->
            run {
                handleItemClick(v, position)
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (mAdapter?.data?.get(position)?.isEnable == false) {
            return
        }
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.get(position)?.let {
                if (it.isEnable == false || it.conditionType == null) {
                    return@let
                }
                showRadioListDialog(it.conditionName, it.conditionType)
            }
        }
    }

    private fun showRadioListDialog(title: String, type: StatusLink) {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int>
        if (type == StatusLink.MOBILE) {
            dialog = RadioListDialogFragment(
                title,
                CheckListType.INPUT_CHECK_MODE,
                mViewModel.getListData(resources, type),
                mViewModel.getLinkStatusConditionList(type)
            )
            dialog.inputArgValueList = mViewModel.getInputArgInfoValueData(type)
        } else {
            dialog = RadioListDialogFragment(
                title,
                CheckListType.DEFAULT_CHECK_MODE,
                mViewModel.getListData(resources, type),
                mViewModel.getLinkStatusConditionList(type)
            )
        }
        dialog.preSetCondition = preCondition
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }
}