package com.dfl.smartscene.ui.overlay.apply

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.customapi.*
import com.dfl.smartscene.communication.SearchAddressManager
import com.dfl.smartscene.databinding.SceneDialogFragmentSearchAddressBinding
import com.dfl.smartscene.ui.edit.action.apply.adapter.SearchAddressAdapter
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.ClearEditText
import com.dfl.soacenter.entity.PoiItem
import com.dfl.soacenter.entity.ReqPlanRoute
import com.dfl.soacenter.entity.RoutePoi
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import java.lang.ref.WeakReference


/**
 *Created by 钟文祥 on 2024/5/22.
 *Describer: 去某地 输入弹框
 */
class SearchAddressDialogFragment : BaseDialogFragment() {
    private var mBinding: SceneDialogFragmentSearchAddressBinding? = null
    private val viewBinding get() = mBinding!!

    var title: String? = null
    var onInputListener: OnInputListener? = null
    private var isDisShowBtnAddMid = false //是否隐藏按钮【添加途经点】
    private var isSearchInputSuccess = false //是否搜索结果列表选择到输入框成功

    private var endPoiInfo: PoiItem? = null    //目的地
    private var midPoiInfo: PoiItem? = null    //途径点

    private var mAdapter: SearchAddressAdapter? = null
    private var mInputSelectType: InputSelectType = InputSelectType.End //选择哪个输入了，默认目的地，不是点击哪个输入框

    /**确定按钮是否允许点击*/
    private var isAllow: Boolean = false
    private val imeOptions: Int = EditorInfo.IME_ACTION_DONE

    interface OnInputListener {
        fun onSelectPoint(endPoint: RoutePoi) {} //返回终点实体
        fun onInput(jsonInputData: String, isAddWayside: Boolean? = false)
        fun cancel() {}
    }

    //选择哪个输入
    enum class InputSelectType {
        None, //没有选择
        End, //目的地
        Mid  //途径地
    }

    companion object {
        fun getInstance(): SearchAddressDialogFragment {
            return SearchAddressDialogFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        mBinding = SceneDialogFragmentSearchAddressBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        viewBinding.tvTitle.text = title
        if (isDisShowBtnAddMid) {
            viewBinding.tvMidAddress.visibility = View.GONE //添加途经点按钮
            viewBinding.ivInputEnd.visibility = View.GONE
            viewBinding.etEditInputEnd.hint = getString(R.string.scene_edit_input_hint_address)
        } else {
            viewBinding.tvMidAddress.visibility = View.VISIBLE //添加途经点按钮
        }
        viewBinding.clMidAddress.visibility = View.GONE //途经点view
        viewBinding.ivCloseEndAddress.visibility = View.GONE //目标点大叉

        initRecycleView()
        initListener()
        addEditChangeListener()
        updateTvConfirmEnabled()
    }

    private fun initListener() {
        viewBinding.tvMidAddress.setOnClickListener(MyOnClickListener(this@SearchAddressDialogFragment))
        viewBinding.ivSearchAddress.setOnClickListener(MyOnClickListener(this@SearchAddressDialogFragment))
        viewBinding.ivWaysideAddressClose.setOnClickListener(MyOnClickListener(this@SearchAddressDialogFragment))
        viewBinding.ivCloseEndAddress.setOnClickListener(MyOnClickListener(this@SearchAddressDialogFragment))
        viewBinding.etEditInputMid.setOnClickListener(MyOnClickListener(this@SearchAddressDialogFragment))

        viewBinding.etEditInputEnd.imeOptions = imeOptions
        viewBinding.etEditInputMid.imeOptions = imeOptions
        viewBinding.etEditInputEnd.setOnEditorActionListener(MyOnEditorActionListener(this@SearchAddressDialogFragment))
        viewBinding.etEditInputMid.setOnEditorActionListener(MyOnEditorActionListener(this@SearchAddressDialogFragment))
    }

    private fun initRecycleView() {
        mAdapter = SearchAddressAdapter()
        viewBinding.rvSearchAddress.adapter = mAdapter

        //滑动事件
        viewBinding.rvSearchAddress.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        })

        //列表点击事件
        mAdapter?.setOnItemClickListener { _, _, position ->
            mAdapter?.data?.let {
                viewBinding.slSearchAddress.visibility = View.GONE

                isSearchInputSuccess = true
                if (mInputSelectType == InputSelectType.End) {

                    endPoiInfo = it[position]
                    viewBinding.etEditInputEnd.setText(endPoiInfo?.name)
                    if (viewBinding.clMidAddress.visibility == View.VISIBLE && viewBinding.etEditInputMid.text.toString()
                                .isEmpty()
                    ) { //途经点显示为空，途经点需要焦点
                        viewBinding.etEditInputEnd.clearFocus()
                        mInputSelectType = InputSelectType.Mid
                        viewBinding.etEditInputMid.requestFocus()
                        //                        showSoftInput()
                    } else { //焦点一直在目标点
                        mInputSelectType = InputSelectType.End
                        viewBinding.etEditInputEnd.setSelection(viewBinding.etEditInputEnd.length())
                    }
                } else if (mInputSelectType == InputSelectType.Mid) {
                    midPoiInfo = it[position]
                    viewBinding.etEditInputMid.setText(midPoiInfo?.name)
                    if (viewBinding.etEditInputEnd.text.toString().isEmpty()) { //目标地为空，需要焦点
                        viewBinding.etEditInputMid.clearFocus()
                        mInputSelectType = InputSelectType.End
                        viewBinding.etEditInputEnd.requestFocus()
                        //                        showSoftInput()
                    } else { //焦点一直在途径点
                        mInputSelectType = InputSelectType.Mid
                        viewBinding.etEditInputMid.selectAll()
                    }
                }
                updateTvConfirmEnabled()
                //                showSoftInput()
            }
        }
    }

    private fun showSoftInput() {
        if (mInputSelectType == InputSelectType.End) {
            KeyboardUtils.showSoftInput(viewBinding.etEditInputEnd)
        } else {
            KeyboardUtils.showSoftInput(viewBinding.etEditInputMid)
        }
    }

    /**改变确定按钮的形态*/
    private fun updateTvConfirmEnabled() {
        isAllow = false
        if (viewBinding.clMidAddress.visibility == View.VISIBLE) { //途经点显示时，需要两个有地址才能有效
            if (endPoiInfo != null && midPoiInfo != null) {
                isAllow = true
            }
        } else {
            if (endPoiInfo != null) {
                isAllow = true
            }
        }

        val editTexts = if (mInputSelectType == InputSelectType.End) {
            listOf<EditText>(viewBinding.etEditInputEnd, viewBinding.etEditInputMid)
        } else {
            listOf<EditText>(viewBinding.etEditInputMid, viewBinding.etEditInputEnd)
        }
        UIUtils.setKeyBroadInputAllow(isAllow, editTexts, activity, dialog)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun addEditChangeListener() {
        //目标点输入后事件，搜索
        viewBinding.etEditInputEnd.setCloseListener(object : ClearEditText.OnCloseListener {
            override fun close() {
                updateTvConfirmEnabled()
            }
        })
        viewBinding.etEditInputEnd.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            @SuppressLint("NotifyDataSetChanged")
            override fun afterTextChanged(s: Editable?) {
                if (!isSearchInputSuccess) {
                    endPoiInfo = null
                    mInputSelectType = InputSelectType.End

                    val content = viewBinding.etEditInputEnd.text.toString()
                    if (content.isNotEmpty()) {
                        SearchAddressManager.sendKeyWordToNavi(content)
                    } else {
                        viewBinding.slSearchAddress.visibility = View.GONE
                    }
                } else {
                    isSearchInputSuccess = false
                }
            }
        })

        viewBinding.etEditInputMid.setCloseListener(object : ClearEditText.OnCloseListener {
            override fun close() {
                updateTvConfirmEnabled()
            }
        })
        //途经点输入后事件，搜索
        viewBinding.etEditInputMid.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                if (viewBinding.clMidAddress.visibility == View.VISIBLE) { //有途径地的情况下
                    if (!isSearchInputSuccess) {
                        midPoiInfo = null
                        mInputSelectType = InputSelectType.Mid

                        val content = viewBinding.etEditInputMid.text.toString()
                        if (content.isNotEmpty()) {
                            SearchAddressManager.sendKeyWordToNavi(content)
                        }
                    } else {
                        isSearchInputSuccess = false
                    }
                } else { //点击 大叉都会进来
                    if (mInputSelectType == InputSelectType.End) {
                        endPoiInfo = midPoiInfo
                        midPoiInfo = null
                    } else if (mInputSelectType == InputSelectType.Mid) {
                    }
                    mInputSelectType = InputSelectType.None
                }
            }
        })

    }


    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        LiveEventBus.get<List<PoiItem>>(GlobalLiveEventConstants.KEY_SEARCH_ADDRESS_RESULT_CALLBACK)
            .observe(viewLifecycleOwner) {
                if (it.isNotEmpty()) {
                    //出现异常数据地址名为空还有返回列表的情况直接进行数据加载前的拦截
                    if (it[0].name.isNullOrEmpty()) {
                        CommonToastUtils.show(getString(R.string.scene_txt_navi_address_get_fail_tips))
                        return@observe
                    }
                    //筛选过滤掉，经纬度都为0的情况,否则无法正常发起导航
                    val filterList = ArrayList<PoiItem>()
                    for (poiItem in it) {
                        if (poiItem.point?.lat != 0.toDouble() && poiItem.point?.lon != 0.toDouble()) {
                            filterList.add(poiItem)
                        }
                    }
                    mAdapter?.data = ArrayList(filterList)
                    val keyWord = if (mInputSelectType == InputSelectType.End) {
                        viewBinding.etEditInputEnd.text.toString()
                    } else {
                        viewBinding.etEditInputMid.text.toString()
                    }
                    mAdapter?.setKeyWord(keyWord)

                    viewBinding.slSearchAddress.visibility = View.VISIBLE
                    mAdapter?.notifyDataSetChanged()
                    viewBinding.slSearchAddress.post { //恢复初始化位置
                        viewBinding.slSearchAddress.scrollY = 0
                    }
                }
            }
    }


    class MyOnEditorActionListener(dialogFragment: SearchAddressDialogFragment) : OnEditorActionListener {
        private var reference: WeakReference<SearchAddressDialogFragment> =
            WeakReference<SearchAddressDialogFragment>(dialogFragment)

        //确定事件
        override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            reference.get()?.let {
                if (actionId == it.imeOptions) {
                    if (it.isAllow || (event?.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                        if (it.onInputListener != null) {
                            it.checkInputText()
                        }
                    }
                }
            }
            return true
        }
    }

    private fun checkInputText() {
        val inputText = viewBinding.etEditInputEnd.text.toString()
        val waySideText = viewBinding.etEditInputMid.text.toString()
        val isAddWaySide = viewBinding.clMidAddress.visibility == View.VISIBLE
        if (waySideText.isEmpty() && isAddWaySide && !isDisShowBtnAddMid) {
            CommonToastUtils.show(getString(R.string.scene_edit_input_hint_wayside_address))
            return
        } else if (inputText.isEmpty()) {
            CommonToastUtils.show(getString(R.string.scene_edit_input_hint_destination_address))
            return
        }

        val endRouteBean = RoutePoi(
            endPoiInfo?.name,
            endPoiInfo?.addr,
            endPoiInfo?.point?.lon ?: 0.0,
            endPoiInfo?.point?.lat ?: 0.0,
            endPoiInfo?.typeCode,
            endPoiInfo?.adCode,
            endPoiInfo?.pid
        )
        onInputListener?.onSelectPoint(endRouteBean)

        val midPois = if (midPoiInfo != null) {
            val midRouteBean = RoutePoi(
                midPoiInfo?.name,
                midPoiInfo?.addr,
                midPoiInfo?.point?.lon ?: 0.0,
                midPoiInfo?.point?.lat ?: 0.0,
                midPoiInfo?.typeCode,
                midPoiInfo?.adCode,
                midPoiInfo?.pid
            )
            arrayListOf(midRouteBean)
        } else {
            null
        }
        val json = Gson().toJson(ReqPlanRoute(endPoi = endRouteBean, midPois = midPois))
        onInputListener?.onInput(json) //保存一个发起规划的ReqPlanRoute的json

        dialog?.window?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }

    //事件
    class MyOnClickListener(dialogFragment: SearchAddressDialogFragment) : View.OnClickListener {
        private var reference: WeakReference<SearchAddressDialogFragment> =
            WeakReference<SearchAddressDialogFragment>(dialogFragment)

        override fun onClick(v: View?) {
            if (!DebouncingUtils.isValid(v!!, SceneEditManager.mViewClickDuration)) return
            reference.get()?.let {
                when (v.id) {
                    R.id.iv_search_address -> { //左上角叉
                        it.dialog?.window?.let {
                            KeyboardUtils.hideSoftInput(it)
                        }
                        it.onInputListener?.cancel()
                        it.dismissWithAnimate()
                    }

                    R.id.tv_mid_address -> { //添加途经点按钮
                        it.viewBinding.tvMidAddress.visibility = View.GONE
                        it.viewBinding.clMidAddress.visibility = View.VISIBLE
                        it.viewBinding.ivCloseEndAddress.visibility = View.VISIBLE

                        it.updateTvConfirmEnabled()
                        it.mInputSelectType = InputSelectType.Mid
                        it.showSoftInput()
                    }

                    R.id.iv_wayside_address_close -> { //途经点大叉删除
                        it.mInputSelectType = InputSelectType.Mid
                        closeWayside()
                        it.updateTvConfirmEnabled()
                    }

                    R.id.iv_close_end_address -> { //目标点大叉删除，将途径内容给目标地
                        it.mInputSelectType = InputSelectType.End
                        it.viewBinding.ivCloseEndAddress.visibility = View.GONE

                        val temp = it.viewBinding.etEditInputMid.text.toString()
                        it.isSearchInputSuccess = true
                        closeWayside()
                        it.viewBinding.etEditInputEnd.setText(temp)
                        it.updateTvConfirmEnabled()
                    }

                }
            }
        }

        //途经点大叉删除
        private fun closeWayside() {
            reference.get()?.let {

                it.viewBinding.tvMidAddress.visibility = View.VISIBLE
                it.viewBinding.clMidAddress.visibility = View.GONE //途径地view 隐藏
                it.viewBinding.ivCloseEndAddress.visibility = View.GONE

                it.viewBinding.etEditInputMid.setText("")
                it.mInputSelectType = InputSelectType.End
                it.showSoftInput()
            }

        }
    }


    override fun onStart() {
        super.onStart()
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        when (mInputSelectType) {
            InputSelectType.End -> {
                viewBinding.etEditInputEnd.post {
                    showSoftInput()
                }
            }

            InputSelectType.Mid -> {
                viewBinding.etEditInputMid.post {
                    showSoftInput()
                }
            }

            else -> {
                dialog?.window?.let {
                    KeyboardUtils.hideSoftInput(it)
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mBinding = null
    }

    override fun getLayoutId(): Int {
        return -1
    }


    //====================对外方法========================

    //不显示添加途径按钮
    fun setMidAddressDisShow() {
        isDisShowBtnAddMid = true
    }
}