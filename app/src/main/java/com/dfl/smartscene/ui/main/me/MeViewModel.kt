package com.dfl.smartscene.ui.main.me

import android.content.res.Resources
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.soa.SceneSortManager

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/19
 * desc   :
 * version: 1.0
 */
class MeViewModel : BaseViewModel() {
    // resources
    private var mDefaultSceneNamePrefix: String? = null
    private var mFirstDefaultSceneName: String? = null
    fun initResources(resources: Resources) {
        mDefaultSceneNamePrefix = resources.getString(R.string.scene_text_common_name)
        mFirstDefaultSceneName = resources.getString(R.string.scene_text_default_scene_name)
    }

    fun getNewSceneName(): String {
        val firstDefaultSceneName = mFirstDefaultSceneName ?: ""
        val defaultSceneNamePrefix = mDefaultSceneNamePrefix ?: ""
        return getNewSceneName(firstDefaultSceneName, defaultSceneNamePrefix)
    }

    fun saveSceneSequence(list: List<MySceneBean>) {
        SceneSortManager.saveSceneSequence(list)
    }

    fun startScene(myScene: MySceneBean): Boolean {
        val scenarioId = myScene.scenario.scenarioInfo?.scenarioId
        scenarioId ?: return false
        return SceneManager.startScene(scenarioId)
    }

    fun stopScene(myScene: MySceneBean): Boolean {
        val scenarioId = myScene.scenario.scenarioInfo?.scenarioId
        scenarioId ?: return false
        return SceneManager.stopScene(scenarioId)
    }

    fun deleteScene(myScene: MySceneBean): Boolean {
        return SceneManager.deleteSceneInfo(myScene)
    }

    /**
     * 自动执行场景
     * @param myScene 场景
     * @param exeState 执行状态，0表示ccm允许自动执行
     * @param autoRunBtnState 自动执行按钮状态，0表示开关是开的
     */
    fun autoExecuteScenario(myScene: MySceneBean, exeState: Int, autoRunBtnState: Int, executeFrequency: Int) {
        myScene.scenario.scenarioInfo?.let {
            SceneManager.autoExecuteScene(exeState, myScene.scenario, autoRunBtnState, executeFrequency)
        }
    }

    private fun getNewSceneName(firstDefaultSceneName: String, defaultSceneNamePrefix: String): String {
        return SceneManager.getNewSceneName(firstDefaultSceneName, defaultSceneNamePrefix)
    }

    fun getNewSceneId(): String {
        return SceneManager.getNewSceneId()
    }

}