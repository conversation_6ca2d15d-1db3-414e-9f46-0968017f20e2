package com.dfl.smartscene.ui.edit.action.smart

import android.annotation.SuppressLint
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dfl.android.common.base.MVVMAdapterFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.SmartDeviceType
import com.dfl.smartscene.communication.CarControlLocalManager
import com.dfl.smartscene.databinding.SceneFragmentEditActionBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.ambientlight.AmbientLightColorDialog
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.common.indicatorseekbar.IndicatorSeekBarDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelDialog
import com.dfl.smartscene.ui.overlay.common.singlewheel.SingleWheelType
import com.dfl.smartscene.ui.overlay.smart.BuySmartDeviceDialog
import com.dfl.smartscene.ui.overlay.smart.SmartDeviceNoBindDialog
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/26
 * desc :动作->智能设备页面
 * version: 1.0
 */
class SmartDeviceActionFragment(private val sequence: ScenarioInfo.Sequence?) :
    MVVMAdapterFragment<SmartDeviceActionAdapter, SceneFragmentEditActionBinding, SmartDeviceActionViewModel>() {

    private var mAdapter: SmartDeviceActionAdapter? = null
    private var mItemDecoration: MyGridItemDecoration? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_edit_action
    }

    override fun getBindingVariable(): Int {
        return 0
    }

    override fun getViewModelClass(): Class<SmartDeviceActionViewModel> {
        return SmartDeviceActionViewModel::class.java
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        mViewModel.initData(resources, CarControlLocalManager.getAiDeviceState())
        LiveEventBus.get<ArrayList<String>>(GlobalLiveEventConstants.CAR_CONTROL_SMART_DEVICE_STATE)
            .observe(viewLifecycleOwner) {
                mViewModel.initData(resources, it)
            }

        mViewModel.mSmartDeviceLiveData.observe(viewLifecycleOwner) {
            if (mAdapter?.data?.size == 0) {
                mItemDecoration = MyGridItemDecoration()
                mItemDecoration?.setData(it)
            } else {
                mItemDecoration?.setData(it)
            }
            mAdapter?.data = it
            mAdapter?.notifyDataSetChanged()
            val position = getSimulateClickPosition(null, sequence)
            if (position != -1) {
                view?.let { it1 -> handleItemClick(it1, position) }
            }
        }
    }

    private fun handleItemClick(view: View, position: Int) {
        if (DebouncingUtils.isValid(view, SceneEditManager.mViewClickDuration)) {
            mAdapter?.data?.let {
                if (it[position].itemType == ActionSkillItemBean.SKILL_TITLE || it[position].actionType == null) {
                    return
                }
                if (it[position].isEnable == false) {
                    showDeviceNoBindDialog()
                    //	showBuyDeviceDialog(it[position].actionItemType)
                    return
                }
                when (it[position].actionType) {
                    SmartDeviceType.FRAGRANCE_DURATION, //香氛散香时长
                    SmartDeviceType.MASSAGE_CUSHION_TIME, //腰靠按摩时间
                    SmartDeviceType.NECK_PILLOW_MASSAGE_TIME, //颈枕按摩时间
                    -> {
                        showSingleWheelDialog(it[position].content, it[position].actionType)
                    }
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_BRIGHTNESS -> { //炫彩氛围灯亮度
                        showAtmosphereLampBrightnessDialog(it[position].content, it[position].actionType)
                    }
                    SmartDeviceType.COLORFUL_AMBIENT_LIGHT_COLOR -> { //炫彩氛围灯颜色
                        showSetRgbDialog(it[position].content, it[position].actionType)
                    }
                    else -> {
                        showRadioListDialog(it[position].content, it[position].actionType!!)
                    }
                }
            }
        }
    }

    override fun getAdapter(): SmartDeviceActionAdapter? {
        return mAdapter
    }

    override fun initView(view: View?) {
        super.initView(view)
        mAdapter = SmartDeviceActionAdapter()
        mAdapter?.setOnItemClickListener { _, v, position ->
            handleItemClick(v, position)
        }

        val manager = GridLayoutManager(context, 4)
        manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.data?.get(position)?.itemType == ActionSkillItemBean.SKILL_TITLE) return 4
                return 1
            }
        }
        mViewDataBinding.rvAction.layoutManager = manager
        mViewDataBinding.rvAction.adapter = mAdapter

    }

    private fun showSetRgbDialog(title: String, type: SmartDeviceType?) {
        val dialog = AmbientLightColorDialog(
            context, title, mViewModel.getActionData(type), mViewModel.getSingleWheelNameList(resources, type)
        )
        dialog.setPreSetAmbientAction(sequence)
        dialog.setAmbientColorThemeValueList(mViewModel.getInputArgIntValue(type))
        dialog.showWithAnimate()
    }

    private fun showAtmosphereLampBrightnessDialog(title: String, type: SmartDeviceType?) {
        val dialog = IndicatorSeekBarDialog(
            context, title, mViewModel.getActionData(type), IndicatorSeekBarDialog.IndicatorInputType.DEFAULT_INPUT
        )
        dialog.defaultIndex = 1f
        dialog.seekBarMin = 1f
        dialog.seekBarMax = 5f
        dialog.tickCount = 6
        dialog.setPreSetAction(sequence)
        dialog.indicatorUnit = getString(R.string.scene_text_indicator_unit_car_light)
        dialog.showWithAnimate()
    }

    private fun showRadioListDialog(title: String, type: SmartDeviceType) {
        //一个sequence根据inputArg选项
        val dialog: RadioListDialogFragment<ScenarioInfo.Sequence, Int> = RadioListDialogFragment(
            title,
            CheckListType.INPUT_CHECK_MODE,
            mViewModel.getListData(resources, type),
            mViewModel.getActionData(type),
        )
        dialog.inputArgValueList = mViewModel.getInputArgIntValue(type)

        dialog.preSetSequence = sequence
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    private fun showSingleWheelDialog(title: String, position: SmartDeviceType?) {
        position?.let {
            //            val list = if (position == SmartDeviceType.CAR_REFRIGERATOR_TEMP) {
            //                mViewModel.initTemperatureWheelData()
            //            } else {
            val list = mViewModel.getStringInputArg(position == SmartDeviceType.FRAGRANCE_DURATION)
            //            }
            val dialog = SingleWheelDialog<ScenarioInfo.Sequence, Int>(
                context, list, title, mViewModel.getActionData(position), SingleWheelType.SINGLE_WHEEL_INPUT_ARG
            )
            dialog.setUnit(getString(R.string.scene_text_edit_minute))
            dialog.setWheelDefaultPosition(0) //默认值10分钟，对应下标9
            dialog.showWithAnimate()
        }
    }

    class MyGridItemDecoration : RecyclerView.ItemDecoration() {
        private var list: ArrayList<ActionSkillItemBean<SmartDeviceType>>? = null
        fun setData(data: ArrayList<ActionSkillItemBean<SmartDeviceType>>) {
            this.list = data
        }

        fun clearData() {
            list?.let {
                it.clear()
                list = null
            }
        }

        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val position = parent.getChildAdapterPosition(view)
            when (list?.get(position)?.actionType) {
                SmartDeviceType.ITEM_TITLE -> {
                    if (position == 0) {
                        outRect.top = 0
                    } else {
                        outRect.top = 56
                    }
                }
                else -> {
                    outRect.top = 24
                }
            }
            outRect.left = 0
            outRect.right = 32
        }
    }

    /**
     * lk1a未绑定设备对话框
     */
    private fun showDeviceNoBindDialog() {
        val dialog = SmartDeviceNoBindDialog(context)
        dialog.showWithAnimate()
    }

    /**
     * 623 未绑定设备对话框
     */
    private fun showBuyDeviceDialog(type: SmartDeviceType?) {
        type?.let {
            val dialog = BuySmartDeviceDialog(context, type)
            dialog.showWithAnimate()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mItemDecoration?.clearData()
        CarControlLocalManager.unregisterCarControl()
    }
}