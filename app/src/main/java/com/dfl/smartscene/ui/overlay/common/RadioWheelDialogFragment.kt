package com.dfl.smartscene.ui.overlay.common

import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.AirConditionerType
import com.dfl.smartscene.bean.action.CarLightType
import com.dfl.smartscene.bean.condition.TriggerDrive
import com.dfl.smartscene.bean.condition.TriggerEnvironment
import com.dfl.smartscene.databinding.SceneDialogRadioWheelBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.UIUtils
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import me.jessyan.autosize.utils.AutoSizeUtils
import org.greenrobot.eventbus.EventBus

/**
 *Created by 钟文祥 on 2023/12/18.
 *Describer: Radio单选 + Wheel滑选  的动作dialog
 */
@Deprecated("2A被合并，使用DoubleWheelDialogFragment")
class RadioWheelDialogFragment<T>(
    private val mTitle: String?, //标题
    private val mActionType: T?, //内容的标识类型
    private val mRbMap: MutableMap<String, String>, //单选的键值
    private val mWheelMap: MutableMap<String, String>, //滑轮的键值
    private val mLabel: String?, //滑轮的单位，默认不带
    private val mDefaultIndex: Int //滑轮默认第几位
) : BaseDialogFragment(), View.OnClickListener {
    //只有一个能力id，默认动作，需要确定时重新赋值post出去
    /**已经设置好的动作，由二级菜单点击传来*/
    private var mPreSetAction: ScenarioInfo.Sequence? = null //已经设置好的动作，由二级菜单点击传来

    /**已经设置好的条件，由二级菜单点击传来*/
    private var mPreSetCondition: ScenarioInfo.Condition? = null //已经设置好的动作，由二级菜单点击传来

    /**传入动作列表,可能有复数动作,对应radio的选项*/
    private var mSequence: MutableList<ScenarioInfo.Sequence>? = null

    /**传入条件列表,可能有复数动作,对应radio的选项*/
    private var mCondition: MutableList<ScenarioInfo.Condition>? = null
    private var mBinding: SceneDialogRadioWheelBinding? = null
    private val viewBinding get() = mBinding!!
    private var mDesc = ""

    constructor(
        mTitle: String?,
        mActionType: T?,
        mRbMap: MutableMap<String, String>,
        mWheelMap: MutableMap<String, String>,
        sequence: MutableList<ScenarioInfo.Sequence>,
        preSetAction: ScenarioInfo.Sequence?,
        mLabel: String?,
        mDefaultIndex: Int,
    ) : this(mTitle, mActionType, mRbMap, mWheelMap, mLabel, mDefaultIndex) {
        mSequence = sequence
        mPreSetAction = preSetAction
    }

    constructor(
        mTitle: String?,
        mActionType: T?,
        mRbMap: MutableMap<String, String>,
        mWheelMap: MutableMap<String, String>,
        condition: MutableList<ScenarioInfo.Condition>,
        preSetCondition: ScenarioInfo.Condition?,
        mLabel: String?,
        mDefaultIndex: Int,
    ) : this(mTitle, mActionType, mRbMap, mWheelMap, mLabel, mDefaultIndex) {
        mCondition = condition
        mPreSetCondition = preSetCondition
    }

    companion object {
        fun <T> showFragment(
            manager: FragmentManager,
            title: String?,
            actionType: T?, //内容的标识类型
            rbMap: MutableMap<String, String>,
            wheelMap: MutableMap<String, String>,
            sequence: MutableList<ScenarioInfo.Sequence>,
            preSetAction: ScenarioInfo.Sequence?,
            label: String = "",
            defaultIndex: Int = 0,
            desc: String = ""
        ) {
            val dialog = RadioWheelDialogFragment(
                title, actionType, rbMap, wheelMap, sequence, preSetAction, label, defaultIndex
            )
            dialog.mDesc = desc
            dialog.show(manager, Companion::class.java.name)
        }

        fun <T> showFragment(
            manager: FragmentManager,
            title: String?,
            actionType: T?, //内容的标识类型
            rbMap: MutableMap<String, String>,
            wheelMap: MutableMap<String, String>,
            condition: MutableList<ScenarioInfo.Condition>,
            preSetCondition: ScenarioInfo.Condition?,
            label: String = "",
            defaultIndex: Int = 0
        ) {
            val dialog = RadioWheelDialogFragment(
                title, actionType, rbMap, wheelMap, condition, preSetCondition, label, defaultIndex
            )
            dialog.show(manager, Companion::class.java.name)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        mBinding = SceneDialogRadioWheelBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mBinding = null
    }

    override fun getLayoutId(): Int {
        return -1
    }

    override fun initView(view: View) {
        mTitle?.let { viewBinding.tvTitle.text = mTitle }

        viewBinding.tabSwitchViewCondition.generateViews(mRbMap.keys.toList())
        viewBinding.includeDialogBottom.btnPrimary.setOnClickListener(this)
        viewBinding.includeDialogBottom.btnNormal.setOnClickListener(this)

        UIUtils.initWheelView(context, viewBinding.wheel, mWheelMap.keys.toList(), "", mDefaultIndex)
        if (!TextUtils.isEmpty(mLabel)) {
            viewBinding.tvUnit.text = mLabel
            viewBinding.tvUnit.visibility = View.VISIBLE
        } else {
            viewBinding.tvUnit.visibility = View.GONE
        }

        if (ObjectUtils.isNotEmpty(mDesc)) {
            viewBinding.tvDesc.text = mDesc
            viewBinding.tvDesc.visibility = View.VISIBLE

            val lp = viewBinding.tabSwitchViewCondition.layoutParams as ViewGroup.MarginLayoutParams
            lp.topMargin = AutoSizeUtils.dp2px(context, 42.0f)
            viewBinding.tabSwitchViewCondition.layoutParams = lp
        }
        setupInitData()
        setCanceledOnTouchOutside(true)
    }

    /**
     * 针对 动作或条件列表值只有一个,其InputArg列表复数
     * 根据 mRbMap,mWheelMap的值列表比对之前数据值
     * 没有之前数据使用传入动作或条件InputArg定位默认值
     * 例如 打开 关闭 场景灯效
     * @param radioInputIndex radio值在InputArg列表索引
     * @param wheelInputIndex wheel值在InputArg列表索引
     */
    private fun updateViewByPreSetForOneSkill(
        radioInputIndex: Int, wheelInputIndex: Int
    ) {
        var indexRadio = -1
        var indexWheel = -1
        mPreSetAction?.let {
            indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                it, mSequence, mRbMap.values.toList(), radioInputIndex
            )
            indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                it, mSequence, mWheelMap.values.toList(), wheelInputIndex
            )
        }
        mPreSetCondition?.let {
            indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                it, mCondition, mRbMap.values.toList(), radioInputIndex
            )
            indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                it, mCondition, mWheelMap.values.toList(), wheelInputIndex
            )
        }
        //没有上次值或上次值定位错误，使用传入值inputArg作为默认值
        if (indexRadio == -1 || indexWheel == -1) {
            mSequence?.let {
                if (it.size <= 0) return
                indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                    it[0], it, mRbMap.values.toList(), radioInputIndex
                )
                indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                    it[0], it, mWheelMap.values.toList(), wheelInputIndex
                )
            }
            mCondition?.let {
                if (it.size <= 0) return
                indexRadio = ObjectUtils.findPreIndexWithMultiInput(
                    it[0], it, mRbMap.values.toList(), radioInputIndex
                )
                indexWheel = ObjectUtils.findPreIndexWithMultiInput(
                    it[0], it, mWheelMap.values.toList(), wheelInputIndex
                )
            }
            //传入值没有在values中使用默认值
            if (indexRadio == -1) {
                indexRadio = 0
            }
            if (indexWheel == -1) {
                indexWheel = 0
            }
        }
        //默认值
        viewBinding.tabSwitchViewCondition.setSelectedIndex(indexRadio)
        viewBinding.wheel.currentItem = indexWheel
    }

    /**
     * 针对 动作或条件列表是复数,子项InputArgList只有一个
     * 根据之前值在动作或条件列表选中radio,故要注意radio文本位置要对应列表中位置
     * 根据之前值InputArg.value对应mWheelMap值列表位置选中wheel
     * 例如 主驾 副驾 空调温度
     */
    private fun updateViewByPreSetForTwoSkill() {
        //默认选中
        viewBinding.tabSwitchViewCondition.setSelectedIndex(0)
        viewBinding.wheel.currentItem = mDefaultIndex
        var indexRadio = -1
        var indexWheel = -1
        //选中之前的值
        mPreSetAction?.let {
            indexRadio = ObjectUtils.findPreIndex(it, mSequence)
            //不符合当前对话框的Sequence直接返回防止indexWheel越界
            if (indexRadio != -1) {
                indexWheel = mWheelMap.values.toList().indexOf(it.action.input[0].value)
            }
        }
        mPreSetCondition?.let {
            indexRadio = ObjectUtils.findPreIndex(it, mCondition)
            if (indexRadio != -1) {
                indexWheel = mWheelMap.values.toList().indexOf(it.input[0].value)
            }
        }
        //indexWheel不为-1,indexRadio肯定不为-1
        if (indexWheel != -1) {
            viewBinding.tabSwitchViewCondition.setSelectedIndex(indexRadio)
            viewBinding.wheel.currentItem = indexWheel
        }
    }

    private fun updateMultiInputArgInfoByView(
        inputArgList: List<InputArgInfo>?, radioInputIndex: Int, wheelInputIndex: Int, wheel: String
    ) {
        if (ObjectUtils.checkScenarioDataIsEmpty(inputArgList) || inputArgList!!.size != 2) {
            return
        }
        inputArgList[radioInputIndex].value = mRbMap.values.toList()[viewBinding.tabSwitchViewCondition.selectedIndex]
        inputArgList[wheelInputIndex].value = mWheelMap[wheel]
    }

    /**根据之前的值,初始化界面*/
    private fun setupInitData() {
        when (mActionType) {
            CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT, CarLightType.CAR_SCENE_LAMP_EFFECT, CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA, CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA -> { //大灯场景灯效
                updateViewByPreSetForOneSkill(
                    1, 0
                )
            }

            AirConditionerType.AIR_CONDITIONER_TEMPERATURE_TWO, TriggerDrive.DRIVE_SPEED, TriggerEnvironment.CAR_INNER_TEMP, TriggerEnvironment.CAR_OUTSIDE_TEMP -> { //双区空调
                updateViewByPreSetForTwoSkill()
            }

        }
    }

    //提交
    private fun confirm() {
        val wheelValue = mWheelMap.keys.toList()[viewBinding.wheel.currentItem]
        mSequence?.let {
            if (ObjectUtils.checkScenarioDataIsEmpty(mSequence)) {
                return
            }
            val postSequence: ScenarioInfo.Sequence
            if (mActionType == AirConditionerType.AIR_CONDITIONER_TEMPERATURE_TWO) {
                postSequence = it[viewBinding.tabSwitchViewCondition.selectedIndex]
                val desc =
                    mRbMap.keys.toList()[viewBinding.tabSwitchViewCondition.selectedIndex] + " " + wheelValue + mLabel
                postSequence.action.desc = desc
                postSequence.action.category = viewBinding.tvTitle.text.toString()
                postSequence.action.input[0].value = mWheelMap[wheelValue]
            } else {
                postSequence = it[0]
                val desc =
                    wheelValue.plus(" ").plus(mRbMap.keys.toList()[viewBinding.tabSwitchViewCondition.selectedIndex])
                postSequence.action.desc = desc
                postSequence.action.category = viewBinding.tvTitle.text.toString()
                when (mActionType) {
                    CarLightType.CAR_OUTSIDE_LAMP_SCENE_EFFECT, CarLightType.CAR_SCENE_LAMP_EFFECT, CarLightType.CAR_OUTSIDE_LAMP_LIGHT_SOA, CarLightType.CAR_AMBIENT_LAMP_AMBIENT_SOA -> {
                        updateMultiInputArgInfoByView(postSequence.action.input, 1, 0, wheelValue)
                    }
                }
            }
            EventBus.getDefault().post(postSequence)

        }

        mCondition?.let {
            if (ObjectUtils.checkScenarioDataIsEmpty(mCondition)) {
                return
            }
            val postCondition: ScenarioInfo.Condition = it[viewBinding.tabSwitchViewCondition.selectedIndex]
            val desc: String =
                mRbMap.keys.toList()[viewBinding.tabSwitchViewCondition.selectedIndex] + " " + wheelValue + mLabel
            postCondition.desc = desc
            postCondition.category = viewBinding.tvTitle.text.toString()
            postCondition.input[0].value = mWheelMap[wheelValue]
            EventBus.getDefault().post(postCondition)
        }


    }

    override fun onClick(v: View?) {
        if (!DebouncingUtils.isValid(v!!, SceneEditManager.mViewClickDuration)) return
        when (v.id) {
            R.id.btn_primary -> { //确认
                confirm()
                dismissWithAnimate()
            }

            R.id.btn_normal -> { //取消
                dismissWithAnimate()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        viewBinding.tabSwitchViewCondition.adapterSkinColor(newConfig)
    }
}


