package com.dfl.smartscene.ui.main

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dfl.android.animationlib.CustomTabLayout.OnTabSelectedListener
import com.dfl.android.animationlib.tablayout.CommonTabLayout
import com.dfl.android.animationlib.tools.AnLog
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.base.MVVMActivity
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.*
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.R
import com.dfl.smartscene.application.SceneApplication
import com.dfl.smartscene.bean.communication.UserLoginStatus
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.post.CommunityPostSelectFragment
import com.dfl.smartscene.community.search.SearchActivity
import com.dfl.smartscene.community.usercenter.UserCenterActivity
import com.dfl.smartscene.databinding.SceneActivityMainBinding
import com.dfl.smartscene.ui.main.help.HelpDialogFragment
import com.dfl.smartscene.ui.main.me.scenerecord.SceneRecordDialogFragment
import com.dfl.smartscene.ui.overlay.myscene.MySceneMenuTopPopup
import com.dfl.smartscene.util.*
import com.dfl.voicehelper.util.VoiceUtils
import com.google.android.material.tabs.TabLayoutMediator
import com.jeremyliao.liveeventbus.LiveEventBus
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.concurrent.TimeUnit


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/18
 * desc   :智慧场景主页
 * version: 1.0
 */
class MainActivity : MVVMActivity<SceneActivityMainBinding, MainViewModel>(), View.OnClickListener {
    companion object {
        private const val TAG = GlobalConstant.GLOBAL_TAG.plus("MainActivity")

        const val FRAGMENT_TAG_COMMUNITY = "FRAGMENT_TAG_COMMUNITY"
        const val FRAGMENT_TAG_DISCOVER = "FRAGMENT_TAG_DISCOVER"
        const val FRAGMENT_TAG_ME = "FRAGMENT_TAG_ME"
        const val FRAGMENT_TAG_NAME = "FRAGMENT_TAG_NAME"

        /**
         * 左滑退出的时长间隔
         */
        const val EXIT_TIMEOUT = 2500L
        fun startMainActivity(context: Context, page: String = FRAGMENT_TAG_ME) {
            val intent = Intent(context, MainActivity::class.java)
            intent.putExtra(FRAGMENT_TAG_NAME, page)
            context.startActivity(intent)
        }
    }

    enum class MainTagType(var value: Int) {
        COMMUNITY(0), ME(1), DISCOVER(2),
    }

    private val mFragmentTagStrArr = arrayOf(
        FRAGMENT_TAG_COMMUNITY, FRAGMENT_TAG_ME, FRAGMENT_TAG_DISCOVER
    )

    /**社区场景是否初始化过*/
    var isCommunityFragmentInit = false

    /**我的场景数量是否为空0*/
    private var isNotMeCardNum: Boolean = true

    /** 触发后退的时间戳*/
    private var mLastBackTimestamp = 0L
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //transparentStatusBar()
        mViewModel.initData()
        //预先加载社区的数据库数据
        APPUtils.initCommunityCachedData()
        APPUtils.initUserCenterCachedData()
        if (BuildConfig.DEBUG) {
            AnLog.isDebug = BuildConfig.DEBUG
            //initPermission()
        }
    }
    /**
     * 菜单弹窗实例
     */
    private var mPopup : MySceneMenuTopPopup = MySceneMenuTopPopup(object : BaseMenuPopup.OnMenuClickListener<MySceneBean>{
        override fun onMenuItemSelected(
            menuPopup: BaseMenuPopup<MySceneBean>,
            menuIndex: Int,
            dataIndex: Int,
            data: MySceneBean?
        ) {
            when(menuIndex){
                MySceneMenuTopPopup.INDEX_HELP ->{
                    showHelpDialog()
                }
                MySceneMenuTopPopup.INDEX_LOG ->{
                    showSceneRecordDialog()
                }
            }
        }
    }).apply {
        // 设置蒙层隐藏监听器
        setOnMaskHideListener(object : MySceneMenuTopPopup.OnMaskHideListener {
            override fun onMaskHide() {
                // 蒙层隐藏时的处理逻辑（如果需要的话）
                CommonLogUtils.logD(TAG, "Menu mask hidden")
            }
        })
    }

    /**
     * 智慧场景动态权限申请，暂无需要申请权限第三库自己会申请
     */
    private fun initPermission() {
        val mRequestPermissionsLauncher =
            registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
                CommonLogUtils.logD(TAG, permissions.toString())
            }
        val requestPermissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
        )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions.plus(Manifest.permission.POST_NOTIFICATIONS)
        }
        mRequestPermissionsLauncher.launch(
            requestPermissions
        )
    }

    override fun getLayoutId(): Int {
        return R.layout.scene_activity_main
    }

    //大于0 表示view绑定viewModel
    override fun getBindingVariable(): Int {
        return 0
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val page = intent?.getStringExtra(FRAGMENT_TAG_NAME)
        if (!page.isNullOrEmpty()) {
            showTag(page)
        }
    }

    override fun getViewModelClass(): Class<MainViewModel> {
        return MainViewModel::class.java
    }

    override fun initView() {
        super.initView()
        initViewPager2() //创建viewpager2
        initListener()
        initLiveEventBus()
        defaultShowMeTag() //默认选择我的
        initVoiceHelperText()
    }

    /**
     * 初始化可见即可说的文本
     */
    private fun initVoiceHelperText() {
//        VoiceUtils.setRegularTextData(
//            mViewDataBinding.btnCommunityPostScene, resources.getString(R.string.scene_text_voice_helper_post_scene)
//        )
//        VoiceUtils.setRegularTextData(
//            mViewDataBinding.btnCommunitySearch, resources.getString(R.string.scene_text_voice_helper_search)
//        )
//        VoiceUtils.setRegularTextData(
//            mViewDataBinding.ivMainHelp, resources.getString(R.string.scene_text_voice_helper_help)
//        )
//        VoiceUtils.setRegularTextData(
//            mViewDataBinding.ivMainLog, resources.getString(R.string.scene_text_voice_helper_log)
//        )
        VoiceUtils.setRegularTextData(
            mViewDataBinding.btnAddScene, resources.getString(R.string.scene_text_voice_helper_create_scene)
        )
    }

    private var mTabLayoutMediator: TabLayoutMediator? = null
    private fun initViewPager2() {
        val mFragmentTypeList: MutableList<MainTagType> = ArrayList()
        mFragmentTypeList.add(MainTagType.COMMUNITY)
        mFragmentTypeList.add(MainTagType.ME)
        //		mFragmentTypeList.add(MainTagType.DISCOVER)

        val mViewPagerAdapter = MainViewPagerAdapter(this, mFragmentTypeList)
        mViewDataBinding.vpContainer.adapter = mViewPagerAdapter
        mViewDataBinding.vpContainer.isUserInputEnabled = false // false 禁止viewpager2左右滑动
        //		mViewDataBinding.vpContainer.setOffscreenPageLimit(1) //屏幕外加载的个数，解决内存泄露

        val mTitleArr = arrayListOf<String>(

            getString(R.string.scene_text_main_discover),
            getString(R.string.scene_text_main_me),
            //getString(R.string.scene_text_main_community)
        )

        mViewDataBinding.tabMain.setTabsData(mTitleArr)
        mViewDataBinding.tabMain.listener = object : CommonTabLayout.OnTabSelectedListener{
            override fun onInitViewComplete(isComplete: Boolean?) {
                mViewDataBinding.tabMain.getTabView(0)?.let {
                    VoiceUtils.setRegularTextData(it, resources.getString(R.string.scene_text_main_discover))
                }
                mViewDataBinding.tabMain.getTabView(1)?.let {
                    VoiceUtils.setRegularTextData(it, resources.getString(R.string.scene_text_main_me))
                }
            }

            override fun onTabClicked(position: Int) {

            }

            override fun onTabSelected(position: Int, isFromUser: Boolean) {
                tabSelected(position)
            }

            override fun onTabUnSelected(lastPosition: Int) {

            }

        }
    }

    private fun tabSelected(position: Int) {
        updateRightView(mFragmentTagStrArr[position])
        if (position == MainTagType.COMMUNITY.value) {
            isCommunityFragmentInit = true
        }
        updateViewPager2TopMargin(position)
        mViewDataBinding.vpContainer.setCurrentItem(position, false)
        mListener?.tabClick(position)
    }

    //改变界面
    private fun updateViewPager2TopMargin(position: Int) {
        val params = mViewDataBinding.vpContainer.layoutParams as (ViewGroup.MarginLayoutParams)
        if (position == MainTagType.COMMUNITY.value) {
            params.topMargin = 0
        } else {
            params.topMargin = AutoSizeUtils.dp2px(this@MainActivity, 176.0f)
        }
        mViewDataBinding.vpContainer.layoutParams = params
    }

    private fun initListener() {
//        mViewDataBinding.ivMainHelp.setOnClickListener(this)
//        mViewDataBinding.ivMainLog.setOnClickListener(this)
//        mViewDataBinding.ivCommunityUserHead.setOnClickListener(this)
//        mViewDataBinding.btnCommunityPostScene.setOnClickListener(this)
//        mViewDataBinding.btnCommunitySearch.setOnClickListener(this)
        mViewDataBinding.btnAddScene.setOnClickListener(this)
        mViewDataBinding.ivMainMenu.setOnClickListener(this)
    }

    @SuppressLint("CheckResult")
    private fun initLiveEventBus() {
        //场景添加成功的事件
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java).observe(this) {
            //添加成功，显示我的tag
            if (it != null) {
                if (mViewDataBinding.tabMain.selectedIndex != MainTagType.ME.value) {
                    showMeTag()
                }
            }
        }

        LiveEventBus.get(GlobalLiveEventConstants.KEY_CCM_CONNECT_STATE, Boolean::class.java).observe(this) {
            if (it) { //如果接收到CCM连接变化，则再次去请求一次数据
                mViewModel.initData()
            }
        }
        //选择场景和发布场景的返回
        LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Int::class.java).observe(this) {
            if (it == 1) {
                if (supportFragmentManager.backStackEntryCount == 1) { //从选择列表页返回的要隐藏，若为2则是点了离开返回的，不需隐藏
                    mViewDataBinding.flPostView.visibility = View.GONE
                }
                supportFragmentManager.popBackStack()
            } else if (it == 2) { //发布成功返回来的，直接隐藏
                supportFragmentManager.popBackStack()
                supportFragmentManager.popBackStack()
                //延时执行
                Observable.just(1).delay(400, TimeUnit.MILLISECONDS)
                    .subscribeOn(Schedulers.io()) //指定被观察者subscribe()(发送事件的线程)在IO线程
                    .observeOn(AndroidSchedulers.mainThread()) //指定观察者接收响应事件的线程在主线程
                    .subscribe {
                        mViewDataBinding.flPostView.visibility = View.GONE
                    }
            }
        }
        //监听个人中心APP用户登录退登事件,加载用户头像
//        LiveEventBus.get<Boolean>(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS)
//            .observe(this) { isLoginOut ->
//                renderUserHead(!isLoginOut, UserCenterManager.userLoginStatus?.iconUrl)
//            }
//        //监听个人中心APP用户最新数据,加载用户头像
//        LiveEventBus.get<UserLoginStatus>(GlobalLiveEventConstants.KEY_USER_CENTER_GET_USERID_LOGIN_STATUS)
//            .observe(this) {
//                renderUserHead(true, it.iconUrl)
//            }

        //监听我的场景应用冷启动后有没有数据
        LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java).observe(
            this
        ) {
            if (it == 0) { //我的场景有数据
                SceneApplication.IS_FIRST_OPEN_SMART_SCENE = false
            } else { //我的场景没数据时，并且是第一次冷启动， 跳转到社区
                if (SceneApplication.IS_FIRST_OPEN_SMART_SCENE) {
                    SceneApplication.IS_FIRST_OPEN_SMART_SCENE = false

                    //没登录跳转登录界面
                    UserCenterManager.checkUserLoginStatus(true)
                    showCommunityTag()
                }
            }
        }
    }

    /**
     * 加载用户头像
     * @param isSignIn 是否登录状态
     * @param imageUrl 头像
     */
//    private fun renderUserHead(isSignIn: Boolean, imageUrl: String?) {
//        if (isSignIn) {
//            CommonLogUtils.logD(TAG, "个人中心用户登录,加载头像,未设置图片加载默认头像")
//            val mHeardOptions: RequestOptions =
//                UIUtils.getGlideOptions(R.drawable.scene_icon_community_user_head_default).circleCrop()
//            Glide.with(this).load(imageUrl).apply(mHeardOptions).into(mViewDataBinding.ivCommunityUserHead)
//        } else {
//            CommonLogUtils.logD(TAG, "个人中心用户退出登录,加载头像")
//            //要先清空glide才能设置生效
//            Glide.with(this).clear(mViewDataBinding.ivCommunityUserHead)
//            mViewDataBinding.ivCommunityUserHead.setImageResource(R.drawable.scene_icon_common_main_user)
//        }
//    }

    override fun onClick(v: View?) {
        if (v != null) {
            //增加防快速点击的判断
            if (!DebouncingUtils.isValid(v)) {
                return
            }
        }
        v?.id?.let {
            when (it) {
                R.id.btn_add_scene -> {
                    mListener?.addSceneClick()
                }

                R.id.iv_main_menu -> {
                    // 显示菜单PopupWindow（包含蒙层）
                    CommonLogUtils.logD(TAG, "Menu button clicked, showing popup")
                    mPopup.show(v)
                }

                else -> {
                    // 其他点击事件处理
                }
            }
        }
    }

    override fun initData() {
        //清空超过30天的场景执行记录
        mViewModel.deleteTimeOutSceneRecord()
    }

    override fun onDestroy() {
        mTabLayoutMediator?.detach() //tab和viewpager绑定解除
        mTabLayoutMediator = null
        CommonLogUtils.logD(TAG, "onDestroy")
        super.onDestroy()
    }

    override fun onResume() {
        super.onResume()
        //请求个人中心登录状态,获取最新个人中心数据
//        UserCenterManager.checkUserLoginStatus(false)?.let {
//            //有数据要加载头像，防止已有数据和之前数据一致退出应用重新进入，不加载头像
//            renderUserHead(true, it.iconUrl)
//        }
        reportFullyDrawn()
    }

    private fun showSceneRecordDialog() {
        SceneRecordDialogFragment.showSceneRecordDialog(supportFragmentManager)
    }

    private fun showHelpDialog() {
        HelpDialogFragment.newInstance(0).showNow(supportFragmentManager, "FRAGMENT_TAG_HELP")
    }


    /**右侧控件显示更改*/
    private fun updateRightView(page: String) {
//        mViewDataBinding.ivMainHelp.visibility = if (page == FRAGMENT_TAG_ME) View.VISIBLE else View.GONE
//        mViewDataBinding.ivMainLog.visibility = if (page == FRAGMENT_TAG_ME) View.VISIBLE else View.GONE
    if (page == FRAGMENT_TAG_ME) {
        mViewDataBinding.btnAddScene.visibility = if (isNotMeCardNum) View.GONE else View.VISIBLE
    } else {
        mViewDataBinding.btnAddScene.visibility = View.VISIBLE
    }

//        mViewDataBinding.ivCommunityUserHead.visibility = if (page == FRAGMENT_TAG_COMMUNITY) View.VISIBLE
//        else View.GONE
//        mViewDataBinding.btnCommunitySearch.visibility = if (page == FRAGMENT_TAG_COMMUNITY) View.VISIBLE else View.GONE
//        mViewDataBinding.btnCommunityPostScene.visibility =
//            if (page == FRAGMENT_TAG_COMMUNITY) View.VISIBLE else View.GONE
    }

    private var mListener: OnMainViewClickListener? = null

    interface OnMainViewClickListener {
        fun addSceneClick()
        fun tabClick(position: Int)
    }

    fun setOnMainViewClickListener(listener: OnMainViewClickListener) {
        this.mListener = listener
    }

    /**我的场景没有数据的情况下，隐藏*/
    fun setBtnAddSceneVisible(isNotMeCardNum: Boolean) {
        this.isNotMeCardNum = isNotMeCardNum
        if (mViewDataBinding.tabMain.selectedIndex == MainTagType.ME.value) {
            mViewDataBinding.btnAddScene.visibility = if (isNotMeCardNum) View.GONE else View.VISIBLE
        }
    }


    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        //这里是取出我们返回栈存在Fragment的个数
        if (supportFragmentManager.backStackEntryCount <= 0) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - mLastBackTimestamp < EXIT_TIMEOUT) {
                super.onBackPressed()
            } else {
                CommonToastUtils.show(getString(R.string.scene_toast_swipe_exit_again))
                mLastBackTimestamp = currentTime
            }
        } else {
            //取出我们返回栈保存的Fragment,这里会从栈顶开始弹栈
            val entryCount = supportFragmentManager.backStackEntryCount
            supportFragmentManager.popBackStack()
            if (entryCount == 1) { //popBackStack() 弹出栈 有延时
                mViewDataBinding.flPostView.visibility = View.GONE
            }
        }
    }

    //显示flPostView
    fun showPostView() {
        mViewDataBinding.flPostView.visibility = View.VISIBLE
    }

    //显示发现页
    fun showDiscoverTag() {
        val page = mFragmentTagStrArr[MainTagType.DISCOVER.value]
        showTag(page)
    }

    //显示社区页
    fun showCommunityTag() {
        val page = mFragmentTagStrArr[MainTagType.COMMUNITY.value]
        showTag(page)
    }

    private fun defaultShowMeTag() {
        val page = mFragmentTagStrArr[MainTagType.ME.value]
        val position = mFragmentTagStrArr.indexOf(page)
        mViewDataBinding.tabMain.setCurrentTab(position,true,true)
        mViewDataBinding.flPostView.visibility = View.GONE
        tabSelected(position)
    }

    //显示我的页
    private fun showMeTag() {
        val page = mFragmentTagStrArr[MainTagType.ME.value]
        showTag(page)
    }

    private fun showTag(page: String) {
        val position = mFragmentTagStrArr.indexOf(page)
        mViewDataBinding.tabMain.setCurrentTab(position)
        mViewDataBinding.flPostView.visibility = View.GONE
    }

    fun showDragView() {
        mViewDataBinding.txtDragDescribe.visibility = View.VISIBLE
    }

    fun disShowDragView() {
        mViewDataBinding.txtDragDescribe.visibility = View.GONE
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        //iv初始未登录图片，切换主题后glide要重新加载之前个人中心头像
//        UserCenterManager.userLoginStatus?.let {
//            //有数据要加载头像，防止已有数据和之前数据一致退出应用重新进入，不加载头像
//            renderUserHead(true, it.iconUrl)
//        }
    }
}