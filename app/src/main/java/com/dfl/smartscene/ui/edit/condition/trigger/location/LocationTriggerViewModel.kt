package com.dfl.smartscene.ui.edit.condition.trigger.location

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerLocation
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *Created by 钟文祥 on 2023/12/12.
 *Describer:
 */
class LocationTriggerViewModel : BaseViewModel() {

    val liveData = MutableLiveData<ArrayList<ConditionItemBean<TriggerLocation>>>()


    fun initData(resources: Resources) {
        val list = ArrayList<ConditionItemBean<TriggerLocation>>()

        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_location_vehicle_arrival),
                R.drawable.scene_icon_me_trigger_location_arrival,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_ARRIVAL
                ),
                TriggerLocation.LOCATION_VEHICLE_ARRIVAL
            )
        )
        list.add(
            ConditionItemBean(
                ConditionItemBean.SKILL_CONTENT,
                resources.getString(R.string.scene_text_edit_condition_location_vehicle_departure),
                R.drawable.scene_icon_me_trigger_location_departure,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_TRIGGER_LOCATION_VEHICLE_DEPARTURE
                ),
                TriggerLocation.LOCATION_VEHICLE_DEPARTURE
            )
        )

        val type = SceneEditManager.TriggerType.LOCATION
        val result = SceneEditManager.checkChildItemIsActive(type, list)
        liveData.postValue(result)

    }

    //根据skillId 组建默认的条件值
    fun getConditionValue(
        resources: Resources,
        item: ConditionItemBean<TriggerLocation>
    ): ScenarioInfo.Condition {


        val inputArgList = mutableListOf<InputArgInfo>(
            InputArgInfo(
                SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LATITUDE,
                ArgType.DOUBLE,
                "0",
                ""
            ),
            InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_LONGITUDE, ArgType.DOUBLE, "0"),
            InputArgInfo(SkillsListConstant.INPUT_ARG_TRIGGER_LOCATION_DISTANCE, ArgType.INT32, "100"),
        )

        return ScenarioInfo.Condition(
            1,
            item.conditionName, //车辆到达，车辆离开
            item.conditionSkillId?.get(0) ?: -1,
            resources.getString(R.string.scene_text_edit_add_condition_location),
            inputArgList
        )

    }
}