package com.dfl.smartscene.ui.overlay.common;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckedTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.android.common.base.BaseDialog;
import com.dfl.android.common.util.ObjectUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.databinding.SceneDialogAirQualityBinding;
import com.dfl.android.common.global.SkillsListConstant;
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType;
import com.iauto.scenarioadapter.ScenarioInfo;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :3个复选项的时候,ui2.0后已被废弃,使用RadioListDialog
 * version: 1.0
 */
@Deprecated
public class ThreeCheckDialog extends BaseDialog implements View.OnClickListener {
    /**
     * 默认选中 第一个
     */
    public int defaultIndex = 0;
    private SceneDialogAirQualityBinding mVB;
    //左
    private LinearLayout mDifferenceLl;
    private ImageView mDifferenceIv;
    private CheckedTextView mDifferenceCtv;
    //中
    private LinearLayout mMiddleLl;
    private ImageView mMiddleIv;
    private CheckedTextView mMiddleCtv;
    //右
    private LinearLayout mExcellentLl;
    private ImageView mExcellentIv;
    private CheckedTextView mExcellentCtv;
    //确定
    private TextView mConfirmTv;
    //取消
    private TextView mCancelTv;
    private TextView mTitleDescTv;
    private Context mContext;
    private TextView mTitleTv;
    private List<ScenarioInfo.Condition> mConditionList;
    private List<ScenarioInfo.Sequence> mSequenceList;
    private List<Integer> mArgsList;
    private String mTitle, mLeft, mMiddle, mRight, mTitleDesc;
    private int mLeftTextIcon = -1;
    private int mCenterTextIcon = -1;
    private int mRightTextIcon = -1;
    private ScenarioInfo.Condition preSetCondition;
    private ScenarioInfo.Sequence preSetAction;
    private int mSkillId = -1;
    private CheckListType type = CheckListType.DEFAULT_CHECK_MODE;

    public ThreeCheckDialog(@NonNull Context context, List<ScenarioInfo.Condition> list,
                            int skillId) {
        super(context);
        this.mContext = context;
        this.mConditionList = list;
        this.mSkillId = skillId;
    }

    public ThreeCheckDialog(@NonNull Context context, List<ScenarioInfo.Condition> list) {
        super(context);
        this.mContext = context;
        this.mConditionList = list;
    }

    public ThreeCheckDialog(Context context, List<ScenarioInfo.Sequence> list, List<Integer> args) {
        super(context);
        this.mContext = context;
        this.mSequenceList = list;
        this.mArgsList = args;
    }

    public void setDialogContent(String title, String left, String middle, String right) {
        this.mTitle = title;
        this.mLeft = left;
        this.mMiddle = middle;
        this.mRight = right;
    }

    public void setTitleDesc(String desc) {
        this.mTitleDesc = desc;
    }

    public void setTextLeftDrawableIcon(@DrawableRes int leftIcon, @DrawableRes int centerIcon,
                                        @DrawableRes int rightIcon) {
        this.mLeftTextIcon = leftIcon;
        this.mCenterTextIcon = centerIcon;
        this.mRightTextIcon = rightIcon;
    }

    public void setPreSetCondition(@Nullable ScenarioInfo.Condition condition) {
        this.preSetCondition = condition;
    }

    public void setPreSetAction(@Nullable ScenarioInfo.Sequence action) {
        this.preSetAction = action;
    }

    public void setType(CheckListType type) {
        this.type = type;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mVB = SceneDialogAirQualityBinding.inflate(getLayoutInflater());
        setContentView(mVB.getRoot());
        initView();
        initClick();
        setupSetData();
    }

    private void initView() {
        mDifferenceLl = findViewById(R.id.ll_difference);
        mDifferenceIv = findViewById(R.id.iv_difference);
        mDifferenceCtv = findViewById(R.id.ctv_difference);

        mMiddleLl = findViewById(R.id.ll_middle);
        mMiddleIv = findViewById(R.id.iv_middle);
        mMiddleCtv = findViewById(R.id.ctv_middle);

        mExcellentLl = findViewById(R.id.ll_excellent);
        mExcellentIv = findViewById(R.id.iv_excellent);
        mExcellentCtv = findViewById(R.id.ctv_excellent);
        mTitleTv = findViewById(R.id.tv_air_title);
        mTitleDescTv = findViewById(R.id.tv_three_check_title_desc);
        if (ObjectUtils.isNotEmpty(mTitleDesc)) {
            mTitleDescTv.setText(mTitleDesc);
            mTitleDescTv.setVisibility(View.VISIBLE);
        }
        if (mTitle != null) {
            mTitleTv.setText(mTitle);
            mDifferenceCtv.setText(mLeft);
            mDifferenceLl.setSelected(true);
            mMiddleCtv.setText(mMiddle);
            mExcellentCtv.setText(mRight);
        } else {
            mTitleTv.setText(mContext.getString(R.string.scene_text_edit_condition_air_quality));
        }
        if (mLeftTextIcon != -1) {
            mDifferenceIv.setImageResource(mLeftTextIcon);
            mDifferenceIv.setSelected(true);
            mDifferenceIv.setVisibility(View.VISIBLE);
            if (mLeft.length() >= 4) {
                mDifferenceLl.setPadding(15, 0, 15, 0);
            }
        }
        if (mCenterTextIcon != -1) {
            mMiddleIv.setImageResource(mCenterTextIcon);
            mMiddleIv.setVisibility(View.VISIBLE);
            if (mMiddle.length() >= 4) {
                mMiddleLl.setPadding(15, 0, 15, 0);
            }
        }
        if (mRightTextIcon != -1) {
            mExcellentIv.setImageResource(mRightTextIcon);
            mExcellentIv.setVisibility(View.VISIBLE);
            if (mRight.length() >= 4) {
                mExcellentLl.setPadding(15, 0, 15, 0);
            }
        }
        changeAirState(defaultIndex);
    }

    private void initClick() {
        mDifferenceLl.setOnClickListener(this);
        mMiddleLl.setOnClickListener(this);
        mExcellentLl.setOnClickListener(this);
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this);
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this);
    }

    private void setupSetData() {
        int index = -1;
        //设置之前的状态
        if (preSetCondition != null && !ObjectUtils.checkScenarioDataIsEmpty(preSetCondition)) {
            int skillId = preSetCondition.getSkillId();
            if (skillId == SkillsListConstant.SKILL_ID_STATUS_ENVIRONMENT_AIR_QUALITY) {
                String value = preSetCondition.getInput().get(0).getValue();
                switch (value) {
                    case "0":
                        index = 0;
                        break;
                    case "1":
                        index = 1;
                        break;
                    case "2":
                        index = 2;
                        break;
                }
            } else if (skillId == SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY) {
                String value = preSetCondition.getInput().get(0).getValue();
                //智能设备的 空气质量 优 : 1,良 : 2, 差:3
                switch (value) {
                    case "3":
                        index = 0;
                        break;
                    case "2":
                        index = 1;
                        break;
                    case "1":
                        index = 2;
                        break;
                }
            }
        }
        //设置之前的动作
        if (preSetAction != null) {
            if (type == CheckListType.MEDIA_OPEN) {
                index = ObjectUtils.findPreIndex(preSetAction, mSequenceList);
            } else {
                index = ObjectUtils.findPreIndexWithInput(preSetAction, mSequenceList, mArgsList);
            }
        }
        if (index != -1) {
            changeAirState(index);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            String content = mDifferenceCtv.getText().toString();
            int index = 0;
            if (mMiddleCtv.isChecked()) {
                index = 1;
                content = mMiddleCtv.getText().toString();
            } else if (mExcellentCtv.isChecked()) {
                index = 2;
                content = mExcellentCtv.getText().toString();
            }
            if (mConditionList != null) {
                ScenarioInfo.Condition condition = mConditionList.get(0);
                condition.getInput().get(0).setValue(getAirQualityValue(index));
                condition.setDesc(content);
                condition.setCategory(mTitleTv.getText().toString());
                EventBus.getDefault().post(condition);
            } else {
                if (mSequenceList != null) {
                    if (type == CheckListType.MEDIA_OPEN) {
                        ScenarioInfo.Sequence action = mSequenceList.get(index);
                        action.getAction().setDesc(content);
                        action.getAction().setCategory(mTitle);
                        EventBus.getDefault().post(action);

                    } else {
                        if (mSequenceList.size() == 1) {
                            ScenarioInfo.Sequence action = mSequenceList.get(0);
                            action.getAction().getInput().get(0).setValue(String.valueOf(mArgsList.get(index)));
                            action.getAction().setDesc(content);
                            action.getAction().setCategory(mTitle);
                            EventBus.getDefault().post(action);
                        } else {
                            //仅有空调前除雾3个选项2个ID的特殊情况
                            ScenarioInfo.Sequence action;
                            if (index < 2) {
                                action = mSequenceList.get(0);
                                action.getAction().getInput().get(0).setValue(String.valueOf(mArgsList.get(index)));
                            } else {
                                action = mSequenceList.get(1);
                                action.getAction().getInput().get(0).setValue(String.valueOf(mArgsList.get(0)));
                            }
                            action.getAction().setDesc(content);
                            action.getAction().setCategory(mTitle);
                            EventBus.getDefault().post(action);
                        }
                    }
                }
            }
            dismissWithAnimate();
            return;
        }
        int index = -1;
        if (v.getId() == R.id.ll_difference) {
            index = 0;
        } else if (v.getId() == R.id.ll_middle) {
            index = 1;
        } else if (v.getId() == R.id.ll_excellent) {
            index = 2;
        } else if (v.getId() == R.id.btn_normal) {
            dismissWithAnimate();
            return;
        }
        changeAirState(index);
    }

    /**
     * 选中哪个选项
     *
     * @param index 0~2
     */
    private void changeAirState(int index) {
        mDifferenceCtv.setChecked(index == 0);
        mDifferenceLl.setSelected(index == 0);
        mDifferenceIv.setSelected(index == 0);

        mMiddleCtv.setChecked(index == 1);
        mMiddleLl.setSelected(index == 1);
        mMiddleIv.setSelected(index == 1);

        mExcellentCtv.setChecked(index == 2);
        mExcellentLl.setSelected(index == 2);
        mExcellentIv.setSelected(index == 2);
    }

    private String getAirQualityValue(int index) {
        if (mSkillId == SkillsListConstant.SKILL_ID_STATUS_SMART_DEVICE_AIR_QUALITY) {
            int[] values = new int[]{3, 2, 1};
            return String.valueOf(values[index]);
        } else {
            int[] values = new int[]{0x0, 0x1, 0x2};
            return String.valueOf(values[index]);
        }
    }
}
