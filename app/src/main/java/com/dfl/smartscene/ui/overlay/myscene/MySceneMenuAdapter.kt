package com.dfl.smartscene.ui.overlay.myscene

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.dfl.smartscene.R
import com.dfl.smartscene.rv.listener.OnItemClickListener

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/20
 * desc   :
 * version: 1.0
 */
/**
 * 我的场景菜单适配器
 * 现在只处理删除选项
 */
class MySceneMenuAdapter(context: Context, var listener: OnItemClickListener<String>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder?>() {
    var array: Array<String> = context.resources.getStringArray(R.array.my_scene_menu)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val rootView = inflater.inflate(R.layout.scene_recycle_item_me_my_scene_menu, parent, false)
        return ViewHolder(rootView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder !is ViewHolder) {
            return
        }
        holder.tvMenuItem.text = array[position]
        // 由于现在只有一个选项，不需要显示分割线
    }

    override fun getItemCount(): Int {
        return array.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvMenuItem: TextView = itemView.findViewById(R.id.tv_my_scene_menu_name)
        val tvMenuItemLL: LinearLayout = itemView.findViewById(R.id.ll_menu_item_container)

        init {
            tvMenuItemLL.setOnClickListener {
                val adapter = bindingAdapter as MySceneMenuAdapter
                // 点击事件传递删除操作
                adapter.listener.onItemClick(
                    absoluteAdapterPosition, adapter.array[absoluteAdapterPosition]
                )
            }
        }
    }
}