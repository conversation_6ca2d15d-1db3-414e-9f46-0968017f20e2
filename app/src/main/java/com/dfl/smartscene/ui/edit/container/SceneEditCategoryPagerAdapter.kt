package com.dfl.smartscene.ui.edit.container

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.bean.edit.EditConditionAndActionItemBean
import com.dfl.smartscene.rv.adapter.DataBindingFragmentStatePagerAdapter
import com.dfl.smartscene.ui.edit.action.air.AirActionFragment
import com.dfl.smartscene.ui.edit.action.apply.ApplyActionFragment
import com.dfl.smartscene.ui.edit.action.door.DoorsWindowsActionFragment
import com.dfl.smartscene.ui.edit.action.drive.DriveActionFragment
import com.dfl.smartscene.ui.edit.action.fridge.FridgeActionFragment
import com.dfl.smartscene.ui.edit.action.light.CarLightActionFragment
import com.dfl.smartscene.ui.edit.action.other.OtherActionFragment
import com.dfl.smartscene.ui.edit.action.recommend.RecommendActionFragment
import com.dfl.smartscene.ui.edit.action.seat.SeatActionFragment
import com.dfl.smartscene.ui.edit.action.smart.SmartDeviceActionFragment
import com.dfl.smartscene.ui.edit.action.system.SystemActionFragment
import com.dfl.smartscene.ui.edit.condition.status.door.DoorsAndWindowsStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.environment.EnvironmentStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.lighting.LightingStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.link.LinkStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.location.LocationStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.navigation.NavigationStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.oms.OMSStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.other.OtherStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.seat.SeatStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.smartdevice.SmartDeviceStatusFragment
import com.dfl.smartscene.ui.edit.condition.status.time.TimeStatusFragment
import com.dfl.smartscene.ui.edit.condition.trigger.charge.ChargeTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.door.DoorsTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.drive.DriveTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.environment.EnvironmentTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.lighting.LightingTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.location.LocationTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.oms.OMSTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.seat.SeatTriggerFragment
import com.dfl.smartscene.ui.edit.condition.trigger.time.TimeTriggerFragment
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   : 条件动作界面Fragment适配器
 * version: 1.0
 */
class SceneEditCategoryPagerAdapter(
    fragmentManager: FragmentManager, lifecycle: Lifecycle
) : DataBindingFragmentStatePagerAdapter<EditConditionAndActionItemBean>(
    fragmentManager, lifecycle
) {
    /** 场景的动作*/
    var sequence: ScenarioInfo.Sequence? = null

    /** 场景的状态条件 */
    var condition: ScenarioInfo.Condition? = null

    /**场景的触发条件*/
    var edgeCondition: ScenarioInfo.Condition? = null

    /**编辑状态并且选择了一级菜单*/
    var isEditAndSelect = false

    /**
     * 设置之前的动作
     * @param sequence
     */
    fun setPreSetAction(sequence: ScenarioInfo.Sequence?) {
        this.sequence = sequence
    }

    /**
     * 设置之前的触发条件
     * @param condition
     */
    fun setPreSetEdgeCondition(condition: ScenarioInfo.Condition?) {
        this.edgeCondition = condition
    }

    /**
     * 设置之前的状态条件
     * @param condition
     */
    fun setPreSetCondition(condition: ScenarioInfo.Condition?) {
        this.condition = condition
    }

    /**
     * 根据条件\动作类型打开对应Fragment
     * @param position
     * @return 对应类别Fragment
     */
    override fun createFragment(position: Int): Fragment {
        if (isEditAndSelect) {
            edgeCondition = null
            condition = null
            sequence = null
        }
        val bean = getData(position)
        bean?.let {
            return when (it.eventCode) {
                //------------条件页面----------
                //时间
                ConditionEventCode.TimeCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        TimeTriggerFragment(it.skillId ?: -1, edgeCondition)
                    } else {
                        TimeStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //位置
                ConditionEventCode.LocationCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        LocationTriggerFragment(edgeCondition)
                    } else {
                        LocationStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //导航
                ConditionEventCode.NavigationCode -> {
                    NavigationStatusFragment(it.skillId ?: -1, condition) //只有状态条件
                }
                //环境
                ConditionEventCode.EnvironmentCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        EnvironmentTriggerFragment(edgeCondition)
                    } else {
                        EnvironmentStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //驾驶
                ConditionEventCode.DriveCode -> {
                    DriveTriggerFragment(edgeCondition) //只有触发条件
                }
                //门窗
                ConditionEventCode.DoorCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        DoorsTriggerFragment(edgeCondition)
                    } else {
                        DoorsAndWindowsStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //座椅
                ConditionEventCode.SeatCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        SeatTriggerFragment(edgeCondition)
                    } else {
                        SeatStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //灯光
                ConditionEventCode.LightingCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        LightingTriggerFragment(edgeCondition)
                    } else {
                        LightingStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //充放电
                ConditionEventCode.ChargeCode -> {
                    ChargeTriggerFragment(edgeCondition)
                }
                //oms 座舱感知
                ConditionEventCode.OMSCode -> {
                    if (it.sceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
                        OMSTriggerFragment(edgeCondition)
                    } else {
                        OMSStatusFragment(it.skillId ?: -1, condition)
                    }
                }
                //其他 胎压
                ConditionEventCode.TirePressureCode -> {
                    OtherStatusFragment(it.skillId ?: -1, condition)
                }
                //连接
                ConditionEventCode.LinkCode -> {
                    LinkStatusFragment(it.skillId ?: -1, condition)
                }
                //智能设备
                ConditionEventCode.SmartDeviceCode -> {
                    SmartDeviceStatusFragment(it.skillId ?: -1, condition) //只有状态条件
                }

                //-------------动作页面--------------
                //推荐
                ActionEventCode.RecommendCode -> {
                    RecommendActionFragment(sequence)
                }
                //空调
                ActionEventCode.AirConditionerCode -> {
                    //打开动作->空调页面
                    AirActionFragment(sequence)
                }
                //门窗
                ActionEventCode.DoorWindowCode -> {
                    DoorsWindowsActionFragment(sequence)
                }
                //灯光
                ActionEventCode.CarLightCode -> {
                    CarLightActionFragment(sequence)
                }
                //座椅
                ActionEventCode.ChairCode -> {
                    SeatActionFragment(sequence)
                }
                //冰箱
                ActionEventCode.FridgeCode -> {
                    FridgeActionFragment(sequence)
                }
                //驾驶
                ActionEventCode.DriveCode -> {
                    DriveActionFragment(sequence)
                }
                //应用
                ActionEventCode.ApplicationCode -> {
                    ApplyActionFragment(sequence)
                }
                //系统
                ActionEventCode.SystemCode -> {
                    SystemActionFragment(sequence)
                }

                //智能设备
                ActionEventCode.SmartDeviceCode -> {
                    SmartDeviceActionFragment(sequence)
                }
                //其他
                ActionEventCode.OtherCode -> {
                    OtherActionFragment(it.skillId ?: 0, sequence)
                }

                else -> {
                    TimeTriggerFragment(it.skillId ?: 0, edgeCondition)
                }
            }
        }

        return TimeTriggerFragment(SkillsListConstant.SKILLS_ID_TRIGGER_TIME_TIME_POINT, edgeCondition)
    }
}