package com.dfl.smartscene.ui.main.help

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * author : r<PERSON><PERSON><PERSON>
 * e-mail : r<PERSON><PERSON><PERSON>@dfl.com.cn
 * time   : 2025/04/23
 * desc   : 帮助页面pager适配器
 * version: 1.0
 */
class HelpBannerFragmentsAdapter(fragmentManager: FragmentManager, lifecycle: Lifecycle) :
    FragmentStateAdapter(fragmentManager, lifecycle) {
    override fun getItemCount(): Int = 4

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> HelpDialogIntroFragment()
            1 -> HelpDialogCreateFragment()
            2 -> HelpDialogPerformFragment()
            else -> HelpDialogSetCardFragment()
        }
    }
}