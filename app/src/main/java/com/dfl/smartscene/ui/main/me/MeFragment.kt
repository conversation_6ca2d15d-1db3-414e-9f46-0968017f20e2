package com.dfl.smartscene.ui.main.me

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.os.Parcel
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.chad.library.adapter.base.BaseQuickAdapter
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.base.MVVMFragment
import com.dfl.android.common.global.GlobalConfig
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.FragmentUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.customapi.DataEventSceneDetailBean
import com.dfl.smartscene.bean.main.AutoSceneResult
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.community.post.CommunityPostFragment
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneFragmentMeBinding
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.soa.WidgetUpdateHelper
import com.dfl.smartscene.ui.edit.SceneNewEditActivity
import com.dfl.smartscene.ui.edit.SceneNewEditEnterType
import com.dfl.smartscene.ui.edit.manager.SceneDataEventManager
import com.dfl.smartscene.ui.main.MainActivity
import com.dfl.smartscene.ui.main.help.HelpDialogFragment
import com.dfl.smartscene.ui.main.help.SoftwareRecordDialogFragment
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.ui.overlay.myscene.MySceneMenuPopup
import com.dfl.smartscene.util.TrackUtils
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.StaticListener
import com.dfl.smartscene.widget.recycleview.ItemHolderMoveCallback
import com.dfl.smartscene.widget.recycleview.MeTouchHelperCallback
import com.dfl.voicehelper.util.VoiceUtils
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.*


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/18
 * desc   :我的场景界面
 * version: 1.0
 */
class MeFragment : MVVMFragment<SceneFragmentMeBinding, MeViewModel>(), View.OnClickListener, ItemHolderMoveCallback {
    companion object {

        /**允许用户点击的最长时间间隔*/
        const val CLICK_INTERVAL_TIME = 1000

        /** 最大的收藏场景数量上限 */
        const val MAX_COLLECT_COUNT = 3

        /** 超过此场景数量则添加一个底部视图*/
        const val ADD_FOOTER_VIEW_COUNT = 6
    }

    /**更多菜单*/
    private var mPopup: MySceneMenuPopup = MySceneMenuPopup(object : BaseMenuPopup.OnMenuClickListener<MySceneBean> {
        override fun onMenuItemSelected(
            menuPopup: BaseMenuPopup<MySceneBean>, menuIndex: Int, dataIndex: Int, data: MySceneBean?
        ) {
            data ?: return
            when (menuIndex) {
                MySceneMenuPopup.INDEX_POST -> { //发布到社区场景
                    //检查用户登录打开跳转弹窗
                    UserCenterManager.checkUserLoginStatusWithDialog(childFragmentManager)?.let {
                        val fragment = data.scenario.let {
                            CommunityPostFragment.newInstance(
                                copyScenarioBean(it), "MeFragment"
                            )
                        }
                        fragment.let {
                            activity?.let { it1 ->
                                TrackUtils.clickPostScene(true)
                                (activity as MainActivity).showPostView()
                                FragmentUtils.addFragment(
                                    it1, R.id.fl_post_view, it
                                )
                            }
                        }
                    }
                }

                MySceneMenuPopup.INDEX_TOP -> { //添加到智能卡片
                    handleSelectTop(dataIndex, data)
                }

                MySceneMenuPopup.INDEX_DELETE -> { //删除卡片
                    with(DefaultDialogFragment) {
                        showDialog(
                            childFragmentManager,
                            "",
                            "",
                            getString(R.string.scene_text_me_delete_scene_confirm),
                            getString(R.string.scene_button_common_delete),
                            getString(R.string.scene_text_common_cancel),
                            object : DefaultDialogFragment.OnDialogButtonClickListener {
                                override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                                    mViewModel.deleteScene(data)
                                    SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                                    dialog.dismissWithAnimate()
                                }

                                override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                                    dialog.dismissWithAnimate()
                                }
                            },
                            cancelTouchOutside = true
                        )
                        SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
                    }
                }
            }
            menuPopup.dismiss()
        }
    })

    /**我的场景rv adapter*/
    private var mAdapter: MeSceneAdapter? = null

    /**上一次点击时间*/
    private var lastClickTime = 0L

    /**拖拽事件回调*/
    private var myLastItemFixedTouchHelperCallback = MeTouchHelperCallback()

    /**实现拖拽*/
    private val mItemTouchHelper = ItemTouchHelper(myLastItemFixedTouchHelperCallback)

    /**动效handler*/
    private val mBoxHandler = Handler(Looper.getMainLooper())

    /**
     * 场景列表数据
     */
    private val mSceneList = ArrayList<MySceneBean>()

    /**
     * 是否为用户编辑场景时触发的设置自动执行开关的操作
     */
    private var mIsAutoConfigFromEdit = false


    /**用于滑动禁止*/
    private var mScrollManager: MeSceneAdapter.ScrollGridLayoutManager? = null

    /**底部备案view*/
    private var mFilingsFootView: View? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_fragment_me
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<MeViewModel> {
        return MeViewModel::class.java
    }


    override fun initView(view: View?) {
        super.initView(view)
        mViewModel.initResources(resources) //初始化刷新控件
        initRefreshLayout()
        initRecycleView()
        initListener()
    }

    private fun initRefreshLayout() {
        mViewDataBinding.slUpdate.setOnRefreshListener {
            clearBoxAnim()
            SceneManager.requestSceneInfoList() //手动刷新时清空所有缓存，可解决提示重复执行的错误
            SceneManager.clearCacheData()
        }
    }

    private val changeTime = 330L
    private fun initRecycleView() {
        //添加拖动效果
        mItemTouchHelper.attachToRecyclerView(mViewDataBinding.rvMySceneList)
        mAdapter = MeSceneAdapter(R.layout.scene_recycle_item_me_my_scene)
        //添加上下蒙层
        mViewDataBinding.rvMySceneList.isVerticalFadingEdgeEnabled = true
        mViewDataBinding.rvMySceneList.setFadingEdgeLength(AutoSizeUtils.dp2px(context, 10F))
        //添加rv item动画
        mViewDataBinding.rvMySceneList.itemAnimator = MeItemAnimator()
        mViewDataBinding.rvMySceneList.itemAnimator?.let {
            it.addDuration = 160L
            it.removeDuration = 0L
            it.moveDuration = changeTime
            it.changeDuration = 0L
            if (it is SimpleItemAnimator) {
                it.supportsChangeAnimations = true
            }
        }
        mViewDataBinding.rvMySceneList.adapter = mAdapter

        mViewDataBinding.rvMySceneList.addItemDecoration(MySceneItemDecoration())
        mScrollManager = MeSceneAdapter.ScrollGridLayoutManager(requireContext(), 3)
        mViewDataBinding.rvMySceneList.recycledViewPool.setMaxRecycledViews(
            BaseQuickAdapter.EMPTY_VIEW, 0
        )
        mScrollManager?.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                if (mAdapter?.hasFooterLayout() == true && position == mAdapter?.data?.size) {
                    return 3
                }
                return 1
            }
        }
        mViewDataBinding.rvMySceneList.layoutManager = mScrollManager
        mViewDataBinding.rvMySceneList.setHasFixedSize(true)
        mAdapter?.setNewInstance(mSceneList)

        //判断列表是否为空，如为空则需要显示空界面
        judgeEmptyList()
        //获取数据
        SceneManager.requestSceneInfoList()
    }

    private fun addSceneClick() {
        TrackUtils.clickBtnNewScene()
        clearBoxAnim()
        addNewScene()
    }

    private fun initListener() {
        mViewDataBinding.tvRecordMySceneListEmpty.setOnClickListener(this) //备案号
        mViewDataBinding.btnAddScene.setOnClickListener { addSceneClick() }
        VoiceUtils.setRegularTextData( //可见即可说
            mViewDataBinding.btnAddScene, resources.getString(R.string.scene_text_voice_helper_create_scene)
        )
        (context as MainActivity).setOnMainViewClickListener(object : MainActivity.OnMainViewClickListener {
            override fun addSceneClick() {
                <EMAIL>()
            }

            override fun tabClick(position: Int) { //点击tab我的触发的埋点
                if (position == MainActivity.MainTagType.ME.value) { //点击我的
                    setClickMySceneDataEvent()
                }
            }
        })
        myLastItemFixedTouchHelperCallback.setItemMoveCallBack(this)
        setAdapterListener()
        initLiveEventBusListener()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initLiveEventBusListener() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_GET_SCENE_LIST, Int::class.java).observe(
            viewLifecycleOwner
        ) { result ->
            if (result == 0 || result == 1) { //				if (mViewDataBinding.srlMain.isRefreshing) {
                //					mViewDataBinding.srlMain.isRefreshing = false//停止刷新
                //				}
                mViewDataBinding.slUpdate.finishRefresh(true)

                mSceneList.clear()
                val list = SceneManager.getSceneInfoList()
                list.forEach {
                    it.meIconBeanList = it.findIconBeanList()
                }
                mSceneList.addAll(list)
                CommonLogUtils.logD(TAG, "adapterSize=" + mAdapter?.data?.size)
                updateSceneListWithRunning()
                mAdapter?.notifyDataSetChanged()
                judgeSceneRefresh()
                judgeEmptyList()
                judgeAddFooterView()
                CommonLogUtils.logI(TAG, "界面数据加载完成--------")
            } else { //超时处理,异常
                //				if (mViewDataBinding.srlMain.isRefreshing) {
                //					mViewDataBinding.srlMain.isRefreshing = false//停止刷新
                //				}
                mViewDataBinding.slUpdate.finishRefresh(false)
            }
        }
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_DELETE_SUCCESS, String::class.java)
            .observe(viewLifecycleOwner) {
                handleDeleteSuccess(it)
            }
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_DELETE_ALL_SUCCESS, Boolean::class.java)
            .observe(viewLifecycleOwner) {
                if (it) {
                    mSceneList.clear()
                    mAdapter?.notifyDataSetChanged()
                    judgeEmptyList()
                    judgeAddFooterView()
                    mViewModel.saveSceneSequence(mAdapter?.data!!)
                    WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)
                    SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
                }
            }
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_MODIFY_SUCCESS, MySceneBean::class.java)
            .observe(viewLifecycleOwner) {
                it.meIconBeanList = it.findIconBeanList()
                handleModifySceneSuccess(it)
            }
        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_ADD_SUCCESS, MySceneBean::class.java)
            .observe(viewLifecycleOwner) {
                it.meIconBeanList = it.findIconBeanList()
                handleSceneAddSuccess(it)
            }

        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_START, String::class.java).observe(viewLifecycleOwner) {
            if (it != null) {
                val index = findSceneIndexById(it)
                if (index != -1) {
                    mAdapter?.getItem(index)?.isSceneStart = true
                    mAdapter?.notifyItemChanged(index)
                }
            }
            judgeSceneRefresh()
        }

        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_END, String::class.java).observe(viewLifecycleOwner) {
            if (it != null) {
                val index = findSceneIndexById(it)
                if (index != -1) {
                    mAdapter?.getItem(index)?.isSceneStart = false
                    mAdapter?.notifyItemChanged(index) //                    CommonLogUtils.logD(TAG,"场景执行结束，停止动画")
                }
            }
            judgeSceneRefresh()
        }

        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_AUTO_SUCCESS, AutoSceneResult::class.java)
            .observe(viewLifecycleOwner) {
                val index = findSceneIndexById(it.sceneId)
                if (index != -1) {
                    mAdapter?.getItem(index)?.scenario?.isAutoRun = it.autoBtnRun == 0
                    mAdapter?.getItem(index)?.scenario?.scenarioInfo?.autoExeFlag = it.autoExeRun
                    //mAdapter?.getItem(index)?.scenario?.scenarioInfo?.executeFrequency = it.executeFrequency
                    //如果是关闭的接口，并且是编辑触发的，则不去变更界面
                    if (mIsAutoConfigFromEdit) {
                        mIsAutoConfigFromEdit = false
                    } else {
                        mAdapter?.notifyItemChanged(index)
                        LiveEventBus.get(GlobalLiveEventConstants.KEY_SCENE_CHANGED, Int::class.java).post(4)
                    }
                }
            }

    }

    private fun handleDeleteSuccess(it: String?) {
        val index = findSceneIndexById(it)
        if (index != -1) {
            mAdapter?.removeAt(index)
            judgeEmptyList()
            judgeAddFooterView()

            mAdapter?.data!!.forEachIndexed { index, mySceneBean ->
                mySceneBean.isTop = index <= 2
            }
            mAdapter?.notifyDataSetChanged()
            //场景卡片移动也需要保存场景顺序，防止突然断电数据丢失
            mViewModel.saveSceneSequence(mAdapter?.data!!)
            WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)
            SoundManager.playSoundEffect(SoundManager.SoundType.DELETE)
        }
    }

    /**
     * 更新场景列表的数据，当有运行的场景时
     */
    private fun updateSceneListWithRunning() {
        if (SceneManager.hasSceneRunning()) {
            for (sceneInfo in mSceneList) {
                val sceneId = sceneInfo.scenario.scenarioInfo?.scenarioId
                if (TextUtils.isEmpty(sceneId)) {
                    continue
                }
                if (SceneManager.isSceneRunning(sceneId)) { //如果是正在运行的场景，则需要设置状态为运行中
                    sceneInfo.isSceneStart = true
                }
            }
        }
    }

    private fun handleModifySceneSuccess(it: MySceneBean?) {
        if (it != null) {
            val index = findSceneIndexById(it.scenario.scenarioInfo?.scenarioId)
            if (index != -1) {
                it.isNewSign = true
                it.isAddNewScene2ShowAnima = true
                mAdapter?.setData(index, it)
                mViewDataBinding.rvMySceneList.smoothScrollToPosition(index)
                delayCloseBox(index) //编辑场景后也需要更新一下widget
                lifecycleScope.launch(Dispatchers.IO) {
                    delay(1000) //延时1秒再播放音效
                    if (isActive) {
                        SoundManager.playSoundEffect(SoundManager.SoundType.DONE)
                    }
                }
                mViewModel.saveSceneSequence(mAdapter?.data!!)
                WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)
            }
        }
    }

    private fun handleSceneAddSuccess(it: MySceneBean?) {
        if (it != null) {
            mAdapter?.addData(it)
            mBoxHandler.removeCallbacksAndMessages(null)
            val size = mAdapter?.data?.size ?: 0
            judgeEmptyList()
            judgeAddFooterView()

            mAdapter?.data!!.forEachIndexed { index, mySceneBean ->
                mySceneBean.isTop = index <= 2
            }
            mAdapter?.notifyDataSetChanged()
            mViewModel.saveSceneSequence(mAdapter?.data!!)
            WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)

            if (size > 0) {
                mViewDataBinding.rvMySceneList.postDelayed({
                    mViewDataBinding.rvMySceneList.smoothScrollToPosition(size - 1)
                }, 500)

                delayCloseBox(mAdapter?.data?.size?.minus(1))
            }

            lifecycleScope.launch(Dispatchers.IO) {
                delay(1000) //延时1秒再播放音效
                if (isActive) {
                    SoundManager.playSoundEffect(SoundManager.SoundType.DONE)
                }
            }
        }
    }

    /**
     * 判断是否可以刷新场景数据
     */
    private fun judgeSceneRefresh() {
        mViewDataBinding.slUpdate.isEnabled = !SceneManager.hasSceneRunning()
    }

    /**
     * 根据rv数据数量判断显示哪个备案号
     */
    private fun judgeAddFooterView() {

        if (mAdapter?.hasFooterLayout() == false) {
            mFilingsFootView =
                LayoutInflater.from(context).inflate(R.layout.scene_recycle_item_no_data_common_foot_view, null)
            mFilingsFootView?.let {
                mAdapter?.addFooterView(it)
            }
        }
        val tvFilings = mFilingsFootView?.findViewById<TextView>(R.id.tv_me_beian_foot)
        tvFilings?.setOnClickListener {
            showSoftwareRecordDialog()
        }

        val lp = tvFilings?.layoutParams as ViewGroup.MarginLayoutParams
        if (mAdapter?.data?.size!! < 4) {
            lp.topMargin = AutoSizeUtils.dp2px(context, 505F)
        } else if (mAdapter?.data?.size!! in 4..6) {
            lp.topMargin = AutoSizeUtils.dp2px(context, 72F)
        } else {
            lp.topMargin = AutoSizeUtils.dp2px(context, 72F)
        }
        tvFilings.layoutParams = lp
    }

    //进入编辑场景
    private fun setAdapterListener() {
        mAdapter?.setOnItemClickListener(object : StaticListener() {
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                if (!isTimeEnabled()) {
                    return
                }
                val myScene = adapter.getItem(position) as MySceneBean
                if (context != null) {
                    if (myScene.isSceneStart == true) {
                        //执行中时不能进行编辑操作
                        mViewModel.stopScene(myScene)
                        return
                    }
                    //当主页卡片正在播放刷新动画时，及时notify更新
                    if (mAdapter?.data?.get(position)?.isAddNewScene2ShowAnima == true) {
                        mAdapter?.notifyItemChanged(position)
                    }
                    //如果当前场景正在等候执行，则不能往下走
                    if (SceneManager.isInAutoRunList(myScene.scenario.scenarioInfo?.scenarioId)) {
                        CommonToastUtils.show(R.string.scene_toast_scene_in_auto_run_list)
                        CommonLogUtils.logE(TAG, "${myScene.scenario.scenarioInfo?.scenarioId} 等候执行")
                        return
                    }
                    //非新建场景，需要变更一下状态
                    myScene.isAddScene = false
                    SceneNewEditActivity.launchSceneEditWithMyScene(
                        context, myScene, enterType = SceneNewEditEnterType.Me, scenario_L3id = ""
                    )
                    //在需要编辑场景的时候，如果该场景为自动执行状态，那么需要发送中止自动执行请求
                    if (myScene.scenario.isAutoRun == true) {
                        mIsAutoConfigFromEdit = true
                        //第二个参数是自动执行动作请求，1表示ccm不允许/终止自动执行。第三个参数是自动执行按钮开关状态，0表示开启。第三个参数好像没用
                        mViewModel.autoExecuteScenario(myScene, 1, 0, 0)
                        //如果是编辑场景时关闭的自动执行操作，则需要设置一下该变量
                    }
                }
            }
        })

        mAdapter?.setOnItemLongClickListener { _, _, position -> //长按开启拖拽
            mViewDataBinding.rvMySceneList.findViewHolderForAdapterPosition(position)?.let {
                mItemTouchHelper.startDrag(it)
            }
            true
        }
        mAdapter?.addChildClickViewIds(
            R.id.ll_menu, R.id.btn_toggle_scene_state, R.id.cl_auto_run_hot_area, R.id.iv_top //, R.id.switch_auto_run
        )
        mAdapter?.setOnItemChildClickListener { adapter, view, position ->
            val myScene = adapter.getItem(position) as MySceneBean
            if (view.id == R.id.cl_auto_run_hot_area) {
                mIsAutoConfigFromEdit = false
                //取相反的值进行发送
                mViewModel.autoExecuteScenario(
                    myScene,
                    if (myScene.scenario.isAutoRun == true) 1 else 0,
                    if (myScene.scenario.isAutoRun == true) 1 else 0,
                    myScene.scenario.executeFrequency ?: 0
                )
            }
            if (view != null) {
                //增加防快速点击的判断
                if (!DebouncingUtils.isValid(view)) {
                    return@setOnItemChildClickListener
                }
            }
            if (position == RecyclerView.NO_POSITION) {
                return@setOnItemChildClickListener
            }
            if (SceneManager.isInAutoRunList(myScene.scenario.scenarioInfo?.scenarioId)) {
                CommonToastUtils.show(R.string.scene_toast_scene_in_auto_run_list)
                CommonLogUtils.logE(TAG, "${myScene.scenario.scenarioInfo?.scenarioId} 等候执行")
                return@setOnItemChildClickListener
            }
            when (view.id) {
                R.id.ll_menu -> {
                    onMenuClick(position, myScene)
                    return@setOnItemChildClickListener
                }

                R.id.btn_toggle_scene_state -> {
                    //不能频繁进行点击
                    if (System.currentTimeMillis() - lastClickTime < CLICK_INTERVAL_TIME) {
                        CommonToastUtils.show(getString(R.string.scene_toast_me_later_to_try))
                        lastClickTime = System.currentTimeMillis()
                        return@setOnItemChildClickListener
                    }
                    lastClickTime = System.currentTimeMillis()
                    if (myScene.isSceneStart == true) {
                        onStopScene(position, myScene)
                    } else {
                        onStartScene(position, myScene)
                    }
                }
                R.id.iv_top -> {
                    activity?.let {
                        HelpDialogFragment.newInstance(3).showNow(it.supportFragmentManager, "FRAGMENT_TAG_HELP")
                    }
                }
            }
        }
    }

    private class MySceneItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
        ) {
            outRect.set(26, 0, 26, 56)
        }
    }

    /**
     * 场景卡片-更多菜单点击
     * @param position
     * @param scene
     */
    private fun onMenuClick(position: Int, scene: MySceneBean?) {
        CommonLogUtils.logV(TAG, "onMenuClick position:$position")
        scene ?: return
        mPopup.rvData = scene
        mPopup.rvDataIndex = position
        mAdapter?.getViewByPosition(position, R.id.cl_root_me)?.let {
            mPopup.show(it)
        }
    }

    /**
     * 用户手动执行场景时，adapter处传入点击事件并在此进行处理
     */
    private fun onStartScene(position: Int, scene: MySceneBean?) {
        val groupId = scene?.scenario?.scenarioInfo?.scenarioId
        CommonLogUtils.logV(TAG, "onStartScene position:$position groupId:$groupId")
        groupId ?: return
        TrackUtils.clickMyManualScene(
            scene.scenario.scenarioInfo?.scenarioName ?: "", scene.scenario.scenarioInfo?.scenarioId ?: ""
        )
        mViewModel.startScene(scene)
    }


    /**
     * 用户手动退出执行场景时，adapter处传入点击事件并在此进行处理
     */
    private fun onStopScene(position: Int, scene: MySceneBean?) {
        val groupId = scene?.scenario?.scenarioInfo?.scenarioId
        CommonLogUtils.logV(TAG, "onStopScene position:$position groupId:$groupId")
        groupId ?: return
        mViewModel.stopScene(scene)
    }

    override fun onClick(v: View?) {
        v ?: return
        if (v.id == R.id.tv_record_my_scene_list_empty) {
            showSoftwareRecordDialog()
        }
    }

    private fun showSoftwareRecordDialog() {
        activity?.let {
            SoftwareRecordDialogFragment().showNow(
                it.supportFragmentManager, "FRAGMENT_TAG_SOFTWARE_RECORD"
            )
        }
    }

    //复制对象 https://blog.51cto.com/u_16213441/9044366
    private fun copyScenarioBean(source: ScenarioBean): ScenarioBean {
        val parcel = Parcel.obtain()
        source.writeToParcel(parcel, 0)
        parcel.setDataPosition(0)
        val copy = ScenarioBean.createFromParcel(parcel)
        parcel.recycle()
        return copy
    }

    /** 处理选择置顶后的逻辑
     * @param position 点击置顶的位置
     * @param scene 点击位置的场景
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun handleSelectTop(position: Int, scene: MySceneBean) { //获取数据源
        val dataList = mAdapter?.data
        dataList ?: return //判空
        if (!scene.isTop) { //非收藏状态下需要判断。取消收藏不需要判断最大值限制
            //处理最大收藏值。如果超标禁止后面的状态变化操作
            val isTopDataLists = dataList.filter {
                return@filter it.isTop
            }
            if (isTopDataLists.size >= MAX_COLLECT_COUNT) { //从UX2.5开始，不用弹框进行提示，而是改用toast提示
                CommonToastUtils.show(R.string.scene_toast_my_scene_favorite_max_limit)
                SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
                return
            }
        } //非置顶数据的第一项下标
        var firstNoTopIndex: Int = -1 //从点击位置的下一项开始遍历查找非置顶的那一项下标
        for (index in 0 until dataList.size) {
            if (!dataList[index].isTop) {
                firstNoTopIndex = index
                break
            }
        }
        scene.isTop = !scene.isTop

        if (scene.isTop) {
            mViewDataBinding.rvMySceneList.itemAnimator?.let {
                it.moveDuration = 0L
            }
            //20220908需求PRD1.1变更，新增的收藏场景位置自动排到收藏的最后一个，而不是排到第一个
            //如果收藏的位置就是找到的非收藏场景的首位，则无须进行调换
            //如果首个非收藏场景是在首位或者最后一位，则无须调整位置,此种情况包含在首位为点击位置的情况之下
            if (firstNoTopIndex != position) {
                if (firstNoTopIndex <= 0) {
                    dataList.add(0, dataList.removeAt(position))
                    mAdapter?.notifyItemMoved(position, 0)
                } else {
                    //需要把收藏的场景移动到之前首位非收藏场景的位置处
                    //                    Collections.swap(dataList, firstNoTopIndex, position)
                    dataList.add(firstNoTopIndex, dataList.removeAt(position))
                    mAdapter?.notifyItemMoved(position, firstNoTopIndex)
                }
            }
            mAdapter?.notifyItemRangeChanged(0, position + 1)
            if (position > 8) {
                val itemHeight: Int = mViewDataBinding.rvMySceneList.getChildAt(0).height
                val scrollDistance = (position / 3 + 3) * itemHeight
                mViewDataBinding.rvMySceneList.smoothScrollBy(0, -scrollDistance)
            } else {
                mViewDataBinding.rvMySceneList.scrollToPosition(0)
            }
            mViewDataBinding.rvMySceneList.itemAnimator?.let {
                it.moveDuration = changeTime
            }
        } else {
            //取消收藏时，一定会有一个收藏场景，非收藏场景位置一定大于0
            // 没找到非收藏场景，则说明当前全部都是收藏场景，则将取消收藏的场景移动到最后
            if (firstNoTopIndex >= 1) {
                //如果取消的收藏场景与非收藏场景首位相邻，则无须操作
                if (firstNoTopIndex != position + 1) {
                    //找到非置顶数据的第一项，然后移除取消置顶的数据，并将之添加到找到的对应下标位置
                    dataList.add(firstNoTopIndex - 1, dataList.removeAt(position))
                    mAdapter?.notifyItemMoved(position, firstNoTopIndex - 1)
                }
                mAdapter?.notifyItemRangeChanged(0, firstNoTopIndex + 1)
            } else {
                // 当场景都为收藏场景时，将取消收藏的场景显示在最后
                dataList.add(dataList.size - 1, dataList.removeAt(position))
                mAdapter?.notifyItemMoved(position, dataList.size - 1)
                mAdapter?.notifyItemRangeChanged(0, dataList.size)
                //                mAdapter?.notifyDataSetChanged()
            }
        }
        //收藏导致顺序发生变化，需要保存更新后的顺序
        mViewModel.saveSceneSequence(dataList)
        //更新桌面卡片中的场景列表
        WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            clearBoxAnim()
        }
    }

    override fun onResume() {
        super.onResume()
        TrackUtils.viewStart(TrackUtils.PageNameType.My)
    }

    override fun onPause() {
        super.onPause()
        TrackUtils.viewEnd(TrackUtils.PageNameType.My)
        clearBoxAnim()
        mViewDataBinding.slUpdate.finishRefresh(true)
    }


    /**
     * 判断列表是否为空，如为空则需要显示空界面
     */
    private fun judgeEmptyList() {
        val count = mAdapter?.data?.size
        if (count != null) {
            if (count > 0) {
                mViewDataBinding.rvMySceneList.visibility = View.VISIBLE
                mViewDataBinding.tvMySceneListEmpty.visibility = View.GONE
                mViewDataBinding.btnAddScene.visibility = View.GONE
                mViewDataBinding.tvRecordMySceneListEmpty.visibility = View.GONE
            } else {
                mViewDataBinding.rvMySceneList.visibility = View.GONE
                mViewDataBinding.tvMySceneListEmpty.visibility = View.VISIBLE
                mViewDataBinding.btnAddScene.visibility = View.VISIBLE
                mViewDataBinding.tvRecordMySceneListEmpty.visibility = View.VISIBLE
            }
            (context as MainActivity).setBtnAddSceneVisible(count == 0)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        CommonLogUtils.logV(TAG, "me fragment on destroy") //页面销毁需要保存记忆当前场景顺序
        mViewModel.saveSceneSequence(mAdapter?.data!!)
        myLastItemFixedTouchHelperCallback.destroyItemMoveCallBack()
        clearBoxAnim()
    }

    /**拖拽卡片开始移动*/
    override fun onItemHolderMoveStart(fromPosition: Int?) {
        fromPosition?.let {
            //拖拽时设置不能下拉刷新
            mViewDataBinding.slUpdate.isEnabled = false
            (context as MainActivity).showDragView()
        }
    }

    /**拖拽卡片移动结束*/
    @SuppressLint("NotifyDataSetChanged")
    override fun onItemHolderMoveEnd(toPosition: Int?) {
        (context as MainActivity).disShowDragView()
        mViewDataBinding.slUpdate.isEnabled = true
        toPosition?.let {
            if (it >= mAdapter?.data?.size!!) return@let
            mAdapter?.data!!.forEachIndexed { index, mySceneBean ->
                mySceneBean.isTop = index <= 2
            }
            mAdapter?.notifyDataSetChanged()
            //场景卡片移动也需要保存场景顺序，防止突然断电数据丢失
            mViewModel.saveSceneSequence(mAdapter?.data!!)
            //每次变更都更新widget卡片
            WidgetUpdateHelper.updateDeskCardSceneList(mSceneList)
        }
    }

    //处理延时两秒关闭特效框
    private fun delayCloseBox(position: Int?) {
        position?.let {
            mBoxHandler.postDelayed({
                val size = mAdapter?.data?.size
                if (size != null && position + 1 > size) {
                    mAdapter?.data?.get(it)?.isAddNewScene2ShowAnima = true
                    return@postDelayed
                }
                mAdapter?.data?.get(it)?.isAddNewScene2ShowAnima = false
                mAdapter?.notifyItemChanged(it)
//                mViewDataBinding.rvMySceneList.scrollToPosition(it)
            }, 2000)
        }
    }

    private fun clearBoxAnim() {
        mBoxHandler.removeCallbacksAndMessages(null)
        mAdapter?.data?.let {
            for (i in it.indices) {
                if (it[i].isAddNewScene2ShowAnima == true) {
                    mAdapter?.data?.get(i)?.isAddNewScene2ShowAnima = false
                    mAdapter?.notifyItemChanged(i)
                }
            }
        }
    }

    private fun addNewScene() {
        if (context != null) {
            val dataList = mAdapter?.data
            dataList ?: return
            //如果超过上限数量，则不能进行添加操作
            val dataSize = dataList.size
            if (dataSize >= GlobalConfig.SCENE_COUNT_MAX) {
                //从UX2.5开始，改用toast提示
                CommonToastUtils.show(R.string.scene_text_me_scene_limit)
                SoundManager.playSoundEffect(SoundManager.SoundType.TOAST)
                return
            }
            SceneNewEditActivity.launchSceneEditWithMyScene(
                context, MySceneBean(
                    ScenarioBean(
                        ScenarioInfo(
                            mViewModel.getNewSceneId(),
                            mViewModel.getNewSceneName(),
                            "",
                            BuildConfig.VERSION_CODE,
                            1,
                            1,
                            0,
                            "",
                            0,
                            null,
                            null,
                            null
                        ), isAskBeforeAutoRun = false, isTemplateScene = false, isAutoRun = false, executeFrequency = 0
                    ), isSceneStart = false, isTop = false, isAddNewScene2ShowAnima = true, isAddScene = true
                ), enterType = SceneNewEditEnterType.AddNew, scenario_L3id = ""
            )
        }
    }

    private fun findSceneIndexById(sceneId: String?): Int {
        for (i in mSceneList.indices) {
            if (mSceneList[i].scenario.scenarioInfo?.scenarioId == sceneId) {
                return i
            }
        }
        return -1
    }

    private fun setClickMySceneDataEvent() {
        mAdapter?.data?.let {
            val list = ArrayList<DataEventSceneDetailBean>()
            var triggerDetail = ""
            var statusDetail = ""
            var actionDetail = ""
            for (i in it.indices) {
                val triggerCon = it[i].scenario.scenarioInfo?.edgeCondition
                triggerCon?.let { tri ->
                    triggerDetail = "触发条件-" + tri.category + "-" + tri.desc + "-" + tri.skillId
                }
                val statusCon = it[i].scenario.scenarioInfo?.conditions
                statusCon?.let { status ->
                    statusDetail = SceneDataEventManager.getStatusConditionDetail(status)
                }
                val actionData = it[i].scenario.scenarioInfo?.sequence
                actionData?.let { action ->
                    actionDetail = SceneDataEventManager.getActionDetail(action)
                }
                list.add(
                    DataEventSceneDetailBean(
                        it[i].scenario.scenarioInfo?.scenarioName ?: "",
                        it[i].scenario.scenarioInfo?.scenarioId ?: "",
                        (i + 1).toString(),
                        it[i].scenario.scenarioInfo?.autoExeFlag == 0,
                        triggerDetail,
                        statusDetail,
                        actionDetail,
                        if (it[i].scenario.scenarioInfo?.secondAskeFlag == 0) "开" else "关"
                    )
                )
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig) //主题切换手动切换进度条png
        UIUtils.setSmartRefreshOnConfigurationChanged(mViewDataBinding.header, null)
        mAdapter?.notifyDataSetChanged()
    }
}