package com.dfl.smartscene.ui.edit.action.system

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.SystemType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->系统view model
 * version: 1.0
 */
class SystemActionViewModel : BaseViewModel() {

    val mSystemLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<SystemType>>>()
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SystemActionViewModel")

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<SystemType>>()
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_condition_link),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                SystemType.LINK_TITLE,
                null,
                SystemType.SYSTEM_LINK_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_condition_bluetooth),
                R.drawable.scene_icon_me_action_system_bluetooth,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.BLUETOOTH,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID),
                SystemType.SYSTEM_LINK_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_condition_hotspot),
                R.drawable.scene_icon_me_action_system_hotspot,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.HOTSPOT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID),
                SystemType.SYSTEM_LINK_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_condition_wlan),
                R.drawable.scene_icon_me_action_system_wifi,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.WLAN,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_WLAN_ID),
                SystemType.SYSTEM_LINK_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_condition_mobile_network),
                R.drawable.scene_icon_me_action_system_4g,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.MOBILE_NETWORK,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MOBILE),
                SystemType.SYSTEM_LINK_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_sound),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                SystemType.VOLUME_TITLE,
                null,
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )
        //list.add(
        //    ActionSkillItemBean(
        //        resources.getString(R.string.scene_text_action_volume_setting),
        //        R.drawable.scene_icon_media_volume,
        //        ActionSkillItemBean.SKILL_CONTENT,
        //        SystemType.VOLUME,
        //        arrayListOf(SkillsListConstant.SKILL_ID_SYSTEM_VOLUME_SETTING),
        //        SystemType.SYSTEM_VOLUME_TYPE
        //    )
        //)


        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_media_volume),
                R.drawable.scene_icon_me_action_system_media_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.MEDIA_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_voice_assistant_volume),
                R.drawable.scene_icon_me_action_system_voice_assistant_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.VOICE_ASSISTANT_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_navigation_volume),
                R.drawable.scene_icon_me_action_system_navi_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.NAVI_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_call_volume),
                R.drawable.scene_icon_me_action_system_call_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.CALL_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_system_volume_bluetooth),
                R.drawable.scene_icon_me_action_system_bluetooth_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.BLUETOOTH_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_cat_out_volume),
                R.drawable.scene_icon_me_action_system_external_speaker_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.CAT_OUT_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_system_ring_volume),
                R.drawable.scene_icon_me_action_system_ring_volume,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.RING_VOLUME,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_RING_VOLUME),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_mute),
                R.drawable.scene_icon_me_action_system_mute_solid,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.MUTE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE),
                SystemType.SYSTEM_VOLUME_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_display),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                SystemType.DISPLAY_TITLE,
                null,
                SystemType.SYSTEM_DISPLAY_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_central_control_screen_brightness),
                R.drawable.scene_icon_me_action_system_screen_light,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.CENTER_SCREEN_BRIGHTNESS,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS),
                SystemType.SYSTEM_DISPLAY_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_screen_mode),
                R.drawable.scene_icon_me_action_system_screen_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                SystemType.DAY_NIGHT_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SYSTEM_SCREEN_MODE),
                SystemType.SYSTEM_DISPLAY_TYPE
            )
        )
        val type = SceneEditManager.ActionType.SYSTEM
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mSystemLiveData.postValue(result)
    }

    /**
     * 根据SystemType获取对应ScenarioInfo封装
     * @param type SystemType
     * @return ScenarioInfo.Sequence
     */
    fun getSystemActionData(type: SystemType, resources: Resources): ScenarioInfo.Sequence {
        val sequence: ScenarioInfo.Sequence
        val args = ArrayList<InputArgInfo>()
        when (type) {
            SystemType.CENTER_SCREEN_BRIGHTNESS -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "10"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CENTRAL_SCREEN_BRIGHTNESS,
                        resources.getString(R.string.scene_text_action_central_control_screen_brightness),
                        args
                    )
                )
            }

            SystemType.DAY_NIGHT_MODE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_DAT_MODE, ArgType.INT32, "3"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_SCREEN_MODE,
                        resources.getString(R.string.scene_text_action_screen_mode),
                        args
                    )
                )
            }

            SystemType.CALL_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_CALL,
                        resources.getString(R.string.scene_text_action_call_volume),
                        args
                    )
                )
            }

            SystemType.VOICE_ASSISTANT_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_ASSISTANT,
                        resources.getString(R.string.scene_text_action_voice_assistant_volume),
                        args
                    )
                )
            }

            SystemType.NAVI_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_NAVI,
                        resources.getString(R.string.scene_text_action_navigation_volume),
                        args
                    )
                )
            }

            SystemType.MEDIA_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_MEDIA,
                        resources.getString(R.string.scene_text_action_media_volume),
                        args
                    )
                )
            }

            SystemType.BLUETOOTH_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_VOLUME_SETTING_BLUETOOTH,
                        resources.getString(R.string.scene_text_action_system_volume_bluetooth),
                        args
                    )
                )
            }
            SystemType.CAT_OUT_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "8"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_CAT_OUT_VOLUME,
                        resources.getString(R.string.scene_text_action_cat_out_volume),
                        args
                    )
                )
            }
            SystemType.RING_VOLUME -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_PER, ArgType.INT32, "16"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_RING_VOLUME,
                        resources.getString(R.string.scene_text_action_system_ring_volume),
                        args
                    )
                )
            }

            SystemType.MUTE -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE,
                        resources.getString(R.string.scene_text_action_mute),
                        args
                    )
                )
            }

            SystemType.BLUETOOTH -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_BLUETOOTH_ID,
                        resources.getString(R.string.scene_text_condition_bluetooth),
                        args
                    )
                )
            }

            SystemType.HOTSPOT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_HOT_SPOT_ID,
                        resources.getString(R.string.scene_text_condition_hotspot),
                        args
                    )
                )
            }

            SystemType.WLAN -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SYSTEM_SWITCH_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_WLAN_ID,
                        resources.getString(R.string.scene_text_condition_wlan),
                        args
                    )
                )
            }

            SystemType.MOBILE_NETWORK -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MOBILE,
                        resources.getString(R.string.scene_text_condition_mobile_network),
                        args
                    )
                )
            }

            else -> {
                CommonLogUtils.logE(TAG, "getSystemActionData 错误的SystemType")
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                sequence = ScenarioInfo.Sequence(
                    1, 1, ScenarioInfo.Action(
                        "",
                        SkillsListConstant.SKILL_ID_ACTION_SYSTEM_MUTE,
                        resources.getString(R.string.scene_text_action_mute),
                        args
                    )
                )
            }

        }
        return sequence
    }

    /**
     * 获取滑动条左侧icon
     */
    @Deprecated("新版ui已不在使用")
    fun getProgressIcon(type: SystemType): Int {
        //return when (type) {
        //SystemType.VOLUME -> {
        //    R.drawable.scene_icon_navi_voice
        //}
        //SystemType.NAVI_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_navi_volume_progress
        //}
        //
        //SystemType.MEDIA_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_media_volume_progress
        //}
        //
        //SystemType.VOICE_ASSISTANT_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_voice_assistant_volume_progress
        //}
        //
        //SystemType.CALL_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_call_volume_progress
        //}
        //
        //SystemType.BLUETOOTH_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_bluetooth_volume_progress
        //}
        //
        //SystemType.CAT_OUT_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_external_speaker_volume_progress
        //}
        //
        //SystemType.RING_VOLUME -> {
        //    R.drawable.scene_icon_me_action_system_ring_volume_progress
        //}
        //
        //else -> {
        //    R.drawable.scene_icon_me_action_system_screen_light_progress
        //}
        //}
        return -1
    }

    fun getListData(resources: Resources, type: SystemType): List<CheckListBean> {
        return when (type) {
            SystemType.DAY_NIGHT_MODE -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_action_auto), true),
                    CheckListBean(resources.getString(R.string.scene_text_action_daytime), false),
                    CheckListBean(resources.getString(R.string.scene_text_action_night), false),
                )
            }
            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }
        }
    }

    fun getInputArgIntValue(type: SystemType): List<Int> {
        return when (type) {
            SystemType.DAY_NIGHT_MODE -> {
                arrayListOf(3, 1, 2)
            }
            else -> {
                arrayListOf(1, 0)
            }
        }
    }

    fun getWheelMap(start: Int, end: Int): MutableMap<String, String> {
        val mutableList = mutableMapOf<String, String>()
        for (i in start..end) {
            mutableList[i.toString()] = i.toString()
        }
        return mutableList
    }
}