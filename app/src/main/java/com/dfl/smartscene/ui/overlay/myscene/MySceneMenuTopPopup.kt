package com.dfl.smartscene.ui.overlay.myscene

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.databinding.ScenePopupMySceneTopMenuBinding
import com.dfl.smartscene.util.TouchEventInterceptor

/**
 * 我的场景顶部菜单弹窗
 * 包含日志和帮助两个菜单项
 */
class MySceneMenuTopPopup(onMenuClickListener: OnMenuClickListener<MySceneBean>?) :
    BaseMenuPopup<MySceneBean>(onMenuClickListener) {

    companion object {
        private const val TAG = "MySceneMenuTopPopup"
        const val INDEX_LOG = 0
        const val INDEX_HELP = 1
    }

    private lateinit var mBinding: ScenePopupMySceneTopMenuBinding
    override var mPopupWindow: PopupWindow? = null
    
    /**
     * 蒙层隐藏监听器接口
     */
    interface OnMaskHideListener {
        fun onMaskHide()
    }
    
    private var maskHideListener: OnMaskHideListener? = null
    private var touchEventInterceptor: TouchEventInterceptor? = null

    /**
     * 设置蒙层隐藏监听器
     */
    fun setOnMaskHideListener(listener: OnMaskHideListener) {
        this.maskHideListener = listener
    }

    /**
     * 显示弹窗
     * @param view 触发显示的视图
     */
    override fun show(view: View) {
        if (mPopupWindow == null) {
            val context = view.context
            mBinding = ScenePopupMySceneTopMenuBinding.inflate(LayoutInflater.from(context))

            // 设置PopupWindow的尺寸为全屏
            val popup = PopupWindow(
                mBinding.root, 
                ViewGroup.LayoutParams.MATCH_PARENT, 
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            popup.isOutsideTouchable = false // 设置为false，防止点击外部关闭
            popup.animationStyle = R.style.popupAnimationTheme
            mPopupWindow = popup
            popup.isClippingEnabled = false

            // 初始化TouchEventInterceptor
            touchEventInterceptor = TouchEventInterceptor()

            // 设置弹窗关闭监听器
            popup.setOnDismissListener {
                // 弹窗关闭时通知隐藏蒙层
                maskHideListener?.onMaskHide()
                // 移除事件拦截
                touchEventInterceptor?.removeEventInterception(mBinding.root)
            }

            // 设置蒙层点击事件，点击蒙层关闭弹窗
            touchEventInterceptor?.setupMaskEventInterception(mBinding.viewMask) {
                CommonLogUtils.logD(TAG, "Mask clicked, dismissing popup")
                popup.dismiss()
            }

            // 设置背景点击事件，点击背景关闭弹窗（备用方案）
            mBinding.cslPopBg.setOnClickListener {
                CommonLogUtils.logD(TAG, "Background clicked, dismissing popup")
                popup.dismiss()
            }

            // 设置菜单项点击事件
            mBinding.tvLogMenu.setOnClickListener {
                CommonLogUtils.logD(TAG, "Log menu clicked")
                onMenuClickListener?.onMenuItemSelected(this, INDEX_LOG, 0, null)
                popup.dismiss()
            }

            mBinding.tvHelpMenu.setOnClickListener {
                CommonLogUtils.logD(TAG, "Help menu clicked")
                onMenuClickListener?.onMenuItemSelected(this, INDEX_HELP, 0, null)
                popup.dismiss()
            }
        }

        // 显示PopupWindow在屏幕中央
        mPopupWindow!!.showAtLocation(
            view,
            Gravity.NO_GRAVITY,
            0,
            0
        )

        CommonLogUtils.logD(TAG, "PopupWindow shown, isShowing=${mPopupWindow?.isShowing}")
    }
    /**
     * 隐藏弹窗
     */
    override fun dismiss() {
        mPopupWindow?.dismiss()
        CommonLogUtils.logD(TAG, "PopupWindow dismissed")
    }
}