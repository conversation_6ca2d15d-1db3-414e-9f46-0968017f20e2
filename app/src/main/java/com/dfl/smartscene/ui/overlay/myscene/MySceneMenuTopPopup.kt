package com.dfl.smartscene.ui.overlay.myscene

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import android.widget.TextView
import com.dfl.android.common.base.BaseMenuPopup
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.databinding.ScenePopupMySceneTopMenuBinding

/**
 * 顶部菜单弹窗，不依赖于RecyclerView
 */
class MySceneMenuTopPopup(onMenuClickListener: OnMenuClickListener<MySceneBean>) :
    BaseMenuPopup<MySceneBean>(onMenuClickListener) {

    companion object {
        const val INDEX_LOG = 0
        const val INDEX_HELP = 1
    }

    override val TAG = GlobalConstant.GLOBAL_TAG.plus("MySceneMenuTopPopup")
    private lateinit var mBinding: ScenePopupMySceneTopMenuBinding
    
    // 添加蒙层隐藏回调接口
    interface OnMaskHideListener {
        fun onMaskHide()
    }
    
    private var maskHideListener: OnMaskHideListener? = null
    
    /**
     * 设置蒙层隐藏监听器
     */
    fun setOnMaskHideListener(listener: OnMaskHideListener) {
        this.maskHideListener = listener
    }

    override fun show(view: View) {
        if (mPopupWindow == null) {
            val context = view.context
            mBinding = ScenePopupMySceneTopMenuBinding.inflate(LayoutInflater.from(context))

            // 设置PopupWindow的尺寸
            val width = view.resources.getDimensionPixelSize(R.dimen.px_313)
            val height = view.resources.getDimensionPixelSize(R.dimen.px_258) // 适合两个菜单项的高度

            val popup = PopupWindow(mBinding.root, width, height)
            popup.isOutsideTouchable = true
            popup.animationStyle = R.style.popupAnimationTheme
            mPopupWindow = popup
            popup.isClippingEnabled = false

            // 设置弹窗关闭监听器
            popup.setOnDismissListener {
                // 弹窗关闭时通知隐藏蒙层
                maskHideListener?.onMaskHide()
            }

            // 设置背景点击事件，点击背景关闭弹窗
            mBinding.cslPopBg.setOnClickListener {
                popup.dismiss()
            }

            // 设置菜单项点击事件
            mBinding.tvLogMenu.setOnClickListener {
                CommonLogUtils.logD(TAG, "Log menu clicked")
                onMenuClickListener?.onMenuItemSelected(this, INDEX_LOG, 0, null)
                popup.dismiss()
            }

            mBinding.tvHelpMenu.setOnClickListener {
                CommonLogUtils.logD(TAG, "help menu clicked")
                onMenuClickListener?.onMenuItemSelected(this, INDEX_HELP, 0, null)
                popup.dismiss()
            }
        }

        // 计算弹窗显示位置
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        // 设置距离顶部和右边的偏移量
        val topOffset = view.resources.getDimensionPixelSize(R.dimen.px_148)
        val rightOffset = view.resources.getDimensionPixelSize(R.dimen.px_64)
        // 显示在右上角
        mPopupWindow!!.showAtLocation(
            view,
            Gravity.TOP or Gravity.END,
            rightOffset, // X偏移，0表示靠右对齐
            topOffset // Y坐标，与触发视图顶部对齐
        )

        CommonLogUtils.logD(TAG, "isShowing=${mPopupWindow?.isShowing}")
    }

    override fun dismiss() {
        mPopupWindow?.dismiss()
    }
}