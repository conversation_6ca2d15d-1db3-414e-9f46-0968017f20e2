package com.dfl.smartscene.ui.edit.action.drive

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.DriveType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : raoyulin
 * e-mail : <EMAIL>
 * time : 2025/04/21
 * desc : 动作->驾驶vm
 * version: 1.0
 */
class DriveActionViewModel : BaseViewModel() {
    val mDriveActionLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<DriveType>>>()
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("DriveActionViewModel")
    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<DriveType>>()
//        val type = SceneEditManager.ActionType.DRIVE
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_drive_mode),
                R.drawable.scene_icon_me_action_drive_driving_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                DriveType.DRIVE_MODE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DRIVE_MODE)
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_me_action_drive_wiper_sensitivity),
                R.drawable.scene_icon_me_action_drive_wiper,
                ActionSkillItemBean.SKILL_CONTENT,
                DriveType.WIPER_SENSITIVITY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_WIPER_SENSITIVITY)
            )
        )
//        val result = SceneEditManager.checkActionItemIsActive(type, list)
//        mDriveActionLiveData.postValue(result)
    }


    /**
     * 根据动作类型获取对应的Sequence实例
     */
    fun getActionList(
        title: String, actionType: DriveType
    ): ArrayList<ScenarioInfo.Sequence> {
        val actionList = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()

        when (actionType) {
            DriveType.DRIVE_MODE -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DRIVE_MODE, ArgType.INT32, "0"))
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_DRIVE_MODE, title, args
                        )
                    )
                )
            }


            DriveType.WIPER_SENSITIVITY -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_WIPER_SENSITIVITY, ArgType.INT32, "0"))
                actionList.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_WIPER_SENSITIVITY, title, args
                        )
                    )
                )
            }

            else -> {
                CommonLogUtils.logE(TAG, "错误的ApplicationActionType:$actionType")
            }
        }
        return actionList
    }

    fun getCheckList(resources: Resources, actionType: DriveType): List<CheckListBean> {
        return when (actionType) {
            DriveType.DRIVE_MODE -> { //驾驶模式
                listOf(
                    CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_comfort), true),
                    CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_sport), false),
                    CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_standard), false),
                    CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_customize), false),
                    //车控有前置条件，暂不上
                    //CheckListBean(resources.getString(R.string.scene_text_me_action_drive_mode_ai), false)
                )
            }

            DriveType.WIPER_SENSITIVITY -> { //雨刮灵敏度
                listOf(
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_lowest), true),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_low), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_high), false),
                    CheckListBean(resources.getString(R.string.scene_text_edit_action_highest), false)
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean("", true)
                )
            }
        }
    }

    fun getInputArgList(actionType: DriveType): List<String> {
        return when (actionType) {
            DriveType.DRIVE_MODE -> {
                //舒适 : 1, 运动: 2, 标准: 3,  自定义 : 4,  AI专属 : 5
                //listOf("1", "2", "3", "4", "5")
                listOf("1", "2", "3", "4")
            }

            DriveType.WIPER_SENSITIVITY -> {
                //最低 : 1,低 : 2,中 : 3,高 : 4,最高 : 5,
                listOf("1", "2", "3", "4", "5")
            }

            else -> {
                listOf("0")
            }
        }
    }
}