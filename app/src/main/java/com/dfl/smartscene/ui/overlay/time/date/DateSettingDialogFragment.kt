package com.dfl.smartscene.ui.overlay.time.date

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.TimeUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.ui.overlay.common.checklist.CheckListType
import com.dfl.smartscene.ui.overlay.common.checklist.RadioListDialogFragment
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog
import com.dfl.smartscene.ui.overlay.time.timepicker.TimePickerDialog.OnTimePickerListener
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus
import java.util.Calendar
import java.util.Locale

/**
 *Created by 钟文祥 on 2025/3/10.
 *Describer:状态条件-周期 lk2a专用
 */
class DateSettingDialogFragment(private var preCondition: ScenarioInfo.Condition?) :
    RadioListDialogFragment<ScenarioInfo.Condition, Int>("", CheckListType.DEFAULT_CHECK_MODE, arrayListOf()) {

    private var mWitchDaySkillId = 0 //选择好的每周几的能力id

    /**本页能力id*/
    private val mSkillList = mutableListOf(
        SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID, //每天
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID, //每周 几
        SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID, //工作日
        SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID, //节假日
        SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID, //某日
    )

    /**周一到周日的能力id*/
    private val mDaySkillList = mutableListOf(
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ONE_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_TWO_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_THREE_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FOUR_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_FIVE_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SIX_ID,
        SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_SEVEN_ID,
    )

    /**多选弹框的值*/
    private val checkDataList by lazy {
        arrayListOf(
            CheckListBean(
                getString(R.string.scene_text_common_monday),
                desc = getString(R.string.scene_text_common_monday_simple),
                isCheck = true,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_tuesday),
                desc = getString(R.string.scene_text_common_tuesday_simple),
                isCheck = false,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_wednesday),
                desc = getString(R.string.scene_text_common_wednesday_simple),
                isCheck = false,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_thursday),
                desc = getString(R.string.scene_text_common_thursday_simple),
                isCheck = false,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_friday),
                desc = getString(R.string.scene_text_common_friday_simple),
                isCheck = false,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_saturday),
                desc = getString(R.string.scene_text_common_saturday_simple),
                isCheck = false,
                isSingleSelectIcon = false
            ),
            CheckListBean(
                getString(R.string.scene_text_common_sunday),
                desc = getString(R.string.scene_text_common_day),
                isCheck = false,
                isSingleSelectIcon = false
            ),
        )
    }

    companion object {
        fun showFragment(manager: FragmentManager, condition: ScenarioInfo.Condition?) {
            val dialog = DateSettingDialogFragment(condition)
            dialog.show(manager, DateSettingDialogFragment::javaClass.name)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        mTitle = resources.getString(R.string.scene_text_condition_cycle)
        mCheckListType = CheckListType.DEFAULT_CHECK_MODE
        preSetCondition = preCondition
        mCheckList = getSettingListData()
        super.initView(savedInstanceState)
    }

    //获取本页的单选列表数据
    private fun getSettingListData(): List<CheckListBean> {
        val list = arrayListOf(
            CheckListBean(resources.getString(R.string.scene_text_edit_condition_time_every_day), false),
            CheckListBean(resources.getString(R.string.scene_text_condition_weekly), false, isItemShowMoreIcon = true),
            CheckListBean(resources.getString(R.string.scene_text_edit_condition_time_legal_working_day), false),
            CheckListBean(resources.getString(R.string.scene_text_edit_condition_time_statutory_holidays), false),
            CheckListBean(
                resources.getString(R.string.scene_text_edit_condition_time_someday), false, isItemShowMoreIcon = true
            )
        )

        var selectedIndex = -1
        preCondition?.let {
            if (mSkillList.contains(it.skillId)) {
                when (it.skillId) {
                    SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID -> {
                        if (it.input.size == 1) {
                            val value: String = it.input[0].value
                            val setValue = Integer.valueOf(value)
                            //每天
                            if (isSelectedDay(setValue, SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID)) {
                                selectedIndex = 0
                            } else {  //每周几
                                selectedIndex = 1
                            }
                        }
                    }

                    SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID -> {
                        selectedIndex = 2
                    }

                    SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID -> {
                        selectedIndex = 3
                    }

                    SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID -> {
                        selectedIndex = 4
                    }
                }
            }
        }
        if (selectedIndex != -1) {
            list[selectedIndex].isCheck = true
        } else { //默认选择第一个
            list[0].isCheck = true
        }
        return list
    }

    //跳转事件
    override fun onRadioItemClick(position: Int) {
        //下面两个是不会走onItemClick的
        if (position == 1) { //每周 几
            openSelectDayDialog()
        } else if (position == 4) { //指定日期
            showTimePickerDialog()
        }
    }

    //选周几
    private fun openSelectDayDialog() {
        val dialog: RadioListDialogFragment<ScenarioInfo.Condition, Int> = RadioListDialogFragment(
            getString(R.string.scene_text_condition_weekly),
            CheckListType.CONDITION_WEEKLY,
            initCheckListFromPreCondition(),
            getDayStatusConditionList(),
        )
        dialog.isLevel4OrAbove = true
        dialog.isSingleSelect = false
        dialog.preSetCondition = preCondition
        dialog.mListener = object : OnRadioListener {
            override fun primarySelect(selectIndexs: ArrayList<Int>) {
                //周几
                val desc = getSelectDayDescAndSkillIds(selectIndexs)
                val skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID //0xFF02
                val list = arrayListOf<InputArgInfo>()
                list.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_TIME_DAY, ArgType.UINT32, mWitchDaySkillId.toString()
                    )
                )
                val condition = ScenarioInfo.Condition(1, desc, skillId, mTitle, list)
                EventBus.getDefault().post(condition)
                dismissWithAnimate()
            }
        }
        dialog.show(parentFragmentManager, "showRadioListDialog")
    }

    //选某天
    private fun showTimePickerDialog() {

        val years: MutableList<String> = arrayListOf()
        val months: MutableList<String> = arrayListOf()
        val days: MutableList<String> = arrayListOf()

        val year = Calendar.getInstance()[Calendar.YEAR]
        val month = Calendar.getInstance()[Calendar.MONTH] + 1
        val day = Calendar.getInstance()[Calendar.DAY_OF_MONTH]

        for (i in year..year + 100) {
            years.add(String.format(Locale.getDefault(), "%02d", i))
        }
        for (i in 1..12) {
            months.add(String.format(Locale.getDefault(), "%02d", i))
        }
        //先默认每个月30天，具体更新逻辑在TimePickerDialog
        for (i in 1..30) {
            days.add(String.format(Locale.getDefault(), "%02d", i))
        }
        var strYear = year.toString()
        var strMonth = month.toString()
        var strDay = day.toString()

        preCondition?.let {
            if (it.skillId == SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID) {
                val inputs = it.input
                val strYear1 = findPointDayValue(inputs, "year")
                val strMonth1 = findPointDayValue(inputs, "month")
                val strDay1 = findPointDayValue(inputs, "day")

                if (strYear1 != null && strMonth1 != null && strDay1 != null) {
                    strYear = strYear1
                    //添加判断后台返回月份和日期为个位数情况，加上0，防止选中之前值失败
                    strMonth = TimeUtils.singleTimeAddZero(strMonth1)
                    strDay = TimeUtils.singleTimeAddZero(strDay1)
                }
            }
        }
        strMonth = TimeUtils.singleTimeAddZero(strMonth)
        strDay = TimeUtils.singleTimeAddZero(strDay)

        val singlePickerDialog = TimePickerDialog(
            requireContext(),
            years,
            months,
            days,
            getString(R.string.scene_text_edit_condition_time_someday),
            SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID,
            strYear,
            strMonth,
            strDay
        )
        singlePickerDialog.setWheelUnit(
            getString(R.string.scene_text_common_year),
            getString(R.string.scene_text_common_month),
            getString(R.string.scene_text_common_day)
        )
        singlePickerDialog.setIsCyclicArr(booleanArrayOf(false, true, true))
        singlePickerDialog.listener = OnTimePickerListener { _, whell1Value, _, whell2Value, _, whell3Value -> //指定时间
            val selectYear: String = whell1Value
            var selectMonth: String = whell2Value
            var selectDay = whell3Value
            val desc =
                selectYear + getString(R.string.scene_text_common_year) + selectMonth + getString(R.string.scene_text_common_month) + selectDay + getString(
                    R.string.scene_text_common_day
                )
            //如果是01保存到ccm为1
            selectMonth = TimeUtils.singleTimeRemoveZero(selectMonth)
            selectDay = TimeUtils.singleTimeRemoveZero(selectDay)
            val list: ArrayList<InputArgInfo> = java.util.ArrayList()
            list.add(InputArgInfo("year", ArgType.UINT32, selectYear))
            list.add(InputArgInfo("month", ArgType.UINT32, selectMonth))
            list.add(InputArgInfo("day", ArgType.UINT32, selectDay))
            val skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_SOME_DAY_ID
            val condition = ScenarioInfo.Condition(
                1, desc, skillId, mTitle, list
            )
            EventBus.getDefault().post(condition)
            dismissWithAnimate()
        }
        singlePickerDialog.isLevel4OrAbove = true
        singlePickerDialog.showWithAnimate()
    }

    private fun getSelectDayDescAndSkillIds(selectIndexes: ArrayList<Int>): String {
        var index = 0
        val buffer = StringBuilder()
        buffer.append(getString(R.string.scene_text_condition_weekly))
        selectIndexes.forEach {
            index++
            mWitchDaySkillId = mWitchDaySkillId or mDaySkillList[it]
        }

        var position = 0
        selectIndexes.forEach {
            buffer.append(checkDataList[it].desc)
            if (index != position + 1) {
                buffer.append("/")
            }
            position++
        }
        if (position == 7) {
            buffer.delete(0, buffer.length)
            buffer.append(getString(R.string.scene_text_edit_condition_time_every_day))
        }
        return buffer.toString()
    }

    private fun getDayStatusConditionList(): ArrayList<ScenarioInfo.Condition> {
        val linkConditionList = ArrayList<ScenarioInfo.Condition>()
        mDaySkillList.forEachIndexed { _, i ->
            linkConditionList.add(
                ScenarioInfo.Condition(
                    1, "", SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID, "", listOf(
                        InputArgInfo(
                            SkillsListConstant.INPUT_ARG_STATUS_TIME_DAY, ArgType.UINT32, i.toString()
                        )
                    )
                )
            )
        }
        return linkConditionList

    }

    private fun initCheckListFromPreCondition(): List<CheckListBean> {
        //周一到周日
        if (preCondition == null) return checkDataList

        val skillId = preCondition!!.skillId
        if (skillId == SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID) { //周几
            val inputs = preCondition!!.input
            if (inputs.size == 1) {
                val strValue = Integer.valueOf(inputs[0].value)

                mDaySkillList.forEachIndexed { index, itemSkillId ->
                    checkDataList[index].isCheck = isSelectedDay(strValue, itemSkillId)
                }
            }
        }
        return checkDataList
    }

    private fun isSelectedDay(value: Int, dest: Int): Boolean {
        return value and dest == dest
    }


    //点击事件
    override fun onClick(v: View) {
        if (!DebouncingUtils.isValid(v, SceneEditManager.mViewClickDuration)) return
        if (v.id == R.id.btn_normal) {
            dismissWithAnimate()
        } else if (v.id == R.id.btn_primary) {
            //先获取选中位置
            primary()
            dismissWithAnimate()
        }
    }

    //提交
    private fun primary() {
        var index = -1
        for (i in mCheckList.indices) {
            if (mCheckList[i].isCheck) {
                index = i
                break
            }
        }
        if (index == -1) return

        //此处将处理数据的回调
        var skillId: Int = -1
        val list: MutableList<InputArgInfo> = ArrayList()
        var desc = ""
        //每天
        when (index) {
            mSkillList.indexOf(SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID) -> {
                //0xFF02---注意每天的设置能力ID和周几一样，只是value属性值变了
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WHICH_DAY_ID
                desc = getString(R.string.scene_text_edit_condition_time_every_day)
                list.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS_TIME_DAY,
                        ArgType.UINT32,
                        SkillsListConstant.SKILL_ID_STATUS_TIME_EVERY_DAY_ID.toString()
                    )
                )
                val condition = ScenarioInfo.Condition(1, desc, skillId, mTitle, list)
                EventBus.getDefault().post(condition)
            }
            //工作日
            mSkillList.indexOf(SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID) -> {
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_WORKDAY_ID
                desc = getString(R.string.scene_text_edit_condition_time_legal_working_day)
                val condition = ScenarioInfo.Condition(1, desc, skillId, mTitle, list)
                EventBus.getDefault().post(condition)
            }
            //节假日
            mSkillList.indexOf(SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID) -> {
                skillId = SkillsListConstant.SKILL_ID_STATUS_TIME_HOLIDAY_ID
                desc = getString(R.string.scene_text_edit_condition_time_statutory_holidays)
                val condition = ScenarioInfo.Condition(1, desc, skillId, mTitle, list)
                EventBus.getDefault().post(condition)
            }

            else -> { //进来就是选中每周或指定日期的情况
                preCondition?.let {
                    EventBus.getDefault().post(it)
                }
            }
        }

    }

    private fun findPointDayValue(inputs: List<InputArgInfo>, name: String): String? {
        for (input in inputs) {
            if (input.name == name) {
                return if (input.value == null || "" == input.value) {
                    null
                } else input.value
            }
        }
        return null
    }
}