package com.dfl.smartscene.ui.edit.condition.trigger.environment

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.condition.ConditionItemBean
import com.dfl.smartscene.bean.condition.TriggerEnvironment

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :条件->环境 适配器
 * version: 1.0
 */
class EnvironmentTriggerAdapter : BaseQuickAdapter<ConditionItemBean<TriggerEnvironment>, BaseViewHolder>(
    R.layout.scene_recycle_item_edit_condition_dialog_item
), MatchableAdapter<ConditionItemBean<TriggerEnvironment>> {
    override fun convert(holder: BaseViewHolder, item: ConditionItemBean<TriggerEnvironment>) {
        holder.getView<TextView>(R.id.tv_condition_reflex_name).text = item.conditionName
        holder.getView<AppCompatImageView>(R.id.iv_condition_reflex_icon).setImageResource(item.conditionIcon)
    }

    override val adapterData: List<ConditionItemBean<TriggerEnvironment>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.conditionName == title && !it.conditionSkillId.isNullOrEmpty() }
    }
}