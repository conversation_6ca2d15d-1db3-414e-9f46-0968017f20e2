package com.dfl.smartscene.ui.overlay.common.singleprogress

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.dfl.android.common.base.BaseDialog
import com.dfl.android.common.util.ObjectUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.CustomSeekBarBean
import com.dfl.smartscene.databinding.SceneDialogSingleProgressBinding
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.seekbar.CustomSeekBar
import com.iauto.scenarioadapter.ScenarioInfo
import org.greenrobot.eventbus.EventBus

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/23
 * desc :中控屏亮度、音量等单进度条式弹窗
 * version: 1.0
 */
@Deprecated("2A合并对话框，使用SingleWheelDialog")
class SingleProgressDialog(
    context: Context,
    val title: String,
    val action: ScenarioInfo.Sequence,
    private val customSeekBarBean: CustomSeekBarBean,
) : BaseDialog(context), View.OnClickListener {
    private lateinit var mVB: SceneDialogSingleProgressBinding
    private var mTitleTv: TextView? = null
    private var mProgressSb: CustomSeekBar? = null
    private var preSequence: ScenarioInfo.Sequence? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mVB = SceneDialogSingleProgressBinding.inflate(layoutInflater)
        setContentView(mVB.root)
        initView()
        setupInitData()
    }

    private fun setupInitData() {
        if (ObjectUtils.checkScenarioDataIsEmpty(preSequence)
                || ObjectUtils.checkScenarioDataIsEmpty(action)) {
            return
        }
        preSequence?.let {
            if (it.action.skillId == action.action.skillId) {
                val value = it.action.input[0].value
                val v = value.toInt()
                if (v in customSeekBarBean.min..customSeekBarBean.max) {
                    mProgressSb?.progress = v
                }
            }
        }

    }

    fun setupPreAction(sequence: ScenarioInfo.Sequence?) {
        this.preSequence = sequence
    }

    private fun initView() {
        mTitleTv = findViewById(R.id.tv_single_progress_title)
        mProgressSb = findViewById(R.id.csb_trunk_height)
        mVB.includeDialogBottom.btnPrimary.setOnClickListener(this)
        mVB.includeDialogBottom.btnNormal.setOnClickListener(this)
        mTitleTv?.text = title
        UIUtils.initSeekBarDefaultWidth(context, mProgressSb, customSeekBarBean)
    }

    override fun onClick(v: View?) {
        v?.id?.let {
            if (it == R.id.btn_normal) {
                dismissWithAnimate()
            } else if (it == R.id.btn_primary) {
                val desc = mProgressSb?.progress.toString()
                //默认第一个InputArg为范围数值
                action.action.input[0].value = mProgressSb?.progress.toString()
                action.action.desc = desc
                action.action.category = title
                EventBus.getDefault().post(action)
                dismissWithAnimate()
            }
        }
    }
}