package com.dfl.smartscene.ui.edit.container

import android.annotation.SuppressLint
import android.view.View
import com.dfl.android.animationlib.TabSwitchView.OnTabSelectedListener
import com.dfl.android.animationlib.bean.TypeInfo
import com.dfl.android.common.base.MVVMDialogFragment
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BR
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.databinding.SceneDialogFragmentEditContainerBinding
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ScenarioInfo
import com.jeremyliao.liveeventbus.LiveEventBus
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.ref.WeakReference


/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/23
 * desc   : 场景条件与编辑的弹框
 * version: 1.0
 */
class SceneEditContainerDialogFragment(
    /**区分是触发条件、状态条件、动作*/
    private val mSceneEditType: SceneNewEditType,
    /**我的场景Bean*/
    private val sceneEditBean: MySceneBean,
    /** 区分是编辑状态还是添加状态*/
    private val isEditItem: Boolean = false,
    /** 集合下标，动作下标没问题，但是条件是触发条件和状态条件整合*/
    private val position: Int = 0
) : MVVMDialogFragment<SceneDialogFragmentEditContainerBinding, SceneEditContainerViewModel>() {

    companion object {
        private val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneEditContainerDialogFragment")
    }

    /** 编辑场景对话框退出事件监听*/
    private var onSceneEditFragmentExitListener: WeakReference<OnSceneEditFragmentExitListener>? = null

    fun setOnSceneEditFragmentExitListener(listener: OnSceneEditFragmentExitListener) {
        this.onSceneEditFragmentExitListener = WeakReference(listener)
    }

    /**条件动作界面Fragment适配器*/
    private var mVpAdapter: SceneEditCategoryPagerAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_edit_container
    }

    override fun getBindingVariable(): Int {
        return BR.vm
    }

    override fun getViewModelClass(): Class<SceneEditContainerViewModel> {
        return SceneEditContainerViewModel::class.java
    }

    //1
    @SuppressLint("CheckResult")
    override fun initData() {
        super.initData()
        setCanceledOnTouchOutside(true)
        initLiveEventBus()
        createObserver()
        var statusSkillId = -1
        if (isEditItem) {
            //如果为编辑状态的话，需要获取对应的编辑条件动作属于左边列表里哪一个菜单下的条件动作，然后滑动到指定位置
            val skillId = when (mSceneEditType) {
                SceneNewEditType.TRIGGER_CONDITION -> {
                    sceneEditBean.scenario.scenarioInfo?.edgeCondition?.skillId
                }

                SceneNewEditType.STATUS_CONDITION -> {
                    //若有触发条件，则在scenarioInfo?.conditions 中的下标需要在position的基础上-1，与编辑
                    val index = if (hasEdgeCondition()) { //index对应状态条件list的index
                        position - 1
                    } else {
                        position
                    }
                    statusSkillId = sceneEditBean.scenario.scenarioInfo?.conditions?.get(index)?.skillId ?: -1
                    statusSkillId
                }

                else -> {
                    sceneEditBean.scenario.scenarioInfo?.sequence?.get(position)?.action?.skillId
                }
            }
            mViewModel?.mDefaultLeftIndex = SceneEditManager.getSkillIdPosition(skillId!!, mSceneEditType)
        }
        mViewModel?.initData(resources, mSceneEditType, !isEditItem, statusSkillId)
        mViewDataBinding.btnClose.setOnClickListener { dismissWithAnimate() }
    }

    private fun initLiveEventBus() {
        if (isEditItem) {
            //一定要用INVISIBLE 隐藏
            view?.visibility = View.INVISIBLE

            LiveEventBus.get(GlobalLiveEventConstants.KEY_EDIT_DIALOG_DISAPPEAR_TOGETHER, Int::class.java)
                .observe(viewLifecycleOwner) { t ->
                    when (t) {
                        -1 -> { //找不到下一级，本菜单还原回去
                            view?.visibility = View.VISIBLE
                        }

                        0 -> { //自动打开下一级菜单，本菜单隐藏
                            view?.visibility = View.INVISIBLE
                        }

                        1 -> { //下一级菜单退出了，本菜单也退出 ,全屏时不能退出，隐藏时能退出
                            if (view?.visibility == View.INVISIBLE) {
                                dismiss()
                            }
                        }
                    }
                }
        }
    }

    private fun createObserver() {
        //一级菜单回调
        mViewModel?.categoryList?.observe(this) {
            val items = ArrayList<TypeInfo>()
            var indexSelected = 0
            it.forEachIndexed { index, bean ->
                items.add(TypeInfo(bean.image, bean.imageSelect, bean.name))
                if (bean.isClicked == true) {
                    indexSelected = index
                }
            }
            initTab(items, indexSelected)
        }
    }


    //2 初始化左边一级菜单rv，和右边ViewPager2
    override fun initView(view: View) {
        super.initView(view)
        initOne()
        initViewPager()
    }

    //初始化TabSwitchView
    private fun initTab(items: ArrayList<TypeInfo>, indexSelected: Int) {
        mViewDataBinding.tabFirstMenu.generateVerticalViews(items)
        mViewDataBinding.tabFirstMenu.selectedIndex = indexSelected
        mViewDataBinding.tabFirstMenu.setOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabClicked(position: Int, isManual: Boolean?) {
                super.onTabClicked(position, isManual)
                if (isManual == false) {
                    mViewDataBinding.rvEditItemList.post {
                        mViewDataBinding.rvEditItemList.smoothScrollTo(
                            0, mViewDataBinding.tabFirstMenu.getTabView(position).top
                        )
                    }
                }
                if (isEditItem) {
                    mVpAdapter?.isEditAndSelect = true
                }
                mViewDataBinding.vpFragmentContainer.setCurrentItem(position, false)
            }

            override fun onTabSelected(position: Int, isManual: Boolean?) {
            }

            override fun onTabUnSelected(lastPosition: Int) {
            }

            override fun onInitViewComplete(isComplete: Boolean?) {
            }
        })
    }

    //初始化右边ViewPager2
    private fun initViewPager() {
        mViewDataBinding.vpFragmentContainer.isUserInputEnabled = false
        mVpAdapter = SceneEditCategoryPagerAdapter(childFragmentManager, viewLifecycleOwner.lifecycle)
        mVpAdapter?.setPreSetAction(getPositionAction(sceneEditBean, position))
        mVpAdapter?.setPreSetCondition(getPositionCondition(sceneEditBean, position))
        mVpAdapter?.setPreSetEdgeCondition(getEdgeCondition(sceneEditBean))
        mViewDataBinding.vpFragmentContainer.adapter = mVpAdapter

        mVpAdapter?.dataLiveData?.observe(viewLifecycleOwner) {
            if (isEditItem) {
                //编辑模式下打开之前动作所在tab栏目录
                val index = mViewModel?.mDefaultLeftIndex!!
                mVpAdapter?.isEditAndSelect = false
                mViewDataBinding.vpFragmentContainer.setCurrentItem(index, false)
            }
        }
    }

    private fun initOne() {
        //左边一级菜单
        //        rvAdapter = SceneEditItemListAdapter()
        //        rvAdapter?.onItemClickListener = CommunityAdapterItemListener { position, _ ->
        //            mViewDataBinding.vpFragmentContainer.setCurrentItem(
        //                position, false
        //            )
        //        } //点击事件
        //        mViewDataBinding.rvEditItemList.adapter = rvAdapter
        //        mViewDataBinding.rvEditItemList.layoutManager = LinearLayoutManager(context)
        //        mViewModel?.mDefaultLeftIndex?.let {
        //            mViewDataBinding.rvEditItemList.post {
        //                mViewDataBinding.rvEditItemList.smoothScrollToPosition(it)
        //            }
        //        }
        //        if (mSceneEditType == SceneNewEditType.TRIGGER_CONDITION) {
        //            //垂直滑条是否启用
        //            mViewDataBinding.rvEditItemList.isVerticalScrollBarEnabled = false
        //        }
    }

    private fun getPositionAction(mySceneBean: MySceneBean, position: Int): ScenarioInfo.Sequence? {
        if (position == -1) return null
        val sequences = mySceneBean.scenario.scenarioInfo?.sequence ?: return null
        if (position >= sequences.size) return null
        return sequences[position]
    }

    private fun getPositionCondition(
        mySceneBean: MySceneBean, position: Int
    ): ScenarioInfo.Condition? {
        var newPosition = position
        if (hasEdgeCondition()) newPosition -= 1
        if (newPosition == -1) return null
        val conditions = mySceneBean.scenario.scenarioInfo?.conditions ?: return null
        if (newPosition >= conditions.size) return null
        return conditions[newPosition]
    }

    private fun getEdgeCondition(mySceneBean: MySceneBean): ScenarioInfo.Condition? {
        if (position == -1) return null
        return mySceneBean.scenario.scenarioInfo?.edgeCondition
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    override fun onStop() {
        super.onStop()
        EventBus.getDefault().unregister(this)
    }

    //动作添加后，界面的回调
    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun addActionCallBack(sequence: ScenarioInfo.Sequence) {
        CommonLogUtils.logE(TAG, "this is eventBus==>$sequence")
        sequence.id = position
        if (isEditItem) {
            sceneEditBean.scenario.scenarioInfo?.sequence?.set(position, sequence)
        } else {
            if (sceneEditBean.scenario.scenarioInfo?.sequence == null) {
                sceneEditBean.scenario.scenarioInfo?.sequence = ArrayList()
            }
            sceneEditBean.scenario.scenarioInfo?.sequence?.add(sequence)
        }
        onSceneEditFragmentExit()
        CommonUtils.runOnUiThread {
            //动画要在主线程
            dismissWithAnimate()
        }
    }

    //条件添加后，界面的回调
    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun addConditionCallBack(event: ScenarioInfo.Condition) {
        CommonLogUtils.logE(TAG, "this is add condition call back$event")
        if (mSceneEditType == SceneNewEditType.STATUS_CONDITION) {
            if (isEditItem) {
                //注意这里由于触发条件和状态条件共用一个recycleView，在编辑已有的条件的时候，
                // 需要下标减3对应的是触发条件标题、触发条件内容（添加item）、状态条件标题
                val index = if (hasEdgeCondition()) {
                    position - 1
                } else {
                    position
                }
                sceneEditBean.scenario.scenarioInfo?.conditions?.set(index, event)
            } else {
                event.id = position - 1
                if (sceneEditBean.scenario.scenarioInfo?.conditions == null) {
                    sceneEditBean.scenario.scenarioInfo?.conditions = ArrayList()
                }
                sceneEditBean.scenario.scenarioInfo?.conditions?.add(event)
            }
        } else {
            event.id = 0
            sceneEditBean.scenario.scenarioInfo?.edgeCondition = event
        }
        onSceneEditFragmentExit()
        CommonUtils.runOnUiThread {
            //动画要在主线程
            dismissWithAnimate()
        }
    }

    private fun hasEdgeCondition() = sceneEditBean.scenario.scenarioInfo?.edgeCondition != null

    override fun onTouchOutside() {
        super.onTouchOutside()
        onSceneEditFragmentExit()
    }

    private fun onSceneEditFragmentExit() {
        mViewModel?.mDefaultLeftIndex = 0
        sceneEditBean.scenario.scenarioInfo?.sequence?.forEach {
            it.action.category = it.action.category.replace("\n", "")
        }
        onSceneEditFragmentExitListener?.get()?.onSceneEditFragmentExit(sceneEditBean)
    }


    interface OnSceneEditFragmentExitListener {
        fun onSceneEditFragmentExit(sceneEditBean: MySceneBean)
    }


    //class MyGridItemDecoration : RecyclerView.ItemDecoration() {
    //	override fun getItemOffsets(
    //		outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
    //	) {
    //		super.getItemOffsets(outRect, view, parent, state)
    //		outRect.top = 16
    //		outRect.bottom = 16
    //	}
    //}
}