package com.dfl.smartscene.ui.edit.action.door

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.customapi.ConfigManager
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.DoorsWindowsType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->门窗view model
 * version: 1.0
 */
class DoorsWindowsActionViewModel : BaseViewModel() {
    val doorsWindowsLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<DoorsWindowsType>>>()
    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<DoorsWindowsType>>()
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_action_car_door_lock),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                DoorsWindowsType.DOOR_TITLE,
                null,
                DoorsWindowsType.CAR_DOOR_TITLE_TYPE
            )
        )

        //电动尾门，电动行李箱不同车型不同文言
        val title = resources.getString(R.string.scene_text_edit_add_action_door_back_pk1b)

        list.add(
            ActionSkillItemBean(
                title,
                R.drawable.scene_icon_me_action_door_back_door,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.DOOR_BACK,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK
                ),
                DoorsWindowsType.CAR_DOOR_TITLE_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_edit_add_action_window),
                -1,
                ActionSkillItemBean.SKILL_TITLE,
                DoorsWindowsType.CAR_WINDOW,
                null,
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_drivers_window),
                R.drawable.scene_icon_me_status_door_main_door_window,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_DRIVER_MAIN,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_front_passenger_window),
                R.drawable.scene_icon_me_status_door_copilot_door_window,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_DRIVER_CHIEF,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_rear_left_window),
                R.drawable.scene_icon_me_status_door_main_door_window,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_LEFT_BACK,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_rear_right_window),
                R.drawable.scene_icon_me_status_door_copilot_door_window,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_RIGHT_BACK,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_window_ventilation),
                R.drawable.scene_icon_me_action_door_window_freshleft,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_VENTILATE,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        //车窗半开，更改ID
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_windows_half_open),
                R.drawable.scene_icon_me_action_door_window_all,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.ALL_WINDOW,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        //车窗通风，更改ID
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_window_fresh_air),
                R.drawable.scene_icon_me_action_door_window_ventilation,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_IN_FRESH_AIR,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_window_skylight),
                R.drawable.scene_icon_me_trigger_door_skylight,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_SKYLIGHT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )
        //天窗透气，更改ID
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_window_skylight_air),
                R.drawable.scene_icon_me_trigger_door_skylight,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_SKYLIGHT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_window_electric_sunshade_curtain),
                R.drawable.scene_icon_me_trigger_door_sunrise_electric_sunshade_curtain,
                ActionSkillItemBean.SKILL_CONTENT,
                DoorsWindowsType.WINDOW_ELECTRIC_SUNSHADE_CURTAIN,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN),
                DoorsWindowsType.CAR_WINDOW_TITLE_TYPE
            )
        )

        val type = SceneEditManager.ActionType.DOOR
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        doorsWindowsLiveData.postValue(result)
    }

    fun getConditionerAction(
        type: DoorsWindowsType, content: String, resources: Resources
    ): ArrayList<ScenarioInfo.Sequence> {
        val openSkillId: Int
        val closeSkillId: Int
        val list = ArrayList<ScenarioInfo.Sequence>()
        val inputArg = ArrayList<InputArgInfo>()
        when (type) {
            DoorsWindowsType.TRUNK -> {
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_NORMAL_LOCK
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content, openSkillId, "", inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.DOOR_BACK -> {
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_BACK_DOOR_ELECTRIC_LOCK
                list.add(
                    ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action(content, openSkillId, "", inputArg))
                )
            }

            DoorsWindowsType.LEFT_CHILD_LOCK -> {
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_LEFT_CHILD_LOCK
                list.add(
                    ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action(content, openSkillId, "", inputArg))
                )
            }

            DoorsWindowsType.RIGHT_CHILD_LOCK -> {
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_RIGHT_CHILD_LOCK
                list.add(
                    ScenarioInfo.Sequence(1, 1, ScenarioInfo.Action(content, openSkillId, "", inputArg))
                )
            }

            DoorsWindowsType.ALL_WINDOW -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content + resources.getString(R.string.scene_text_common_open), openSkillId, "", inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_VENTILATE -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_VENTILATE_OPEN
                closeSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "10"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content + resources.getString(R.string.scene_text_common_open), openSkillId, "", ArrayList()
                        )
                    )
                )

                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content + resources.getString(R.string.scene_text_common_close), closeSkillId, "", inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_DRIVER_MAIN -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_MAIN_PERCENT
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_drivers_window_open),
                            openSkillId,
                            "",
                            inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_DRIVER_CHIEF -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_DRIVER_CHIEF_PERCENT
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_front_passenger_window_open),
                            openSkillId,
                            "",
                            inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_LEFT_BACK -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_LEFT_PERCENT
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_rear_left_window_open),
                            openSkillId,
                            "",
                            inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_RIGHT_BACK -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_BACK_RIGHT_PERCENT
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_rear_right_window_open),
                            openSkillId,
                            "",
                            inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_SKYLIGHT -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_SKYLIGHT
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_WINDOW_SKYLIGHT, ArgType.INT32, 0x21.toString()
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_window_skylight), openSkillId, "", inputArg
                        )
                    )
                )
            }

            DoorsWindowsType.WINDOW_ELECTRIC_SUNSHADE_CURTAIN -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN
                inputArg.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_DOOR_WINDOW_ELECTRIC_SUNSHADE_CURTAIN, ArgType.INT32, "33"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            resources.getString(R.string.scene_text_action_window_electric_sunshade_curtain),
                            openSkillId,
                            "",
                            inputArg
                        )
                    )
                )
            }

            else -> {
                openSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_IN_FRESH_AIR
                closeSkillId = SkillsListConstant.SKILL_ID_ACTION_DOOR_WINDOW_ALL_OPEN
                inputArg.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_DOOR_LEVEL, ArgType.INT32, "10"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content + resources.getString(R.string.scene_text_common_open), openSkillId, "", ArrayList()
                        )
                    )
                )

                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            content + resources.getString(R.string.scene_text_common_close), closeSkillId, "", inputArg
                        )
                    )
                )
            }

        }
        return list
    }


    fun getInputArgsValueData(type: DoorsWindowsType): ArrayList<Int> {

        return when (type) {
            //lk1a动作行李箱改为单选
            DoorsWindowsType.DOOR_BACK -> {
                //if (ConfigManager.isHighConfig()){
                arrayListOf(2, 1)
                //}
            }
            //	else
            //		arrayListOf(0, 1)
            //}
            DoorsWindowsType.WINDOW_DRIVER_MAIN, DoorsWindowsType.WINDOW_DRIVER_CHIEF, DoorsWindowsType.WINDOW_LEFT_BACK, DoorsWindowsType.WINDOW_RIGHT_BACK -> {
                arrayListOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0xa)
            }

            DoorsWindowsType.ALL_WINDOW -> {
                arrayListOf(0, 10)
            }

            DoorsWindowsType.WINDOW_SKYLIGHT -> {
                arrayListOf(0x21, 0x1)
            }

            DoorsWindowsType.WINDOW_ELECTRIC_SUNSHADE_CURTAIN -> {
                arrayListOf(33, 1)
            }

            else -> {
                arrayListOf(0, 1)
            }
        }

    }

    fun getListData(resources: Resources, type: DoorsWindowsType, title: String): List<CheckListBean> {
        return when (type) {
            DoorsWindowsType.ALL_WINDOW -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_open), false),
                    CheckListBean(resources.getString(R.string.scene_text_condition_full_close), true),
                )
            }

            DoorsWindowsType.TRUNK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true)
                )
            }

            DoorsWindowsType.LEFT_CHILD_LOCK, DoorsWindowsType.RIGHT_CHILD_LOCK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_condition_unlock), true),
                    CheckListBean(resources.getString(R.string.scene_text_condition_lock), false),
                )
            }

            DoorsWindowsType.DOOR_BACK -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), true),
                )
            }

            DoorsWindowsType.WINDOW_SKYLIGHT, //
            DoorsWindowsType.WINDOW_ELECTRIC_SUNSHADE_CURTAIN -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), true),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), false),
                )
            }

            else -> {
                arrayListOf(
                    CheckListBean(resources.getString(R.string.scene_text_common_open), false),
                    CheckListBean(resources.getString(R.string.scene_text_common_close), true),
                )
            }
        }
    }

    /**
     * 滚轮显示数据
     */
    fun initWindowClosureWheelData(): ArrayList<String> {
        val list = ArrayList<String>()
        var index = 0
        while (index <= 100) {
            list.add(index.toString())
            index += 10
        }
        return list
    }

    /**
     * 传递给ccm的参数
     */
    fun getWindowClosureArgInfoList(): ArrayList<Int> {
        val list = ArrayList<Int>()
        var index = 0
        while (index <= 10) {
            list.add(index)
            index += 1
        }
        return list
    }
}