package com.dfl.smartscene.ui.edit.adapter

import android.graphics.Rect
import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.view.marginStart
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.util.UIUtils

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :编辑页面条件适配器
 * version: 1.0
 */
class SceneEditConditionAdapter : BaseMultiItemQuickAdapter<SceneConditionBean, BaseViewHolder>() {
    private var mImgOptions: RequestOptions = UIUtils.getGlideOptions()

    init {
        addItemType(SceneConditionBean.ITEM_TITLE, R.layout.scene_recycle_item_edit_condition_title)
        addItemType(SceneConditionBean.ITEM_CONTENT, R.layout.scene_recycle_item_edit_new_conditions)
    }

    override fun convert(holder: BaseViewHolder, item: SceneConditionBean) {
        if (item.itemType == SceneConditionBean.ITEM_TITLE) {
            val nameTv = holder.getView<TextView>(R.id.tv_edit_scene_condition_name_item)
            if (item.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                holder.getView<AppCompatImageView>(R.id.iv_condition_item_drawable).visibility = View.VISIBLE
            } else {
                holder.getView<AppCompatImageView>(R.id.iv_condition_item_drawable).visibility = View.GONE
            }
            nameTv.text = item.title
        } else {
            val deleteImv = holder.getView<AppCompatImageView>(R.id.iv_condition_item_delete)
            val contentTv = holder.getView<TextView>(R.id.tv_condition_item_name)
            val conditionIcon = holder.getView<AppCompatImageView>(R.id.iv_condition_item_icon)
            val signView = holder.getView<View>(R.id.v_trigger_condition_sign_item)
            when (item.conditionType) {
                SceneConditionBean.ADD_CONDITION_ITEM -> { //添加提示卡片
                    signView.visibility = View.GONE
                    deleteImv.visibility = View.GONE
                    contentTv.setTextColor(ContextCompat.getColor(context, R.color.scene_color_text_1))

                    val marginInDp = 22
                    val marginInPx = (marginInDp * context.resources.displayMetrics.density).toInt()
                    val layoutParams = contentTv.layoutParams as? ViewGroup.MarginLayoutParams
                    layoutParams?.leftMargin = marginInPx

                    if (item.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                        if (item.title == "1") {
                            val htmlStr = context.getString(
                                R.string.scene_text_skill_must_add_condition,
                                context.getColor(R.color.scene_primary_color_highlight).toString()
                            )
                            contentTv.text = Html.fromHtml(htmlStr, Html.FROM_HTML_MODE_COMPACT)
                        } else {
                            contentTv.text = context.getString(R.string.scene_text_skill_add_condition)
                            contentTv.setTextColor(ContextCompat.getColor(context, R.color.scene_color_text_3))
                            contentTv.layoutParams = layoutParams
                        }
                    } else {
                        contentTv.text = context.getString(R.string.scene_text_skill_continue_add_condition)
                        contentTv.setTextColor(ContextCompat.getColor(context, R.color.scene_color_text_3))
                        contentTv.layoutParams = layoutParams
                    }
                    Glide.with(context).load(item.conditionIcon).into(conditionIcon)
                }

                SceneConditionBean.ADD_CONDITION_CONTENT_ITEM -> { //具体条件卡片
                    if (item.type == SceneConditionBean.ConditionType.TRIGGER_CONDITION) {
                        signView.visibility = View.VISIBLE
                    } else {
                        signView.visibility = View.GONE
                    }

                    contentTv.setTextColor(ContextCompat.getColor(context, R.color.scene_color_text_4))
                    contentTv.text = UIUtils.getConditionHtmlStr(
                        context, item.condition?.category, item.condition?.desc
                    )
                    deleteImv.visibility = View.VISIBLE
                    Glide.with(context).load(item.conditionIcon).apply(mImgOptions).into(conditionIcon)
                }
            }
        }
    }

    fun getLayoutManager(): GridLayoutManager.SpanSizeLookup {
        return object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                //返回类型为标题类型时候占3格，返回条件类型占1格
                return if (getDefItemViewType(position) == SceneConditionBean.ITEM_TITLE) 3
                else 1
            }
        }
    }

    class MyGridItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            //            val position = parent.getChildAdapterPosition(view)
            outRect.bottom = 24
            outRect.left = 0
            outRect.right = 24
        }
    }


    override fun onViewRecycled(holder: BaseViewHolder) {
        context.let {
            val conditionIcon = holder.getView<AppCompatImageView>(R.id.iv_condition_item_icon)
            conditionIcon.let {
                Glide.with(context).clear(it)
            }
        }
        super.onViewRecycled(holder)
    }
}