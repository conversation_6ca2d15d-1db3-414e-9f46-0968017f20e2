package com.dfl.smartscene.ui.edit.action.seat

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.CheckListBean
import com.dfl.smartscene.bean.action.SeatType
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.iauto.scenarioadapter.ArgType
import com.iauto.scenarioadapter.InputArgInfo
import com.iauto.scenarioadapter.ScenarioInfo

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/07
 * desc :动作->座椅view model
 * version: 1.0
 */
class SeatActionViewModel : BaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SeatActionViewModel")

    val mSeatActionLiveData = MutableLiveData<ArrayList<ActionSkillItemBean<SeatType>>>()

    fun initData(resources: Resources) {
        val list = ArrayList<ActionSkillItemBean<SeatType>>()
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_seat_location),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                SeatType.SEAT_POSITION_TITLE,
                null,
                SeatType.SEAT_POSITION_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_main_driver_seat_memory),
                R.drawable.scene_icon_me_action_seat_main_memory,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_MEMORY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING),
                SeatType.SEAT_POSITION_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_copilot_seat_memory),
                R.drawable.scene_icon_me_action_seat_copilot_memory,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_MEMORY,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING),
                SeatType.SEAT_POSITION_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_seat_ai),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                SeatType.SEAT_AI_TITLE,
                null,
                SeatType.SEAT_AI_TYPE
            )
        )

        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_left_seat_self_reset),
                R.drawable.scene_icon_me_action_seat_reset_left,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_SELF_RESET,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_right_seat_self_reset),
                R.drawable.scene_icon_me_action_seat_reset_right,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_SELF_RESET,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ai_back_seat),
                R.drawable.scene_icon_me_action_seat_exclusive_enjoyment,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.AI_BACK_SEAT,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_SEAT,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ai_back_left_seat_fold),
                R.drawable.scene_icon_me_action_seat_fold_left,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.AI_BACK_LEFT_SEAT_FOLD,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ai_back_right_seat_fold),
                R.drawable.scene_icon_me_action_seat_fold_right,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.AI_BACK_RIGHT_SEAT_FOLD,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_ai_zero_g_seat),
                R.drawable.scene_icon_me_trigger_seat_main_seat,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.AI_ZERO_G_SEAT,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT,
                ),
                SeatType.SEAT_AI_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_seat_snug),
                0,
                ActionSkillItemBean.SKILL_TITLE,
                SeatType.SEAT_COMFORT_TITLE,
                null,
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_main_driver_seat_heating),
                R.drawable.scene_icon_me_action_seat_main_heating,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_HEAT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_copilot_seat_heating),
                R.drawable.scene_icon_me_action_seat_copilot_heating,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_HEAT,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_left_seat_heating),
                R.drawable.scene_icon_me_action_seat_left_heating,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_LEFT_SEAT_HEATING,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_right_seat_heating),
                R.drawable.scene_icon_me_action_seat_right_heating,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_RIGHT_SEAT_HEATING,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_main_driver_seat_air),
                R.drawable.scene_icon_me_action_seat_main_air,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_BLOW,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_copilot_seat_air),
                R.drawable.scene_icon_me_action_seat_copilot_air,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_BLOW,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_left_seat_air),
                R.drawable.scene_icon_me_action_seat_left_air,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_LEFT_SEAT_AIR,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_right_seat_air),
                R.drawable.scene_icon_me_action_seat_right_air,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_RIGHT_SEAT_AIR,
                arrayListOf(SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_left_seat_massage),
                R.drawable.scene_icon_me_action_seat_right_massage_strength,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_MASSAGE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH,
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_left_seat_massage_mode),
                R.drawable.scene_icon_me_action_seat_right_massage_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.LEFT_SEAT_MASSAGE_MODE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE,
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_right_seat_massage),
                R.drawable.scene_icon_me_action_seat_left_massage_strength,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_MASSAGE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH,
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_right_seat_massage_mode),
                R.drawable.scene_icon_me_action_seat_left_massage_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.RIGHT_SEAT_MASSAGE_MODE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE,
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_left_seat_massage),
                R.drawable.scene_icon_me_action_seat_left_massage,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_LEFT_SEAT_MASSAGE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF,
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_left_seat_massage_mode),
                R.drawable.scene_icon_me_action_seat_back_left_massage_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_LEFT_SEAT_MASSAGE_MODE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE,
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_right_seat_massage),
                R.drawable.scene_icon_me_action_seat_right_massage,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_RIGHT_SEAT_MASSAGE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF,
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        list.add(
            ActionSkillItemBean(
                resources.getString(R.string.scene_text_action_back_right_seat_massage_mode),
                R.drawable.scene_icon_me_action_seat_back_right_massage_mode,
                ActionSkillItemBean.SKILL_CONTENT,
                SeatType.BACK_RIGHT_SEAT_MASSAGE_MODE,
                arrayListOf(
                    SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE,
                ),
                SeatType.SEAT_COMFORT_TYPE
            )
        )
        val type = SceneEditManager.ActionType.SEAT
        val result = SceneEditManager.checkActionItemIsActive(type, list)
        mSeatActionLiveData.postValue(result)
    }

    fun getSeatListData(resources: Resources, position: SeatType): ArrayList<CheckListBean> {
        val list = ArrayList<CheckListBean>()
        when (position) {
            SeatType.LEFT_SEAT_MEMORY -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_edit_add_condition_drive), true))
                //list.add(CheckListBean(resources.getString(R.string.scene_text_action_leave_car), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_rest), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_spare), false))
            }

            SeatType.RIGHT_SEAT_MEMORY -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_common_use), true))
                //list.add(CheckListBean(resources.getString(R.string.scene_text_action_leave_car), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_rest), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_spare), false))
            }

            SeatType.LEFT_SEAT_MASSAGE, SeatType.RIGHT_SEAT_MASSAGE,
            SeatType.BACK_LEFT_SEAT_MASSAGE, SeatType.BACK_RIGHT_SEAT_MASSAGE -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_common_close), true))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_light), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_edit_action_middle), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_weight), false))
            }

            SeatType.LEFT_SEAT_MASSAGE_MODE, SeatType.RIGHT_SEAT_MASSAGE_MODE -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_right_seat_massage_mode_1), true))
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_3),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_9),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_5),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_7),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_2),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_4),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_10),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_6),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_8),
                        false
                    )
                )
            }

            SeatType.BACK_LEFT_SEAT_MASSAGE_MODE, SeatType.BACK_RIGHT_SEAT_MASSAGE_MODE -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_right_seat_massage_mode_1), true))
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_2),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_3),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_4),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_5),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_6),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_7),
                        false
                    )
                )
                list.add(
                    CheckListBean(
                        resources.getString(R.string.scene_text_action_right_seat_massage_mode_8),
                        false
                    )
                )
            }

            SeatType.AI_BACK_SEAT, SeatType.RIGHT_SEAT_SELF_RESET, SeatType.LEFT_SEAT_SELF_RESET -> {
                list.add(
                    CheckListBean(resources.getString(R.string.scene_text_common_click_and_open), true)
                )
            }

            SeatType.AI_BACK_LEFT_SEAT_FOLD, SeatType.AI_BACK_RIGHT_SEAT_FOLD -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_common_open), true))
            }

            SeatType.AI_ZERO_G_SEAT -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_spread), true))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_reset), false))
            }

            else -> {
                list.add(CheckListBean(resources.getString(R.string.scene_text_common_close), true))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_one_gear), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_two_gear), false))
                list.add(CheckListBean(resources.getString(R.string.scene_text_action_three_gear), false))
            }
        }
        return list
    }

    fun getSeatActionListData(
        position: SeatType, resources: Resources
    ): ArrayList<ScenarioInfo.Sequence> {
        val list = ArrayList<ScenarioInfo.Sequence>()
        val args = ArrayList<InputArgInfo>()
        when (position) {
            SeatType.LEFT_SEAT_MEMORY -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_POSITION_MODE, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_MEMORY_SETTING,
                            resources.getString(R.string.scene_text_action_main_driver_seat_memory),
                            args
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_MEMORY -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_POSITION_MODE, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_MEMORY_SETTING,
                            resources.getString(R.string.scene_text_action_copilot_seat_memory),
                            args
                        )
                    )
                )
            }

            SeatType.LEFT_SEAT_BLOW -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_SEAT_VENT_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_AIR_SETTING,
                            resources.getString(R.string.scene_text_action_main_driver_seat_air),
                            args
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_BLOW -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_SEAT_VENT_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_AIR_SETTING,
                            resources.getString(R.string.scene_text_action_copilot_seat_air),
                            args
                        )
                    )
                )
            }

            SeatType.BACK_LEFT_SEAT_AIR -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_SEAT_VENT_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_AIR,
                            "",
                            args
                        )
                    )
                )
            }

            SeatType.BACK_RIGHT_SEAT_AIR -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_SEAT_VENT_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_AIR,
                            "",
                            args
                        )
                    )
                )
            }

            SeatType.LEFT_SEAT_HEAT -> {
                args.add(InputArgInfo(SkillsListConstant.INPUT_ARG_ACTION_SEAT_HEAT_LEVEL, ArgType.INT32, "0"))
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_MAIN_DRIVER_SEAT_HEATING_SETTING,
                            resources.getString(R.string.scene_text_action_main_driver_seat_heating),
                            args
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_HEAT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_HEAT_LEVEL, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_COPILOT_SEAT_HEATING_SETTING,
                            resources.getString(R.string.scene_text_action_copilot_seat_heating),
                            args
                        )
                    )
                )
            }

            SeatType.BACK_LEFT_SEAT_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_HEATING,
                            "",
                            args
                        )
                    )
                )
            }

            SeatType.BACK_RIGHT_SEAT_HEATING -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_HEATING,
                            "",
                            args
                        )
                    )
                )
            }

            SeatType.LEFT_SEAT_MASSAGE -> { //主驾座椅按摩
                //关闭
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_SWITCH,
                            resources.getString(R.string.scene_text_action_left_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_SW, ArgType.INT32, "0"
                                )
                            )
                        )
                    )
                )
                //轻中重
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_left_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "3"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_left_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "2"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_left_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
            }

            SeatType.LEFT_SEAT_MASSAGE_MODE -> { //主驾座椅按摩
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
                //默认
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_MASSAGE_MODE, "", args
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_MASSAGE -> { //副驾坐骑按摩
                //关闭
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_SWITCH,
                            resources.getString(R.string.scene_text_action_right_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_SW, ArgType.INT32, "0"
                                )
                            )
                        )
                    )
                )
                //轻中重
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_right_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "3"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_right_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "2"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_INTENSITY,
                            resources.getString(R.string.scene_text_action_right_seat_massage),
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_MASSAGE_MODE -> { //副驾座椅按摩
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_MASSAGE_MODE, "", args
                        )
                    )
                )
            }

            SeatType.BACK_LEFT_SEAT_MASSAGE -> {
                //关闭
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_OFF,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_SW, ArgType.INT32, "0"
                                )
                            )
                        )
                    )
                )
                //轻中重
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "3"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "2"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
            }

            SeatType.BACK_LEFT_SEAT_MASSAGE_MODE -> {
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_LEFT_SEAT_MASSAGE_MODE, "", args
                        )
                    )
                )
            }

            SeatType.BACK_RIGHT_SEAT_MASSAGE -> {
                //关闭
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_OFF,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_SW, ArgType.INT32, "0"
                                )
                            )
                        )
                    )
                )
                //轻中重
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "3"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "2"
                                )
                            )
                        )
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "",
                            SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_INTENSITY,
                            "",
                            arrayListOf(
                                InputArgInfo(
                                    SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_INTENSITY, ArgType.INT32, "1"
                                )
                            )
                        )
                    )
                )
            }

            SeatType.BACK_RIGHT_SEAT_MASSAGE_MODE -> {
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_MASSAGE_MODE, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_BACK_RIGHT_SEAT_MASSAGE_MODE, "", args
                        )
                    )
                )
            }

            SeatType.LEFT_SEAT_SELF_RESET -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_RESET, ArgType.UINT8, "7"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_LEFT_SEAT_RESET, "", args
                        )
                    )
                )
            }

            SeatType.RIGHT_SEAT_SELF_RESET -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_SEAT_RESET, ArgType.UINT8, "7"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_RIGHT_SEAT_RESET, "", args
                        )
                    )
                )
            }

            SeatType.AI_BACK_SEAT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_ACTION_AI_BACK_SEAT, ArgType.UINT8, "0"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_SEAT, "", args
                        )
                    )
                )
            }

            SeatType.AI_BACK_LEFT_SEAT_FOLD -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_LEFT_SEAT_FOLD, "", args
                        )
                    )
                )
            }

            SeatType.AI_BACK_RIGHT_SEAT_FOLD -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_BACK_RIGHT_SEAT_FOLD, "", args
                        )
                    )
                )
            }

            SeatType.AI_ZERO_G_SEAT -> {
                args.add(
                    InputArgInfo(
                        SkillsListConstant.INPUT_ARG_STATUS, ArgType.INT32, "1"
                    )
                )
                list.add(
                    ScenarioInfo.Sequence(
                        1, 1, ScenarioInfo.Action(
                            "", SkillsListConstant.SKILL_ID_ACTION_SEAT_AI_ZERO_G_SEAT, "", args
                        )
                    )
                )
            }

            else -> {
                CommonLogUtils.logE(TAG, "错误的SeatType:$position")
            }
        }
        return list
    }

    fun getSeatValueList(position: SeatType): ArrayList<Int> {
        return when (position) {
            SeatType.LEFT_SEAT_MEMORY, SeatType.RIGHT_SEAT_MEMORY -> {
                //arrayListOf(1, 2, 3, 4)
                arrayListOf(1, 3, 4)
            }

            SeatType.LEFT_SEAT_MASSAGE_MODE, SeatType.RIGHT_SEAT_MASSAGE_MODE -> {
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8,臀腿波浪:9,臀腿脉冲:10
                arrayListOf(1, 3, 9, 5, 7, 2, 4, 10, 6, 8)
            }

            SeatType.BACK_LEFT_SEAT_MASSAGE_MODE, SeatType.BACK_RIGHT_SEAT_MASSAGE_MODE -> {
                //全身波浪:1,全身猫步:2,肩部敲击:3,肩部单排:4,背部波浪:5,背部脉冲:6,腰部波浪:7,腰部脉冲:8
                arrayListOf(1, 2, 3, 4, 5, 6, 7, 8)
            }

            SeatType.AI_BACK_SEAT, SeatType.AI_BACK_LEFT_SEAT_FOLD, SeatType.AI_BACK_RIGHT_SEAT_FOLD -> {
                arrayListOf(1)
            }

            SeatType.LEFT_SEAT_SELF_RESET, SeatType.RIGHT_SEAT_SELF_RESET -> {
                arrayListOf(7)
            }

            SeatType.AI_ZERO_G_SEAT -> {
                arrayListOf(1, 2)
            }

            else -> {
                arrayListOf(0, 1, 2, 3)
            }
        }
    }
}