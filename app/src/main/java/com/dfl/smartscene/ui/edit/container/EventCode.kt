package com.dfl.smartscene.ui.edit.container

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/23
 * desc   : 条件,动作事件代码
 * version: 1.0
 */
object ConditionEventCode { //界面条件的code
    internal const val BaseCodeOffset = 100

    /**
     * 时间
     */
    const val TimeCode = BaseCodeOffset + 1

    /**
     * 位置
     */
    const val LocationCode = BaseCodeOffset + 2

    /**
     * 环境
     */
    const val EnvironmentCode = BaseCodeOffset + 3

    /**
     * 驾驶
     */
    const val DriveCode = BaseCodeOffset + 4

    /**
     * 座椅
     */
    const val SeatCode = BaseCodeOffset + 5

    /**
     * 门窗
     */
    const val DoorCode = BaseCodeOffset + 6

    /**
     * 胎压
     */
    const val TirePressureCode = BaseCodeOffset + 7

    /**
     * 连接
     */
    const val LinkCode = BaseCodeOffset + 8

    /**
     * 导航
     */
    const val NavigationCode = BaseCodeOffset + 9

    /**
     * 智能设备
     */
    const val SmartDeviceCode = BaseCodeOffset + 10

    /**灯光*/
    const val LightingCode = BaseCodeOffset + 11

    /**充放电*/
    const val ChargeCode = BaseCodeOffset + 12

    /**座舱感知*/
    const val OMSCode = BaseCodeOffset + 13

}

object ActionEventCode { //界面动作的code
    private const val BaseCodeOffset = ConditionEventCode.BaseCodeOffset + 100

    /**
     * 推荐
     */
    const val RecommendCode = BaseCodeOffset + 1

    /**
     * 空调
     */
    const val AirConditionerCode = BaseCodeOffset + 2

    /**
     * 门窗
     */
    const val DoorWindowCode = BaseCodeOffset + 3

    /**
     * 车灯
     */
    const val CarLightCode = BaseCodeOffset + 4

    /**
     * 座椅
     */
    const val ChairCode = BaseCodeOffset + 5

    /**冰箱*/
    const val FridgeCode = BaseCodeOffset + 6

    /**
     * 驾驶
     */
    const val DriveCode = BaseCodeOffset + 7

    /**
     * 应用
     */
    const val ApplicationCode = BaseCodeOffset + 8

    /**
     * 系统
     */
    const val SystemCode = BaseCodeOffset + 9

    /**
     * 其他
     */
    const val OtherCode = BaseCodeOffset + 10

    /**
     * 智能设备
     */
    const val SmartDeviceCode = BaseCodeOffset + 11

}