package com.dfl.smartscene.ui.edit.action.smart

import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.MatchableAdapter
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.action.ActionSkillItemBean
import com.dfl.smartscene.bean.action.SmartDeviceType
import me.jessyan.autosize.utils.AutoSizeUtils

/**
 * author : wbwangws
 * e-mail : <EMAIL>
 * time : 2022/09/26
 * desc :
 * version: 1.0
 */
class SmartDeviceActionAdapter : BaseMultiItemQuickAdapter<ActionSkillItemBean<SmartDeviceType>, BaseViewHolder>(
), MatchableAdapter<ActionSkillItemBean<SmartDeviceType>> {
    init {
        addItemType(ActionSkillItemBean.SKILL_TITLE, R.layout.scene_recycle_item_edit_action_dialog_title)
        addItemType(ActionSkillItemBean.SKILL_CONTENT, R.layout.scene_recycle_item_edit_action_dialog_item)
    }

    override fun convert(holder: BaseViewHolder, item: ActionSkillItemBean<SmartDeviceType>) {
        if (item.itemType == ActionSkillItemBean.SKILL_CONTENT) {
            holder.getView<AppCompatImageView>(R.id.iv_action_reflex_icon).setImageResource(item.iconId)
            holder.getView<TextView>(R.id.tv_action_reflex_name).text = item.content
        } else {
            val txtTitle = holder.getView<TextView>(R.id.tv_edit_scene_action_name_item_dialog)
            val params = txtTitle.layoutParams as ViewGroup.MarginLayoutParams
            txtTitle.text = item.content
            if (holder.layoutPosition == 0) {
                params.topMargin = AutoSizeUtils.dp2px(context, 0.0f)
            } else {
                params.topMargin = AutoSizeUtils.dp2px(context, 20.0f)
            }
            txtTitle.layoutParams = params
        }
        if (item.isEnable == false) {
            holder.itemView.alpha = 0.5f
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override val adapterData: List<ActionSkillItemBean<SmartDeviceType>>
        get() = data

    override fun findPositionByTitle(title: String): Int {
        return data.indexOfFirst { it.content == title && !it.actionSkillId.isNullOrEmpty() }
    }
}