package com.dfl.smartscene.ui.main.discover.detail

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.android.common.util.ObjectUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.bean.edit.SceneEditBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.community.home.CommunityBaseViewModel
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.ui.edit.manager.SceneEditManager
import com.dfl.smartscene.util.ConditionOrActionIconUtils
import com.iauto.scenarioadapter.ScenarioInfo

/**
 *  author: wbwswang
 *  email: <EMAIL>
 *  time: 2022/08/05
 *  desc:场景详情view model
 *  version:1.0
 */
class SceneDetailViewModel : CommunityBaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("SceneDetailViewModel")

    //控制界面
    var sceneDetailLiveData = MutableLiveData<ScenarioBean>()

    //试用方法回调
    var onTryStartSceneLiveData = MutableLiveData<Boolean>()

    //是否显示试用按钮
    var tryStartSceneReadyLiveData = MutableLiveData<Boolean>()

    var sceneConditionLiveData = MutableLiveData<ArrayList<SceneConditionBean>>()
    var sceneActionLiveData = MutableLiveData<ArrayList<SceneActionBean>>()

    //场景详情
    var scenarioBean: ScenarioBean? = null

    //社区场景数据
    var communitySceneInfo: CommunitySceneInfo? = null


    fun initData(resources: Resources): Boolean {
        scenarioBean?.let {
            //			this.scenarioBeanTemp = it
            sceneDetailLiveData.postValue(it)
            tryStartSceneReadyLiveData.postValue(true)
            it.scenarioInfo?.let { scene ->
                addCondition(scene, resources)
            }
            it.scenarioInfo?.sequence?.let { actions ->
                addAction(actions)
            }
            return true
        }
        return false
    }

    fun tryStartScene() {
        if (onTryStartSceneLiveData.value == true) {
            return
        }
        //临停判断
        if (SceneManager.isOnDisableSceneStatus()) {
            return
        }
        val bean = scenarioBean
        bean ?: return
        onTryStartSceneLiveData.postValue(true)
        tryStartScene(SceneEditBean(bean, true)) //是新场景 试用
    }

    fun stopTryScene() {
        val bean = scenarioBean
        bean ?: return
        stopTryScene(SceneEditBean(bean, true))
    }

    fun handleTrySceneEnd(sceneId: String?) {
        if (scenarioBean?.scenarioInfo?.scenarioId == sceneId) {
            onTryStartSceneLiveData.postValue(false)
        }
    }


    private fun addCondition(scene: ScenarioInfo, resources: Resources) {
        val list = ArrayList<SceneConditionBean>()
        val triggerCondition = scene.edgeCondition
        if (triggerCondition != null) {
            val trigger = if (triggerCondition.skillId == SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE) {
                triggerCondition
            } else {
                null
            }
            list.add(
                SceneConditionBean(
                    resources.getString(R.string.scene_text_edit_scene_trigger_conditions),
                    triggerCondition,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.TRIGGER_CONDITION,
                    ConditionOrActionIconUtils.getTriggerConditionIcon(
                        triggerCondition.skillId, trigger
                    ),
                    SceneConditionBean.ITEM_CONTENT,
                    //社区详情需要将不可用能力置灰
                    isActive = if (communitySceneInfo != null) {
                        //先校验数据是否有效,再校验配置是否正确
                        val isActive = !ObjectUtils.checkScenarioDataIsInvalid(triggerCondition)
                                && SceneEditManager.checkIdIsActive(triggerCondition.skillId, 1)
                        if (!isActive) {
                            CommonLogUtils.logI(TAG, "triggerCondition不有效：${triggerCondition}")
                        }
                        isActive
                    } else {
                        true
                    }
                )
            )
        }
        scene.conditions?.forEach { condition ->
            list.add(
                SceneConditionBean(
                    "",
                    condition,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.STATUS_CONDITION,
                    ConditionOrActionIconUtils.getStatusConditionIcon(condition.skillId),
                    SceneConditionBean.ITEM_CONTENT,
                    //社区详情需要将不可用能力置灰
                    isActive = if (communitySceneInfo != null) {
                        //先校验数据是否有效,再校验配置是否正确
                        val isActive = !ObjectUtils.checkScenarioDataIsInvalid(condition)
                                && SceneEditManager.checkIdIsActive(condition.skillId, 2)
                        if (!isActive) {
                            CommonLogUtils.logI(TAG, "condition不有效：${condition}")
                        }
                        isActive
                    } else {
                        true
                    }
                )
            )
        }

        sceneConditionLiveData.postValue(list)
    }

    private fun addAction(sequence: List<ScenarioInfo.Sequence>) {
        val actions = ArrayList<SceneActionBean>()
        sequence.forEach {
            actions.add(
                SceneActionBean(
                    it,
                    ConditionOrActionIconUtils.getActionBgImg(it.action.skillId),
                    SceneActionBean.ADD_ACTION_CONTENT_ITEM,
                    isActive = if (communitySceneInfo != null) {
                        //先校验数据是否有效,再校验配置是否正确
                        val isActive = !ObjectUtils.checkScenarioDataIsInvalid(it)
                                && SceneEditManager.checkIdIsActive(it.action.skillId, 0)
                        if (!isActive) {
                            CommonLogUtils.logI(
                                TAG,
                                "sequence没有效：${it.action.skillId},${it.action.desc},${it.action.category}"
                            )
                        }
                        isActive
                    } else {
                        true
                    }
                )
            )
        }
        sceneActionLiveData.postValue(actions)
    }


    //试用
    private fun tryStartScene(editBean: SceneEditBean) {
        editBean.scenarioBean?.scenarioInfo?.let {
            SceneManager.trySceneInfo(it)
        }
    }

    //停止试用
    private fun stopTryScene(editBean: SceneEditBean) {
        editBean.scenarioBean?.scenarioInfo?.let {
            SceneManager.stopTryScene(it.scenarioId)
        }
    }
}