package com.dfl.smartscene.room.dao

import androidx.room.*
import com.dfl.smartscene.bean.community.reponse.CommunityRecentSearchResponse


/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/03/04
 * desc : 社区搜索-最近搜索响应 dao
 * version: 1.0
 */
@Dao
interface RecentSearchDao {
	@Query("select * from recent_search")
	fun findAll(): List<CommunityRecentSearchResponse>

	@Insert(onConflict = OnConflictStrategy.REPLACE)
	fun insert(communityRecentSearchResponse: CommunityRecentSearchResponse)
}