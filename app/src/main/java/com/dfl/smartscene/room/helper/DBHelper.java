package com.dfl.smartscene.room.helper;

import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.android.common.global.GlobalConstant;

import java.util.List;

import io.reactivex.Single;
import io.reactivex.SingleObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by 钟文祥 on 2023/11/18.
 * Describer:
 */
public class DBHelper {

    private static final String TAG = GlobalConstant.GLOBAL_TAG + "DBHelper";

    /**
     * 数据库操作后在主线程执行任务
     */
    public static <T> void daoFinds(OnDaoFindsListener<T> listener) {
        Single.fromCallable(() -> {
                    if (listener != null) {
                        return listener.call();
                    }
                    return null;
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<T>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(T values) {
                        if (listener != null) {
                            listener.onSuccess(values);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (listener != null) {
                            listener.onError(e);
                        }
                    }
                });
    }


    /**
     * 数据库新增操作
     */
    public static void doInsert(OnDaoInsertListener listener) {
        Single.fromCallable(() -> {
                    if (listener != null) {
                        return listener.call();
                    }
                    return null;
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<List<Long>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(List<Long> longs) {
                        if (listener != null) {
                            listener.onSuccess(longs);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (listener != null) {
                            listener.onError(e);
                        }
                        CommonLogUtils.logD(TAG, e.getMessage());
                    }
                });
    }

    /**
     * 数据库修改或删除操作
     */
    public static void doUpdateOrDelete(OnDaoUpdateOrDeleteListener listener) {
        Single.fromCallable(() -> {
                    if (listener != null) {
                        return listener.call();
                    }
                    return 0;
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SingleObserver<Integer>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onSuccess(Integer i) {
                        if (listener != null) {
                            listener.onSuccess(i);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (listener != null) {
                            listener.onError(e);
                        }
                        CommonLogUtils.logD(TAG, e.getMessage());
                    }
                });
    }

}
