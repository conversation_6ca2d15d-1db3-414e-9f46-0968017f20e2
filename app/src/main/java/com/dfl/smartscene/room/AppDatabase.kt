package com.dfl.smartscene.room

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.RoomDatabase
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse
import com.dfl.smartscene.bean.community.reponse.CommunityRecentSearchResponse
import com.dfl.smartscene.bean.community.reponse.CommunityUserInfo
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.smartscene.room.dao.CommunityListResponseDao
import com.dfl.smartscene.room.dao.CommunityUserInfoDao
import com.dfl.smartscene.room.dao.RecentSearchDao
import com.dfl.smartscene.room.dao.SceneRecordDao

/**
 * Created with Android Studio.
 *
 * <p>
 * {@author} ly-huangas
 * 2022/4/19
 * </p>
 */
@Database(
    entities = [
        SceneRecordEntity::class,
        CommunityListResponse::class,
        CommunityRecentSearchResponse::class,
        CommunityUserInfo::class
    ],
    version = 7,
    autoMigrations = [
        AutoMigration(from = 2, to = 7), AutoMigration(from = 3, to = 7), AutoMigration(from = 4, to = 7),
        AutoMigration(from = 5, to = 7), AutoMigration(from = 6, to = 7),
    ]
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun SceneRecordDao(): SceneRecordDao

    abstract fun CommunityListResponseDao(): CommunityListResponseDao

    /**
     * 社区搜索最近搜索
     */
    abstract fun RecentSearchDao(): RecentSearchDao

    /**
     * 社区账号页
     */
    abstract fun CommunityUserInfoDao(): CommunityUserInfoDao

}