package com.dfl.smartscene.rv

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.View
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.State
import com.dfl.smartscene.R

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/23
 * desc   :
 * version: 1.0
 */
open class DividerDecoration(
    context: Context,
    orientation: Int,      // layout的方向
    drawable: Int          // 引入的drawable的ID
) : RecyclerView.ItemDecoration() {

    companion object {
        val HORIZONTAL_LIST: Int = LinearLayoutManager.HORIZONTAL
        val VERTICAL_LIST: Int = LinearLayoutManager.VERTICAL
    }

    private var mDivider: Drawable? = null
    private var mOrientation = VERTICAL_LIST
    private var mPaint: Paint? = null
    var leftInset: Int = 0 // 分割线左缩进值
    var rightInset: Int = 0 // 分割线右缩进值
    private var isShowBottomLine = false

    init {
        if (drawable > 0) {
            mDivider = ResourcesCompat.getDrawable(context.resources, drawable, context.theme)
        }
        mPaint = Paint()
        mPaint?.color = context.resources.getColor(R.color.scene_color_divider)
        mPaint?.style = Paint.Style.FILL
        mPaint?.isAntiAlias = true
        setOrientation(orientation)
    }

    constructor(context: Context, colorId: Int) : this(
        context,
        VERTICAL_LIST,
        -1
    ) {
        mDivider = ColorDrawable(colorId)
    }

    fun setOrientation(orientation: Int) {
        if (orientation != HORIZONTAL_LIST && orientation != VERTICAL_LIST) {
            throw IllegalArgumentException("invalid orientation")
        }
        mOrientation = orientation
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: State) {
        if (mOrientation == VERTICAL_LIST) {
            drawVertical(c, parent)
        } else {
            drawHorizontal(c, parent)
        }
    }

    fun setShowBottomLine(isShow: Boolean) {
        this.isShowBottomLine = isShow
    }

    private fun drawVertical(c: Canvas, parent: RecyclerView) {
        val divider = mDivider
        val paint = this.mPaint
        divider ?: return
        paint ?: return

        val left = parent.paddingLeft
        val right = parent.width - parent.paddingRight
        val childCount = parent.childCount

        //最后一个item不画分割线
        var i = 0
        val size = if (isShowBottomLine) childCount else childCount - 1
        while (i < size) {
            val child: View = parent.getChildAt(i)
            val params = child.layoutParams as RecyclerView.LayoutParams
            val top: Int = child.bottom + params.bottomMargin
            val bottom: Int = top + divider.intrinsicHeight
            if (leftInset > 0 || rightInset > 0) {
                c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), paint)
                divider.setBounds(left + leftInset, top, right - rightInset, bottom)
            } else {
                divider.setBounds(left, top, right, bottom)
            }
            divider.draw(c)
            i++
        }
    }

    private fun drawHorizontal(c: Canvas, parent: RecyclerView) {
        val divider = mDivider
        divider ?: return

        val top = parent.paddingTop
        val bottom = parent.height - parent.paddingBottom
        val childCount = parent.childCount
        var i = 0
        val size = if (isShowBottomLine) childCount else childCount - 1
        while (i < size) {
            val child: View = parent.getChildAt(i)
            val params = child.layoutParams as RecyclerView.LayoutParams
            val left: Int = child.right + params.rightMargin
            val right: Int = left + divider.intrinsicHeight
            divider.setBounds(left, top, right, bottom)
            divider.draw(c)
            i++
        }
    }

    // 由于Divider也有宽高，每一个Item需要向下或者向右偏移
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val divider = mDivider
        divider ?: return

        if (mOrientation == VERTICAL_LIST) {
            outRect.set(0, 0, 0, divider.intrinsicHeight)
        } else {
            outRect.set(0, 0, divider.intrinsicWidth, 0)
        }
    }
}