package com.dfl.smartscene.rv.adapter

import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/06/24
 * desc   :
 * version: 1.0
 */
abstract class DataBindingFragmentStatePagerAdapter<T>(
    fragmentManager: FragmentManager,
    lifecycle: Lifecycle
) : FragmentStateAdapter(fragmentManager, lifecycle),
    IDataAdapter<T> {
    var dataLiveData = MutableLiveData<Boolean>()
    var data: List<T?>? = null

    fun getData(position: Int): T? {
        return data?.get(position)
    }

    override fun getItemCount(): Int {
        return data?.size ?: 0
    }

    override fun fillData(data: MutableList<T?>?) {
        this.data = data
        notifyDataSetChanged()
        dataLiveData.postValue(true)
    }

    override fun getItemId(position: Int): Long {
        if (data != null) {
            return this.data!![position].hashCode().toLong() //针对ITEM刷新问题
        }
        return super.getItemId(position)
    }
}