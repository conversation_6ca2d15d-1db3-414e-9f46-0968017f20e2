package com.dfl.smartscene.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.communication.LauncherManager
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.soa.SceneManager
import com.dfl.smartscene.util.TrackUtils

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/10/23
 * desc : 智能卡片和通知横幅广播事件
 * version: 1.0
 */
class LauncherBroadcastReceiver : BroadcastReceiver() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("LauncherBroadcastReceiver")
    override fun onReceive(context: Context, intent: Intent?) {
        CommonLogUtils.logI(TAG, "收到卡片点击事件:${intent?.action}")
        if (intent?.action == LauncherManager.ACTION_OPERATE) {
            val operateType = intent.getIntExtra(LauncherManager.EXTRA_OPERATE_NAME, -1)
            val sceneId = intent.getStringExtra(LauncherManager.EXTRA_SCENE_ID)
            CommonLogUtils.logI(TAG, "operateType:$operateType,sceneId:$sceneId")
            if (operateType == -1 || TextUtils.isEmpty(sceneId)) {
                return
            }
            sceneId ?: return
            when (operateType) {
                LauncherManager.EXTRA_OPERATE_RUN -> { //执行的动作参数
                    val name = SceneManager.findSceneById(sceneId)?.scenario?.scenarioInfo?.scenarioName ?: ""
                    TrackUtils.clickWidgetManualScene(name, sceneId)
                    val isSuccess = SceneManager.startScene(sceneId)
                    TrackUtils.resultWidgetManualScene(sceneId, isSuccess)
                }
                LauncherManager.EXTRA_OPERATE_STOP -> { //场景中断动作的参数
                    SceneManager.stopScene(sceneId)
                }
                LauncherManager.EXTRA_OPERATE_NOTIFY_RUN -> { //通知卡片场景执行
                    val name = SceneManager.findSceneById(sceneId)?.scenario?.scenarioInfo?.scenarioName ?: ""
                    TrackUtils.clickMyAutomaticSceneInquiry(name, sceneId, 0)
                    SceneManager.notifyAskIfExecuteScenario(sceneId, true)
                }
                LauncherManager.EXTRA_OPERATE_RUN_TIME_OUT -> { //通知卡片倒计时自动结束，要自动执行
                    val name = SceneManager.findSceneById(sceneId)?.scenario?.scenarioInfo?.scenarioName ?: ""
                    TrackUtils.clickMyAutomaticSceneInquiry(name, sceneId, 2)
                    SceneManager.notifyAskIfExecuteScenario(sceneId, true)
                }
                LauncherManager.EXTRA_OPERATE_NOTIFY_IGNORE -> { //通知卡片场景主动点击关闭
                    val name = SceneManager.findSceneById(sceneId)?.scenario?.scenarioInfo?.scenarioName ?: ""
                    TrackUtils.clickMyAutomaticSceneInquiry(name, sceneId, 1)
                    SceneManager.notifyAskIfExecuteScenario(sceneId, false)
                }
            }
        }
    }
}