package com.dfl.smartscene.community.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.smartscene.databinding.SceneRecycleItemSearchRecentBinding
import com.dfl.smartscene.widget.flodlayout.FlowAdapter

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2024/03/04
 * desc : 最近搜索rv 适配器
 * version: 1.0
 */
class RecentSearchAdapter() : FlowAdapter<String>() {
	private lateinit var mViewBind: SceneRecycleItemSearchRecentBinding

	override fun getView(parent: ViewGroup, item: String?, position: Int): View {
		mViewBind = SceneRecycleItemSearchRecentBinding.inflate(LayoutInflater.from(parent.context), parent, false)
		return mViewBind.root
	}

	override fun initView(view: View, item: String, position: Int) {
		//最多显示9个字
		mViewBind.tvSearchRecent.text = if (item.length > 9) {
			item.substring(0, 9) + "..."
		} else {
			item
		}
		view.setOnClickListener {
			if (!DebouncingUtils.isValid(it)) return@setOnClickListener
			onClickListener?.onClick(item)
		}
	}
}