package com.dfl.smartscene.community.usercenter.adpter

import android.widget.ImageView
import androidx.core.view.isVisible
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.dfl.android.common.util.NumberToCNUtil
import com.dfl.smartscene.R
import com.dfl.smartscene.community.usercenter.UserNumberInfo

/**
 * author : xulili
 * e-mail : <EMAIL>
 * time : 2023/11/01
 * desc : 显示用户发布，关注，粉丝数rv的adapter
 * version: 1.0
 */
class UserNumberInfoAdapter() : BaseQuickAdapter<UserNumberInfo, BaseViewHolder>(R.layout.scene_recycle_item_userinfo) {
    override fun convert(holder: BaseViewHolder, item: UserNumberInfo) {
        holder.setText(R.id.tv_userinfo_num, NumberToCNUtil.formatOverString(item.userNumberInfoNum))
        holder.setText(R.id.tv_userinfo_desc, item.userNumberInfoName)
        val ivNumberTip: ImageView = holder.getView(R.id.iv_number_tips)
        /**数据有增加才显示红点*/
        ivNumberTip.isVisible = item.isNumberAdd
    }

    /**
     * 场景详情关注事件更新粉丝数,索引位置1
     * @param isFocus 是否关注
     */
    fun notifyByFocus(isFocus: Boolean) {
        if (isFocus) {
            data[1].userNumberInfoNum += 1
        } else {
            data[1].userNumberInfoNum -= 1
        }
        notifyItemChanged(1)
    }

    /**
     * 场景详情点赞事件更新获赞数,索引位置2
     * @param isLike 是否关注
     */
    fun notifyByLike(isLike: Boolean) {
        if (isLike) {
            data[2].userNumberInfoNum += 1
        } else {
            data[2].userNumberInfoNum -= 1
        }
        notifyItemChanged(2)
    }
}