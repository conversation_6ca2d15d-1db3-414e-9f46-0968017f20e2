package com.dfl.smartscene.community.post

import android.annotation.SuppressLint
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import com.dfl.android.common.base.BaseDialogFragment
import com.dfl.android.commonlib.KeyboardUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.util.UIUtils
import com.dfl.smartscene.widget.ClearEditText
import com.dfl.smartscene.widget.RegexEditText

/**
 *Created by 钟文祥 on 2023/12/5.
 *Describer: 社区场景 场景发布 编辑名称简介框
 */
class CommunityEditDialogFragment(
    private var isEditName: Boolean = true,
    private var inputValue: String = "",
    private var onConfirmListener: OnConfirmListener?
) : BaseDialogFragment() {

    private var tvTitle: TextView? = null
    private var etInput: ClearEditText? = null
    private var tvCheckMsg: TextView? = null
    private var ivClose: ImageView? = null

    private val minNum = 3
    private var maxNum = 10
    private val imeOptions = 9

    interface OnConfirmListener {
        fun onConfirm(value: String)
    }

    companion object {
        fun showDialog(
            fm: FragmentManager, isEditName: Boolean, inputValue: String, onConfirmListener: OnConfirmListener
        ) {
            val REGEX_NAME = "[^a-zA-Z0-9\\u4e00-\\u9fa5]*"
            val mDialog = CommunityEditDialogFragment(
                isEditName, if (isEditName) inputValue.replace(Regex(REGEX_NAME), "") else inputValue, onConfirmListener
            )
            mDialog.showNow(fm, CommunityEditDialogFragment.javaClass.name)

        }
    }

    override fun initParams() {

    }

    override fun getLayoutId(): Int {
        return R.layout.scene_dialog_fragment_me_rename
    }

    override fun initData() {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }


    @SuppressLint("SetTextI18n")
    override fun initView(view: View) {
        setCanceledOnTouchOutside(true)
        if (isEditName) {
            maxNum = 10
        } else {
            maxNum = 66
        }
        ivClose = view.findViewById(R.id.iv_rename_close)
        tvTitle = view.findViewById(R.id.tv_rename_title)
        tvTitle?.text =
            getString(if (isEditName) R.string.scene_text_community_edit_name else R.string.scene_text_community_edit_desc)

        tvCheckMsg = view.findViewById(R.id.tv_check_msg)
        etInput = view.findViewById(R.id.edt_input_area)
        etInput?.hint =
            getString(if (isEditName) R.string.scene_text_community_edit_name_please else R.string.scene_text_community_edit_desc_please)
        etInput?.setText(inputValue)

        if (isEditName) {
            UIUtils.setKeyBroadInputMinIfNotEnable(etInput, minNum)
        }
        etInput?.imeOptions = imeOptions
        //将光标设置在字符最后
        etInput?.length()?.let { etInput?.setSelection(it) }
        setListener()
        onTextChanged(etInput?.text.toString())
        KeyboardUtils.showSoftInput(etInput!!)
    }

    private fun cancel() {
        etInput?.let {
            KeyboardUtils.hideSoftInput(it)
        }
        dismissWithAnimate()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setListener() {
        ivClose?.setOnClickListener {
            cancel()
        }
        etInput?.setOnEditorActionListener { v, actionId, event ->
            if (actionId == imeOptions) {
                onConfirmListener?.onConfirm(etInput?.text.toString())
                cancel()
            }
            true
        }

        etInput?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                onTextChanged(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {

            }
        })

        if (isEditName) {
            etInput?.setInputRegex(RegexEditText.REGEX_NAME)
            //监听是否有输入特殊字符串
            if (etInput is RegexEditText) {
                (etInput as RegexEditText).onMatchListener = object : RegexEditText.OnMatchListener {
                    @SuppressLint("SetTextI18n")
                    override fun onMatchFail() {
                        tvCheckMsg?.text =
                            "${getString(R.string.scene_edit_input_hint_short_text_tips)} (${etInput?.text.toString().length}/${maxNum})"
                    }
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun onTextChanged(inputStr: String) {
        //输入场景名称时
        if (isEditName) {
            if (inputStr.length > maxNum) {
                etInput?.setText(inputStr.substring(0, maxNum))
                etInput?.setSelection(maxNum)
            }
            tvCheckMsg?.text =
                "${getString(R.string.scene_edit_input_hint_short_text_tips)} (${etInput?.text.toString().length}/${maxNum})"

        }
        //输入场景简介时
        else {
            if (inputStr.length > maxNum) {
                etInput?.setText(inputStr.substring(0, maxNum))
                etInput?.setSelection(maxNum)
            }
            tvCheckMsg?.text =
                "${getString(R.string.scene_edit_input_hint_community_desc)} (${etInput?.text.toString().length}/${maxNum})"
        }
    }

    override fun onResume() {
        super.onResume()
        etInput?.post {
            etInput?.let {
                KeyboardUtils.showSoftInput(it)
            }
        }
        KeyboardUtils.registerSoftInputChangedListener(dialog?.window!!) {
            if (it == 0) {
                etInput?.clearFocus()
            } else {
                etInput?.requestFocus()
            }
        }
    }

    override fun onTouchOutside() {
        super.onTouchOutside()
        cancel()
    }
}