package com.dfl.smartscene.community.post

import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.databinding.SceneRecycelItemCommunityMySelectBinding
import com.dfl.smartscene.util.UIUtils

/**
 *Created by 钟文祥 on 2023/11/23.
 *Describer: 发布场景 之 选择场景 adapter
 */
class CommunityPostSelectAdapter :
    BaseQuickAdapter<MySceneBean, BaseDataBindingHolder<SceneRecycelItemCommunityMySelectBinding>>(
        R.layout.scene_recycel_item_community_my_select
    ) {

    var mSelectedIndex = -1

    /**是否没有选择数据*/
    fun isNoSelect(): Boolean {
        return mSelectedIndex == -1
    }

    //https://blog.csdn.net/u011213403/article/details/123734600
    init {
        setHasStableIds(true)
    }

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycelItemCommunityMySelectBinding>, item: MySceneBean
    ) {
        holder.dataBinding?.bean = item

        holder.dataBinding?.isSelected = mSelectedIndex == holder.layoutPosition
        //holder.dataBinding?.isShowGray = mSelectedIndex >= 0 && mSelectedIndex != holder.layoutPosition
        //未被选中透明度50%
        holder.itemView.alpha = if (mSelectedIndex >= 0 && mSelectedIndex != holder.layoutPosition) {
            0.5f
        } else {
            1f
        }
        UIUtils.initRecyclerView(
            context,
            holder.dataBinding?.wllIconList,
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false),
            false
        )
        val adapter = CommunityIconBeanAdapter()
        holder.dataBinding?.wllIconList?.adapter = adapter
        adapter.setList(item.meIconBeanList)

    }

    fun getSelectItem(): ScenarioBean {
        return data[mSelectedIndex].scenario
    }


}