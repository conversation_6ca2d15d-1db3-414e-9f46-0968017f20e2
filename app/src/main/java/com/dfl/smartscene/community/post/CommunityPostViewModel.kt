package com.dfl.smartscene.community.post

import android.content.res.Resources
import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.apiweb.service.CommunityService
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.RxSubscriber
import com.dfl.smartscene.bean.apibase.BasePostValue
import com.dfl.smartscene.bean.apibase.BaseResponseBean
import com.dfl.smartscene.bean.community.reponse.CommunityGenImg
import com.dfl.smartscene.bean.community.reponse.ScenarioInfoConverter
import com.dfl.smartscene.bean.community.request.CommunityUploadRequest
import com.dfl.smartscene.bean.edit.SceneActionBean
import com.dfl.smartscene.bean.edit.SceneConditionBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.global.SkillsListConstant
import com.dfl.smartscene.util.ConditionOrActionIconUtils
import com.dfl.smartscene.util.TrackUtils
import com.iauto.scenarioadapter.ScenarioInfo
import io.reactivex.disposables.Disposable

/**
 *Created by 钟文祥 on 2023/11/24.
 *Describer: 发布场景 之 发布场景 viewModel
 */
class CommunityPostViewModel : BaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG + "CommunityPostViewModel"
    var fromPage: String? = null //从哪个页面进入发布场景

    //详情
    var scenarioBean: ScenarioBean? = null
    var sceneConditionLiveData = mutableListOf<SceneConditionBean>()
    var sceneActionLiveData = mutableListOf<SceneActionBean>()

    //封面图
    var mCommunityGenImg = MutableLiveData<CommunityGenImg?>()
    var genimgDisposable: Disposable? = null

    //上传
    var mUpload = MutableLiveData<BasePostValue<BaseResponseBean>>()
    var loadDisposable: Disposable? = null

    //初始化数据
    fun initData(scenarioBean: ScenarioBean?, resources: Resources): Boolean {
        scenarioBean?.let {
            this.scenarioBean = it
            it.scenarioInfo?.let { scene ->
                addCondition(scene, resources)
            }
            it.scenarioInfo?.sequence?.let { actions ->
                addAction(actions)
            }
            return true
        }
        return false
    }

    private fun addCondition(scene: ScenarioInfo, resources: Resources) {
        val list = ArrayList<SceneConditionBean>()
        val triggerCondition = scene.edgeCondition
        if (triggerCondition != null) {
            val trigger = if (triggerCondition.skillId == SkillsListConstant.SKILL_ID_TRIGGER_DOOR_ALL_STATE) {
                triggerCondition
            } else {
                null
            }
            //            list.add(
            //                SceneConditionBean(
            //                    resources.getString(R.string.scene_text_edit_meet_all_conditions),
            //                    null,
            //                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
            //                    SceneConditionBean.ConditionType.TRIGGER_CONDITION,
            //                    0,
            //                    SceneConditionBean.ITEM_TITLE
            //                )
            //            )
            list.add(
                SceneConditionBean(
                    resources.getString(R.string.scene_text_edit_scene_trigger_conditions),
                    triggerCondition,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.TRIGGER_CONDITION,
                    ConditionOrActionIconUtils.getTriggerConditionIcon(
                        triggerCondition.skillId, trigger
                    ),
                    SceneConditionBean.ITEM_CONTENT
                )
            )
        }
        scene.conditions?.forEach { condition ->
            list.add(
                SceneConditionBean(
                    "",
                    condition,
                    SceneConditionBean.ADD_CONDITION_CONTENT_ITEM,
                    SceneConditionBean.ConditionType.STATUS_CONDITION,
                    ConditionOrActionIconUtils.getStatusConditionIcon(condition.skillId),
                    SceneConditionBean.ITEM_CONTENT
                )
            )
        }
        sceneConditionLiveData.addAll(list)
    }

    private fun addAction(sequence: List<ScenarioInfo.Sequence>) {
        val actions = ArrayList<SceneActionBean>()
        sequence.forEach {
            actions.add(
                SceneActionBean(
                    it,
                    ConditionOrActionIconUtils.getActionBgImg(it.action.skillId),
                    SceneActionBean.ADD_ACTION_CONTENT_ITEM
                )
            )
        }
        sceneActionLiveData.addAll(actions)
    }

    /**生成封面图*/
    fun genimg() {
        CommunityService.genimg().subscribe(object : RxSubscriber<CommunityGenImg>() {
            override fun onAddDisposable(d: Disposable?) {
                cancelDisposableGenimg()
                genimgDisposable = d
            }

            override fun onApiNext(value: CommunityGenImg?) {
                mCommunityGenImg.value = value
                CommonLogUtils.logD(TAG, "发布场景，获取到的图片：" + value?.imgUrl)
            }

            override fun onApiError(ex: ApiException?) {
                mCommunityGenImg.value = null
                CommonLogUtils.logD(TAG, "发布场景，获取到的图片失败")
            }
        })
    }

    /**发布*/
    fun upload() {
        scenarioBean?.let {
            CommonToastUtils.showLoadingToast(R.string.scene_text_community_post_ing)
            val time = System.currentTimeMillis()
            val communityId = "SQ_VEHICLE_${time}"
            TrackUtils.clickMePostScene(isFromMeFragment(), it, communityId)

            val req = CommunityUploadRequest(communityId, ScenarioInfoConverter(it.scenarioInfo!!), time)
            CommunityService.upload(
                req
            ).subscribe(object : RxSubscriber<BaseResponseBean>() {
                override fun onAddDisposable(d: Disposable?) {
                    cancelDisposableLoad()
                    loadDisposable = d
                }

                override fun onApiNext(value: BaseResponseBean?) {
                    //必须手动取消，否则一直显示
                    CommonToastUtils.cancelLoadingToast()
                    mUpload.value = BasePostValue(value = value)
                    TrackUtils.resultClickMePostScene(isFromMeFragment(), communityId, true)
                }

                override fun onApiError(ex: ApiException?) {
                    //必须手动取消，否则一直显示
                    CommonToastUtils.cancelLoadingToast()
                    CommonLogUtils.logE(TAG, "发布社区失败：" + ex?.message)
                    mUpload.value = BasePostValue(ex = ex)
                    TrackUtils.resultClickMePostScene(isFromMeFragment(), communityId, false)
                }
            })
        }


    }

    /**删除所有请求*/
    fun cancelDisposable() {
        cancelDisposableGenimg()
        cancelDisposableLoad()
    }

    private fun cancelDisposableGenimg() {
        genimgDisposable?.takeIf { !it.isDisposed }?.dispose()
        genimgDisposable = null
    }

    private fun cancelDisposableLoad() {
        loadDisposable?.takeIf { !it.isDisposed }?.dispose()
        loadDisposable = null
    }

    fun isFromMeFragment(): Boolean {
        return fromPage == "MeFragment"
    }
}