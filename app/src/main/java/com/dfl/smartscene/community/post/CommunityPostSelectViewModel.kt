package com.dfl.smartscene.community.post

import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.base.BaseViewModel
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.soa.SceneManager

/**
 *Created by 钟文祥 on 2023/11/23.
 *Describer: 发布场景 之 选择场景 viewModel
 */
class CommunityPostSelectViewModel : BaseViewModel() {

    private val mSceneList = MutableLiveData<List<MySceneBean>>()

    fun getSceneList(): MutableLiveData<List<MySceneBean>> {
        return mSceneList
    }

    fun getMySceneBeanList() {
        val list = SceneManager.getSceneInfoList()
        list.forEach {
            it.meIconBeanList = it.findIconBeanList()
        }
        mSceneList.value = list
    }
}