package com.dfl.smartscene.community.home;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.dfl.android.common.base.BaseVBFragment;
import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.common.util.CommonToastUtils;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.smartscene.bean.community.request.SmartRefreshState;
import com.dfl.smartscene.bean.mechanism.BaseDataSource;
import com.dfl.smartscene.communication.UserCenterManager;
import com.dfl.smartscene.databinding.SceneFragmentCommunityBinding;
import com.dfl.smartscene.rv.EquallySpaceDecoration;
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity;
import com.dfl.smartscene.util.UIUtils;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;

import java.lang.ref.WeakReference;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;


/**
 * Created by 钟文祥 on 2023/11/1.
 * Describer: 社区场景 fragment - 融合版本，直接显示热门列表
 */
@SuppressLint("CheckResult")
public class CommunityFragment extends BaseVBFragment<CommunityViewModel,
        SceneFragmentCommunityBinding> {

    private static final String TAG = "CommunityFragment";
    
    public CommunityAdapter mAdapter;
    private Disposable showLottieDisposable = null;//闪烁的延时处理
    private Disposable noMoreDataDisposable = null;//无更多数据的延时处理

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        // 初始化RecyclerView
        initRV();
        // 初始化刷新控件
        initRefreshLayout();
    }

    @Override
    public void createObserver() {
        // 初始化数据观察
        initObserveRvDataList();
        // 初始化API请求状态观察
        initObserveApiReqResState();
        // 关注回调
        initObserverFocusApiReqResState();
        // 关注事件回调
        initEventBusFocusToUpdateAllUI();
        // 点赞事件回调
        initEventBusLikeToUpdateAllUI();
        // 下载事件回调
        initEventBusDownloadToUpdateAllUI();
        // 个人中心登录状态回调
        initEventBusLoginOutSuccess();
        // 选择场景和发布场景的返回
        initEventBusPostFragmentBack();

        // 设置为热门tab类型并加载数据
        mViewModel.setMCurTabType(CommunityTabType.Hot);
        mViewModel.firstLoadRvData();

        UIUtils.initLoadingLottieSync(mViewBind);
    }

    @SuppressLint("CheckResult")
    private void initObserveRvDataList() {
        mViewModel.getRvDataList().observe(this, communityListResponse -> {
            if (communityListResponse == null) {
                if (mAdapter.getData() != null
                        && mAdapter.getData().size() > 0
                        && mAdapter.getData().get(0).getScenarioInfoDa() == null
                        && mViewModel.getMNullDataType() == BaseDataSource.DataSourceType.Remote) {
                    CommonLogUtils.logE(TAG, "CommunityViewModel社区列表数据有异常,需要15个缺省图来占位");
                    mAdapter.setList(CommunitySceneInfo.Companion.getErrorNullList());
                }
                return;
            }
            if (mViewModel.getMSmartRefreshState() == SmartRefreshState.Refresh ||
                    mViewModel.getMSmartRefreshState() == SmartRefreshState.None) {
                mAdapter.setList(communityListResponse.getRows());

                //闪烁
                if (communityListResponse.getRows().size() >= 1 && communityListResponse.getRows().get(0)
                        .isShowLottieAnimation()) {
                    getMViewBind().rvCommunity.scrollToPosition(0);
                    showLottieDisposable = Observable.just(1).delay(4, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                            .subscribe(integer -> {
                                CommunitySceneInfo item0 = mAdapter.getData().get(0);
                                item0.setShowLottieAnimation(false);
                                mAdapter.setData(0, item0);
                            });
                }
            } else if (mViewModel.getMSmartRefreshState() == SmartRefreshState.LoadMore) {
                if (communityListResponse.getRows() != null && communityListResponse.getRows().size() > 0) {
                    for (int i = mAdapter.getData().size() - 1; i >= 0; i--) {
                        if (mAdapter.getData().get(i).getScenarioInfoDa() == null) {
                            mAdapter.getData().remove(i);
                        }
                    }
                }
                mAdapter.addData(communityListResponse.getRows());
            }

            getMViewBind().slUpdate.finishRefresh();
            getMViewBind().slUpdate.finishLoadMore();
            if (communityListResponse.hasNextPage()) {
                getMViewBind().slUpdate.setNoMoreData(false);//恢复没有更多数据的原始状态
            } else {
                noMoreDataDisposable = Observable.just(1).delay(1, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                        .subscribe(integer -> {
                            getMViewBind().slUpdate.setNoMoreData(true); //显示没有更多
                        });
            }
        });
    }

    private void initObserveApiReqResState() {
        mViewModel.getApiReqResState().observe(this, apiReqResState -> {
            switch (apiReqResState.getState()) {
                case GetValueStart:
                    UIUtils.showLoading(getMViewBind());
                    break;
                case GetValueFinish:
                    UIUtils.disShow(getMViewBind());
                    break;
                case Error:
                    if (apiReqResState.getEx() != null && !TextUtils.isEmpty(apiReqResState.getEx().toastMsg)) {
                        CommonToastUtils.show(R.string.scene_toast_common_web_server_error);
                    }
                    getMViewBind().slUpdate.finishRefresh(false);
                    getMViewBind().slUpdate.finishLoadMore(false);
                    UIUtils.disShow(getMViewBind());
                    break;
            }
        });
    }

    //关注回调
    private void initObserverFocusApiReqResState() {
        mViewModel.getMFocusApiReqResState().observe(this, apiReqResState -> {
            switch (apiReqResState.getState()) {
                case GetValueStart:
                case GetValueFinish:
                    break;
                case Error:
                    CommonToastUtils.show((String) apiReqResState.getMap().get(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI));
                    break;
            }
        });
    }

    //关注事件成功后更新数据库和UI
    private void initEventBusFocusToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Focus");
                    //更新列表
                    mAdapter.notifyByFocus(item);
                });
    }

    //下载事件成功后更新数据库和UI
    private void initEventBusDownloadToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Down");
                    //更新列表
                    mAdapter.notifyByDown(item);
                });
    }

    //点赞事件成功后更新数据库和UI
    private void initEventBusLikeToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Like");
                    //更新列表
                    mAdapter.notifyByLike(item);
                });
    }

    //个人中心登录退登成功回调
    private void initEventBusLoginOutSuccess() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS,
                Boolean.class).observe(getViewLifecycleOwner(), item -> {
            if (item) {
                CommonLogUtils.logD(TAG, "退登成功 界面回调");
            } else {
                CommonLogUtils.logD(TAG,
                        "登录成功 界面回调" + Objects.requireNonNull(UserCenterManager.getUserLoginStatus()).getUuid());
            }
            // 刷新数据
            mViewModel.refreshLoadRvData();
        });
    }

    //选择场景和发布场景的返回
    private void initEventBusPostFragmentBack() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Integer.class)
                .observe(this, integer -> {
                    if (integer == 2) {//场景发布成功
                        // 刷新并闪烁第一个卡片
                        mViewModel.refreshLoadRvDataAndShowLottieAnimation();
                    }
                });
    }

    private void initRV() {
        UIUtils.initRecyclerView(getActivity(), getMViewBind().rvCommunity,
                new GridLayoutManager(getActivity(), 3), false);
    
        // 添加上下蒙层模糊效果
        getMViewBind().rvCommunity.setVerticalFadingEdgeEnabled(true);
        getMViewBind().rvCommunity.setFadingEdgeLength(AutoSizeUtils.dp2px(requireContext(), 10f));
    
        mAdapter = new CommunityAdapter((position, object) -> {
            CommunitySceneInfo item = mAdapter.getData().get(position);
            switch (object) {
                case Subscribe: {
                    mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.SUBSCRIBED);
                    break;
                }
                case UnSubscribe: {
                    mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.UNSUBSCRIBED);
                    break;
                }
                case Likes: {
                    mViewModel.likeOrNoLike(item, getChildFragmentManager());
                    break;
                }
                case Down: {
                    mViewModel.downloadOrNo(item, getChildFragmentManager(), requireActivity(), true);
                    break;
                }
            }
        });
        getMViewBind().rvCommunity.setAdapter(mAdapter);
        getMViewBind().rvCommunity.addItemDecoration(new EquallySpaceDecoration(
                AutoSizeUtils.dp2px(requireContext(), 24f),
                AutoSizeUtils.dp2px(requireContext(), 24f),
                true));
        getMViewBind().rvCommunity.addOnScrollListener(new MyOnScrollListener(CommunityFragment.this));
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (mAdapter.getData().get(position).isShowLottieAnimation()) return;
            if (mAdapter.getData().get(position).getScenarioInfoDa() == null) return;
            SceneDetailActivity.Companion.startCommunitySceneDetailActivity(requireActivity(),
                    mAdapter.getData().get(position));
        });

        //加载列表时，有闪烁效果
        mAdapter.setList(CommunitySceneInfo.Companion.getLottieList());
    }

    private void initRefreshLayout() {
        UIUtils.initRefreshLayout(getMViewBind());
        getMViewBind().slUpdate.setEnableRefresh(true); // 启用下拉刷新
        getMViewBind().slUpdate.setOnRefreshLoadMoreListener(new MyOnRefreshLoadMoreListener(CommunityFragment.this));
    }

    @Override
    public void onStart() {
        super.onStart();
        CommonLogUtils.logI(TAG, "onStart: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onResume() {
        super.onResume();
        CommonLogUtils.logI(TAG, "onResume: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onPause() {
        super.onPause();
        CommonLogUtils.logI(TAG, "onPause: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onStop() {
        super.onStop();
        CommonLogUtils.logI(TAG, "onStop: " + mViewModel.getMCurTabType());
    }

    @Override
    public void onDestroy() {
        if (showLottieDisposable != null && !showLottieDisposable.isDisposed()) {
            showLottieDisposable.dispose();
        }
        showLottieDisposable = null;
        if (noMoreDataDisposable != null && !noMoreDataDisposable.isDisposed()) {
            noMoreDataDisposable.dispose();
        }
        noMoreDataDisposable = null;
        mViewModel.cancelDisposable();
        CommonLogUtils.logI(TAG, "onDestroy: " + mViewModel.getMCurTabType());
        super.onDestroy();
    }

    /**
     * 主题切换
     */
    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //手动切换进度条png
        UIUtils.setSmartRefreshOnConfigurationChanged(null, getMViewBind().classicsFooter);
    }

    private static class MyOnScrollListener extends RecyclerView.OnScrollListener {
        private WeakReference<CommunityFragment> reference;

        public MyOnScrollListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (reference == null || reference.get() == null) return;
            if (newState == 0) { //停止
                Glide.with(reference.get()).resumeRequests();
            } else { //滑动
                Glide.with(reference.get()).pauseRequests();
            }
        }
    }

    private static class MyOnRefreshLoadMoreListener implements OnRefreshLoadMoreListener {
        private WeakReference<CommunityFragment> reference;

        public MyOnRefreshLoadMoreListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
            if (reference == null || reference.get() == null) return;
            reference.get().getMViewModel().loadMoreRvData();
        }

        @Override
        public void onRefresh(@NonNull RefreshLayout refreshLayout) {
            if (reference == null || reference.get() == null) return;
            reference.get().getMViewModel().refreshLoadRvData();
        }
    }
}