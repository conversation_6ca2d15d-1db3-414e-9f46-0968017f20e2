package com.dfl.smartscene.community.home

import androidx.lifecycle.MutableLiveData
import com.dfl.android.common.global.GlobalConstant
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.TaskExecutor
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.apiweb.utils.ApiException
import com.dfl.smartscene.apiweb.utils.ApiReqResState
import com.dfl.smartscene.apiweb.utils.ExceptionEngine
import com.dfl.smartscene.bean.community.datasource.CommunityDataSource
import com.dfl.smartscene.bean.community.datasource.CommunityRepository
import com.dfl.smartscene.bean.community.reponse.CommunityListResponse
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityTabType
import com.dfl.smartscene.bean.community.request.SmartRefreshState
import com.dfl.smartscene.bean.mechanism.BaseDataSource.DataSourceType
import com.dfl.smartscene.bean.mechanism.BaseRepository
import com.dfl.smartscene.bean.mechanism.ILoadDataCallBack
import com.dfl.smartscene.room.DbManager
import com.dfl.smartscene.room.helper.DBHelper
import com.dfl.smartscene.room.helper.OnDaoFindsListener
import com.dfl.smartscene.room.helper.OnDaoUpdateOrDeleteListener
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2023/11/18.
 *Describer: 社区场景 viewModel
 */
class CommunityViewModel : CommunityBaseViewModel() {
    private val TAG = GlobalConstant.GLOBAL_TAG.plus("CommunityViewModel")

    ///==========存储库===========
    private var mRvRepository: CommunityRepository<CommunityListResponse>? = null

    ///==========liveData==========
    //列表数据
    private val mRvDataListResponse = MutableLiveData<CommunityListResponse>()

    //列表数据请求状态
    private val mRvDataApiReqResState = MutableLiveData<ApiReqResState>()


    /** 主页+fragment 当前的tab类型  需要在loadData之前设置*/
    var mCurTabType: CommunityTabType? = null

    /**被选中的tab类型*/
    var mSelectTabType: CommunityTabType = CommunityTabType.Official

    /**当前请求第几页*/
    private var mPageNum: Int = 1

    /** 刷新控件获取数据的方式  */
    var mSmartRefreshState = SmartRefreshState.None

    /**列表回调*/
    private var mRvLoadDataCallBack: RvLoadDataCallBack? = null

    /**是否需要第一个卡片闪烁*/
    private var isShowLottieAnimation = false

    /**数据为空时，数据来源是什么，用在当来自远程时，ui停止闪烁*/
    var mNullDataType: DataSourceType = DataSourceType.Cached


    fun getRvDataList(): MutableLiveData<CommunityListResponse> {
        return mRvDataListResponse
    }

    fun getApiReqResState(): MutableLiveData<ApiReqResState> {
        return mRvDataApiReqResState
    }


    /**第一次加载*/
    fun firstLoadRvData() {
        mPageNum = 1
        mSmartRefreshState = SmartRefreshState.None
        loadRvData(BaseRepository.LoadType.Refresh)
    }

    /**刷新加载*/
    fun refreshLoadRvData() {
        mPageNum = 1
        mSmartRefreshState = SmartRefreshState.Refresh
        loadRvData(BaseRepository.LoadType.RefreshOnlyRemote)
    }

    /**刷新加载,需要闪烁*/
    fun refreshLoadRvDataAndShowLottieAnimation() {
        isShowLottieAnimation = true
        refreshLoadRvData()
    }

    /**加载更多*/
    fun loadMoreRvData() {
        mPageNum++
        mSmartRefreshState = SmartRefreshState.LoadMore
        loadRvData(BaseRepository.LoadType.LoadMore)
    }

    private fun loadRvData(
        loadType: BaseRepository.LoadType
    ) {
        TaskExecutor.io {
            //1 设置数据源
            mRvRepository = CommunityRepository.getInstance()
            //2 设置加载方式
            mRvRepository?.setLoadType(mCurTabType, loadType)
            //3 设置请求参数
            mRvRepository?.setDataSource(
                mCurTabType, CommunityDataSource.init(mCurTabType, mPageNum)
            )

            if (mRvLoadDataCallBack == null) mRvLoadDataCallBack = RvLoadDataCallBack(this)
            mRvRepository?.loadData(mCurTabType, mRvLoadDataCallBack)
        }
    }

    //inner class非静态内部类，包含一个指向外部类的对象的引用,可以访问外部类成员属性和成员函数.
    //其余内部类class为静态内部类
    class RvLoadDataCallBack(
        communityViewModel: CommunityViewModel
    ) : ILoadDataCallBack<CommunityListResponse> {

        /** 防止本地有数据 但没网络报错无数据时，进入错误页面 ，false为没数据显示*/
        private var mTempCurIsGetFallData: Boolean = false
        private var reference: WeakReference<CommunityViewModel> = WeakReference<CommunityViewModel>(communityViewModel)

        override fun onLoadRemoteStart() {
            reference.get()?.let {
                if (!mTempCurIsGetFallData) {
                    CommonLogUtils.logD(it.TAG, "显示加载视图")
                    //setValue只可以在主线程中调用。postValue可以在主线程或者子线程中调用
                    //https://www.jianshu.com/p/f3be82e2f70d
                    it.mRvDataApiReqResState.postValue(ApiReqResState(ApiReqResState.ReqResState.GetValueStart))
                }
            }
        }

        override fun onDataLoaded(
            values: CommunityListResponse?, type: DataSourceType
        ) {
            reference.get()?.let {
                mTempCurIsGetFallData = true
                //是否需要闪烁
                if (it.isShowLottieAnimation) {
                    if (values?.rows?.size!! >= 1) {
                        values.rows.get(0).isShowLottieAnimation = true
                    }
                }
                it.mRvDataListResponse.postValue(values)
                it.isShowLottieAnimation = false

                CommonLogUtils.logD(
                    it.TAG, "${it.mCurTabType?.getName()} 有数据${values?.rows?.size}个 来自  ${type.valueStr}"
                )
                if (values?.hasNextPage() == false) {
                    CommonLogUtils.logD(it.TAG, "tab页:${it.mCurTabType?.getName()} 没有更多了")
                }
                if (values == null) return
                //轮播图没数据或异常，来自远程，整页显示异常页
                if (it.mCurTabType == CommunityTabType.Banner && values.rows?.size == 0 && (type == DataSourceType.Remote || type == DataSourceType.Local)) {
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.Error, ApiException(null, ExceptionEngine.DATA_SIZE_0)
                        )
                    )
                } else {
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.GetValueFinish, ApiException(null, ExceptionEngine.UNKNOWN)
                        )
                    )
                }
                //    轮播图没数据或异常，来自远程，整页不显示异常页 （UX是需要显示异常页）
                //                it.mRvDataApiReqResState.postValue(
                //                    ApiReqResState(
                //                        ApiReqResState.ReqResState.GetValueFinish
                //                    )
                //                )
            }
        }

        override fun onDataIsNullOrError(type: DataSourceType) {
            reference.get()?.let {
                it.mNullDataType = type
                it.mRvDataListResponse.postValue(null)
                it.isShowLottieAnimation = false

                CommonLogUtils.logD(it.TAG, "${it.mCurTabType?.getName()} 无数据 来自 ${type.valueStr}")


                // 轮播图没数据或异常，来自远程，整页显示异常页
                if (it.mCurTabType == CommunityTabType.Banner && type == DataSourceType.Remote) {
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(
                            ApiReqResState.ReqResState.Error
                        )
                    )
                }

                // 轮播图没数据或异常，来自远程，整页不显示异常页 可以注释上面代码（UX是需要显示异常页）
            }
        }

        override fun onApiError(ex: ApiException) {
            reference.get()?.let {
                CommonLogUtils.logD(
                    it.TAG,
                    "错误：" + ex.getHttpCode() + " " + ex.message + " " + !mTempCurIsGetFallData + " ex.toastMsg:" + ex.getToastMsg()
                )
                it.isShowLottieAnimation = false

                if (!mTempCurIsGetFallData) { //本地没数据，网络出错，显示错误界面
                    it.mRvDataApiReqResState.postValue(
                        ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                    )
                } else { //本地有数据 ，但网络出错，此时不需显示错误界面
                    ex.getToastMsg()?.let {
                        CommonToastUtils.show(ex.getToastMsg())
                    }
                    if (it.mCurTabType == CommunityTabType.Banner) {
                        it.mRvDataApiReqResState.postValue(
                            ApiReqResState(ApiReqResState.ReqResState.Error, ex)
                        )
                    } else {
                        it.mRvDataApiReqResState.postValue(
                            ApiReqResState(ApiReqResState.ReqResState.Error)
                        )
                    }

                }
            }

        }
    }


    //因为关注改变 需要更改数据库
    fun updateLocalByFocusLikeDown(updateItem: CommunitySceneInfo, type: String) {
        val dao = DbManager.getDBMaster()?.CommunityListResponseDao()

        DBHelper.daoFinds(object : OnDaoFindsListener<List<CommunityListResponse>?> {
            override fun call(): List<CommunityListResponse>? {
                return dao?.findAll()
            }

            override fun onSuccess(values: List<CommunityListResponse>?) {
                values?.forEachIndexed { index, communityListResponse ->
                    communityListResponse.rows.forEachIndexed { index2, communitySceneInfo ->

                        when (type) {
                            "Focus" -> {
                                if (communitySceneInfo.uuid == updateItem.uuid) {
                                    communitySceneInfo.userSubscribe = updateItem.userSubscribe
                                }
                            }

                            "Like" -> {
                                if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                                    communitySceneInfo.isLike = updateItem.isLike
                                    communitySceneInfo.likes = updateItem.likes

                                }
                            }

                            "Down" -> {
                                if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                                    communitySceneInfo.downloads = updateItem.downloads
                                }
                            }
                        }
                    }
                }

                DBHelper.doUpdateOrDelete(object : OnDaoUpdateOrDeleteListener {
                    override fun call(): Int? {
                        return dao?.update(values)
                    }

                    override fun onSuccess(integer: Int) {
                        CommonLogUtils.logD(
                            "Community_CommunityFragment", "${type} 的数据库更新成功：" + integer
                        )
                    }

                    override fun onError(e: Throwable?) {
                    }
                })
            }

            override fun onError(e: Throwable?) {

            }
        })
    }

    override fun cancelDisposable() {
        super.cancelDisposable()
        mRvRepository?.getDataSource(mCurTabType)?.let {
            it as CommunityDataSource
            it.cancelDisposable()
        }
    }
}