package com.dfl.smartscene.community.home;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.ImageViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dfl.android.animationlib.LoadingImageView;
import com.dfl.smartscene.R;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.ui.main.MainActivity;
import com.dfl.smartscene.util.APPUtils;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.CornerImageView;
import com.dfl.smartscene.widget.bannerview.BannerViewPager;
import com.dfl.smartscene.widget.bannerview.BaseBannerAdapter;
import com.dfl.smartscene.widget.bannerview.BaseViewHolder;
import com.dfl.smartscene.widget.bannerview.utils.BannerUtils;

import java.util.HashMap;
import java.util.Map;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * Created by 钟文祥 on 2023/11/2.
 * Describer: 社区场景 轮播图 adapter
 */
public class CommunityBannerAdapter extends BaseBannerAdapter<CommunitySceneInfo> {

    public Map<Integer, BaseViewHolder<CommunitySceneInfo>> holderMap = new HashMap<>();
    private Context mContext;
    private RequestOptions mBgOptions;
    private CommunityAdapterItemListener mListener;
    private boolean isLight;

    @SuppressLint("CheckResult")
    public CommunityBannerAdapter(Context context, CommunityAdapterItemListener mListener) {
        this.mContext = context;
        this.mListener = mListener;
        this.mBgOptions = UIUtils.getGlideOptions(
                R.drawable.scene_img_community_banner_loading, //轮播图的加载中与失败图一样
                R.drawable.scene_img_community_banner_loading_fail,
                AutoSizeUtils.dp2px(context, 2560.0f), AutoSizeUtils.dp2px(context, 850.0f));
        this.isLight = UIUtils.isLightMode();
    }

    @Override
    public int getLayoutId(int viewType) {
        return R.layout.scene_recycel_item_community_banner;
    }

    @SuppressLint({"SetTextI18n", "UseCompatLoadingForDrawables"})
    @Override
    protected void bindData(BaseViewHolder<CommunitySceneInfo> holder, CommunitySceneInfo data,
                            int position2, int pageSize) {
        int realPosition = BannerUtils.getRealPosition(holder.getBindingAdapterPosition(), mList.size());
        holderMap.put(realPosition, holder);

        CornerImageView ivBG = holder.findViewById(R.id.iv_bg);
        LoadingImageView loadingImageView = holder.findViewById(R.id.me_loading_view);

        ivBG.setLayerType(View.LAYER_TYPE_HARDWARE, null);

        String imgUrl = isLight ? data.getBannerImg() : data.getDarkBannerImg();
        Glide.with(mContext).load(imgUrl).apply(mBgOptions).
                into(new ImageViewTarget<Drawable>(ivBG) {
                    @Override //开始加载 1次
                    public void onLoadStarted(@Nullable Drawable placeholder) {
                        super.onLoadStarted(placeholder);
                        loadingImageView.setVisibility(View.VISIBLE);
                        //BindingUtils.setGifPlayMode(loadingImageView, true);
                    }

                    @Override//加载失败
                    public void onLoadFailed(@Nullable Drawable errorDrawable) {
                        super.onLoadFailed(errorDrawable);
                        loadingImageView.setVisibility(View.GONE);
                        //                        BindingUtils.setGifPlayMode(loadingImageView, false);
                    }

                    @Override //加载成功 多次
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable>
                            transition) {
                        super.onResourceReady(resource, transition);
                        loadingImageView.setVisibility(View.GONE);
                        //                        BindingUtils.setGifPlayMode(loadingImageView, false);
                        ivBG.setImageDrawable(resource);
                    }

                    @Override  //多次
                    protected void setResource(@Nullable Drawable resource) {
                    }
                });

        //        ScaleImageButton tvDownloads = holder.findViewById(R.id.tv_downloads);
        //        ScaleImageButton tvLikes = holder.findViewById(R.id.tv_likes);
        //        tvDownloads.adapterSkinColor();
        //        tvLikes.adapterSkinColor();
        //        TextView tvSceneDesc = holder.findViewById(R.id.tv_scene_desc);
        //        TextView tvSceneName = holder.findViewById(R.id.tv_scene_name);

        //        TextView tvUploadTime = holder.findViewById(R.id.tv_upload_times);
        //        CornerImageView ivUserHeard = holder.findViewById(R.id.iv_user_heard);
        //        ivUserHeard.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        //        ivUserHeard.setRoundCorner(50);
        //        TextView tvUserName = holder.findViewById(R.id.tv_user_name);
        //        Button btnSubscribe = holder.findViewById(R.id.btn_subscribe);

        //        tvSceneDesc.setText(data.getScenarioInfoDa().getScenarioDesc());
        //        tvSceneName.setText(data.getScenarioInfoDa().getScenarioName());
        //        tvDownloads.setText(NumberToCNUtil.formatOverString(data.getDownloads()));
        //        tvLikes.setText(NumberToCNUtil.formatOverString(data.getLikes()));
        //        tvLikes.setTextColor(data.getLikeTextColor4Banner());

        //        //点赞的逻辑
        //        if (data.isLike()) {
        //            // 进入到此行，说明数字颜色已改变了
        //            if (!tvLikes.getImgView().isAnimating()) { //已成功点赞后，发现动画还没播，改变图片
        //                tvLikes.getImgView().setProgress(1.0f);
        //            } else {
        //                //已成功点赞后，发现动画还在播，不改变图片
        //            }
        //            tvLikes.getImgView().removeAllUpdateListeners();
        //        } else {
        //            tvLikes.getImgView().setProgress(0.0f);
        //            tvLikes.getImgView().addAnimatorListener(new Animator.AnimatorListener() {
        //                @Override
        //                public void onAnimationStart(@NonNull Animator animation) {
        //
        //                }
        //
        //                @Override
        //                public void onAnimationEnd(@NonNull Animator animation) {
        //                    tvLikes.getImgView().setProgress(1.0f);
        //                    tvLikes.setTextColor(Color.parseColor("#FFDF7727"));
        //                }
        //
        //                @Override
        //                public void onAnimationCancel(@NonNull Animator animation) {
        //
        //                }
        //
        //                @Override
        //                public void onAnimationRepeat(@NonNull Animator animation) {
        //
        //                }
        //            });
        //        }
        //
        //        //下载事件
        //        tvDownloads.setOnClickListener(v -> {
        //            //增加防快速点击的判断
        //            if (!DebouncingUtils.isValid(v)) {
        //                return;
        //            }
        //            if (mListener != null) {
        //                mListener.onClick(realPosition, CommunityReqType.Down);
        //            }
        //        });
        //        //点赞事件
        //        tvLikes.setOnClickListener(v -> {
        //            //增加防快速点击的判断
        //            if (!DebouncingUtils.isValid(v)) {
        //                return;
        //            }
        //            if (data.isLike() == false) {
        //                tvLikes.getImgView().setRepeatCount(0); //多重复0次循环
        //                tvLikes.getImgView().playAnimation();
        //            }
        //            if (mListener != null) {
        //                mListener.onClick(realPosition, CommunityReqType.Likes);
        //            }
        //        });

        //        tvUploadTime.setText(TimeUtils.mills2HideCurYearString(data.getUploadTimestamp()));

        //        Glide.with(mContext).load(data.getUserAvatar()).apply(mHeardOptions).into(ivUserHeard);
        //        tvUserName.setText(data.getUserName());

        //        switch (data.getUserSubscribe()) {
        //            case 0://未关注
        //                btnSubscribe.setBackgroundResource(R.drawable.scene_shape_community_banner_subscribe);
        //                btnSubscribe.setText(mContext.getString(R.string.scene_text_user_center_subscribes_desc));
        //                btnSubscribe.setTextColor(mContext.getColor(R.color.scene_primary_color));
        //                btnSubscribe.setVisibility(View.VISIBLE);
        //                break;
        //            case 1://已关注
        //                btnSubscribe.setBackgroundResource(R.drawable.scene_shape_community_banner_subscribed);
        //                btnSubscribe.setText(mContext.getString(R.string.scene_text_user_center_subscribed));
        //                btnSubscribe.setTextColor(mContext.getColor(R.color.scene_community_text_banner_color));
        //                btnSubscribe.setVisibility(View.VISIBLE);
        //                break;
        //            case 2://用户自己
        //                btnSubscribe.setVisibility(View.GONE);
        //                break;
        //        }
        //        //关注按钮
        //        btnSubscribe.setOnClickListener(v -> {
        //            if (!DebouncingUtils.isValid(v)) {
        //                return;
        //            }
        //            NetWorkUtils.NetWorkType netWorkType = NetWorkUtils.checkNetWork();
        //            if (netWorkType == NetWorkUtils.NetWorkType.HaveNetWork) {
        //                if (mListener != null) {
        //                    mListener.onClick(realPosition, CommunityReqType.Subscribe);
        //                }
        //            } else {
        //                UIUtils.showToastNetError(netWorkType);
        //            }
        //
        //        });
        //        //用户区域点击进入个人页
        //        ShapeBlurView viewBottom = holder.findViewById(R.id.view_bottom);
        //        //用来显示是否跳转登录弹框
        //        FragmentManager fragmentManager = ((AppCompatActivity) mContext).getSupportFragmentManager();
        //        viewBottom.setOnClickListener(v -> UserCenterManager.INSTANCE.checkDebouncingAndNetworkAndLogin(v,
        //                fragmentManager, () -> {
        //                    UserCenterActivity.Companion.actionStart(mContext, data.isOfficial(), data.getUuid());
        //                    return null;
        //                }));
    }


    @Override
    public void onViewRecycled(@NonNull BaseViewHolder<CommunitySceneInfo> holder) {

        if (mContext != null) {
            ImageView ivBG = holder.findViewById(R.id.iv_bg);
            if (ivBG != null) {
                Glide.with(mContext).clear(ivBG);
            }
            ImageView ivLeftMix = holder.findViewById(R.id.iv_left_mix);
            if (ivLeftMix != null) {
                Bitmap bitmap = BannerUtils.getBitmapFromImageView(ivLeftMix);
                if (bitmap != null) {
                    bitmap.recycle();
                    bitmap = null;
                }
                ivLeftMix.setImageBitmap(null);
            }
            ImageView ivRightMix = holder.findViewById(R.id.iv_right_mix);
            if (ivRightMix != null) {
                Bitmap bitmap = BannerUtils.getBitmapFromImageView(ivRightMix);
                if (bitmap != null) {
                    bitmap.recycle();
                    bitmap = null;
                }
                ivRightMix.setImageBitmap(null);
            }

            //            CornerImageView ivUserHeard = holder.findViewById(R.id.iv_user_heard);
            //            if (ivUserHeard != null) {
            //                Glide.with(mContext).clear(ivUserHeard);
            //            }
        }
        super.onViewRecycled(holder);
    }


    //关注改变，需要更新列表
    public void notifyByFocus(CommunitySceneInfo updateItem, BannerViewPager<CommunitySceneInfo> mBannerView) {
        for (int i = 0, size = mList.size(); i < size; i++) {
            if (mList.get(i).getUuid().equals(updateItem.getUuid())) {
                mList.get(i).setUserSubscribe(updateItem.getUserSubscribe());
                if (holderMap.get(i) != null) {
                    notifyItemChanged(holderMap.get(i).getBindingAdapterPosition());
                    boolean isStartLoop =
                            APPUtils.getCurrentActivityName(mContext).equals(MainActivity.class.getName());
                    mBannerView.setCurrentItem(mBannerView.getCurrentItem() + mList.size(), false, isStartLoop);
                }
            }
        }
    }

    //点赞改变，需要更新列表
    public void notifyByLike(CommunitySceneInfo updateItem, BannerViewPager<CommunitySceneInfo> mBannerView) {
        for (int i = 0, size = mList.size(); i < size; i++) {
            if (mList.get(i).getCommunityScenarioId().equals(updateItem.getCommunityScenarioId())) {
                mList.get(i).setLikes(updateItem.getLikes());
                mList.get(i).setLike(updateItem.isLike());
                if (holderMap.get(i) != null) {
                    notifyItemChanged(holderMap.get(i).getBindingAdapterPosition());
                    boolean isStartLoop =
                            APPUtils.getCurrentActivityName(mContext).equals(MainActivity.class.getName());
                    mBannerView.setCurrentItem(mBannerView.getCurrentItem() + mList.size(), false, isStartLoop);
                }
                return;
            }
        }
    }

    //下载改变，需要更新列表
    public void notifyByDown(CommunitySceneInfo updateItem, BannerViewPager<CommunitySceneInfo> mBannerView) {
        for (int i = 0, size = mList.size(); i < size; i++) {
            if (mList.get(i).getCommunityScenarioId().equals(updateItem.getCommunityScenarioId())) {
                mList.get(i).setDownloads(updateItem.getDownloads());
                if (holderMap.get(i) != null) {
                    notifyItemChanged(holderMap.get(i).getBindingAdapterPosition());
                    boolean isStartLoop =
                            APPUtils.getCurrentActivityName(mContext).equals(MainActivity.class.getName());
                    mBannerView.setCurrentItem(mBannerView.getCurrentItem() + mList.size(), false, isStartLoop);
                }
                return;
            }
        }
    }

    /**
     * 当白黑模式改变 刷新banner
     */
    public void notifyByLightMode(boolean isLight, BannerViewPager<CommunitySceneInfo> mBannerView) {
        this.isLight = isLight;
        holderMap.values().forEach(communitySceneInfoBaseViewHolder -> Glide.with(mContext).clear((CornerImageView) communitySceneInfoBaseViewHolder.findViewById(R.id
                .iv_bg)));
        notifyDataSetChanged();
        mBannerView.setCurrentItem(mBannerView.getCurrentItem() + mList.size(), false);
    }
}
