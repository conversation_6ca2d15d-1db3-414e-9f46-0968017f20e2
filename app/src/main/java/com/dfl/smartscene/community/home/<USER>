package com.dfl.smartscene.community.home

import android.animation.Animator
import android.annotation.SuppressLint
import android.text.Html
import android.view.View
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.CommonUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo
import com.dfl.smartscene.bean.community.request.CommunityReqType
import com.dfl.smartscene.community.usercenter.UserCenterActivity
import com.dfl.smartscene.databinding.SceneRecycelItemCommunityBinding
import com.dfl.smartscene.util.NetWorkUtils
import com.dfl.smartscene.util.UIUtils

/**
 *Created by 钟文祥 on 2023/11/2.
 *Describer: 社区场景 列表adapter
 */
@SuppressLint("CheckResult")
open class CommunityAdapter(
    private var mListener: CommunityAdapterItemListener?,
) : BaseQuickAdapter<CommunitySceneInfo, BaseDataBindingHolder<SceneRecycelItemCommunityBinding>>(
    R.layout.scene_recycel_item_community
) {

    private var mHeardOptions: RequestOptions =
        UIUtils.getGlideOptions(R.drawable.scene_icon_community_user_head_default)
    private var mItemOptions: RequestOptions = UIUtils.getGlideOptions(
        R.drawable.scene_img_community_card_load, //加载时图片
        R.drawable.scene_img_community_card_error, //失败时图片
        -1, -1
    )

    override fun convert(
        holder: BaseDataBindingHolder<SceneRecycelItemCommunityBinding>, item: CommunitySceneInfo
    ) {
        holder.dataBinding?.item = item
        //不在xml设置可见,会导致头像无法隐藏
//        holder.dataBinding?.ivUserHeard?.isVisible = !item.isLoadingStatus()
        //设置从后端返回的搜索结果渲染
        val htmlUserName = Html.fromHtml(item.userName ?: "", Html.FROM_HTML_MODE_COMPACT)
        val htmlSceneName = Html.fromHtml(item.scenarioInfoDa?.scenarioName ?: "", Html.FROM_HTML_MODE_COMPACT)
        val htmlSceneDesc = Html.fromHtml(item.scenarioInfoDa?.scenarioDesc ?: "", Html.FROM_HTML_MODE_COMPACT)
        holder.dataBinding?.tvUserName?.text = htmlUserName
        holder.dataBinding?.tvSceneName?.text = htmlSceneName
        holder.dataBinding?.tvSceneDesc?.text = htmlSceneDesc

        holder.dataBinding?.ivUserHeard?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        Glide.with(context).load(item.userAvatar).apply(mHeardOptions).into(holder.dataBinding?.ivUserHeard!!)

        holder.dataBinding?.ivBg?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        if (item.bannerImg != "LoadImg" && !item.isShowLottieAnimation) {
            Glide.with(context).load(item.scenarioInfoDa?.imgPath).apply(
                UIUtils.getGlideOptionsByImageView(
                    holder.dataBinding?.ivBg!!, //加载时图片
                    R.drawable.scene_img_community_card_error, //失败时图片
                    -1, -1
                )
            ).into(holder.dataBinding?.ivBg!!)
        }

        //用户头像,用户名点击事件,只判断双击,网络
        holder.dataBinding?.ivUserHeard?.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) return@setOnClickListener
            if (!NetWorkUtils.isConnectNetWork()) {
                CommonToastUtils.showLong(CommonUtils.getString(R.string.scene_toast_web_exception_device_no_network))
                return@setOnClickListener
            }
            UserCenterActivity.actionStart(context, item.isOfficial, item.uuid)
        }
        holder.dataBinding?.tvUserName?.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) return@setOnClickListener
            if (!NetWorkUtils.isConnectNetWork()) {
                CommonToastUtils.showLong(CommonUtils.getString(R.string.scene_toast_web_exception_device_no_network))
                return@setOnClickListener
            }
            UserCenterActivity.actionStart(context, item.isOfficial, item.uuid)
        }

        //点赞的逻辑
        if (item.isLike == true) {
            // 进入到此行，说明数字颜色已改变了
            if (holder.dataBinding?.tvLikes?.imgView?.isAnimating == false) { //已成功点赞后，发现动画还没播，改变图片
                holder.dataBinding?.tvLikes?.imgView?.progress = 1.0f
            } else {
                //已成功点赞后，发现动画还在播，不改变图片
            }
            holder.dataBinding?.tvLikes?.imgView?.removeAllUpdateListeners()
        } else {
            holder.dataBinding?.tvLikes?.imgView?.progress = 0.0f
            holder.dataBinding?.tvLikes?.imgView?.addAnimatorListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) { //动画播放完后改变文字颜色，和定住最后一帧
                    holder.dataBinding?.tvLikes?.imgView?.progress = 1.0f
                    holder.dataBinding?.tvLikes?.setTextColor(context.getColor(R.color.scene_primary_color_highlight))
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
        }
        // setUiModeChangeListener必须在adapterSkinColor前
        holder.dataBinding?.tvLikes?.setUiModeChangeListener { theme ->
            if (theme == 0) {
                holder.dataBinding?.tvLikes?.imgView?.setAnimation(R.raw.scene_likes_day)
            } else if (theme == 1) {
                holder.dataBinding?.tvLikes?.imgView?.setAnimation(R.raw.scene_likes_night)
            }
            holder.dataBinding?.tvLikes?.setTextColor(context.getColor(if (item.isLike == true) R.color.scene_primary_color_highlight else R.color.scene_color_text_1))

        }

        //点赞事件
        holder.dataBinding?.tvLikes?.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) return@setOnClickListener
            if (item.isLike == false) {
                holder.dataBinding?.tvLikes?.imgView?.repeatCount = 0 //多重复0次循环
                holder.dataBinding?.tvLikes?.imgView?.playAnimation()
            }
            mListener?.onClick(holder.layoutPosition, CommunityReqType.Likes)
        }
        //下载事件
        holder.dataBinding?.tvDownloads?.setOnClickListener {
            if (!DebouncingUtils.isValid(it)) return@setOnClickListener
            mListener?.onClick(holder.layoutPosition, CommunityReqType.Down)
        }


        //        //取消关注相关,判断为真则是低分屏逻辑
        //        if (holder.dataBinding?.viewSubscribe?.visibility != View.GONE) {
        //            when (item.userSubscribe) {
        //                //未关注
        //                0 -> {
        //                    holder.dataBinding?.tvSubscribe?.setBackgroundResource(R.drawable.scene_shape_community_subscribe)
        //                    holder.dataBinding?.tvSubscribe?.text =
        //                        context.getString(R.string.scene_text_user_center_subscribes_desc)
        //                    holder.dataBinding?.tvSubscribe?.setTextColor(context.getColor(R.color.scene_primary_color))
        //                    holder.dataBinding?.tvSubscribe?.visibility = View.VISIBLE
        //                }
        //                //已关注
        //                1 -> {
        //                    holder.dataBinding?.tvSubscribe?.setBackgroundResource(R.drawable.scene_shape_community_subscribed)
        //                    holder.dataBinding?.tvSubscribe?.text =
        //                        context.getString(R.string.scene_text_user_center_subscribed)
        //                    holder.dataBinding?.tvSubscribe?.setTextColor(context.getColor(R.color.scene_text_color_card_content))
        //                    holder.dataBinding?.tvSubscribe?.visibility = View.VISIBLE
        //                }
        //                //用户自己
        //                2 -> {
        //                    holder.dataBinding?.tvSubscribe?.visibility = View.GONE
        //                }
        //            }
        //        }
        //
        //        //关注按钮
        //        holder.dataBinding?.viewSubscribe?.setOnClickListener(View.OnClickListener {
        //            if (!DebouncingUtils.isValid(it)) return@OnClickListener
        //            val netWorkType = NetWorkUtils.checkNetWork()
        //            if (netWorkType == NetWorkUtils.NetWorkType.HaveNetWork) {
        //                //未关注
        //                if (item.userSubscribe == 0) {
        //                    //在onClick内已判断用户登录并打开弹窗
        //                    mListener?.onClick(holder.layoutPosition, CommunityReqType.Subscribe)
        //                }
        //                //已关注 打开不再关注
        //                else if (item.userSubscribe == 1) {
        //                    holder.dataBinding?.viewPop?.visibility = View.VISIBLE
        //                }
        //            } else {
        //                UIUtils.showToastNetError(netWorkType)
        //            }
        //        })
        //
        //        //不再关注按钮
        //        holder.dataBinding?.tvNoLongerSub?.setOnClickListener {
        //            val fragmentManager = (context as AppCompatActivity).supportFragmentManager
        //            UserCenterManager.checkDebouncingAndNetworkAndLogin(it, fragmentManager) {
        //                holder.dataBinding?.viewPop?.visibility = View.GONE
        //                mListener?.onClick(holder.layoutPosition, CommunityReqType.Subscribe)
        //            }
        //        }
        //        //空白
        //        holder.dataBinding?.viewPop?.setOnClickListener(View.OnClickListener {
        //            if (!DebouncingUtils.isValid(it)) return@OnClickListener
        //            holder.dataBinding?.viewPop?.visibility = View.GONE
        //        })
        //
        //        //取消按钮
        //        holder.dataBinding?.tvNoLongerSubCancel?.setOnClickListener(View.OnClickListener {
        //            if (!DebouncingUtils.isValid(it)) return@OnClickListener
        //            holder.dataBinding?.viewPop?.visibility = View.GONE
        //        })

    }


    override fun onViewRecycled(holder: BaseDataBindingHolder<SceneRecycelItemCommunityBinding>) {
        if (context != null) {
            holder.dataBinding?.ivUserHeard?.let {
                Glide.with(context).clear(it)
            }
            holder.dataBinding?.ivBg?.let {
                Glide.with(context).clear(it)
            }
        }
        super.onViewRecycled(holder)
    }


    //关注改变，需要更新列表
    open fun notifyByFocus(updateItem: CommunitySceneInfo) {
        data.forEachIndexed { index, communitySceneInfo ->
            if (communitySceneInfo.uuid == updateItem.uuid) {
                communitySceneInfo.userSubscribe = updateItem.userSubscribe
                notifyItemChanged(index)
                //不能用setdata，会让多个卡片的值内容变成一样
            }
        }
    }

    //点赞改变，需要更新列表
    open fun notifyByLike(updateItem: CommunitySceneInfo) {
        data.forEachIndexed { index, communitySceneInfo ->
            if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                communitySceneInfo.likes = updateItem.likes
                communitySceneInfo.isLike = updateItem.isLike
                communitySceneInfo.bannerImg = ""
                notifyItemChanged(index)
                return
            }
        }
    }


    //下载改变，需要更新列表
    open fun notifyByDown(updateItem: CommunitySceneInfo) {
        data.forEachIndexed { index, communitySceneInfo ->
            if (communitySceneInfo.communityScenarioId == updateItem.communityScenarioId) {
                communitySceneInfo.downloads = updateItem.downloads
                notifyItemChanged(index)
                return
            }
        }
    }

}