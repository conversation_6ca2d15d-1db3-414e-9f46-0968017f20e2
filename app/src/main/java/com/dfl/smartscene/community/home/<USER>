package com.dfl.smartscene.community.home

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.dfl.smartscene.bean.community.request.CommunityTabType

/**
 *Created by 钟文祥 on 2023/11/17.
 *Describer: 社区场景 viewpager 的适配器
 */
class CommunityViewPagerAdapter(
	fragmentActivity: FragmentActivity, var mTabTypeList: List<CommunityTabType>
) : FragmentStateAdapter(fragmentActivity) {


	override fun createFragment(position: Int): Fragment {
		val tabType: CommunityTabType = mTabTypeList[position]
		return CommunityTabFragment.newInstance(tabType)
	}

	override fun getItemCount(): Int {
		return mTabTypeList.size
	}

	//根据数据生成唯一id**
	// 如果不重写，那么在调用[notifyDataSetChanged]更新的时候**
	// 会抛出```new IllegalStateException("Fragment already added")```异常
	override fun getItemId(position: Int): Long {
		return mTabTypeList[position].value.toLong()
	}

	override fun containsItem(itemId: Long): Boolean {
		for (tabType in mTabTypeList) {
			if (tabType.value.toLong() == itemId) {
				return true
			}
		}
		return false
	}
}