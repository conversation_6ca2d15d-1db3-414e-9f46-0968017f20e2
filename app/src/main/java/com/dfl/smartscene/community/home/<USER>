package com.dfl.smartscene.community.home;

import android.animation.Animator;
import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import com.bumptech.glide.Glide;
import com.dfl.android.animationlib.CustomTabLayout;
import com.dfl.android.common.base.BaseVBFragment;
import com.dfl.android.common.global.GlobalLiveEventConstants;
import com.dfl.android.common.util.CommonToastUtils;
import com.dfl.android.common.util.DebouncingUtils;
import com.dfl.android.common.util.NumberToCNUtil;
import com.dfl.android.commonlib.log.CommonLogUtils;
import com.dfl.smartscene.R;
import com.dfl.smartscene.apiweb.utils.ApiReqResState;
import com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo;
import com.dfl.smartscene.bean.community.request.CommunityFocusRequest;
import com.dfl.smartscene.bean.community.request.CommunityReqType;
import com.dfl.smartscene.bean.community.request.CommunityTabType;
import com.dfl.smartscene.bean.community.request.SmartRefreshState;
import com.dfl.smartscene.communication.UserCenterManager;
import com.dfl.smartscene.databinding.SceneFragmentCommunityBinding;
import com.dfl.smartscene.ui.main.discover.detail.SceneDetailActivity;
import com.dfl.smartscene.util.MMKVUtils;
import com.dfl.smartscene.util.NetWorkUtils;
import com.dfl.smartscene.util.TrackUtils;
import com.dfl.smartscene.util.UIUtils;
import com.dfl.smartscene.widget.bannerview.BannerViewPager;
import com.dfl.smartscene.widget.bannerview.constants.IndicatorGravity;
import com.dfl.smartscene.widget.bannerview.constants.PageStyle;
import com.dfl.smartscene.widget.bannerview.shape.MultiIndicatorView;
import com.dfl.voicehelper.util.VoiceUtils;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager2.widget.ViewPager2;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;


/**
 * Created by 钟文祥 on 2023/11/1.
 * Describer: 社区场景 fragment
 */
@SuppressLint("CheckResult")
public class CommunityFragment extends BaseVBFragment<CommunityViewModel,
        SceneFragmentCommunityBinding> {

    private BannerViewPager<CommunitySceneInfo> mBannerView;
    private TabLayoutMediator mTabLayoutMediator;
    private Disposable showLottieDisposable = null; //闪烁延时处理

    private MyOnOffsetChangedListener mOnOffsetChangedListener = null;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        //初始化吸顶
        initAppBarLayout();
        //创建banner
        initBanner();
        //初始化刷新控件
        initRefreshLayout();
        //创建viewpager2
        initViewPager2();
    }

    //初始化吸顶
    private void initAppBarLayout() {
        mOnOffsetChangedListener = new MyOnOffsetChangedListener(this);
        getMViewBind().viewAppBarLayout.addOnOffsetChangedListener(mOnOffsetChangedListener);
        getMViewBind().viewTopBg.setOnClickListener(null); //吸顶区域的点击事件为空，不让把事件往下传递
    }

    /**
     * 初始化可见即可说的文本
     */
    private void initVoiceHelperText() {
        View newTab = getMViewBind().tabLayout.getTabView(0);
        if (newTab != null) {
            VoiceUtils.setRegularTextData(newTab, getResources().getString(R.string.scene_text_community_official));
        }
        View hotTab = getMViewBind().tabLayout.getTabView(1);
        if (hotTab != null) {
            VoiceUtils.setRawTextData(hotTab, getResources().getString(R.string.scene_text_community_new));
        }
        View officeTab = getMViewBind().tabLayout.getTabView(2);
        if (officeTab != null) {
            VoiceUtils.setRegularTextData(officeTab,
                    getResources().getString(R.string.scene_text_community_hot));
        }
        View profileTab = getMViewBind().tabLayout.getTabView(3);
        if (profileTab != null) {
            VoiceUtils.setRegularTextData(profileTab,
                    getResources().getString(R.string.scene_text_voice_helper_specified));
        }
    }

    @Override
    public void createObserver() {
        //轮播图回调
        initObserveRvDataList();
        //轮播图请求状态回调
        initObserveApiReqResState();
        //列表回调
        initEventBusSmartRefreshState();
        //关注回调
        initObserverFocusApiReqResState();
        //选择场景和发布场景的返回
        initEventBusPostFragmentBack();
        //关注事件回调
        initEventBusFocusToUpdateAllUI();
        //点赞事件回调
        initEventBusLikeToUpdateAllUI();
        //下载事件回调
        initEventBusDownloadToUpdateAllUI();
        //个人中心登录状态回调
        initEventBusLoginOutSuccess();

        // 加载 轮播图数据
        mViewModel.setMCurTabType(CommunityTabType.Banner);
        mViewModel.firstLoadRvData();

        UIUtils.initLoadingLottieSync(mViewBind);
    }

    //全部刷新
    private void allRefresh() {
        defaultSelectTab0();
        mViewModel.refreshLoadRvData();
        for (int i = 0; i < 4; i++) {
            CommunityTabFragment tabFragment = getFragmentByTab(CommunityTabType.values()[i]);
            if (tabFragment != null) {
                tabFragment.mViewModel.refreshLoadRvData();
            }
        }
    }

    //个人中心登录退登成功回调
    private void initEventBusLoginOutSuccess() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_USER_CENTER_USER_IS_LOGIN_OUT_STATUS,
                Boolean.class).observe(getViewLifecycleOwner(), item -> {
            if (item) {
                CommonLogUtils.logD(TAG, "退登成功 界面回调");
            } else {
                CommonLogUtils.logD(TAG,
                        "登录成功 界面回调" + Objects.requireNonNull(UserCenterManager.getUserLoginStatus()).getUuid());
            }
            allRefresh();
        });
    }

    private void initObserveRvDataList() {
        //轮播图回调
        mViewModel.getRvDataList().observe(this, communityListResponse -> {
            if (communityListResponse == null) return;
            mBannerView.create(communityListResponse.getRows());
            if (communityListResponse.getRows().size() > 0) {
                updateBannerTvInfo(communityListResponse.getRows().get(0));
                mBannerView.setVisibility(View.VISIBLE);
            } else {
                mBannerView.setVisibility(View.INVISIBLE);
            }
        });
    }

    //注意：整体的加载和异常页面由轮播图控制
    private void initObserveApiReqResState() {
        //轮播图请求状态回调
        mViewModel.getApiReqResState().observe(this, apiReqResState -> {
            if (apiReqResState.getState() == ApiReqResState.ReqResState.GetValueStart) {
                UIUtils.showLoading(getMViewBind());
            } else if (apiReqResState.getState() == ApiReqResState.ReqResState.GetValueFinish) {
                UIUtils.disShow(getMViewBind());
                if (MMKVUtils.getInstance().getMeUploadShowLottieAnimation()) {
                    MMKVUtils.getInstance().saveMeUploadShowLottieAnimation(false);
                    showLottieDisposable = Observable.just(0).delay(2, TimeUnit.SECONDS).subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread()).subscribe(integer -> showLottieAnimation4Card0());
                }
            } else if (apiReqResState.getState() == ApiReqResState.ReqResState.Error) {
                MMKVUtils.getInstance().saveMeUploadShowLottieAnimation(false);
                getMViewBind().slUpdate.finishRefresh(false);
                getMViewBind().slUpdate.finishLoadMore();
                if (apiReqResState.getEx() != null) {
                    UIUtils.showError(getMViewBind(), apiReqResState.getEx(),
                            v -> onRefresh(true));
                } else {
                    UIUtils.disShow(getMViewBind());
                }
                MMKVUtils.getInstance().saveMeUploadShowLottieAnimation(false);
            }
        });
    }

    //注意：刷新控件的加载 由tab页控制
    private void initEventBusSmartRefreshState() {
        //列表回调
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_GET_VALUE_CALLBACK,
                SmartRefreshState.class).observe(this, smartRefreshState -> {
            if (smartRefreshState == SmartRefreshState.Refresh) {
                getMViewBind().slUpdate.finishRefresh();
            }
        });
    }

    //关注事件成功后更新数据库和所有UI
    private void initEventBusFocusToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Focus");
                    //更新轮播图和所有tab页
                    ((CommunityBannerAdapter) mBannerView.getAdapter()).notifyByFocus(item, mBannerView);
                    for (int i = 0; i < 4; i++) {
                        CommunityTabFragment tabFragment =
                                getFragmentByTab(CommunityTabType.values()[i]);
                        if (tabFragment != null) {
                            tabFragment.mAdapter.notifyByFocus(item);
                        }
                    }
                });
    }

    //下载事件成功后更新数据库和所有UI
    private void initEventBusDownloadToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_DOWNLOAD_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Down");
                    //更新轮播图和所有tab页
                    ((CommunityBannerAdapter) mBannerView.getAdapter()).notifyByDown(item, mBannerView);
                    CommunitySceneInfo data =
                            mBannerView.getData().get(getMViewBind().bannerView.getCurrentItem());
                    if (data.getScenarioInfoDa() != null && data.getScenarioInfoDa().getScenarioId().equals(item.getScenarioInfoDa().getScenarioId())) {
                        updateBannerTvInfo(item);
                    }
                    for (int i = 0; i < 4; i++) {
                        CommunityTabFragment tabFragment =
                                getFragmentByTab(CommunityTabType.values()[i]);
                        if (tabFragment != null) {
                            tabFragment.mAdapter.notifyByDown(item);
                        }
                    }
                });
    }

    //点赞事件成功后更新数据库和所有UI
    private void initEventBusLikeToUpdateAllUI() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_COMMUNITY_LIKE_SUCCESS_UPDATE_UI,
                        CommunitySceneInfo.class)
                .observe(getViewLifecycleOwner(), item -> {
                    //更新数据库
                    mViewModel.updateLocalByFocusLikeDown(item, "Like");
                    //更新轮播图
                    ((CommunityBannerAdapter) mBannerView.getAdapter()).notifyByLike(item, mBannerView);

                    CommunitySceneInfo data =
                            mBannerView.getData().get(getMViewBind().bannerView.getCurrentItem());
                    if (data.getScenarioInfoDa() != null && data.getScenarioInfoDa().getScenarioId().equals(item.getScenarioInfoDa().getScenarioId())) {
                        updateBannerTvInfo(item);
                    }
                    //更新所有tab页
                    for (int i = 0; i < 4; i++) {
                        CommunityTabFragment tabFragment =
                                getFragmentByTab(CommunityTabType.values()[i]);
                        if (tabFragment != null) {
                            tabFragment.mAdapter.notifyByLike(item);
                        }
                    }
                });
    }

    //关注回调
    private void initObserverFocusApiReqResState() {
        mViewModel.getMFocusApiReqResState().observe(this, apiReqResState -> {
            switch (apiReqResState.getState()) {
                case GetValueStart:
                case GetValueFinish:
                    break;
                case Error:
                    CommonToastUtils.show((String) apiReqResState.getMap().get(GlobalLiveEventConstants.KEY_COMMUNITY_FOCUS_SUCCESS_UPDATE_UI));
                    break;

            }
        });
    }

    //选择场景和发布场景的返回
    private void initEventBusPostFragmentBack() {
        LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Integer.class)
                .observe(this, integer -> {
                    if (integer == 2) {//场景发布成功
                        showLottieAnimation4Card0();
                    }
                });
    }

    //闪烁
    private void showLottieAnimation4Card0() {
        CommunityTabFragment lastNewFragment =
                getFragmentByTab(CommunityTabType.LastNew);
        defaultSelectTab(CommunityTabType.LastNew.getValue());
        if (lastNewFragment != null) {
            //刷新并闪烁第一个卡片
            lastNewFragment.getMViewModel().refreshLoadRvDataAndShowLottieAnimation();
        }
    }

    private void onItemRecyclerListener_OnClick(CommunitySceneInfo item, CommunityReqType object) {
        switch (object) {
            case Subscribe: {
                mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.SUBSCRIBED);
                break;
            }
            case UnSubscribe: {
                mViewModel.focusOrNoFocus(item, getChildFragmentManager(), CommunityFocusRequest.UNSUBSCRIBED);
                break;
            }
            case Likes: {
                mViewModel.likeOrNoLike(item, getChildFragmentManager());
                break;
            }
            case Down: {
                mViewModel.downloadOrNo(item, getChildFragmentManager(), requireActivity(), true);
                break;
            }
        }
    }

    private void initBanner() {
        mBannerView = getMViewBind().bannerView;
        int scrollDuration = 1182; //滑动时间
        mBannerView.setCanLoop(true)  //是否开启循环 | 默认值true|
                .setAutoPlay(true)//是否开启自动轮播 | 默认值true|
                .setInterval(scrollDuration + 5000) //自动轮播时间间隔 |单位毫秒，默认值3000 |
                .setScrollDuration(scrollDuration)//设置页面滚动时间 |单位毫秒，默认值800 |
                .setRoundCorner(AutoSizeUtils.dp2px(getActivity(), 0))//设置外部圆角
                // 默认无圆角 需要SDK_INT>=LOLLIPOP(API 21)
                .setRevealWidth(AutoSizeUtils.dp2px(getActivity(), 0)) //设置一屏多页模式下左右两边 Item 显示出来的宽度 设置向左右的宽度
                //同样大 有间距
                .setPageStyle(PageStyle.MULTI_PAGE_FREE, 1f)
                .setPageMargin(AutoSizeUtils.dp2px(getActivity(), 0)) //设置页面间隔
                //中间大两边小 有间距
                //MULTI_PAGE_OVERLAP 叠层， MULTI_PAGE_SCALE 并列排,MULTI_PAGE_OVERLAP_4_SCENE 社区特殊
                //                .setPageStyle(PageStyle.MULTI_PAGE_OVERLAP, (float) (450.0 / 500.0))
                //                /*设置画廊模式*///小图除大图的高度
                //                .setPageMargin(0)
                .setAdapter(new CommunityBannerAdapter(getActivity(), (position, object) -> {
                    //轮播图关注的点击事件
                    CommunitySceneInfo item = mBannerView.getData().get(position);
                    onItemRecyclerListener_OnClick(item, object);
                }))
                //自定义指示器
                .setIndicatorView(new MultiIndicatorView(getActivity()))
                .setIndicatorVisibility(View.VISIBLE)
                //                //设置适配器    必须
                .setIndicatorGravity(IndicatorGravity.CENTER) //指示器位置
                //                .setIndicatorSlideMode(IndicatorSlideMode.NORMAL)
                //                .showIndicatorWhenOneItem(true)//是否显示指示器|默认值true
                //                .setIndicatorVisibility(View.VISIBLE)
                //                .stopLoopWhenDetachedFromWindow(true)//当BVP滑动出屏幕的时候是否要停止轮播
                //                //圆形+圆形
                //                //                .setIndicatorStyle(IndicatorStyle.CIRCLE)  //指示器样式
                //                //                .setIndicatorSlideMode(IndicatorSlideMode.WORM)
                //                //                .setIndicatorSliderGap(14) /*指示器的间距*/
                //                //                .setIndicatorSliderRadius(8, 8)
                //
                //                //扁形+圆形  https://github.com/zhpanvip/BannerViewPager/blob/master/README_CN.md
                //                .setIndicatorStyle(IndicatorStyle.ROUND_RECT)  //指示器样式
                //                .setIndicatorSlideMode(IndicatorSlideMode.SCALE) //SMOOTH光滑 SCALE缩放  WORM蠕动 COLOR颜色
                //                NORMAL普通
                //                .setIndicatorSliderGap(AutoSizeUtils.dp2px(getActivity(), 10))
                //                .setIndicatorSliderRadius(AutoSizeUtils.dp2px(getActivity(), 3))
                //                .setIndicatorMargin(0, 0, 0, AutoSizeUtils.dp2px(getActivity(), 40))
                .setIndicatorHeight(AutoSizeUtils.dp2px(getActivity(), 10))
                .setIndicatorSliderWidth(AutoSizeUtils.dp2px(getActivity(), 8), AutoSizeUtils.dp2px(getActivity(),
                        26))
                .setIndicatorSliderColor(getResources().getColor(R.color.scene_community_banner_indicator_normal,
                        null), getResources().getColor(R.color.scene_community_banner_indicator_select, null))
                //指示器 不选中与选中颜色

                //滑动时触发事件
                .registerOnPageChangeCallback(new BannerOnPageChangeCallback(this))
                //                .addOverlayView(pageOverlayView)
                .setOnPageClickListener(new BannerOnPageClickListener(this));
        //        getMViewBind().bannerView.setUserInputEnabled(false);
    }

    private void initViewPager2() {
        List<CommunityTabType> mFragmentTypeList = new ArrayList<>();
        for (int i = 0, size = CommunityTabType.values().length - 1; i < size; i++) {
            mFragmentTypeList.add(CommunityTabType.values()[i]);
        }

        CommunityViewPagerAdapter mViewPagerAdapter = new CommunityViewPagerAdapter(requireActivity(),
                mFragmentTypeList);
        getMViewBind().vpCommunity.setAdapter(mViewPagerAdapter);
        getMViewBind().vpCommunity.setUserInputEnabled(false); // false 禁止viewpager2左右滑动
        getMViewBind().vpCommunity.setOffscreenPageLimit(1);//屏幕外加载的个数，解决内存泄露
        //        ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT  https://blog.csdn.net/2401_83641360/article/details/138627748

        List<Object> mTitleArr = new ArrayList<>();
        mTitleArr.add(getString(R.string.scene_text_community_official));
        mTitleArr.add(getString(R.string.scene_text_community_new));
        mTitleArr.add(getString(R.string.scene_text_community_hot));
        mTitleArr.add(getString(R.string.scene_text_community_love_car));
        getMViewBind().tabLayout.generateViews(mTitleArr);
        getMViewBind().tabLayout.setOnTabSelectedListener(new CustomTabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(int position, Boolean isManual) {
                CommonLogUtils.logI(TAG, "onTabSelected: " + position);
                tabSelected(position);
            }

            @Override
            public void onTabUnSelected(int lastPosition) {

            }

            @Override
            public void onInitViewComplete(Boolean isComplete) {
                //初始化可见即可说的文本
                initVoiceHelperText();
            }
        });
        getMViewBind().tabLayout.setCurrentItem(0);
        tabSelected(0);
    }

    private void tabSelected(int position) {
        mViewModel.setMSelectTabType(CommunityTabType.values()[position]);
        getMViewBind().vpCommunity.setCurrentItem(position, false);
    }

    private void defaultSelectTab0() {
        getMViewBind().tabLayout.setClickTab(0);
    }

    private void defaultSelectTab(int index) {
        getMViewBind().tabLayout.setClickTab(index);
    }

    private CommunityTabFragment getFragmentByTab(CommunityTabType tabType) {
        CommunityTabFragment fragment =
                (CommunityTabFragment) getParentFragmentManager().findFragmentByTag("f" + tabType
                        .getValue());
        return fragment;
    }

    @SuppressLint("CheckResult")
    private void initRefreshLayout() {
        getMViewBind().slUpdate.setEnableLoadMore(false);
        getMViewBind().slUpdate.setEnableFooterFollowWhenNoMoreData(true);
        getMViewBind().slUpdate.setOnRefreshListener(new MyOnRefreshLoadMoreListener(this));
    }

    private void onRefresh(boolean isShowLoadingView) {
        NetWorkUtils.NetWorkType netWorkType = NetWorkUtils.checkNetWork();
        if (netWorkType == NetWorkUtils.NetWorkType.HaveNetWork) {
            if (isShowLoadingView) {
                UIUtils.showLoading(getMViewBind());
            }
            //轮播图刷新
            mViewModel.refreshLoadRvData();
            //当前tab页刷新
            //            CommunityTabFragment cTabFragment = getFragmentByTab(mViewModel.getMSelectTabType());
            //            if (cTabFragment != null && cTabFragment.getMViewModel() != null) {
            //                cTabFragment.getMViewModel().refreshLoadRvData();
            //            }
            //存在的tab页都刷新 ，20241016修复从无网异常界面重新加载后看到列表数据是失败的情况
            for (int i = 0, size = CommunityTabType.values().length; i < size; i++) {
                CommunityTabFragment cTabFragment = getFragmentByTab(CommunityTabType.values()[i]);
                if (cTabFragment != null && cTabFragment.getMViewModel() != null) {
                    cTabFragment.getMViewModel().refreshLoadRvData();
                }
            }


        } else {
            if (!UIUtils.isShowErrorView(getMViewBind())) {
                getMViewBind().slUpdate.finishRefresh(false);
                UIUtils.showToastNetError(netWorkType);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        TrackUtils.viewStart(TrackUtils.PageNameType.Community);
        mBannerView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        mBannerView.setCurrentItem(mBannerView.getCurrentItem(), false);
        TrackUtils.viewEnd(TrackUtils.PageNameType.Community);
        mBannerView.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
        mBannerView.onPause();
    }

    @Override
    public void onDestroyView() {
        if (showLottieDisposable != null && !showLottieDisposable.isDisposed()) {
            showLottieDisposable.dispose();
        }
        showLottieDisposable = null;
        mViewModel.cancelDisposable();

        if (mTabLayoutMediator != null) {
            mTabLayoutMediator.detach();//tab和viewpager绑定解除
            mTabLayoutMediator = null;
        }
        if (getMViewBind() != null) {
            if (getMViewBind().bannerView != null) {
                getMViewBind().bannerView.stopLoop();
                getMViewBind().bannerView.onDestroy();
            }
        }
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getMViewBind().viewAppBarLayout.removeOnOffsetChangedListener(mOnOffsetChangedListener);
        mOnOffsetChangedListener = null;
        UIUtils.clearReqResLottieOnDestroy(getMViewBind());
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        //指示器不选中与选中颜色
        mBannerView.setIndicatorSliderColor(getResources().getColor(R.color.scene_community_banner_indicator_normal,
                        null),
                getResources().getColor(R.color.scene_community_banner_indicator_select, null));
        UIUtils.setSmartRefreshOnConfigurationChanged(getMViewBind().header, null);

        //刷新轮播图的图片
        ((CommunityBannerAdapter) getMViewBind().bannerView.getAdapter()).notifyByLightMode(UIUtils.isLightMode(),
                getMViewBind().bannerView);
    }

    /**
     * 轮播图改变时修改左侧内容
     */
    @SuppressLint("UseCompatLoadingForDrawables")
    private void updateBannerTvInfo(CommunitySceneInfo data) {

        getMViewBind().tvSceneDesc.setText(data.getScenarioInfoDa().getScenarioDesc());
        getMViewBind().tvSceneName.setText(data.getScenarioInfoDa().getScenarioName());

        getMViewBind().tvDownloads.setText(NumberToCNUtil.formatOverString(data.getDownloads()));
        getMViewBind().tvLikes.setText(NumberToCNUtil.formatOverString(data.getLikes()));
        getMViewBind().tvLikes.setTextColor(getResources().getColor(data.getLikeTextColor(), null));
        getMViewBind().tvLikes.setUiModeChangeListener(theme -> {
            if (theme == 0) {
                getMViewBind().tvLikes.getImgView().setAnimation(R.raw.scene_likes_day);
            } else if (theme == 1) {
                getMViewBind().tvLikes.getImgView().setAnimation(R.raw.scene_likes_night);
            }
            getMViewBind().tvLikes.setTextColor(getResources().getColor(data.getLikeTextColor(), null));
        });
        if (UIUtils.isLightMode()) {
            getMViewBind().tvLikes.getImgView().setAnimation(R.raw.scene_likes_day);
        } else {
            getMViewBind().tvLikes.getImgView().setAnimation(R.raw.scene_likes_night);
        }

        //点赞的逻辑
        if (data.isLike()) {
            // 进入到此行，说明数字颜色已改变了
            if (!getMViewBind().tvLikes.getImgView().isAnimating()) { //已成功点赞后，发现动画还没播，改变图片
                getMViewBind().tvLikes.getImgView().setProgress(1.0f);
            } else {
                //已成功点赞后，发现动画还在播，不改变图片
            }
            getMViewBind().tvLikes.getImgView().removeAllUpdateListeners();
        } else {
            getMViewBind().tvLikes.getImgView().setProgress(0.0f);
            getMViewBind().tvLikes.getImgView().addAnimatorListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(@NonNull Animator animation) {

                }

                @Override
                public void onAnimationEnd(@NonNull Animator animation) {
                    getMViewBind().tvLikes.getImgView().setProgress(1.0f);
                    getMViewBind().tvLikes.setTextColor(getResources().getColor(R.color.scene_primary_color_highlight
                            , null));
                }

                @Override
                public void onAnimationCancel(@NonNull Animator animation) {

                }

                @Override
                public void onAnimationRepeat(@NonNull Animator animation) {

                }
            });
        }

        //下载事件
        getMViewBind().tvDownloads.setOnClickListener(v -> {
            //增加防快速点击的判断
            if (!DebouncingUtils.isValid(v)) {
                return;
            }
            onItemRecyclerListener_OnClick(data, CommunityReqType.Down);
        });
        //点赞事件
        getMViewBind().tvLikes.setOnClickListener(v -> {
            //增加防快速点击的判断
            if (!DebouncingUtils.isValid(v)) {
                return;
            }
            if (data.isLike() == false) {
                getMViewBind().tvLikes.getImgView().setRepeatCount(0); //多重复0次循环
                getMViewBind().tvLikes.getImgView().playAnimation();
            }
            onItemRecyclerListener_OnClick(data, CommunityReqType.Likes);
        });
    }

    private static class MyOnRefreshLoadMoreListener implements OnRefreshLoadMoreListener {

        private WeakReference<CommunityFragment> reference;

        public MyOnRefreshLoadMoreListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onLoadMore(@NonNull RefreshLayout refreshLayout) {

        }

        @Override
        public void onRefresh(@NonNull RefreshLayout refreshLayout) {
            if (reference == null || reference.get() == null) return;
            reference.get().onRefresh(false);
        }
    }

    private static class MyOnOffsetChangedListener implements AppBarLayout.OnOffsetChangedListener {

        private int oldVerticalOffset = 1;
        private WeakReference<CommunityFragment> reference;

        public MyOnOffsetChangedListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
            if (reference == null || reference.get() == null) return;
            verticalOffset = verticalOffset * -1;
            if (oldVerticalOffset == verticalOffset) return;
            //850+10 = tablayout实际距顶的高度 276=固定的Toolbar的高度  相减为滑动距离
            //            if (verticalOffset <= -AutoSizeUtils.dp2px(getActivity(), 850.0f + 10.0f - 276.0f)) {
            //                getMViewBind().viewTopBg.setVisibility(View.VISIBLE);
            //            } else {
            //                getMViewBind().viewTopBg.setVisibility(View.GONE);
            //            }
            float maxHeight = 850.0f + 10.0f - 276.0f; //Banner高度850+padding10-Toolbar固定高度276
            float updateDistance = 300.0f; //改变颜色的距离范围
            float minHeight = maxHeight - updateDistance;

            int autoMaxHeight = AutoSizeUtils.dp2px(reference.get().getActivity(), maxHeight);
            int autoMinHeight = AutoSizeUtils.dp2px(reference.get().getActivity(), minHeight);
            int autoDistance = AutoSizeUtils.dp2px(reference.get().getActivity(), updateDistance);

            float dg = 1f;
            if (autoMinHeight <= verticalOffset && verticalOffset < autoMaxHeight) {
                dg = (verticalOffset - autoMinHeight) * 1.0f / autoDistance;
            } else if (autoMaxHeight <= verticalOffset) {
                dg = 1.0f;
            } else {
                dg = 0.0f;
            }
            reference.get().getMViewBind().viewTopBg.setAlpha(dg);
            if (dg == 1) { //到顶,停止轮播
                reference.get().mBannerView.setAutoPlay(false);
                reference.get().mBannerView.onPause();
            } else {
                if (!reference.get().mBannerView.isAutoPlay()) {
                    reference.get().mBannerView.setAutoPlay(true);
                    reference.get().mBannerView.onResume();
                }
            }

            //左边按钮的颜色渐变
            float maxHeight2 = 200.0f; //Banner高度850+padding10-Toolbar固定高度276
            float updateDistance2 = 200.0f; //改变颜色的距离范围
            float minHeight2 = maxHeight2 - updateDistance2;

            int autoMaxHeight2 = AutoSizeUtils.dp2px(reference.get().getActivity(), maxHeight2);
            int autoMinHeight2 = AutoSizeUtils.dp2px(reference.get().getActivity(), minHeight2);
            int autoDistance2 = AutoSizeUtils.dp2px(reference.get().getActivity(), updateDistance2);

            float dg2 = 1f;
            if (autoMinHeight2 <= verticalOffset && verticalOffset < autoMaxHeight2) {
                dg2 = (verticalOffset - autoMinHeight2) * 1.0f / autoDistance2;
            } else if (autoMaxHeight2 <= verticalOffset) {
                dg2 = 1.0f;
            } else {
                dg2 = 0.0f;
            }

            reference.get().getMViewBind().tvDownloads.setAlpha(1 - dg2);
            reference.get().getMViewBind().tvLikes.setAlpha(1 - dg2);
            reference.get().getMViewBind().tvSceneDesc.setAlpha(1 - dg2);
            reference.get().getMViewBind().tvSceneName.setAlpha(1 - dg2);

            oldVerticalOffset = verticalOffset;
        }
    }

    private static class BannerOnPageChangeCallback extends ViewPager2.OnPageChangeCallback {
        private final WeakReference<CommunityFragment> reference;

        //        private final Map<Integer, View> leftHolderViewMap = new HashMap<>();
        //        private final Map<Integer, View> rightHolderViewMap = new HashMap<>();
        //        private final Bitmap leftMaskBitmap; //左蒙层
        //        private final Bitmap rightMaskBitmap; //右蒙层
        //        private ObjectAnimator exitAnimation = null;
        //        //mix view
        //        private ImageView mixView = null;
        //        private boolean isMixViewVisible = false;
        //        private Bitmap mixBitmap;
        //        private Bitmap leftRightBitmap;

        public BannerOnPageChangeCallback(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
            //            leftMaskBitmap = BannerUtils.createMaskBitmap(true);
            //            rightMaskBitmap = BannerUtils.createMaskBitmap(false);
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            if (reference == null || reference.get() == null) return;
            if (reference.get().isDetached()) return;
            if (state == 0) {//停
                Glide.with(reference.get()).resumeRequests();
                if (reference.get().getMViewBind().bannerView.getCurrentItem() < reference.get().getMViewBind().bannerView.getData
                        ().size()) {
                    CommunitySceneInfo data =
                            reference.get().mBannerView.getData().get(reference.get().getMViewBind().bannerView
                                    .getCurrentItem());
                    reference.get().updateBannerTvInfo(data);
                }
                //                startExitAnimation();
            } else { //  开始 1手动滑 ，2自动滑
                Glide.with(reference.get()).pauseRequests();
            }
        }

        //        //滑到顶之后动画渐变消失
        //        private void startExitAnimation() {
        //            if (mixView == null) return;
        //            float startValue = mixView.getAlpha();
        //            if (exitAnimation != null && exitAnimation.isRunning()) {
        //                exitAnimation.cancel();
        //            }
        //            exitAnimation = ObjectAnimator.ofFloat(mixView, "alpha", startValue, 0f);
        //            exitAnimation.setDuration(500);
        //            exitAnimation.start();
        //        }
        //
        //        @Override
        //        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        //            super.onPageScrolled(position, positionOffset, positionOffsetPixels);
        //            if (reference == null || reference.get() == null) return;
        //            //positionOffset 比例 从右到左 0到1  ，positionOffsetPixels 从右往左 0到2560
        //            //标识
        //            //            scrollingIndex =
        //            //                    BannerUtils.getBannerScrollingIndex(reference.get().getMViewBind()
        //            .bannerView,
        //            //                            position, positionOffset, positionOffsetPixels);
        //            BannerScrollLeftRightBean scrollingIndex =
        //                    BannerUtils.getBannerScrollingIndex(reference.get().getMViewBind().bannerView,
        //                            0, 0, 0);
        //            if (!scrollingIndex.isEnable()) return;
        //
        //            //2.左页
        //            View leftHolderView = leftHolderViewMap.get(scrollingIndex.getLeftVisiblePosition());
        //            if (leftHolderView == null) {
        //                View lastViewTemp = BannerUtils.getBannerHolderView(reference.get().getMViewBind().bannerView,
        //                        scrollingIndex.getLeftVisiblePosition());
        //                if (lastViewTemp == null) return;
        //                leftHolderViewMap.clear();
        //                leftHolderViewMap.put(scrollingIndex.getLeftVisiblePosition(), lastViewTemp);
        //                leftHolderView = leftHolderViewMap.get(scrollingIndex.getLeftVisiblePosition());
        //            }
        //
        //            //3.右页
        //            View rightHolderView = rightHolderViewMap.get(scrollingIndex.getRightVisiblePosition());
        //            if (rightHolderView == null) {
        //                //要获取正在滑动的那个item
        //                View rightViewTemp = BannerUtils.getBannerHolderView(reference.get().getMViewBind()
        //                .bannerView,
        //                        scrollingIndex.getRightVisiblePosition());
        //                if (rightViewTemp == null) return;
        //                rightHolderViewMap.clear();
        //                rightHolderViewMap.put(scrollingIndex.getRightVisiblePosition(), rightViewTemp);
        //                rightHolderView = rightHolderViewMap.get(scrollingIndex.getRightVisiblePosition());
        //            }
        //            if (leftHolderView == null || rightHolderView == null) return;
        //
        //            if (BannerUtils.isLeftSlide == BannerUtils.BannerSlide.LEFT) {
        //                if (!isMixViewVisible) {
        //                    mixView = rightHolderView.findViewById(R.id.iv_left_mix);
        //                    mixView.setAlpha(1f);
        //                    isMixViewVisible = true;
        //
        //                    //左图
        //                    leftRightBitmap = BannerUtils.getBitmapFromImageView(leftHolderView.findViewById(R.id
        //                    .iv_bg));
        //                    if (leftRightBitmap == null) return;
        //                }
        //            } else if (BannerUtils.isLeftSlide == BannerUtils.BannerSlide.RIGHT) {
        //                if (!isMixViewVisible) {
        //                    mixView = leftHolderView.findViewById(R.id.iv_right_mix);
        //                    mixView.setAlpha(1f);
        //                    isMixViewVisible = true;
        //
        //                    //右图
        //                    leftRightBitmap = BannerUtils.getBitmapFromImageView(rightHolderView.findViewById(R.id
        //                    .iv_bg));
        //                    if (leftRightBitmap == null) return;
        //                }
        //            } else {
        //                mixView = rightHolderView.findViewById(R.id.iv_left_mix);
        //                mixView.setAlpha(0f);
        //                mixView = leftHolderView.findViewById(R.id.iv_right_mix);
        //                mixView.setAlpha(0f);
        //                isMixViewVisible = false;
        //                return;
        //            }
        //
        //            //加了以下代码 能令内存保持在一个上下范围，不加内存一直飙升
        //            int maxMixViewWidth = 640; //最大的混合view宽度 要和xml里的一样
        //            if (BannerUtils.isLeftSlide == BannerUtils.BannerSlide.LEFT) {
        //                int mixViewWidth = Math.min(positionOffsetPixels, maxMixViewWidth); //mixView的宽度
        //                ViewGroup.LayoutParams lp = mixView.getLayoutParams();
        //                lp.width = mixViewWidth;
        //                mixView.setLayoutParams(lp);
        //                mixBitmap = BannerUtils.createMixImages(leftRightBitmap, leftMaskBitmap,
        //                        1 - positionOffset, mixViewWidth, false);
        //            } else {
        //                int mixViewWidth = Math.min(2560 - positionOffsetPixels, maxMixViewWidth); //mixView的宽度
        //                ViewGroup.LayoutParams lp = mixView.getLayoutParams();
        //                lp.width = mixViewWidth;
        //                mixView.setLayoutParams(lp);
        //                mixBitmap = BannerUtils.createMixImages(leftRightBitmap, rightMaskBitmap,
        //                        1 - positionOffset, mixViewWidth, true);
        //            }
        //            mixView.setImageBitmap(mixBitmap);
        //        }
    }

    //轮播图点击事件
    private static class BannerOnPageClickListener implements BannerViewPager.OnPageClickListener {
        private WeakReference<CommunityFragment> reference;

        public BannerOnPageClickListener(CommunityFragment fragment) {
            reference = new WeakReference<>(fragment);
        }

        @Override
        public void onPageClick(View clickedView, int position) {
            if (reference == null || reference.get() == null) return;
            if (reference.get().isDetached()) return;
            if (position == reference.get().mBannerView.getCurrentItem()) { //中间
                SceneDetailActivity.Companion.startCommunitySceneDetailActivity(reference.get().requireActivity(),
                        reference.get().mBannerView.getData().get(position));
            } else { //两边
                reference.get().mBannerView.setCurrentItem(position, true);
            }
        }

    }
}