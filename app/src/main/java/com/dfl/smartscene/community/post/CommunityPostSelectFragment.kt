package com.dfl.smartscene.community.post

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Parcel
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.dfl.android.common.base.BaseVBFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.common.util.FragmentUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.databinding.SceneFragmentCommunityPostSelectBinding
import com.dfl.smartscene.rv.EquallySpaceDecoration
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import me.jessyan.autosize.utils.AutoSizeUtils
import java.lang.ref.WeakReference

/**
 *Created by 钟文祥 on 2023/11/23.
 *Describer: 发布场景 之 选择场景 界面
 */
class CommunityPostSelectFragment :
    BaseVBFragment<CommunityPostSelectViewModel, SceneFragmentCommunityPostSelectBinding>() {

    private var mAdapter: CommunityPostSelectAdapter? = null

    companion object {
        fun newInstance(): CommunityPostSelectFragment {
            return CommunityPostSelectFragment()
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        initRV()
        initListener()
    }

    override fun createObserver() {
        initObserverSceneList()
        mViewModel.getMySceneBeanList()

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initRV() {
        UIUtils.initRecyclerView(
            activity, mViewBind.rvSelect, GridLayoutManager(activity, 3), false
        )
        mViewBind.rvSelect.addItemDecoration(
            EquallySpaceDecoration(
                AutoSizeUtils.dp2px(activity, 52f), AutoSizeUtils.dp2px(activity, 52f)
            )
        )
        mAdapter = CommunityPostSelectAdapter()
        mAdapter?.setOnItemClickListener(MyOnItemClickListener(this@CommunityPostSelectFragment))
        mViewBind.rvSelect.adapter = mAdapter
    }

    private class MyOnItemClickListener(context: CommunityPostSelectFragment) : OnItemClickListener {
        private var reference: WeakReference<CommunityPostSelectFragment>? = null

        init {
            reference = WeakReference<CommunityPostSelectFragment>(context)
        }

        override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            reference?.get()?.let {
                val lastSelected = it.mAdapter?.mSelectedIndex
                it.mAdapter?.mSelectedIndex = position

                if (lastSelected == -1) {
                    it.mAdapter?.notifyDataSetChanged()
                } else {
                    if (it.mAdapter?.mSelectedIndex == lastSelected) {
                        it.mAdapter?.mSelectedIndex = -1
                        it.mAdapter?.notifyDataSetChanged()
                    } else {
                        lastSelected?.let { lastSelectedIt ->
                            it.mAdapter?.notifyItemChanged(lastSelectedIt)
                        }
                        it.mAdapter?.mSelectedIndex?.let { mSelectedIndexIt ->
                            it.mAdapter?.notifyItemChanged(mSelectedIndexIt)
                        }
                    }
                }
                it.mViewBind.tvNext.isEnable = !it.mAdapter?.isNoSelect()!!
            }

        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initListener() {
        mViewBind.tvNext.setOnActionDownListen {
            mViewBind.tvNext.let { //增加防快速点击的判断
                if (!DebouncingUtils.isValid(mViewBind.tvNext)) {
                    return@setOnActionDownListen
                }
            }
            if (mAdapter?.isNoSelect() == true) {
                CommonToastUtils.show(getString(R.string.scene_toast_community_select_please))
            } else {
                val fragment = mAdapter?.getSelectItem()?.let {
                    CommunityPostFragment.newInstance(
                        copyScenarioBean(it), "CommunityPostSelectFragment"
                    )
                }
                fragment?.let {
                    activity?.let { it1 ->
                        FragmentUtils.addFragment(
                            it1, R.id.fl_post_view, it
                        )
                    }
                }
            }
        }
        //        mViewBind.tvNext.setOnClickListener(object : OnClickListener {
        //            override fun onClick(v: View?) {
        //
        //            }
        //        })
        mViewBind.ivBack.setOnClickListener { v -> back(v) }
        //		mViewBind.viewDisShow.setOnClickListener { v -> back(v) }
        // 拦截触摸事件，防止泄露下去
        view?.setOnTouchListener { _, _ -> true }
    }

    //复制对象 https://blog.51cto.com/u_16213441/9044366
    private fun copyScenarioBean(source: ScenarioBean): ScenarioBean {
        val parcel = Parcel.obtain()
        source.writeToParcel(parcel, 0)
        parcel.setDataPosition(0)
        val copy = ScenarioBean.createFromParcel(parcel)
        parcel.recycle()
        return copy
    }

    private fun back(v: View?) {
        v?.let { //增加防快速点击的判断
            if (!DebouncingUtils.isValid(it)) return
        }
        LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Int::class.java).post(1)
    }

    private fun initObserverSceneList() {
        mViewModel.getSceneList().observe(this) {
            mAdapter?.setList(it)
        }
    }

    override fun onDestroy() {
        mAdapter?.setOnItemClickListener(null)
        mViewBind.rvSelect.adapter = null
        super.onDestroy()
    }

}