package com.dfl.smartscene.community.post

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.View.OnClickListener
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dfl.android.common.base.BaseVBFragment
import com.dfl.android.common.global.GlobalLiveEventConstants
import com.dfl.android.common.util.CommonToastUtils
import com.dfl.android.common.util.DebouncingUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.android.commonlib.toast.ToastUtils
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.smartscene.communication.UserCenterManager
import com.dfl.smartscene.customapi.SoundManager
import com.dfl.smartscene.databinding.SceneFragmentCommunityPostBinding
import com.dfl.smartscene.ui.main.MainActivity
import com.dfl.smartscene.ui.main.discover.detail.adapter.SceneDetailActionAdapter
import com.dfl.smartscene.ui.main.discover.detail.adapter.SceneDetailConditionAdapter
import com.dfl.smartscene.ui.overlay.common.DefaultDialogFragment
import com.dfl.smartscene.util.MMKVUtils
import com.dfl.smartscene.util.UIUtils
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 *Created by 钟文祥 on 2023/11/24.
 *Describer: 发布场景 之 发布场景 界面
 */
class CommunityPostFragment : BaseVBFragment<CommunityPostViewModel, SceneFragmentCommunityPostBinding>() {

    private var mConditionAdapter: SceneDetailConditionAdapter? = null
    private var mActionAdapter: SceneDetailActionAdapter? = null
    private var dialog: DefaultDialogFragment? = null

    companion object {
        private const val COMMUNITY_DETAIL = "COMMUNITY_DETAIL" //场景原始数据
        private const val FROM_PAGE = "FROM_PAGE" //从哪个页面进入发布场景

        fun newInstance(bean: ScenarioBean, fromPage: String): CommunityPostFragment {
            val fragment = CommunityPostFragment()
            val args = Bundle()
            bean.scenarioInfo?.scenarioTimeStamp = System.currentTimeMillis()
            args.putParcelable(COMMUNITY_DETAIL, bean)
            args.putString(FROM_PAGE, fromPage)
            fragment.arguments = args
            return fragment
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun initView(savedInstanceState: Bundle?) {
        arguments?.let {
            mViewModel.fromPage = it.getString(FROM_PAGE)
            mViewModel.initData(it.getParcelable<ScenarioBean?>(COMMUNITY_DETAIL), resources)
            mViewBind.scenarioInfo = mViewModel.scenarioBean?.scenarioInfo
        }
        initSceneDescHint()
        initRV()
        initListener()
    }

    override fun createObserver() {
        initObserverCommunityGenImg()
        initObserverUpload()
        showGenImgIng(true)
        mViewModel.genimg()
    }

    private fun initSceneDescHint() {
        if (mViewModel.scenarioBean?.scenarioInfo?.scenarioDesc?.isNotEmpty() == true) {
            mViewBind.tvSceneDesc.visibility = View.VISIBLE
            mViewBind.tvSceneDescHint.visibility = View.GONE
        } else {
            mViewBind.tvSceneDesc.visibility = View.GONE
            mViewBind.tvSceneDescHint.visibility = View.VISIBLE
        }
    }

    private fun initRV() {
        //条件
        UIUtils.initRecyclerView(
            activity, mViewBind.rvDetailSceneCondition, GridLayoutManager(activity, 3), false
        )
        mConditionAdapter = SceneDetailConditionAdapter()
        mViewBind.rvDetailSceneCondition.adapter = mConditionAdapter

        if (mViewModel.sceneConditionLiveData.size == 0) {
            mViewBind.rvDetailSceneCondition.visibility = View.GONE
            mViewBind.tvDetailSceneConditionName.visibility = View.GONE
        } else {
            mConditionAdapter?.data = mViewModel.sceneConditionLiveData
            mViewBind.rvDetailSceneCondition.addItemDecoration(SceneDetailConditionAdapter.MyGridItemDecoration())
            mViewBind.rvDetailSceneCondition.visibility = View.VISIBLE
            mViewBind.tvDetailSceneConditionName.visibility = View.VISIBLE
        }

        //动作
        mActionAdapter = SceneDetailActionAdapter()
        mViewBind.rvDetailSceneAction.addItemDecoration(
            SceneDetailActionAdapter.MyGridItemDecoration()
        )
        mViewBind.rvDetailSceneAction.adapter = mActionAdapter
        mActionAdapter?.data = mViewModel.sceneActionLiveData
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun initListener() {
        //返回
        mViewBind.ivBack.setOnClickListener { v -> back(v) }
        //		mViewBind.viewDisShow.setOnClickListener { v -> back(v) }
        // 拦截触摸事件，防止泄露下去
        view?.setOnTouchListener { v, event -> true }
        //发布
        mViewBind.tvPost.setOnClickListener(object : OnClickListener {
            override fun onClick(v: View?) {
                if (v == null) return
                UserCenterManager.checkDebouncingAndNetworkAndLogin(v, activity?.supportFragmentManager!!) {
                    mViewBind.tvPost.isEnable = false
                    mViewModel.upload()
                }
            }
        })
        //场景名称
        mViewBind.tvEditName.setOnClickListener(object : OnClickListener {
            override fun onClick(v: View?) {
                v?.let { //增加防快速点击的判断
                    if (!DebouncingUtils.isValid(it)) return
                    showInputDialog(true)
                }
            }
        })
        //场景简介
        mViewBind.tvEditDesc.setOnClickListener(object : OnClickListener {
            override fun onClick(v: View?) {
                v?.let { //增加防快速点击的判断
                    if (!DebouncingUtils.isValid(it)) return
                    showInputDialog(false)
                }
            }
        })
        //编辑简介
        mViewBind.tvSceneDescHint.setOnClickListener(object : OnClickListener {
            override fun onClick(v: View?) {
                v?.let { //增加防快速点击的判断
                    if (!DebouncingUtils.isValid(it)) return
                    showInputDialog(false)
                }
            }
        })
        //换一张
        mViewBind.llChangeOne.setOnClickListener {
            showGenImgIng(true)
            mViewModel.genimg()
        }
    }

    private fun showInputDialog(isInputName: Boolean) {
        CommunityEditDialogFragment.showDialog(parentFragmentManager,
            isInputName,
            if (isInputName) mViewModel.scenarioBean?.scenarioInfo?.scenarioName ?: ""
            else mViewBind.tvSceneDesc.text.toString(),
            object : CommunityEditDialogFragment.OnConfirmListener {
                override fun onConfirm(value: String) {
                    if (isInputName) {
                        mViewBind.tvSceneName.text = value
                    } else {
                        mViewBind.tvSceneDesc.text = value
                        initSceneDescHint()
                    }
                }
            })
    }

    private fun back(v: View?) {
        v?.let { //增加防快速点击的判断
            if (!DebouncingUtils.isValid(it)) return
        }

        dialog = DefaultDialogFragment.showDialog(
            manager = parentFragmentManager,
            action = "",
            title = getString(R.string.scene_dialog_community_title_leave_current),
            //封面生成中
            content = if (mViewBind.isGenImgIng == true) getString(R.string.scene_dialog_community_genimg_ing_to_back)
            else getString(R.string.scene_dialog_community_missing_information),
            leftButtonText = getString(R.string.scene_text_edit_condition_quit),
            rightButtonText = getString(R.string.scene_text_edit_condition_quit_no),
            object : DefaultDialogFragment.OnDialogButtonClickListener {
                override fun onLeftButtonClick(dialog: DefaultDialogFragment) {
                    mViewModel.cancelDisposable()
                    dialog.dismissWithAnimate()
                    LiveEventBus.get(GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Int::class.java).post(1)
                }

                override fun onRightButtonClick(dialog: DefaultDialogFragment) {
                    dialog.dismissWithAnimate()
                }
            },
            isCloseVisible = false,
            isSoundToast = true
        )
    }

    private fun initObserverCommunityGenImg() {
        showGenImgIng(true)
        mViewModel.mCommunityGenImg.observe(this) {
            showGenImgIng(false)
            val mBgOptions: RequestOptions = UIUtils.getGlideOptions(
                R.drawable.scene_img_community_card_loading, //加载时图片
                R.drawable.scene_img_community_card_bg_fail, //失败时图片
                584, 256
            )
            it?.let {
                context?.let { it1 ->
                    Glide.with(it1).load(it.imgUrl).apply(mBgOptions).into(mViewBind.ivSceneBg)
                    mViewModel.scenarioBean?.scenarioInfo?.imgPath = it.imgUrl
                }
            }
            if (it == null) {
                //发布场景时，若封面为缺省状态，发布到社区时显示一张默认兜底封面
                if (TextUtils.isEmpty(mViewModel.scenarioBean?.scenarioInfo?.imgPath)) {
                    context?.let { it1 ->
                        Glide.with(it1).load("").apply(mBgOptions).into(mViewBind.ivSceneBg)
                    }
                    mViewModel.scenarioBean?.scenarioInfo?.imgPath =
                        "https://vitfilezh.venucia.com/communityScene/9.png"
                }
            }
        }
    }

    private fun initObserverUpload() {
        mViewModel.mUpload.observe(this) { it ->
            mViewBind.tvPost.isEnable = true
            ToastUtils.cancel()
            dialog?.dismissWithAnimate()
            it.value?.let {
                if (it.isSuccess()) {
                    CommonToastUtils.show(R.string.scene_toast_community_upload_success)
                    SoundManager.playSoundEffect(SoundManager.SoundType.DONE)

                    LiveEventBus.get(
                        GlobalLiveEventConstants.KEY_POST_FRAGMENT_BACK, Int::class.java
                    ).post(2)

                    //当现在是我的fragment，并且社区fragment没有初始化，需要在加载图消失后闪烁
                    if (mViewModel.isFromMeFragment()) {
                        activity?.let {
                            if (!(activity as MainActivity).isCommunityFragmentInit) {
                                MMKVUtils.getInstance().saveMeUploadShowLottieAnimation(true)
                            }
                            (activity as MainActivity).showCommunityTag()
                        }
                    }
                } else {
                    CommonToastUtils.show(R.string.scene_dialog_community_upload_fail_error)
                    CommonLogUtils.logE(TAG, "场景发布失败1")
                }
            }
            it.ex?.let {
                if (it.message.contains("含敏感词")) {
                    CommonToastUtils.show(R.string.scene_toast_community_upload_fail_sensitive)
                } else {
                    CommonToastUtils.show(R.string.scene_toast_community_upload_fail)
                }
                SoundManager.playSoundEffect(SoundManager.SoundType.FAILED)
                CommonLogUtils.logE(TAG, "场景发布失败：" + it.message)
            }
        }
    }

    override fun onDestroy() {
        mViewModel.cancelDisposable()
        if (mViewBind.lottieLoading.isVisible) {
            mViewBind.lottieLoading.cancelAnimation()
            mViewBind.lottieLoading.clearAnimation()
        }
        CommonToastUtils.cancelLoadingToast()
        super.onDestroy()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (mViewBind.lottieLoading.isVisible) {
            mViewBind.lottieLoading.cancelAnimation()
            mViewBind.lottieLoading.playAnimation()
        }
    }

    private fun showGenImgIng(isGenImgIng: Boolean) {
        mViewBind.isGenImgIng = isGenImgIng
        if (isGenImgIng) {
            mViewBind.lottieLoading.playAnimation()
        } else {
            mViewBind.lottieLoading.cancelAnimation()
        }
    }

}