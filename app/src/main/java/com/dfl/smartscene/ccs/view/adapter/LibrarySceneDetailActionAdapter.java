package com.dfl.smartscene.ccs.view.adapter;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.util.ImageUtil;

/**
 * <AUTHOR>
 * @date ：2022/10/12 16:48
 * @description ：用户广场场景详情里的执行操作描述item
 */
public class LibrarySceneDetailActionAdapter extends BaseAdapter<LibrarySceneDetailActionAdapter.DataBean> {

    @Override
    protected BaseHolder<DataBean> getViewHolder(View view, int viewType) {
        return new Holder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_library_scene_detail_action;
    }

    static class Holder extends BaseHolder<DataBean>{

        private ImageView mImageViewIcon;
        private TextView mTextViewTitle;
        public Holder(View itemView) {
            super(itemView);
            mImageViewIcon = itemView.findViewById(R.id.imageview_item_library_scene_detail_action_icon);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_library_scene_detail_action_title);
        }

        @Override
        public void setupData(DataBean dataBean, int position) {
            setTitle(dataBean.getTitle());
            ImageUtil.loadIcon(dataBean.getIcon(),mImageViewIcon);
        }

        protected void setTitle(String value){
            if(value.contains(" ")){
                SpannableString spannableString = new SpannableString(value);
                int index = value.indexOf(" ");
                spannableString.setSpan(new ForegroundColorSpan(mTextViewTitle.getResources().getColor(R.color.color_text_selected)),index,value.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                mTextViewTitle.setText(spannableString);
            }else {
                mTextViewTitle.setText(value);
            }
        }
    }

    public static class DataBean{
        private String title;
        private String icon;

        public DataBean(String title, String icon) {
            this.title = title;
            this.icon = icon;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }
}
