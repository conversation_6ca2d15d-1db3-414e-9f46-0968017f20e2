package com.dfl.smartscene.ccs.view.operationview;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.AdjustSlideView;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

public class AdjustSlideOperation implements OperationBaseView {

    private final SingleOperation mSingleOperation;
    private final AdjustSlideView mAdjustSlideView;
    private int maxValue = 1;
    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);
        values.add(String.valueOf(mAdjustSlideView.getProgress()));
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if (mAdjustSlideView != null) {
            MLog.d("AdjustSlideOperation","desc " + desc);
            int progress = mAdjustSlideView.getProgress();
            if (desc.contains("&%")){
                desc = desc.replaceFirst("&%", String.valueOf(progress));
            }else {
                desc = desc.replaceFirst("&", String.valueOf(progress));
            }
            MLog.d("AdjustSlideOperation","progress:"+progress + ",,," + desc);
        }
        return desc;
    }

    public AdjustSlideOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;

        String valueTypeStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_TYPE);

        String startPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_START);
        String endPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_END);
        String defaultValueStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        String maxValueStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_HIGH);
        String minValueStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_LOW);

        String stepValueStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_STEP);

        int min = 0;
        int max = 100;
        int defaultValue = 0;
        int stepValue = 1;
        if ("int".equals(valueTypeStr)) {
            if (!TextUtils.isEmpty(minValueStr)) {
                min = Integer.parseInt(minValueStr);
            }
            if (!TextUtils.isEmpty(maxValueStr)) {
                max = Integer.parseInt(maxValueStr);
            }
            if (!TextUtils.isEmpty(defaultValueStr)) {
                defaultValue = Integer.parseInt(defaultValueStr);
            }
            if (!TextUtils.isEmpty(stepValueStr)) {
                stepValue = Integer.parseInt(stepValueStr);
            }

        }
        maxValue = max;
        mAdjustSlideView = new AdjustSlideView(parent.getContext());
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(560, 56);
        params.gravity = Gravity.CENTER;
        parent.addView(mAdjustSlideView, params);

        //        adjustSlideView.setStartAndEndIconSize();
        mAdjustSlideView.setProgress(defaultValue);
        mAdjustSlideView.setPicName(startPicStr, endPicStr);
        mAdjustSlideView.setProgressMinMax(min, max);
        mAdjustSlideView.setStepValue(stepValue);
        mAdjustSlideView.refreshView();
    }

    @Override
    public void onConfigurationChanged() {
        if (mAdjustSlideView!=null) {
            String startPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_START);
            String endPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_END);
            mAdjustSlideView.setPicName(startPicStr, endPicStr);
            mAdjustSlideView.refreshView();
        }
    }

}
