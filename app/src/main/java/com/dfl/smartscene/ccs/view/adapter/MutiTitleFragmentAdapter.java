package com.dfl.smartscene.ccs.view.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.view.fragment.LibraryFragment;


/**
 * tablayout + viewpager的adapter
 * @author: huangzezheng
 * @date: 2021/11/29
 */
public class MutiTitleFragmentAdapter extends FragmentStateAdapter {
    private String[] mFragmentNames;

    public MutiTitleFragmentAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle, String[] fragmentNames) {
        super(fragmentManager, lifecycle);
        mFragmentNames = fragmentNames;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (mFragmentNames[position]){
            case ConstantViewValue.PAGE_MAIN_LIBRARY:

                return new LibraryFragment();
            case ConstantViewValue.PAGE_MAIN_MY:
                return new MineFragment();
            default:
                return new Fragment();
        }
    }

    @Override
    public int getItemCount() {
        return mFragmentNames.length;
    }

    /**
     * 20240528：fix:CSS5-9599 画面“正在加载”与“请检查网络重试”文字重叠 经典的页面重叠问题
     * @param position
     * @return
     */
    @Override
    public long getItemId(int position) {
        return mFragmentNames[position].hashCode();
    }

    @Override
    public boolean containsItem(long itemId) {
        for (String name : mFragmentNames) {
            if (name.hashCode() == itemId) {
                return true;
            }
        }
        return false;
    }
}
