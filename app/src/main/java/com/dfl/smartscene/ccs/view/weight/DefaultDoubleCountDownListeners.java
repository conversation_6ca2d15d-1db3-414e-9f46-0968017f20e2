package com.dfl.smartscene.ccs.view.weight;

import android.widget.FrameLayout;

import com.dfl.smartscene.R;


public abstract class DefaultDoubleCountDownListeners extends DefaultDoubleDialogListeners implements DoubleCountDownView.CountDownInterface {


    public int getCountDownTextId() {
        return R.id.button_default_double_negative;
    }

    public void onCountDownFinish(FrameLayout dialog) {

    }
}