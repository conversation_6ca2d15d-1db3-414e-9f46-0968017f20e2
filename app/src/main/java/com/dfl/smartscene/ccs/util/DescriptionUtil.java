package com.dfl.smartscene.ccs.util;

import android.view.View;

/**
 * <AUTHOR>
 * @description 可见即可说的生成工具
 * @date 2023/12/20
 * @memo
 */
public class DescriptionUtil {
    /**
     * 不同条目之间的可见即可说的分隔符
     */
    private static final String VISIBLE_SEE_SPLITER = "#";
    /**
     * 列表项目前缀词
     */
    private static final String PREFIX_NUM = "第";
    /**
     * 列表项目后缀词
     */
    private static final String[] SUFFIX_NUM = {"个", "条", "项"};
    private static final String[] PREFIX_BUTTON = {"打开","点击","执行","点"};
    private static final String[] PREFIX_BUTTON_EXCEPT_EXECUTE = {"打开","点击","点"};

    /**
     * 基于可点击按钮的文字生成可见即可说的标签
     * */
    public static void generateButtonDescriptionVisibleToSay(String content, View view){
        if(null == content || "".equals(content)){
            return;
        }
        StringBuilder description = new StringBuilder();
        for(int i = 0; i < PREFIX_BUTTON.length; i++){
            description.append(PREFIX_BUTTON[i]).append(content).append(VISIBLE_SEE_SPLITER);
        }
        description.append(content);
        view.setContentDescription(description.toString());
    }

    /**
     * 基于可点击按钮的文字生成可见即可说的标签,排除执行按钮
     * */
    public static void generateItemViewExceptButtonDescriptionVisibleToSay(String content, View view){
        if(null == content || "".equals(content)){
            return;
        }
        StringBuilder description = new StringBuilder();
        for(int i = 0; i < PREFIX_BUTTON_EXCEPT_EXECUTE.length; i++){
            description.append(PREFIX_BUTTON_EXCEPT_EXECUTE[i]).append(content).append(VISIBLE_SEE_SPLITER);
        }
        description.append(content);
        view.setContentDescription(description.toString());
    }

}
