package com.dfl.smartscene.ccs.view.dialogfragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseDialogFragment;
import com.dfl.smartscene.ccs.base.MyClickableSpan;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.model.AiotModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.DescriptionUtil;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.LibrarySceneDetailActionAdapter;
import com.dfl.smartscene.ccs.view.adapter.LibrarySceneDetailConditionAdapter;
import com.dfl.smartscene.ccs.viewmodel.LibraryDetailViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/10/12 11:20
 * @description ：首页场景详情弹窗
 */
public class LibrarySceneDetailDialog extends BaseDialogFragment {

    private LibrarySceneDetailConditionAdapter mLibrarySceneDetailConditionAdapter;
    private LibrarySceneDetailActionAdapter mLibrarySceneDetailActionAdapter;
    private TextView mTextViewTitle;//场景名称
    private TextView mTextViewDesc;//场景描述
    private ImageView mImageViewIcon;//场景icon
    private View mLayoutCollect;//收藏按钮
    private TextView mTextViewCollect;//收藏描述
    private ImageView mImageViewClose;//关闭按钮
    private TextView mTextViewDialogTitle;//弹窗标题
    private TextView mTextViewAiotNotice;
    private LibraryDetailViewModel mLibraryDetailViewModel;

    private RecyclerView mRecyclerViewCondition,mRecyclerViewAction;
    private ConstraintLayout mLayoutExcute;//执行按钮

    private SceneBean mSceneBean;

    private String categoryName;
    private String categoryId;

    public static LibrarySceneDetailDialog newInstance(SceneBean sceneBean , String categoryName,String categoryId){
        LibrarySceneDetailDialog librarySceneDetailDialog = new LibrarySceneDetailDialog();
        Bundle bundle = new Bundle();
        bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_SCENE_BEAN,sceneBean);
        bundle.putString(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_NAME,categoryName);
        bundle.putString(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_ID,categoryId);

        librarySceneDetailDialog.setArguments(bundle);
        return librarySceneDetailDialog;
    }
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_library_scene_detail, container, false);
    }


    @Override
    protected void init() {
        if (getArguments() != null) {
            mSceneBean = (SceneBean) getArguments().getSerializable(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_SCENE_BEAN);
            categoryName = getArguments().getString(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_NAME);
            categoryId = getArguments().getString(ConstantViewValue.FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_ID);
        }
        mLibraryDetailViewModel = new ViewModelProvider(this).get(LibraryDetailViewModel.class);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mRecyclerViewCondition!=null) {
            mRecyclerViewCondition.removeAllViews();
            mRecyclerViewCondition.removeAllViewsInLayout();
            mRecyclerViewCondition.setAdapter(mLibrarySceneDetailConditionAdapter);
            mRecyclerViewCondition.getRecycledViewPool().clear();
            mLibrarySceneDetailConditionAdapter.setDataList(mLibrarySceneDetailConditionAdapter.getDataList());
        }

        if (mRecyclerViewAction!=null) {
            mRecyclerViewAction.removeAllViews();
            mRecyclerViewAction.removeAllViewsInLayout();
            mRecyclerViewAction.setAdapter(mLibrarySceneDetailActionAdapter);
            mRecyclerViewAction.getRecycledViewPool().clear();
            mLibrarySceneDetailActionAdapter.setDataList(mLibrarySceneDetailActionAdapter.getDataList());

        }

        if(mTextViewAiotNotice != null && mTextViewAiotNotice.getText().toString().contains("前往购买")){
            SpannableString spannableString = ToastManager.getSpanString(mTextViewAiotNotice.getText().toString(), "前往购买", new MyClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    ViewControlManager.openAiotStorePage();
                }
            });
            mTextViewAiotNotice.setText(spannableString);
        }
    }


    @Override
    protected void initView(View view) {
        mTextViewTitle = view.findViewById(R.id.textview_library_scene_detail_title);
        mTextViewDesc = view.findViewById(R.id.textview_library_scene_detail_desc);
        mImageViewIcon = view.findViewById(R.id.imageview_library_scene_detail_icon);
        mLayoutCollect = view.findViewById(R.id.clickarea_library_detail_collect_button);
        mLayoutExcute = view.findViewById(R.id.layout_library_detail_excute_button);
        mTextViewCollect = view.findViewById(R.id.textview_library_detail_collect_button);
        mImageViewClose = view.findViewById(R.id.button_content_top_close);
        mTextViewDialogTitle = view.findViewById(R.id.text_view_content_top_title);
        mTextViewAiotNotice = view.findViewById(R.id.textview_libary_detail_buy_notice);

        mRecyclerViewCondition = view.findViewById(R.id.recyclerview_library_scene_detail_condition_state);
        RecyclerViewUtil.setLinearRecycleView(mRecyclerViewCondition, RecyclerView.VERTICAL, R.dimen.x_px_0);
        mRecyclerViewCondition.setHasFixedSize(true);

        mRecyclerViewAction = view.findViewById(R.id.recyclerview_library_scene_detail_action);
        RecyclerViewUtil.setGridRecycleView(mRecyclerViewAction, RecyclerView.VERTICAL, 2,R.dimen.x_px_0,R.dimen.y_px_50);
        mRecyclerViewAction.setHasFixedSize(true);

        mLibrarySceneDetailConditionAdapter = new LibrarySceneDetailConditionAdapter();
        mRecyclerViewCondition.setAdapter(mLibrarySceneDetailConditionAdapter);

        mLibrarySceneDetailActionAdapter = new LibrarySceneDetailActionAdapter();
        mRecyclerViewAction.setAdapter(mLibrarySceneDetailActionAdapter);

        //收藏按钮点击监听
        mLayoutCollect.setOnClickListener(v -> {
            if(mSceneBean.isCollectState()){
                ViewControlManager.uncollectScene(mSceneBean.getSceneCategoryId(), mSceneBean);
                LogUtil.d("LibrarySceneDetailDialog", "点击取消收藏");
                BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_un_collect));
            }else {
                ViewControlManager.collectScene(categoryId, mSceneBean);
                //埋点：点击场景模式
                LogUtil.d("LibrarySceneDetailDialog", "点击收藏");
                BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_collect));
            }
            dismiss();
        });
        //执行按钮点击监听
        mLayoutExcute.setOnClickListener( v-> {
            //埋点：点击场景模式
            LogUtil.d("LibrarySceneDetailDialog", "点击试用");
            BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_use));
            DialogManager.showExcuteSceneDialog(mSceneBean);
            dismiss();
        });
        //关闭按钮点击监听
        mImageViewClose.setOnClickListener(v -> dismiss());
    }

    @Override
    protected void initObserver() {
        mLibraryDetailViewModel.getAiotDesc().observe(getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if(s != null && !"".equals(s)){
                    SpannableString spannableString = ToastManager.getSpanString("您还没有" + s + "点击前往购买,或打开手机端智联日产APP选购", "前往购买", new MyClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            ViewControlManager.openAiotStorePage();
                        }
                    });
                    mTextViewAiotNotice.setVisibility(View.VISIBLE);
                    mTextViewAiotNotice.setText(spannableString);
                    mTextViewAiotNotice.setMovementMethod(LinkMovementMethod.getInstance());

                }
            }
        });
    }

    @Override
    protected void initData() {
        mTextViewDialogTitle.setText(categoryName);
        mTextViewTitle.setText(mSceneBean.getSceneName());
        mTextViewDesc.setText(mSceneBean.getSceneDesc());
        ImageUtil.loadImageUrlNoCorner(mImageViewIcon,mSceneBean.getSceneIcon(),R.mipmap.icon_item_my_scene_default);
        List<LibrarySceneDetailConditionAdapter.DataBean> conditionStateData = getConditonViewBean();
        conditionStateData.addAll(getStateViewBean());
        LogUtil.d("TAG","conditionStateData:"+conditionStateData.size());
        mLibrarySceneDetailConditionAdapter.setDataList(conditionStateData);
        mLibrarySceneDetailActionAdapter.setDataList(getActionDataBeans());
        if(mSceneBean.isCollectState()){
            mTextViewCollect.setText(getString(R.string.string_library_detail_uncollect_button));
        }else {
            mTextViewCollect.setText(getString(R.string.string_library_detail_collect_button));
        }
        mLibraryDetailViewModel.requestSceneUnbuyDevices(mSceneBean.getActionOperations());
        //设置可见即可说标签
        DescriptionUtil.generateButtonDescriptionVisibleToSay(mTextViewCollect.getText().toString(), mLayoutCollect);
        DescriptionUtil.generateButtonDescriptionVisibleToSay(mLayoutExcute.getResources().getString(R.string.string_library_detail_excute_button), mLayoutExcute);
    }

    /**
     * 从触发条件中拿到语音触发的信息
     * @return
     */
    private List<LibrarySceneDetailConditionAdapter.DataBean> getVrConditionBean() {
        List<LibrarySceneDetailConditionAdapter.DataBean> dataBeans = new ArrayList<>();
        for (SettingOperation settingOperation : mSceneBean.getConditionOperations()) {
            if (ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(settingOperation.getOperationId())) {
                if (dataBeans.size() == 0) {
                    dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("语音指令", settingOperation.getListArgs().get(0),"gone"));
                } else {
                    dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("", settingOperation.getListArgs().get(0),"gone"));
                }
            }
        }
        if (dataBeans.size() != 0) {
            dataBeans.get(dataBeans.size() - 1).setDivide(true);
        }
        return dataBeans;
    }

    /**
     * 获取语音加触发条件数据
     * @return
     */
    private List<LibrarySceneDetailConditionAdapter.DataBean> getConditonViewBean() {
        List<LibrarySceneDetailConditionAdapter.DataBean> dataBeans = new ArrayList<>(getVrConditionBean());
        for (SettingOperation settingOperation : mSceneBean.getConditionOperations()) {
            if (!ConstantModelValue.OPERATION_ID_CONDITION_VR.equals(settingOperation.getOperationId())) {
                dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("当触发条件", settingOperation.getDesc(), CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId())));
                break;
            }

        }
        if (dataBeans.size() != 0) {
            dataBeans.get(dataBeans.size() - 1).setDivide(true);
        }

        return dataBeans;
    }


    /**
     * 获取状态条件数据
     * @return
     */
    private List<LibrarySceneDetailConditionAdapter.DataBean> getStateViewBean() {
        List<LibrarySceneDetailConditionAdapter.DataBean> dataBeans = new ArrayList<>();
        SettingOperation timeState = null;
        SettingOperation dayState = null;
        for (SettingOperation settingOperation : mSceneBean.getStateOperations()) {
            if(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK.equals(settingOperation.getOperationId())){
                timeState = settingOperation;
                continue;
            }
            if(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY.equals(settingOperation.getOperationId())){
                dayState = settingOperation;
                continue;
            }

            if (dataBeans.size() == 0) {
                dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("且满足全部条件", settingOperation.getDesc(),CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId())));
            } else {
                dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("", settingOperation.getDesc(),CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId())));
            }
        }
        if (dataBeans.size() != 0) {
            dataBeans.get(dataBeans.size() - 1).setDivide(true);
        }
        String daydesc = "每天";
        String timedesc = "全天";
        if (dayState != null){
            daydesc = dayState.getDesc().split(" ")[1];
        }
        if(timeState != null){
            timedesc = timeState.getDesc().split(" ")[1];
        }
        String timeDesc = daydesc + " " +timedesc;
        dataBeans.add(new LibrarySceneDetailConditionAdapter.DataBean("生效时间",timeDesc,"icon_time",true));
        return dataBeans;
    }

    /**
     * 获取执行动作数据
     * @return
     */
    private List<LibrarySceneDetailActionAdapter.DataBean> getActionDataBeans(){
        List<LibrarySceneDetailActionAdapter.DataBean> dataBeans = new ArrayList<>();
        for(SettingOperation settingOperation : mSceneBean.getActionOperations()){
            if(settingOperation.getShowType() == ConstantModelValue.SCENE_OPERATION_SHOW_TYPE_NORMAL){
                if(settingOperation.getDeviceId().equals(ConstantModelValue.DEVICE_ID_AIOT)){
                    dataBeans.add(new LibrarySceneDetailActionAdapter.DataBean(settingOperation.getDesc(), AiotModel.getInstance().findAiotIconByDesc(settingOperation.getDesc())));
                }else {
                    dataBeans.add(new LibrarySceneDetailActionAdapter.DataBean(settingOperation.getDesc(),CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId())));
                }

            }
        }
        return dataBeans;
    }
}
