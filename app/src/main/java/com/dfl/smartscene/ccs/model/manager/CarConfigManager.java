package com.dfl.smartscene.ccs.model.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.DebugValue;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.model.CarConfigResetModel;
import com.dfl.smartscene.ccs.model.factory.SceneModelFactory;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.wrapper.HttpWrapper;
import com.iauto.uibase.utils.MLog;
import com.tencent.mmkv.MMKV;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/8 14:33
 * @description ：用于管理车辆具有的执行能力，触发能力，状态能力配置表
 */
public class CarConfigManager {

    private static final String TAG = "CarConfigManager";
    private MMKV mmkv;
    private List<DeviceOperation> conditionOperations = new ArrayList<>();//车辆具备的触发条件
    private List<DeviceOperation> stateOperations = new ArrayList<>();//车辆具备的状态条件
    private List<DeviceOperation> actionOperations = new ArrayList<>();//车辆具备的执行动作
    private List<String> pLimitOperation = new ArrayList<>();//具有p挡限制的操作
    private int carconfigDataStatus = DATA_STATUS_NULL;
    public static final int DATA_STATUS_CLOUD = 2;//云端有返回
    public static final int DATA_STATUS_LOCAL = 1;//云端没返回，但本地有数据
    public static final int DATA_STATUS_NULL = 0;//云端没返回，本地也没数据

    private static volatile CarConfigManager sInstance;

    private CarConfigManager() {
        mmkv = MMKV.defaultMMKV();
    }

    public int getCarConfigDataStatus() {
        return carconfigDataStatus;
    }

    public static CarConfigManager getInstance() {
        if (null == sInstance) {
            synchronized (CarConfigManager.class) {
                if (null == sInstance) {
                    sInstance = new CarConfigManager();
                }
            }
        }
        return sInstance;
    }


    private HashSet<CarConfigInitListener> mCarConfigInitListeners = new HashSet<>();

    public void registerInitListener(CarConfigInitListener listener) {
        mCarConfigInitListeners.add(listener);
    }

    public void unregisterInitListener(CarConfigInitListener listener) {
        mCarConfigInitListeners.remove(listener);
    }

    public interface CarConfigInitListener {
        void onCarConfigInit(CarConfig carConfig);
    }

    /**
     * 根据操作id找到对应的车辆操作
     *
     * @param operationid
     * @return
     */
    public SingleOperation findSingleOperationById(String operationid) {
        LogUtil.d(TAG,"operationid:"+operationid);
        if (operationid == null) {
            return null;
        }
        //遍历寻找触发条件
        for (DeviceOperation deviceOperation : conditionOperations) {
            for (SingleOperation singleOperation : deviceOperation.getSingleOperations()) {
                if (operationid.equals(singleOperation.getOperationId())) {
                    return singleOperation;
                }
            }
        }
        //遍历寻找状态条件
        for (DeviceOperation deviceOperation : stateOperations) {
            for (SingleOperation singleOperation : deviceOperation.getSingleOperations()) {
                if (operationid.equals(singleOperation.getOperationId())) {
                    return singleOperation;
                }
            }
        }
        //遍历寻找执行动作
        for (DeviceOperation deviceOperation : actionOperations) {
            for (SingleOperation singleOperation : deviceOperation.getSingleOperations()) {
                if (operationid.equals(singleOperation.getOperationId())) {
                    return singleOperation;
                }
            }
        }

        //日期状态条件
        if (operationid.equals(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY)) {
            return SpecialBeanFactory.productStateDayOperation();
        }

        //时间状态条件
        if (operationid.equals(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK)) {
            return SpecialBeanFactory.productStateClockOperation();
        }


        return null;
    }

    private Map<String , String> iconMap = new HashMap<>();
    public String findOperationIconById(String id) {
        //一些特殊的id
        if (ConstantModelValue.OPERATION_ID_ADD.equals(id)) {
            return "icon_add";
        }
        if(iconMap.containsKey(id)){
            return iconMap.get(id);
        }
        //常规id
        SingleOperation singleOperation = findSingleOperationById(id);
        if (singleOperation == null) {
            return null;
        } else {
            iconMap.put(id , singleOperation.getOperationIcon());
            return singleOperation.getOperationIcon();
        }
    }


    //初始化车辆配置信息
    public Observable<CarConfig> initCarConfig() {
        MLog.d(TAG, "initCarConfig");

        if(DebugValue.Debug_carconfig_error && BuildConfig.DEBUG){
            return Observable.error(new Exception());
        }

        if(carconfigDataStatus != DATA_STATUS_NULL){
            CarConfig carConfig = new CarConfig();
            carConfig.setConditionConfig(conditionOperations);
            carConfig.setActionConfig(actionOperations);
            carConfig.setStateConfig(stateOperations);
            return Observable.just(carConfig);
        }
        MLog.d(TAG, "initCarConfig cloud");

        return HttpWrapper.getInstance().requestCarConfig()
                .doOnNext(new Consumer<CarConfig>() {
                    @Override
                    public void accept(CarConfig carConfig) throws Exception {
                        //LogUtil logUtil = new LogUtil();
                        //logUtil.printJsonLogger(TAG, "initCarConfig origin data return , carconfig = " + JSON.toJSONString(carConfig));
                        carconfigDataStatus = DATA_STATUS_CLOUD;
                        //临时添加deviceid
                        for (DeviceOperation dop : carConfig.getActionConfig()) {
                            for (SingleOperation so : dop.getSingleOperations()) {
                                so.setDeviceId(dop.getDeviceId());
                            }
                        }
                        for (DeviceOperation dop : carConfig.getConditionConfig()) {
                            for (SingleOperation so : dop.getSingleOperations()) {
                                so.setDeviceId(dop.getDeviceId());
                            }
                        }
                        for (DeviceOperation dop : carConfig.getStateConfig()) {
                            for (SingleOperation so : dop.getSingleOperations()) {
                                so.setDeviceId(dop.getDeviceId());
                            }
                        }

                        checkAndRefreshCarConfig(carConfig.getActionConfig(), ConstantModelValue.OPERATION_TYPE_ACTION);
                        checkAndRefreshCarConfig(carConfig.getConditionConfig(), ConstantModelValue.OPERATION_TYPE_CONDITION);
                        checkAndRefreshCarConfig(carConfig.getStateConfig(), ConstantModelValue.OPERATION_TYPE_STATE);
                        //logUtil.printJsonLogger(TAG, "initCarConfig reset data return , carconfig = " + JSON.toJSONString(carConfig));
                        pLimitOperation = new ArrayList<>();
                    }
                }).onErrorReturn(new Function<Throwable, CarConfig>() {
                    @Override
                    public CarConfig apply(Throwable throwable) throws Exception {
                        MLog.e(TAG, "requestCarConfig error" + throwable.getMessage());
                        throwable.printStackTrace();
                        if(mmkv.contains(ConstantModelValue.OPERATION_TYPE_ACTION)){
                            if(!ListUtil.isEmpty(getLocalConditionOperations()) && !ListUtil.isEmpty(getLocalActionOperations())
                            && !ListUtil.isEmpty(getLocalStateOperations())){
                                carconfigDataStatus = DATA_STATUS_LOCAL;
                                CarConfig carConfig = new CarConfig();
                                carConfig.setConditionConfig(getLocalConditionOperations());
                                carConfig.setActionConfig(getLocalActionOperations());
                                carConfig.setStateConfig(getLocalStateOperations());
                                return carConfig;
                            }else {
                                carconfigDataStatus = DATA_STATUS_NULL;
                                return null;
                            }
                        }else {
                            carconfigDataStatus = DATA_STATUS_NULL;
                            return null;
                        }
                    }
                }).doOnNext(new Consumer<CarConfig>() {
                    @Override
                    public void accept(CarConfig carConfig) throws Exception {
                        if(carConfig == null){
                            return;
                        }
                        initPLimitList(carConfig.getActionConfig());
                        initPLimitList(carConfig.getConditionConfig());
                        initPLimitList(carConfig.getStateConfig());
                        for (CarConfigInitListener listener : mCarConfigInitListeners) {
                            listener.onCarConfigInit(carConfig);
                        }
                    }
                });
    }


    /**
     * 初始遍历操作中的和P挡有关联的信息，操作id并存储备用
     *
     * @param operations
     */
    private void initPLimitList(List<DeviceOperation> operations) {
        operations.forEach(deviceOperation -> {
            deviceOperation.getSingleOperations().forEach(singleOperation -> {
                if ("true".equals(singleOperation.getArg(ConstantModelValue.GEAR_P_LIMIT))) {
                    pLimitOperation.add(singleOperation.getOperationId());
                }
            });
        });
    }

    /**
     * 检查传入操作id是否和p挡相关联
     *
     * @param operationId
     * @return
     */
    public boolean checkOperationPLimit(String operationId) {
        return pLimitOperation.contains(operationId);
    }

    /**
     * 检查传入操作id是否和p挡相关联
     *
     * @return
     */
    public boolean checkOperationPLimit(List<SettingOperation> settingOperations) {
        for(SettingOperation settingOperation : settingOperations){
            if(checkOperationPLimit(settingOperation.getOperationId())){
                return true;
            }
        }
        return false;
    }

    /**
     * 有车型差异的设备及操作，从后台返回数据后需本地再用customApi验证下
     * 每个操作的子操作也都需要检查
     * 如果检查出现修改则进行改正，如果检查出现删除则进行删除
     */
    public void checkAndRefreshCarConfig(List<DeviceOperation> operations, String type) {
//        for (DeviceOperation deviceOperation : operations) {
//            List<SingleOperation> tempList = new ArrayList<>(deviceOperation.getSingleOperations());
//            for (int i = 0; i < tempList.size(); i++) {
//                int resetResult = ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//
//                //对子操作进行更正
//                if (tempList.get(i).getViewType().equals(ConstantModelValue.VIEW_TYPE_LAYOUT_VERTICAL)) {
//                    List<SingleOperation> tempSubList = new ArrayList<>(tempList.get(i).getSubOperations());
//                    for (int j = 0; j < tempSubList.size(); j++) {
//                        int subResult = resetSingleConfig(tempSubList.get(j));
//                        if (subResult == ConstantModelValue.CONFIG_RESET_RESULT_NOCONFIG) {
//                            tempList.get(i).getSubOperations().remove(tempSubList.get(i));
//                        }
//                    }
//
//                    //如果子操作更正后为空，则需将主操作删除
//                    if (ListUtil.isEmpty(tempList.get(i).getSubOperations())) {
//                        resetResult = ConstantModelValue.CONFIG_RESET_RESULT_NOCONFIG;
//                    }
//                } else {
//                    resetResult = resetSingleConfig(tempList.get(i));
//                }
//
//                //如果主操作的更正结果的无此配置，则删除
//                if (resetResult == ConstantModelValue.CONFIG_RESET_RESULT_NOCONFIG) {
//                    deviceOperation.getSingleOperations().remove(tempList.get(i));
//                }
//            }
//        }

        //将更正后的操作保存本地
        switch (type) {
            case ConstantModelValue.OPERATION_TYPE_CONDITION:
                saveLocalConditionOperations(operations);
                break;
            case ConstantModelValue.OPERATION_TYPE_STATE:
                saveLocalStateOperations(operations);
                break;
            case ConstantModelValue.OPERATION_TYPE_ACTION:
                saveLocalActionOperations(operations);
                break;
            default:
                MLog.e(TAG, "invalid type, type = " + type);
                break;
        }
    }

    private int resetSingleConfig(SingleOperation singleOperation) {
        int moduleResetResult = LibraryManager.getInstance().getLibraryModel().resetSingleConfig(singleOperation);
        if (ConstantModelValue.CONFIG_RESET_RESULT_NORELATIVE != moduleResetResult) {
            return moduleResetResult;
        }
        CarConfigResetModel carConfigResetModel = SceneModelFactory.productCarConfigModel(singleOperation.getDeviceId(), singleOperation.getOperationId());
        if (carConfigResetModel != null) {
            return carConfigResetModel.resetSingleConfig(singleOperation);
        } else {
            return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
        }
    }


    /**
     * 获取触发条件列表
     *
     * @return
     */
    public Observable<List<DeviceOperation>> requestConditionOperations() {
        if (!ListUtil.isEmpty(getLocalConditionOperations())) {
            return Observable.just(getLocalConditionOperations());
        }
        return Observable.create(new ObservableOnSubscribe<List<DeviceOperation>>() {
            @Override
            public void subscribe(ObservableEmitter<List<DeviceOperation>> observableEmitter) throws Exception {

            }
        });
    }

    /**
     * 获取状态条件列表
     *
     * @return
     */
    public Observable<List<DeviceOperation>> requestStateOperations() {
        if (!ListUtil.isEmpty(getLocalStateOperations())) {
            return Observable.just(getLocalStateOperations());
        }
        return Observable.create(new ObservableOnSubscribe<List<DeviceOperation>>() {
            @Override
            public void subscribe(ObservableEmitter<List<DeviceOperation>> observableEmitter) throws Exception {

            }
        });
    }

    /**
     * 获取执行条件列表
     *
     * @return
     */
    public Observable<List<DeviceOperation>> requestActionOperations() {
        if (!ListUtil.isEmpty(getLocalActionOperations())) {
            return Observable.just(getLocalActionOperations());
        }
        return Observable.create(new ObservableOnSubscribe<List<DeviceOperation>>() {
            @Override
            public void subscribe(ObservableEmitter<List<DeviceOperation>> observableEmitter) throws Exception {

            }
        });
    }


    /**
     * 保存执行状态至本地
     *
     * @param operations
     */
    private void saveLocalActionOperations(List<DeviceOperation> operations) {
        actionOperations = operations;
        String actionSave = JSON.toJSONString(operations);
        LogUtil logUtil = new LogUtil();
        logUtil.printJsonLogger(TAG,"save localss : " + actionSave);
        mmkv.encode(ConstantModelValue.OPERATION_TYPE_ACTION, JSON.toJSONString(operations));
    }

    /**
     * 保存触发条件列表至本地
     *
     * @param operations
     */
    private void saveLocalConditionOperations(List<DeviceOperation> operations) {
        conditionOperations = operations;
        mmkv.encode(ConstantModelValue.OPERATION_TYPE_CONDITION, JSON.toJSONString(operations));
    }

    /**
     * 保存状态条件至本地
     *
     * @param operations
     */
    private void saveLocalStateOperations(List<DeviceOperation> operations) {
        stateOperations = operations;
        mmkv.encode(ConstantModelValue.OPERATION_TYPE_STATE, JSON.toJSONString(operations));
    }

    /**
     * 如果内存里有，返回内存数据
     * 如果本地有存，返回本地数据
     * 如果都没有，请求网络数据
     *
     * @return
     */
    public List<DeviceOperation> getLocalConditionOperations() {
        if (!ListUtil.isEmpty(conditionOperations)) {
            return conditionOperations;
        }
        conditionOperations = JSON.parseObject(mmkv.decodeString(ConstantModelValue.OPERATION_TYPE_CONDITION), new TypeReference<List<DeviceOperation>>() {
        });
        return conditionOperations != null ? conditionOperations : new ArrayList<>();
    }

    /**
     * 如果内存里有，返回内存数据
     * 如果本地有存，返回本地数据
     * 如果都没有，请求网络数据
     *
     * @return
     */
    public List<DeviceOperation> getLocalStateOperations() {
        if (!ListUtil.isEmpty(stateOperations)) {
            return stateOperations;
        }
        stateOperations = JSON.parseObject(mmkv.decodeString(ConstantModelValue.OPERATION_TYPE_STATE), new TypeReference<List<DeviceOperation>>() {
        });
        return stateOperations != null ? stateOperations : new ArrayList<>();
    }

    /**
     * 如果内存里有，返回内存数据
     * 如果本地有存，返回本地数据
     * 如果都没有，请求网络数据
     *
     * @return
     */
    public List<DeviceOperation> getLocalActionOperations() {
        if (!ListUtil.isEmpty(actionOperations)) {
            return actionOperations;
        }

        LogUtil logUtil = new LogUtil();
        logUtil.printJsonLogger(TAG,"get localss:" + mmkv.decodeString(ConstantModelValue.OPERATION_TYPE_ACTION));
        actionOperations = JSON.parseObject(mmkv.decodeString(ConstantModelValue.OPERATION_TYPE_ACTION), new TypeReference<List<DeviceOperation>>() {
        });
        return actionOperations != null ? actionOperations : new ArrayList<>();
    }

//    public String getOperationDesc(SettingOperation settingOperation){
//        SingleOperation singleOperation = findSingleOperationById(settingOperation.getOperationId());
//        if(singleOperation == null){
//            return null;
//        }
//        String desc = singleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
//        for(String string : settingOperation.getListArgs()){
//            int pos = singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST).indexOf(string);
//            desc.replaceFirst("&",)
//        }
//    }

}
