package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.OperationActionEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface OperationActionDao {

    @Insert
    void insertStudent(OperationActionEntity item);

    @Delete
    void deleteStudent(OperationActionEntity item);

    @Update
    void updateStudent(OperationActionEntity item);


    @Query("SELECT * FROM OPERATION_ACTION")
    Observable<List<OperationActionEntity>> queryAll();

    @Query("SELECT * FROM OPERATION_ACTION WHERE :id='' or id= :id")
    Observable<List<OperationActionEntity>> queryById(String id);

    @Query("SELECT * FROM OPERATION_ACTION WHERE id in (:ids)")
    Observable<List<OperationActionEntity>> queryByIds(String[] ids);
}
