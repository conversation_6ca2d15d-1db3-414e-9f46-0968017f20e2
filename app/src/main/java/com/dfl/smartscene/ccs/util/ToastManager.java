package com.dfl.smartscene.ccs.util;

import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.toast.ToastUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ImageBase;

/**
 * Toast工具类
 */
public class ToastManager {
    private static final String TAG = "ToastManager";
    private Context mContext = null;

    private ToastManager() {
    }

    private static class SingletonHolder{
        private static final ToastManager sToastManager = new ToastManager();
    }

    public static ToastManager getInstance(){
        return SingletonHolder.sToastManager;
    }

    /**
     * 为了保证Toast内容在Activity显示区的居中，务必传入Activity的上下文
     * @param context 传入Activity的上下文
     */
    public static void init(Context context){
        getInstance().mContext = context;
    }

    /**
     * 打开一个只有文言的toast
     * @param text Toast文言内容
     * @param time 显示时长（两行文言 Long：3.5s，一行文言 Short：2.5s）
     * @param isImportant 是否重要
     *           重要：true（不会被其他toast替换，一定会显示完完整的时间。）
     *           不重要：false (Toast在显示时若出现其他Toast显示需求,则会被立即替代)
     */
    public void showToast(CharSequence text, int time, Boolean isImportant){
        MLog.d(TAG, "showToast text");

        new ToastUtil.ToastUtilBuilder(text)
                .setNotImportant(isImportant)
                .setContext(mContext)
                .setDuration(time)
                .setClickable(false)
                .build().showToast();
    }

    /**
     * 打开一个icon + 文言的toast
     * @param stringID Toast文言id
     * @param time 显示时长（两行文言 Long：3.5s，一行文言 Short：2.5s）
     * @param isImportant 是否重要
     *           重要：true（不会被其他toast替换，一定会显示完完整的时间。）
     *           不重要：false (Toast在显示时若出现其他Toast显示需求,则会被立即替代)
     */
    public void showToast(int icon, int stringID, int time, Boolean isImportant){
        MLog.d(TAG, "showToast icon + text");

        LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(R.layout.common_toast_icon_text,null);

        TextView tv = layout.findViewById(R.id.message);
        String text = mContext.getString(stringID);
        tv.setText(text);

        ImageBase iv = layout.findViewById(R.id.toast_icon);
        iv.setImageString(icon);

        new ToastUtil.ToastUtilBuilder(text)
                .setNotImportant(isImportant)
                .setDuration(time)
                .setClickable(false)
                .setView(layout)
                .build().showToast();
    }


    public static SpannableString getSpanString(String text, String span, ClickableSpan clickListener){
        SpannableString toast = new SpannableString(text);
        int startIndex = text.indexOf(span);
        int endIndex = startIndex + span.length();
        toast.setSpan(clickListener,startIndex,endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        toast.setSpan(new ForegroundColorSpan(ScenePatternApp.getInstance().getColor(R.color.color_text_selected)),startIndex,endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);

        return toast;
    }

    public static void showSpanToast(String text, String span, ClickableSpan clickListener,View view){
        SpannableString toast = getSpanString(text, span, clickListener);
        if(view == null){
            new ToastUtil.ToastUtilBuilder(toast).setClickable(true).build().showToast();
        }else {
            new ToastUtil.ToastUtilBuilder(toast).setClickable(true).build().showToast(view);

        }
    }
    public static void showSpanToast(String text, String span, ClickableSpan clickListener){
        showSpanToast(text, span, clickListener,null);
    }

    public static void showToast(CharSequence message){
        (new ToastUtil.ToastUtilBuilder(message)).setContext(ActivityManager.getInstance().getCurrentActivity()).setClickable(false).build().showToast();
    }
}