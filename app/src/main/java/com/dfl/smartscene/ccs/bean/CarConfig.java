package com.dfl.smartscene.ccs.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/10 14:19
 * @description ：从后台返回的车辆配置表
 */
public class CarConfig {
    /**
     * 触发条件列表
     */
    private List<DeviceOperation> conditionConfig = new ArrayList<>();
    /**
     * 状态条件列表
     */
    private List<DeviceOperation> stateConfig = new ArrayList<>();
    /**
     * 动作条件列表
     */
    private List<DeviceOperation> actionConfig = new ArrayList<>();

    public List<DeviceOperation> getConditionConfig() {
        return conditionConfig;
    }

    public void setConditionConfig(List<DeviceOperation> conditionConfig) {
        this.conditionConfig = conditionConfig;
    }

    public List<DeviceOperation> getStateConfig() {
        return stateConfig;
    }

    public void setStateConfig(List<DeviceOperation> stateConfig) {
        this.stateConfig = stateConfig;
    }

    public List<DeviceOperation> getActionConfig() {
        return actionConfig;
    }

    public void setActionConfig(List<DeviceOperation> actionConfig) {
        this.actionConfig = actionConfig;
    }
}
