package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.dfl.smartscene.ccs.db.entity.CarConfigOverviewEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface CarConfigOverviewDao {

    @Insert
    void insertStudent(CarConfigOverviewEntity carConfigOverviewEntity);

    @Delete
    void deleteStudent(CarConfigOverviewEntity carConfigOverviewEntity);

    @Update
    void updateStudent(CarConfigOverviewEntity carConfigOverviewEntity);

    @Query("SELECT * FROM car_config_overview")
    Observable<List<CarConfigOverviewEntity>> queryAll();

    @Query("SELECT * FROM car_config_overview WHERE :car_type='' or car_type= :car_type")
    Observable<List<CarConfigOverviewEntity>> queryByCarType(String car_type);

}
