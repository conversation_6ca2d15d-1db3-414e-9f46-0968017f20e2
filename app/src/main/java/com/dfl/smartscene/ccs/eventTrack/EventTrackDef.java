package com.dfl.smartscene.ccs.eventTrack;

public class EventTrackDef {
    //EventId
    public static final String MOD_EVENT_ID_01 = "v_smartscene_click";
    public static final String MOD_EVENT_ID_02 = "v_smartscene_entr";
    public static final String MOD_EVENT_ID_03 = "v_smartscene_bnt_click";
    public static final String MOD_EVENT_ID_04 = "v_smartscene_window";
    public static final String MOD_EVENT_ID_05 = "v_smartscene_window_bnt";
    public static final String MOD_EVENT_ID_06 = "v_smartscene_empower";
    public static final String MOD_EVENT_ID_07 = "v_smartscene_empower_bnt_click";

    //EventName
    public static final String MOD_Event_NAME_01 = "智慧场景首页点击";//智慧场景首页点击   （已舍弃：原因是无法区分是从应用池拉起还是语音拉起
    public static final String MOD_Event_NAME_02 = "智慧场景浏览";//智慧场景浏览
    public static final String MOD_Event_NAME_03 = "智慧场景按钮点击";//智慧场景按钮点击
    public static final String MOD_Event_NAME_04 = "浏览智慧场景弹窗";//浏览智慧场景弹窗
    public static final String MOD_Event_NAME_05 = "浏览智慧场景弹窗按钮点击";//浏览智慧场景弹窗按钮点击
    public static final String MOD_Event_NAME_06 = "智慧场景权限授权";//智慧场景权限授权   （已舍弃：权限弹窗采用共通模块
    public static final String MOD_Event_NAME_07 = "智慧场景权限授权点击";//智慧场景权限授权点击   （已舍弃:权限弹窗采用共通模块

    // eventType
    public static final String MOD_EVENT_TYPE_CLICK = "click"; //点击事件
    public static final String MOD_EVENT_TYPE_CUSTOM = "custom"; //自定义事件
    public static final String MOD_EVENT_TYPE_START = "start"; //启动事件
    public static final String MOD_EVENT_TYPE_POPUP = "popup"; //弹窗事件
    public static final String MOD_EVENT_TYPE_PV = "pv"; //浏览事件
}

