package com.dfl.smartscene.ccs.view.commonview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Environment;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.dfl.api.da.soundeffect.SoundEffectId;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.core.SceneStateListener;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.MediaModel;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.iauto.uibase.utils.MBeepManager;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ViewPagerAdapter;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> Zezheng
 * @date ：2023/3/10 9:41
 * @description ：舒压模式视频半屏播放和全屏播放页面的基类
 */
public abstract class ImageOrVideoBaseView extends FrameLayout implements SceneStateListener {
    private static final String TAG = ImageOrVideoBaseView.class.getSimpleName();

    protected ViewPager mViewPager;
    private ViewPagerAdapter mViewPagerAdapter;
    protected CardContainerMark mContainerMark;

    protected View mSlideLeftView,mSlideRightView;//可见即可说标签使用

    protected TextView mCountDownView;
    protected View mBtnPlay;
    protected View mBtnPause;
    private View mBtnStop;
    protected View mButtonLayout;

    protected List<View> mContainerViews;
    protected int mCheckPosition = 0;

    protected boolean isFullScreenStatus = true;//全屏下才做显示隐藏，服务态控件常显

    private View mVolumeLayout;
    private SeekBar mSeekBarVolume;

    private GestureDetector mGestureDetector;

    public ImageOrVideoBaseView(@NonNull Context context) {
        super(context);
        initView(context);
    }

    protected int getLayoutId(){
        return R.layout.fragment_scene_big_view;
    }
    @SuppressLint("ClickableViewAccessibility")
    protected void initView(Context context) {
        LayoutInflater.from(context).inflate(getLayoutId(), this, true);
        mCountDownView = findViewById(R.id.textview_scene_small_video_time);
        mBtnPlay = findViewById(R.id.textview_scene_small_video_resume);
        mBtnPause = findViewById(R.id.textview_scene_small_video_pause);
        mBtnStop = findViewById(R.id.textview_scene_small_video_stop);

        mButtonLayout = findViewById(R.id.layout_scene_small_video_part_view);

        mContainerMark = findViewById(R.id.mark_viewpager_small_view);
        mViewPager = findViewById(R.id.viewpager_scene_small_video);
        mSlideLeftView = findViewById(R.id.view_slide_left);
        mSlideRightView = findViewById(R.id.view_slide_right);

        // 按钮：播放
        mBtnPlay.setOnClickListener(v -> {
            SceneControlManager.getInstance().resumeScene(true);
            startImmersion();
        });

        // 按钮：暂停
        mBtnPause.setOnClickListener(v -> {
            SceneControlManager.getInstance().pauseScene(true);
            startImmersion();
        });

        // 按钮：退出
        mBtnStop.setOnClickListener(v -> {
            stopVideo();
        });

        ViewPagerUtil.reducedSlidingSensitivity(mViewPager,-1,60);
        mViewPager.setOffscreenPageLimit(1);

        mVolumeLayout = findViewById(R.id.linear_layout_volume_layout);
        mSeekBarVolume = findViewById(R.id.seekbar_scene_small_volume);
        mSeekBarVolume.setProgress(10);
        mGestureDetector = new GestureDetector(context, new SimpleOnGestureListenerImpl());
        mSeekBarVolume.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if(fromUser){
                    setVolume(progress);
                    showVolumeLayout(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        int status = RelieveStressPatternModel.getInstance().getRelaxStatus();
        if(status == 0){
            mBtnPlay.setVisibility(VISIBLE);
            mBtnPause.setVisibility(GONE);
        }else {
            mBtnPlay.setVisibility(GONE);
            mBtnPause.setVisibility(VISIBLE);
        }
    }
    protected abstract void onViewPagerScrollStateChanged(int state);

    protected abstract boolean isShow();
    public void setData(List<String> data,int initIndex) {

        if (data == null) {
            return;
        }

        String viewType = "video";

        List<String> resNames = data;
        int itemSize = resNames.size();
        mContainerViews = new ArrayList<>(itemSize);
        mViewPagerAdapter = new ViewPagerAdapter();
        mViewPagerAdapter.setViews(mContainerViews);
        MLog.d(TAG, "initVideoImageView, viewType = " + viewType + ", itemSize = " + itemSize + ",,,data = " + data);

        if ("image".equals(viewType)) {

            for (String imageName : resNames) {
                ImageView imageViewItem = new ImageView(getContext());
                imageViewItem.setScaleType(ImageView.ScaleType.FIT_XY);
                String imageUrl = getImageUrl(imageName);
                ImageUtil.loadImageUrlNoCorner(imageViewItem, imageUrl);

                mContainerViews.add(imageViewItem);
            }

            mViewPager.setAdapter(mViewPagerAdapter);
        } else if ("video".equals(viewType)) {
            //使用本地内置的视频资源
            List<String> rawName = new ArrayList<>();
            rawName.add(getContext().getExternalFilesDir(Environment.DIRECTORY_MOVIES).getPath() + "/video/lizi.mp4");
            rawName.add(getContext().getExternalFilesDir(Environment.DIRECTORY_MOVIES).getPath() + "/video/shenkong.mp4");
            rawName.add(getContext().getExternalFilesDir(Environment.DIRECTORY_MOVIES).getPath() + "/video/yunhai.mp4");
            for (int i = 0; i < rawName.size(); i++) {
                String videoName = rawName.get(i);

                CustomVideoHolder videoViewItem = new CustomVideoHolder(getContext());
                videoViewItem.setTag(i);
                videoViewItem.setVideoPath(Uri.parse(videoName));
                videoViewItem.setOnPreparedListener(new PrepareListener());
                mContainerViews.add(videoViewItem);
            }
            itemSize = rawName.size();
            mViewPager.setAdapter(mViewPagerAdapter);
        }

        mContainerMark.setVisibility(itemSize > 1 ? View.VISIBLE : View.GONE);
        mContainerMark.setCount(itemSize);

        if(initIndex > 0){
            setViewPagerIndex(initIndex);
            mContainerMark.setPosition(initIndex);
        }

        mViewPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {

            @Override
            public void onPageSelected(int position) {
                mCheckPosition = position;
                DAModel.getInstance().getVideoController().setPlayIndex(mCheckPosition);
                mContainerMark.setPosition(position);
                if(SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_START && isShow()){
                    MediaModel.getInstance().playFullNext();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if(SceneControlManager.getInstance().getSceneController().getsState() == SceneController.STATE_IDLE){
                    return;
                }
                onViewPagerScrollStateChanged(state);
            }
        });
        mSlideLeftView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                int currentItem = mViewPager.getCurrentItem();
                if(currentItem > 0){
                    mViewPager.setCurrentItem(currentItem -1,true);
                }
            }
        });
        mSlideRightView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                int currentItem = mViewPager.getCurrentItem();
                if(currentItem +1 < mViewPager.getChildCount()){
                    mViewPager.setCurrentItem(currentItem +1,true);
                }
            }
        });

    }

    private static class PrepareListener implements IMediaPlayer.OnPreparedListener{
        @Override
        public void onPrepared(IMediaPlayer mp) {
            mp.setLooping(true);

            MLog.d(TAG, "onPrepared!!!");
        }
    }

    protected void startVideo() {
        if(!ListUtil.isEmpty(mContainerViews)){
            View view = mContainerViews.get(DAModel.getInstance().getVideoController().getPlayIndex());
            if (view instanceof CustomVideoHolder) {
                ((CustomVideoHolder) view).start();
            }
        }

        if (mBtnPlay != null) {
            mBtnPlay.setVisibility(GONE);
        }
        if (mBtnPause != null) {
            mBtnPause.setVisibility(VISIBLE);
        }
        RelieveStressPatternModel.getInstance().setRelaxStatus(1);
    }

    protected void resumeVideo() {
        if (null == mViewPager || null == mContainerViews) {
            return;
        }

        int playIndex = DAModel.getInstance().getVideoController().getPlayIndex();
        int currentItem = mViewPager.getCurrentItem();
        if (playIndex != currentItem) {
            mViewPager.setCurrentItem(playIndex,false);
            currentItem = playIndex;
        }

        if (ListUtil.checkIndex(mContainerViews, currentItem)) {
            View view = mContainerViews.get(currentItem);
            if (view instanceof CustomVideoHolder) {
                ((CustomVideoHolder) view).seekTo(DAModel.getInstance().getVideoController().getPlayProgress());
                ((CustomVideoHolder) view).start();
            }
        }

        if (mBtnPlay != null) {
            mBtnPlay.setVisibility(GONE);
        }
        if (mBtnPause != null) {
            mBtnPause.setVisibility(VISIBLE);
        }
        RelieveStressPatternModel.getInstance().setRelaxStatus(1);
    }

    protected void setViewPagerIndex(int index){
        int playIndex = index;
        int currentItem = mViewPager.getCurrentItem();
        if (playIndex != currentItem) {
            mViewPager.setCurrentItem(playIndex,false);
        }
    }

    protected void stopVideo() {
        pauseVideo();
        SceneControlManager.getInstance().stopScene();
        RelieveStressPatternModel.getInstance().setRelaxStatus(0);
    }

    protected void pauseVideo() {

        if (null == mViewPager || null == mContainerViews) {
            return;
        }

        int currentItem = mViewPager.getCurrentItem();
        if (ListUtil.checkIndex(mContainerViews, currentItem)) {
            View view = mContainerViews.get(currentItem);
            if (view instanceof CustomVideoHolder) {
                ((CustomVideoHolder) view).pause();
            }
        }

        if (mBtnPlay != null) {
            mBtnPlay.setVisibility(VISIBLE);
        }
        if (mBtnPause != null) {
            mBtnPause.setVisibility(GONE);
        }
        RelieveStressPatternModel.getInstance().setRelaxStatus(0);
    }

    protected int getCurrentPosition() {
        if (null == mViewPager || null == mContainerViews) {
            return 0;
        }

        int currentItem = mViewPager.getCurrentItem();
        if (ListUtil.checkIndex(mContainerViews, currentItem)) {
            View view = mContainerViews.get(currentItem);
            if (view instanceof CustomVideoHolder) {
                LogUtil.d(TAG,"getCurrentPosition :" + ((CustomVideoHolder) view).getCurrentPosition());
                return ((CustomVideoHolder) view).getCurrentPosition();
            }
        }
        return 0;
    }


    @Override
    public void onScenePrepare() {

    }

    @Override
    public void onSceneStart() {
        //才注册触控监听
        if(null != mViewPager){
            mViewPager.setOnTouchListener(mOnTouchListener);
        }
    }

    @Override
    public void onSceneStop() {
        LogUtil.d(TAG, "on scene stop");
        pauseVideo();
    }

    @Override
    public void onScenePause() {
        pauseVideo();
    }

    // 接收倒计时消息
    @Subscribe
    public void sceneTimeListener(VideoTimeEvent videoTimeEvent) {
        LogUtil.d(TAG, "videoTimeEvent = " + videoTimeEvent.getLeftTime());
        if(videoTimeEvent.getLeftTime() == 0){
            stopVideo();
            mCountDownView.setVisibility(GONE);
            MBeepManager.getInstance().playCharmSoundEffect(SoundEffectId.EFFECT_GEN_NOTIFICATION_PROMPT);
            return;
        }
        String time = secondToMinuteSecond(videoTimeEvent.getLeftTime());
        if (mCountDownView != null) {
            mCountDownView.setVisibility(VISIBLE);
            mCountDownView.setText(time);
        }
    }

    @Subscribe
    public void onSceneFullViewListener(ImageOrVideoFullViewEvent event) {
        if(event.getIsFullState() == 1){
            onFullScreenStateChange(true);
        }else {
            onFullScreenStateChange(false);
        }
    }

    protected abstract void onFullScreenStateChange(boolean status);

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        RxBus.getDefault().register(this);
        SceneControlManager.getInstance().registerSceneStateListener(this);

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        LogUtil.d(TAG, "on detached from window");
        RxBus.getDefault().unRegister(this);
        SceneControlManager.getInstance().unregisterSceneStateListener(this);
//        AudioFocusManager.getInstance().abandonAudioFocus();
        if (mContainerViews != null) {
            for (View view : mContainerViews) {
                if (view instanceof CustomVideoHolder) {
                    ((CustomVideoHolder) view).suspend();
                }
            }
            mContainerViews.clear();
        }

    }

    protected abstract Uri getVideoUrl(String name);

    private String secondToMinuteSecond(long millSecond) {
        long minutes = millSecond / 1000 / 60 % 60;
        long seconds = millSecond / 1000 % 60;
        String stringMinutes = minutes < 10 ? "0" + minutes : "" + minutes;
        String stringSeconds = seconds < 10 ? "0" + seconds : "" + seconds;
        return stringMinutes + ":" + stringSeconds;
    }

    protected String getImageUrl(String name) {
        String s = name.contains(".") ? ConstantModelValue.RESOURCE_IMAGE_SMALL_URL + File.separator + name : ConstantModelValue.RESOURCE_IMAGE_SMALL_URL + File.separator + name + ".png";
        return s;
    }

    public boolean isFullScreen(){
        return DAModel.getInstance().getVideoController().isFullScreen();
    }

    public int getSceneStatus(){
        return SceneControlManager.getInstance().getSceneController().getsState();
    }


    Runnable mVolumeRunnable = () -> {
        if (mVolumeLayout != null) {
            mVolumeLayout.setVisibility(GONE);
        }
    };


    private void showVolumeLayout(int volume) {
        if (mVolumeLayout != null) {
            mVolumeLayout.setVisibility(VISIBLE);
        }

        if (mSeekBarVolume != null) {
            mSeekBarVolume.setProgress(volume);
        }
        HandlerUtil.getMainHandler().removeCallbacks(mVolumeRunnable);
        HandlerUtil.getMainHandler().postDelayed(mVolumeRunnable, 5000);
    }

    private void hideVolumeLayout() {
        HandlerUtil.getMainHandler().removeCallbacks(mVolumeRunnable);
        if (mVolumeLayout != null) {
            mVolumeLayout.setVisibility(GONE);
        }
    }

    /**
     * 手势监听及对逻辑处理
     * */
    private class SimpleOnGestureListenerImpl extends GestureDetector.SimpleOnGestureListener {
        @Override
        public boolean onDown(MotionEvent e) {
            // 退出沉浸态
            stopImmersion();
            return true;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            if (mButtonLayout != null && getSceneStatus() != SceneController.STATE_DESTROY && mContainerMark != null  && isFullScreenStatus)  {
                if (mContainerMark.getVisibility() != View.VISIBLE) {
                    mContainerMark.setVisibility(VISIBLE);
                }
                //触碰页面进入和退出沉浸态
                if (mButtonLayout.getVisibility() != View.VISIBLE) {
                    mButtonLayout.setVisibility(VISIBLE);
                }
            }
            stopImmersion();
            return false;
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            LogUtil.d(TAG,"onSingleTapUp : " + getSceneStatus());
            if (mButtonLayout != null && getSceneStatus() != SceneController.STATE_DESTROY && mContainerMark != null && isFullScreenStatus) {
                if (mContainerMark.getVisibility() == View.VISIBLE) {
                    mContainerMark.setVisibility(GONE);
                }else {
                    mContainerMark.setVisibility(VISIBLE);
                }
                //触碰页面进入和退出沉浸态
                if (mButtonLayout.getVisibility() == View.VISIBLE) {
                    mButtonLayout.setVisibility(GONE);
                }else {
                    mButtonLayout.setVisibility(VISIBLE);
                }
            }
            return super.onSingleTapUp(e);
        }

        @Override
        public void onLongPress(MotionEvent e) {

        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            stopImmersion();
            return super.onFling(e1, e2, velocityX, velocityY);
        }
    }

    /**
     * 由于设置频繁设置音量会导致视频卡顿，所以采用当相差5时再设置，以及松开手指时设置
     * 监听按下事件时记录
     */
    private int mTempVolume = 0;

    private void syncVolume(){
        mTempVolume = DAModel.getInstance().getMediaVolume();
    }

    public void setVolume(int volume){
        DAModel.getInstance().setMediaVolume(volume);
    }


    // 全屏模式：进入沉浸态
    protected void startImmersion() {
        if(isFullScreenStatus){
            HandlerUtil.getMainHandler().removeCallbacks(mImmersionRunnable);
            HandlerUtil.getMainHandler().postDelayed(mImmersionRunnable, 5000);
        }
    }

    // 全屏模式：退出沉浸态
    private void stopImmersion() {
        HandlerUtil.getMainHandler().removeCallbacks(mImmersionRunnable);
    }

    Runnable mImmersionRunnable = () -> {
        if (mContainerMark != null) {
            mContainerMark.setVisibility(GONE);
        }
        if (mButtonLayout != null) {
            mButtonLayout.setVisibility(GONE);
        }
    };

    @Override
    public void onSceneDestroy() {
        //释放声音焦点
        LogUtil.d(TAG, "on scene destroy");
    }

    private static final  int MAX_VOLUME = 40;
    public static final int FULL_SCREEN_MAX_DISTANCE = 480;//全屏高度2/3
    public static final int NO_FULL_SCREEN_MAX_DISTANCE = 392;//服务态高度2/3
    private float downY = 0;
    private float downX = 0;
    private float moveY = 0;
    private float moveX = 0;
    protected OnTouchListener mOnTouchListener = new OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            mGestureDetector.onTouchEvent(event);
            if(event.getAction() == MotionEvent.ACTION_UP){
                // 后续再倒数进入沉浸态
                if (mButtonLayout.getVisibility() == View.VISIBLE) {
                    startImmersion();
                }
            }
            else if(event.getAction() == MotionEvent.ACTION_DOWN){
                syncVolume();
                downY = event.getY();
                downX = event.getX();
            } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                moveY = event.getY() - downY;
                moveX = event.getX() - downX;
                int maxScreenDistance = isFullScreenStatus ? FULL_SCREEN_MAX_DISTANCE:NO_FULL_SCREEN_MAX_DISTANCE;
                if (Math.abs(moveY) > Math.abs(moveX)) {
                    float volumeChange = MAX_VOLUME * (Math.abs(moveY) / maxScreenDistance);
                    int newVolume = mTempVolume + (int) ((moveY < 0 ? 1 : -1) * volumeChange);
                    LogUtil.d(TAG, "onTouch: changed " + mTempVolume + " " + volumeChange + " " + newVolume);
                    int result = Math.max(0, Math.min(MAX_VOLUME, newVolume));
                    LogUtil.d(TAG, "adjustVolume: " + result);
                    setVolume(result);
                }

            }

            return false;
        }
    };
}
