package com.dfl.smartscene.ccs.http;

/**
 * <AUTHOR>
 * @date ：2023/2/27 15:42
 * @description ：用户上传接口中无网络下删除的场景
 */
public class UploadDelBean {
    String category;
    String sceneId;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public UploadDelBean(String category, String sceneId) {
        this.category = category;
        this.sceneId = sceneId;
    }
}
