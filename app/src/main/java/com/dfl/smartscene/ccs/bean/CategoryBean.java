package com.dfl.smartscene.ccs.bean;

import java.io.Serializable;
import java.util.Objects;

/**
 * 分类recyclerview 的adapter中持有的对象
 * @author: huangzezheng
 * @date: 2021/11/15
 */
public class CategoryBean implements Serializable {
    private String name;
    private String id;
    private String imageUrl;

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public CategoryBean(String id, String name) {
        this.name = name;
        this.id = id;
    }

    public CategoryBean(String name) {
        this.name = name;
    }

    public CategoryBean(String id, String name, String imageUrl) {
        this.name = name;
        this.id = id;
        this.imageUrl = imageUrl;
    }

    public CategoryBean() {

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CategoryBean that = (CategoryBean) o;
        return id == that.id &&
                Objects.equals(name, that.name);
    }

}
