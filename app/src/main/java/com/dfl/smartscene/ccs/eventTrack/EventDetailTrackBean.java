package com.dfl.smartscene.ccs.eventTrack;

/**
 * 埋点详情数据
 */
public class EventDetailTrackBean {
    private String page_title;
    private String entry_source;
    private String smartscene_model;
    private String bnt_title;
    private String window_title;
    private String bnt_status;
    private String model_value;

    public String getModel_value() {
        return model_value;
    }

    public void setModel_value(String model_value) {
        this.model_value = model_value;
    }

    public String getPage_title() {
        return page_title;
    }

    public void setPage_title(String page_title) {
        this.page_title = page_title;
    }

    public String getEntry_source() {
        return entry_source;
    }

    public void setEntry_source(String entry_source) {
        this.entry_source = entry_source;
    }

    public String getSmartscene_model() {
        return smartscene_model;
    }

    public void setSmartscene_model(String smartscene_model) {
        this.smartscene_model = smartscene_model;
    }

    public String getBnt_title() {
        return bnt_title;
    }

    public void setBnt_title(String bnt_title) {
        this.bnt_title = bnt_title;
    }

    public String getWindow_title() {
        return window_title;
    }

    public void setWindow_title(String window_title) {
        this.window_title = window_title;
    }

    public String getBnt_status() {
        return bnt_status;
    }

    public void setBnt_status(String bnt_status) {
        this.bnt_status = bnt_status;
    }

    @Override
    public String toString() {
        return "EventDetailTrackBean{" +
                "page_title='" + page_title + '\'' +
                ", entry_source='" + entry_source + '\'' +
                ", smartscene_model='" + smartscene_model + '\'' +
                ", bnt_title='" + bnt_title + '\'' +
                ", window_title='" + window_title + '\'' +
                ", bnt_status='" + bnt_status + '\'' +
                '}';
    }
}
