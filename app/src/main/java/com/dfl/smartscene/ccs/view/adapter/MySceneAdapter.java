package com.dfl.smartscene.ccs.view.adapter;

import android.os.Handler;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.log.LogUtil;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.util.ExpandTouchArea;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.view.AnimManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.base.AdapterItemDrag;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/31 9:58
 * @description ：我的收藏场景展示的场景卡片
 */
public class MySceneAdapter extends BaseAdapter<SceneBean> implements AdapterItemDrag {
    private static final String TAG = "MySceneAdapter";
    private static final int VIEW_TYPE_NORMAL = 0;
    private static final int VIEW_TYPE_ADD = 1;

    private boolean editMode = false;

    private HashSet<String> selectSceneIds = new HashSet<>();

    public boolean isEditMode() {
        return editMode;
    }

    @Override
    protected BaseHolder<SceneBean> getViewHolder(View view, int viewType) {
        if (viewType == VIEW_TYPE_ADD) {
            return new MySceneAddHolder(view);
        } else {
            return new MySceneHolder(view);
        }
    }

    public boolean isAllSelected() {
        if (getItemCount() == 0) {
            return false;
        }
        return getDataList().size() == selectSceneIds.size();
    }

    public boolean isAllUnSelected() {
        if (getItemCount() == 0) {
            return true;
        }

        return selectSceneIds.size() == 0;
    }

    public void changeEditMode(boolean editMode) {
        if (this.editMode == editMode) {
            return;
        }
        this.editMode = editMode;
        if (!editMode) {
            selectSceneIds = new HashSet<>();
        }
        notifyDataSetChanged();
    }

    public void changeItemSelected(int pos) {
        if (!editMode) {
            return;
        }
        String sceneId = getItemData(pos).getSceneId();
        if (selectSceneIds.contains(sceneId)) {
            selectSceneIds.remove(sceneId);
        } else {
            selectSceneIds.add(sceneId);
        }
        notifyItemChanged(pos);
    }

    public void changeAllItemSelected(boolean status) {
        if (!editMode) {
            return;
        }
        if (status) {
            if (getDataList().size() == selectSceneIds.size()) {
                return;
            }
            getDataList().forEach(sceneBean -> selectSceneIds.add(sceneBean.getSceneId()));
        } else {
            if (selectSceneIds.size() == 0) {
                return;
            }
            selectSceneIds = new HashSet<>();
        }
        notifyDataSetChanged();
    }

    public List<SceneBean> getUnSelectedScenes() {
        List<SceneBean> sceneBeans = new ArrayList<>();
        getDataList().forEach(sceneBean -> {
            if (!selectSceneIds.contains(sceneBean.getSceneId())) {
                sceneBeans.add(sceneBean);
            }
        });
        return sceneBeans;
    }

    public List<String> getSelectedSceneIds() {
        List<String> sceneIds = new ArrayList<>();
        getDataList().forEach(sceneBean -> {
            if (selectSceneIds.contains(sceneBean.getSceneId())) {
                sceneIds.add(sceneBean.getSceneId());
            }
        });
        return sceneIds;
    }

    public List<SceneBean> getSelectedSceneBeans() {
        List<SceneBean> scenes = new ArrayList<>();
        getDataList().forEach(sceneBean -> {
            if (selectSceneIds.contains(sceneBean.getSceneId())) {
                scenes.add(sceneBean);
            }
        });
        return scenes;
    }

    public void hideAllDeleteButtons(RecyclerView recyclerView) {
        int childCount = recyclerView.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = recyclerView.getChildAt(i);
            RecyclerView.ViewHolder viewHolder = recyclerView.getChildViewHolder(child);

            if (viewHolder instanceof MySceneHolder) {
                MySceneHolder holder = (MySceneHolder) viewHolder;
                // 仅当 mDeleteScene 是可见时，更新可见性
                if (holder.isDeleteSceneVisible()) {
                    holder.updateDeleteAndMoreVisibility();
                }
            }
        }
    }

    public boolean checkItemAllSelected() {
        return getDataList().size() == selectSceneIds.size();
    }

    @Override
    protected int getLayoutId(int viewType) {
        if (viewType == VIEW_TYPE_ADD) {
            return R.layout.item_my_scene_add;
        } else {
            return R.layout.item_mine_scene;
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (getItemData(position).getSceneId().equals(ConstantModelValue.SCENE_ID_ADD)) {
            return VIEW_TYPE_ADD;
        } else {
            return VIEW_TYPE_NORMAL;
        }
    }

    @Override
    public void onItemMove(RecyclerView.ViewHolder source, RecyclerView.ViewHolder target) {
        int fromPosition = source.getAdapterPosition();
        int toPosition = target.getAdapterPosition();
        if (fromPosition < getDataList().size() && toPosition < getDataList().size()) {
            int tempSort;
            if(fromPosition < toPosition){
                for(int i = fromPosition ; i < toPosition ; i++){
                    //交换数据内容
                    Collections.swap(getDataList(), i, i+1);
                    //应云端需求，换序时改变sort值
                    tempSort = getDataList().get(i).getSort();
                    getItemData(i).setSort(getItemData(i+1).getSort());
                    getItemData(i+1).setSort(tempSort);
                }
            }else if(fromPosition > toPosition){
                for(int i = fromPosition ; i > toPosition ; i--){
                    //交换数据内容
                    Collections.swap(getDataList(), i, i-1);
                    //keep原位次的sort值,需要交换i和i-1位置的sort值
                    tempSort = getDataList().get(i).getSort();
                    getDataList().get(i).setSort(getItemData(i-1).getSort());
                    getItemData(i-1).setSort(tempSort);
                }
            }
            notifyItemMoved(fromPosition, toPosition);
        }
        MLog.d(TAG,"onItemMove");
    }

    @Override
    public void onItemClear(RecyclerView.ViewHolder source) {

    }

    class MySceneHolder extends BaseHolder<SceneBean> {

        private TextView mTextViewName;
        private ImageButton mImageButtonExcute;
        private ImageView mDeleteScene;
        private ImageView mMore;
        private CheckBox mCheckBoxEdit;
        private ImageView mImageViewIcon;
        private ImageView itemBg;

        private Animation rotateAnimation = AnimationUtils.loadAnimation(ScenePatternApp.getInstance(), R.anim.executing_rotate_anim);


        public MySceneHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_my_scene_name);
            mImageButtonExcute = itemView.findViewById(R.id.imagebutton_my_scene_excute);
            mDeleteScene = itemView.findViewById(R.id.imageButton_my_scene_delete);
            mMore = itemView.findViewById(R.id.image_item_my_scene_delete);
            mCheckBoxEdit = itemView.findViewById(R.id.checkbox_item_my_scene);
            mImageViewIcon = itemView.findViewById(R.id.imageview_item_my_scene_icon);
            itemBg = itemView.findViewById(R.id.item_mine_scene_bg);
            ExpandTouchArea.expandTouchArea(mImageButtonExcute, 20);
            rotateAnimation.setInterpolator(new LinearInterpolator());
            mImageButtonExcute.setOnClickListener(v -> {
                updateExecutingStatus(getAdapterPosition());
                ViewControlManager.executeScene(getDataList().get(getAdapterPosition()),"正在执行 ");
            });

            //点击三个点（更多按钮）显示删除按钮，4秒后没有操作则恢复原状
            Handler handler = new Handler();

            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    mMore.setVisibility(View.VISIBLE);
                    mDeleteScene.setVisibility(View.GONE);
                }
            };

            mMore.setOnClickListener(view -> {
                mMore.setVisibility(View.GONE);
                mDeleteScene.setVisibility(View.VISIBLE);
                // 4 秒（4000 毫秒）后执行 runnable
                handler.postDelayed(runnable, 4000);
            });

            //点击删除按钮
            mDeleteScene.setOnClickListener(view -> {
                handler.removeCallbacks(runnable);
                SceneBean sceneBean = getDataList().get(getAdapterPosition());
                    DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                        @Override
                        public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                            return v -> {
                                LogUtil.d(TAG, "getPositiveButtonListener 删除此场景");
                                SceneDataModel.getInstance().deleteUserScenes(sceneBean.getSceneCategoryId(), Collections.singletonList(sceneBean));
                                removeData(sceneBean);
                                libDialog.dismiss();
                            };
                        }

                        //点击取消按钮恢复卡片原状
                        public View.OnClickListener getNegativeButtonListener(LibDialog libDialog){
                            return v -> {
                                mMore.setVisibility(View.VISIBLE);
                                mDeleteScene.setVisibility(View.GONE);
                                libDialog.dismiss();
                            };
                        }

                        @Override
                        public String getDialogMessage() {
                            return "是否删除此场景";
                        }
                    });
            });


        }

        public boolean isDeleteSceneVisible() {
            return mDeleteScene.getVisibility() == View.VISIBLE;
        }

        public void updateDeleteAndMoreVisibility() {
                mDeleteScene.setVisibility(View.GONE);
                mMore.setVisibility(View.VISIBLE);
        }


        @Override
        public void setupData(SceneBean sceneBean, int position) {
            mDeleteScene.setVisibility(View.GONE);
            mTextViewName.setText(sceneBean.getSceneName());
            if (editMode) {
                mImageButtonExcute.setVisibility(View.GONE);
                mImageButtonExcute.setClickable(false);
                mCheckBoxEdit.setVisibility(View.VISIBLE);
                if (selectSceneIds.contains(sceneBean.getSceneId())) {
                    mCheckBoxEdit.setChecked(true);
                } else {
                    mCheckBoxEdit.setChecked(false);
                }
                AnimManager.startShakeAnim(itemView);
                //设置执行按钮的可见即可说
                mImageButtonExcute.setContentDescription(null);
            } else {
                mImageButtonExcute.setVisibility(View.VISIBLE);
                mImageButtonExcute.setClickable(true);

                mCheckBoxEdit.setVisibility(View.GONE);
                AnimManager.stopShakeAnim(itemView);
                //设置执行按钮的可见即可说
                mImageButtonExcute.setContentDescription(mImageButtonExcute.getResources().getString(R.string.string_visible_able_say_execute)+sceneBean.getSceneName());
            }
            if(sceneBean.isExecuteStatus()){
                mImageButtonExcute.setBackgroundResource(R.drawable.drawable_item_my_scene_executing);
                mImageButtonExcute.startAnimation(rotateAnimation);
                mMore.setVisibility(View.GONE);
            }else{
                mImageButtonExcute.clearAnimation();
                mImageButtonExcute.setBackgroundResource(R.drawable.drawable_item_my_scene_execute);
                mMore.setVisibility(View.VISIBLE);
            }


            //设置醒神模式，上班模式，回家模式的icon
//            if (sceneBean.getSceneIcon() != null){
                if(ConstantModelValue.SCENE_ID_OLD_SLEEP.equals(sceneBean.getSceneId())){
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_sleep_mode);
                } else if(ConstantModelValue.SCENE_ID_OLD_WAKE.equals(sceneBean.getSceneId())){
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_wake_mode);
                }else if (ConstantModelValue.SCENE_ID_OLD_WORK.equals(sceneBean.getSceneId())){
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_work_mode);
                }else if (ConstantModelValue.SCENE_ID_OLD_HOME.equals(sceneBean.getSceneId())){
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_home_mode);
                }else if (ConstantModelValue.SCENE_ID_OLD_CHILD.equals(sceneBean.getSceneId())){
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_qinzi_mode);
                }else{
                    ImageUtil.loadImageUrlNoCorner(mImageViewIcon,"",R.drawable.icon_edit_scene);
                }
//            }

                char lastNum = sceneBean.getSceneId().charAt(sceneBean.getSceneId().length() - 1);
                if (Character.isDigit(lastNum)){
                    switch (lastNum){
                        case '0':
                        case '1':
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_2);
                            break;
                        case '2':
                        case '3':
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_3);
                            break;
                        case '4':
                        case '5':
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_4);
                            break;
                        case '6':
                        case '7':
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_5);
                            break;
                        case '8':
                        case '9':
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_7);
                            break;
                        default:
                            itemBg.setImageResource(R.drawable.scene_bg_my_card_1);
                            break;
                    }
                }else {
                    //后台配置的
                    if(ConstantModelValue.SCENE_ID_OLD_SLEEP.equals(sceneBean.getSceneId())){
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_3);
                    } else if(ConstantModelValue.SCENE_ID_OLD_WAKE.equals(sceneBean.getSceneId())){
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_1);
                    }else if (ConstantModelValue.SCENE_ID_OLD_WORK.equals(sceneBean.getSceneId())){
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_2);
                    }else if (ConstantModelValue.SCENE_ID_OLD_HOME.equals(sceneBean.getSceneId())){
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_4);
                    }else if (ConstantModelValue.SCENE_ID_OLD_CHILD.equals(sceneBean.getSceneId())){
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_7);
                    }else{
                        itemBg.setImageResource(R.drawable.scene_bg_my_card_5);
                    }
                }

            LogUtil.d(TAG,"场景信息----" + sceneBean.getSceneId() + ",,," + sceneBean.getSceneName() + "..." + sceneBean.getSceneCategoryId());

        }

        private void updateExecutingStatus(int position){
           List<SceneBean> list =  getDataList();
            if (position < list.size()) {
                SceneBean sceneBean = list.get(position);
                sceneBean.setExecuteStatus(true);
                modifyData(position,sceneBean);
            }else {
                LogUtil.e(TAG, "updateExecutingStatus: index out of size");
            }
        }
    }

    class MySceneAddHolder extends BaseHolder<SceneBean> {

        private TextView mTextViewName;

        public MySceneAddHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_item_my_scene_add_title);
        }

        @Override
        public void setupData(SceneBean sceneBean, int position) {
            mTextViewName.setText(sceneBean.getSceneName());
        }
    }

}
