package com.dfl.smartscene.ccs.view.baseadapter;

import android.view.View;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.CategoryBean;
import com.dfl.smartscene.ccs.util.DescriptionUtil;


/**
 * 左侧分类页面左侧的分类adapter
 * @author: huangzezheng
 * @date: 2021/11/15
 */
public class ClassifyAdapter extends BaseAdapter<CategoryBean> {
    protected int mSelectedPos;
    @Override
    protected BaseHolder<CategoryBean> getViewHolder(View view, int viewType) {
        return new ClassifyHolder(view);
    }

    //选定分类，更新旧的被选定与新的被选定
    public void setSelectedPos(int selectedPos) {
        if(selectedPos != this.mSelectedPos){
            int oldPos = this.mSelectedPos;
            this.mSelectedPos = selectedPos;
            notifyItemChanged(oldPos);
            notifyItemChanged(selectedPos);
        }
    }

    public void setSelectedPosById(String id){
        if(getDataList() == null){
            return;
        }
        int newpos = 0;
        for(int i = 0 ; i < getItemCount() ; i ++){
            if(getItemData(i).getId().equals(id)){
                newpos = i;
                break;
            }
        }
        setSelectedPos(newpos);
    }

    public int getSelectedPos() {
        return mSelectedPos;
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_category_classify;
    }

    protected class ClassifyHolder extends BaseHolder<CategoryBean> {
        private TextView textViewClassify;

        public ClassifyHolder(View itemView) {
            super(itemView);
            textViewClassify = itemView.findViewById(R.id.textview_base_category_item_classify);
        }

        @Override
        public void setupData(CategoryBean categoryBean, int position) {
            //选定的展示不同UI
            if(position == mSelectedPos){
                textViewClassify.setTextColor(textViewClassify.getResources().getColor(R.color.color_text_selected));
                itemView.setBackgroundResource(R.drawable.bg_classify_item_selected);
            }else {
                textViewClassify.setTextColor(textViewClassify.getResources().getColor(R.color.color_text_secondary_label));
                itemView.setBackgroundResource(R.color.transparent);
            }
            textViewClassify.setText(categoryBean.getName());
            //设置标题的可见即可说
            DescriptionUtil.generateButtonDescriptionVisibleToSay(categoryBean.getName(), itemView);
        }
    }
}
