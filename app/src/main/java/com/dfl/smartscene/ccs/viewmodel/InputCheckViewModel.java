package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;


import com.dfl.smartscene.ccs.bean.InputCheckResult;
import com.dfl.smartscene.ccs.wrapper.HttpWrapper;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date ：2023/2/1 14:17
 * @description ：
 */
public class InputCheckViewModel extends ViewModel {
    private MutableLiveData<InputCheckResult> mInputCheckResultMutableLiveData = new MutableLiveData<>();
    // 防止内存泄漏
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    public MutableLiveData<InputCheckResult> getInputCheckResultMutableLiveData() {
        return mInputCheckResultMutableLiveData;
    }

    public void checkInput(String input){
        HttpWrapper.getInstance().checkInputLegal(input).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<InputCheckResult>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(InputCheckResult inputCheckResult) {
                mInputCheckResultMutableLiveData.setValue(inputCheckResult);
            }

            @Override
            public void onError(Throwable e) {
                mInputCheckResultMutableLiveData.setValue(new InputCheckResult(input,InputCheckResult.RESULT_NONETWORK));

            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        mCompositeDisposable.dispose();
    }
}
