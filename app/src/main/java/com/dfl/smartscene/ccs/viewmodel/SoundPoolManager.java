package com.dfl.smartscene.ccs.viewmodel;

import android.media.AudioAttributes;
import android.media.SoundPool;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

import java.util.concurrent.TimeUnit;

import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @description 提示音管理类，目前仅有舒压模式倒计时结束有这个
 * @date 2023/11/17
 * @memo
 */
public class SoundPoolManager {
    private SoundPool mSoundPool;
    private boolean mLoadComplete = false;
    private int mToDoPlayWork = 0;
    private volatile static SoundPoolManager sPoolManager;
    private static final String TAG = "SoundPoolManager";
    private int mSoundId;

    private SoundPoolManager() {
        initData();
    }

    public static SoundPoolManager getInstance(){
        if(null == sPoolManager){
            synchronized (SoundPoolManager.class){
                if(null == sPoolManager){
                    sPoolManager = new SoundPoolManager();
                }
            }
        }
        return sPoolManager;
    }

    /**
     * 初始化提示音数据
     * */
    private void initData(){
        LogUtil.d(TAG, "init data");
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage((int)(AudioAttributes.USAGE_NOTIFICATION))
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build();
        mSoundPool = new SoundPool.Builder().setMaxStreams(20).setAudioAttributes(audioAttributes).build();
        mSoundId = mSoundPool.load(ScenePatternApp.getInstance().getApplicationContext(), R.raw.scene_timer_finish, 1);
    }

    private void doPlayWork(){
        LogUtil.d(TAG, "do play work : "+mToDoPlayWork);
        if(mToDoPlayWork != 0){
            switch (mToDoPlayWork){
                case PLAY_WORK_TYPE.SCENE_FINISH_NOTIFICATION:
                    playSceneFinishNotification();
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 播放情景模式倒计时结束的提示音
     * */
    public void playSceneFinishNotification(){
        LogUtil.d(TAG, "play scene finish notification");
        mSoundPool.play(mSoundId, 1.0f, 1.0f, 1, 0, 1.0f);
        mToDoPlayWork = 0;
        //release();
    }

    private void release(){
        Schedulers.computation().scheduleDirect(() -> {
            mSoundPool.unload(R.raw.scene_timer_finish);
            mSoundPool.release();
        }, 2, TimeUnit.SECONDS);
    }

    private @interface PLAY_WORK_TYPE{
        int SCENE_FINISH_NOTIFICATION = 1;
    }
}
