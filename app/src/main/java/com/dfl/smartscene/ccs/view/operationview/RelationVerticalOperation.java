package com.dfl.smartscene.ccs.view.operationview;

import android.view.ViewGroup;

import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.CulculateUtil;
import com.iauto.uibase.utils.MLog;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/19 11:11
 * @description ：
 */
public class RelationVerticalOperation extends VerticalLayoutOperation{
    private static final String TAG = "RelationVerticalOperation";
    private String relationExpression;
    public RelationVerticalOperation(SingleOperation singleOperation, ViewGroup parent) {
        super(singleOperation, parent);
        relationExpression = singleOperation.getArg(ConstantModelValue.RELATED_DATA_CULCULATE);
    }

    @Override
    public List<String> extractArgs() {
        if(relationExpression == null){
            MLog.e(TAG,"RelationVerticalOperation have no relationExpression");
            return super.extractArgs();
        }
        List<String> arglists = super.extractArgs();
        for(String arg : arglists){
            relationExpression = relationExpression.replaceFirst("&",arg);
        }
        Double culculateResult = CulculateUtil.getResult(relationExpression);
        return Arrays.asList(String.valueOf(culculateResult == null ? "null" : culculateResult.intValue()));
    }
}
