package com.dfl.smartscene.ccs.view;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Lifecycle;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.util.PopupViewControl;
import com.dfl.smartscene.databinding.ToastMessageBinding;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MPopupViewBase;
import com.iauto.uicontrol.ButtonBase;

public class MessageToast extends MPopupViewBase {
    private static final String TAG = "MessageToast";
    private ToastMessageBinding mBinding;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private String mPopupId = "";
    private String mContent = "";
    private int mShowTime = 3000;
    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            PopupViewControl.getInstance().closePopup(mPopupId);
        }
    };

    public MessageToast(Context context) {
        super(context);
        MLog.d(TAG,"BTPhoneMessageToast");
    }

    public MessageToast(Context context, String popupId) {
        super(context);
        MLog.d(TAG,"BTPhoneMessageToast");
        mPopupId = popupId;
    }

    public void setParams(String text, int time) {
        mContent = text;
        mShowTime = time;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        MLog.d(TAG,"onCreate");

        mBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.toast_message, this, true);
        mBinding.setLifecycleOwner(this);
        initCCEventReceiver((ViewGroup) mBinding.getRoot());
        MLog.d(TAG,"onCreate end");
    }

    private void initCCEventReceiver(ViewGroup view) {
        for (int i = 0; i < view.getChildCount(); i++) {
            View childView = view.getChildAt(i);
            if (childView instanceof ButtonBase) {
                ButtonBase child = (ButtonBase) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ViewGroup) {
                initCCEventReceiver((ViewGroup) childView);
            }
        }
    }

    /**
     * attached to window event
     */
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        // 派发生命周期，如果不使用MVVM架构，可以不进行生命周期的派发
        performStatus(Lifecycle.Event.ON_RESUME);
    }

    /**
     * detached from window event
     */
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 派发生命周期，如果不使用MVVM架构，可以不进行生命周期的派发
        performStatus(Lifecycle.Event.ON_STOP);
    }

    @Override
    public void onStart() {
        super.onStart();
        MLog.d(TAG, "onStart");
        mBinding.textViewToastText.setText(mContent);
    }

    @Override
    public void onStop() {
        super.onStop();
        MLog.d(TAG, "onStop");
        mHandler.removeCallbacks(mRunnable);
    }

    @Override
    public void onResume() {
        super.onResume();
        MLog.d(TAG, "onResume");
        mHandler.postDelayed(mRunnable, mShowTime);
    }

    @Override
    public void onPause() {
        super.onPause();
        MLog.d(TAG, "onPause");
    }

    @Override
    public void onDestroy() {
        if (mBinding != null) {
            mBinding.unbind();
            mBinding = null;
        }
        super.onDestroy();
        MLog.d(TAG, "onDestroy: ");
    }
}