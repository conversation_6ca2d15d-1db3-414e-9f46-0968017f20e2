package com.dfl.smartscene.ccs.util;

import android.content.Context;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.dfl.dflcommonlibs.toast.ToastUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.view.ScenePatternMainActivity;
import com.dfl.smartscene.ccs.view.ScenePatternMenuFragment;
import com.dfl.smartscene.ccs.view.relievestress.RelieveStressPatternFragment;
import com.dfl.smartscene.ccs.view.surpriseegg.SurpriseEggListFragment;
import com.iauto.uibase.utils.MLog;

import java.util.Stack;

/*
 * ScenePatternViewManager class
 * 舒压模式view管理类
 * <AUTHOR>
 * @date 2022/5/24
 */
public class ScenePatternViewManager {
    private static final String                   TAG                      = "ScenePatternViewManager";
    private static       ScenePatternViewManager  mScenePatternViewManager = null;
    private ScenePatternMainActivity mActivity;
    private              Stack<String>            fragmentList              = new Stack();
    FragmentTransaction transaction;

    public Fragment mRelieveStressPatternFragment = null;
    public Fragment mScenePatternMenuFragment = null;
    public Fragment mSurpriseEggListFragment = null;

    /**
     * ScenePatternViewManager object constructor
     */
    public ScenePatternViewManager() {

    }

    /**
     * get ScenePatternViewManager object Instance
     */
    public static synchronized ScenePatternViewManager getInstance() {
        if (mScenePatternViewManager == null) {
            mScenePatternViewManager = new ScenePatternViewManager();
        }
        return mScenePatternViewManager;
    }

    /**
     * 取得情景模式的activity
     * @return ScenePatternMainActivity 情景模式的activity
     */
    public ScenePatternMainActivity getScenePatternActivity() {
        return mActivity;
    }

    /**
     * 设定情景模式的activity
     * @param activity 情景模式的activity
     */
    public void init(ScenePatternMainActivity activity) {
        mActivity = activity;

    }

    /**
     * app退出时调用，清空所有fragment，避免app启动闪烁
     *
     */
    public void resetFragmentManager(){
        removeAllFragment();
    }

    /**
     * app退出时调用，清空所有fragment，避免app启动闪烁
     *
     */
    private void removeAllFragment() {
        MLog.d(TAG, "removeAllFragment" );
        if (null == mActivity) {
            return;
        }

        if (fragmentList.isEmpty()) {
            return;
        }

        FragmentManager fragmentManager = mActivity.getSupportFragmentManager();
        if (fragmentManager.getFragments().isEmpty()) {
            return;
        }

        FragmentTransaction transaction = mActivity.getSupportFragmentManager().beginTransaction();

        while(!fragmentList.isEmpty()) {
            Fragment curFragment = null;
            switch (fragmentList.pop()) {
                case ScenePatternFuncDef.ScenePatternMenuFragment_TAG:
                    curFragment = mScenePatternMenuFragment;
                    mScenePatternMenuFragment = null;
                    break;
                case ScenePatternFuncDef.RelieveStressPatternFragment_TAG:
                    curFragment = mRelieveStressPatternFragment;
                    mRelieveStressPatternFragment = null;
                    break;
                case ScenePatternFuncDef.SurpriseEggListFragment_TAG:
                    curFragment = mSurpriseEggListFragment;
                    mSurpriseEggListFragment = null;
                    break;
                default:
                    break;
            }
            if(null != curFragment) {
                transaction.remove(curFragment);
            }
        }

        transaction.commit();
    }

    /**
     * remove当前表示的fragment
     *
     */
    private void removeCurrentFragment() {
        MLog.d(TAG, "removeCurrentFragment");
        if (null == mActivity) {
            return;
        }
        FragmentTransaction transaction = mActivity.getSupportFragmentManager().beginTransaction();
        Fragment curFragment = getCurFragment();
        //remove Current Fragment
        if (curFragment != null) {
            transaction.remove(curFragment);
        }
        transaction.commit();
    }

    /**
     * 返回前一个法让fragment或者退出  
     *
     */
    public void backWard() {
        MLog.d (TAG, "backWard " + fragmentList.size());

        if (fragmentList.size() > 1) {
            MLog.d (TAG, ">1 fragments ");
            String lastFragment = fragmentList.get(fragmentList.size() - 2);
            MLog.d(TAG, "backWard: lastFragment:"+lastFragment );
            mActivity.stopRelieveStressPattern();
            transFragment(lastFragment, ScenePatternFuncDef.TransType.ScenePattern_Back);
        } else if (fragmentList.size() == 1) {
            MLog.d (TAG, "=1 fragments ");
            mActivity.finishScenePattern();
        } else {
            MLog.e (TAG, "backWard error" );
        }
    }

    /**
     * 启动或重置情景模式的ActivityFragment
     * @param toFragment 初期启动的fragment
     */
    public void start_reset_ScenePatternActivityFragment(String toFragment ) {
        MLog.d(TAG, "start_reset_ScenePatternActivityFragment" );
        if (null == fragmentList) {
            fragmentList = new Stack();
        }
        if (fragmentList.isEmpty()) {
            transFragment(toFragment, ScenePatternFuncDef.TransType.ScenePattern_Top);
        } else {
            transFragment(fragmentList.peek(), ScenePatternFuncDef.TransType.ScenePattern_Reset);
        }
    }

    /**
     * 情景模式activity fragment迁移处理
     * @param toFragment 目标fragment
     * @param toFragment 迁移类型
     */
    public void transFragment(String toFragment, int type) {
        transFragment(toFragment, type, false);
    }
    /**
     * 情景模式activity fragment迁移处理
     * @param toFragment 目标fragment
     * @param toFragment 迁移类型
     */
    public void transFragment(String toFragment, int type, boolean isStart) {
        MLog.d(TAG, "transFragment " + toFragment + " transType: " + type+ " isStart: " + isStart);
        if (null == mActivity) {
            return;
        }
        transaction = mActivity.getSupportFragmentManager().beginTransaction();
        Fragment curFragment = getCurFragment();

        if (!fragmentList.isEmpty()) {
            MLog.d(TAG, "curFragment:" + fragmentList.peek());
        }
        MLog.d(TAG, "toFragment:"+toFragment);

        if (!fragmentList.isEmpty() && fragmentList.peek().equals(toFragment)) {
            MLog.d(TAG, "trans self");
            return;
        }

        //迁移到当前fragment的顶部
        if (ScenePatternFuncDef.TransType.ScenePattern_Top == type) {
            fragmentList.clear();
            MLog.d(TAG, "ScenePattern_Top");
            fragmentList.push(toFragment);

        //隐藏式迁移,当前fragment隐藏，目标表示
        }  else if (ScenePatternFuncDef.TransType.ScenePattern_Normal == type) {
            MLog.d(TAG, "ScenePattern_Normal");
            fragmentList.push(toFragment);

        //back迁移，当前fragment出栈，目标表示
        } else if(ScenePatternFuncDef.TransType.ScenePattern_Back == type){
            if (!fragmentList.isEmpty() && fragmentList.contains(toFragment)) {
                fragmentList.remove(toFragment);
            }
            MLog.d(TAG, "curFragment.getClass().getName()"+curFragment.getClass().getName());
            fragmentList.remove(fragmentList.peek());
            fragmentList.push(toFragment);
        }

        if (isStart) {
            transaction.setTransition(FragmentTransaction.TRANSIT_NONE);
        } else {
            if (ScenePatternFuncDef.TransType.ScenePattern_Top == type) {
                transaction.setTransition(FragmentTransaction.TRANSIT_NONE);
            } else {
                transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN);
            }
        }

        //隐藏当前fragment或从fragmentManagerlist中删除当前fragment
        if (curFragment != null) {
            if (ScenePatternFuncDef.TransType.ScenePattern_Reset != type){
                transaction.remove(curFragment);
            }else {
                //do nothing
            }
        }

        MLog.d(TAG, "transFragment " + fragmentList.peek() + " go ");
        //fragment迁移并表示
        switch (toFragment) {
            case ScenePatternFuncDef.ScenePatternMenuFragment_TAG:
                if (null == mScenePatternMenuFragment) {
                    MLog.d(TAG, "create ScenePatternMenuFragment");
                    mScenePatternMenuFragment = new ScenePatternMenuFragment();
                }

                transaction.replace(R.id.fragment_container, mScenePatternMenuFragment, ScenePatternFuncDef.ScenePatternMenuFragment_TAG);
                mActivity.setCloseButtonVisible(true);
                mActivity.setBackButtonVisible(false);
                break;

            case ScenePatternFuncDef.RelieveStressPatternFragment_TAG:
                if (null == mRelieveStressPatternFragment) {
                    MLog.d(TAG, "create RelieveStressPatternFragment");
                    mRelieveStressPatternFragment = new RelieveStressPatternFragment();
                }

                transaction.replace(R.id.fragment_container, mRelieveStressPatternFragment, ScenePatternFuncDef.RelieveStressPatternFragment_TAG);
                mActivity.setCloseButtonVisible(true);
                mActivity.setBackButtonVisible(true);
                break;

            case ScenePatternFuncDef.SurpriseEggListFragment_TAG:
                if (null == mSurpriseEggListFragment) {
                    mSurpriseEggListFragment = new SurpriseEggListFragment();
                }

                transaction.replace(R.id.fragment_container, mSurpriseEggListFragment, ScenePatternFuncDef.SurpriseEggListFragment_TAG);
                mActivity.setCloseButtonVisible(true);
                mActivity.setBackButtonVisible(true);
                break;

            default:
                break;
        }
        transaction.commit();
    }

    /**
     * 取得当前的fragment
     * @return Fragment 当前的fragment
     */
    public Fragment getCurFragment() {
        MLog.d(TAG, "ScenePatternViewManager:getCurFragment " );
        if (fragmentList.isEmpty()) {
            return null;
        }
        Fragment curFragment = null;
        switch (fragmentList.peek()) {
            case ScenePatternFuncDef.ScenePatternMenuFragment_TAG:
                curFragment = mScenePatternMenuFragment;
                break;
            case ScenePatternFuncDef.RelieveStressPatternFragment_TAG:
                curFragment = mRelieveStressPatternFragment;
                break;
            case ScenePatternFuncDef.SurpriseEggListFragment_TAG:
                curFragment = mSurpriseEggListFragment;
                break;
            default:
                break;
        }
        MLog.d(TAG, "ScenePatternViewManager:getCurFragment " + fragmentList.peek() );

        return curFragment;
    }

    public void clearFragmentList(){
        if(null != fragmentList && !fragmentList.isEmpty()) {
            fragmentList.clear();
        }
    }

    /**
     * 判断指定的fragment是否是顶部fragment
     * @param fragment 指定的fragment
     * @return boolean true 是 false 不是
     * 
     */
    public boolean isFragmentTop(String fragment) {
        MLog.d(TAG, "ScenePatternViewManager:isFragmentTop " + fragment);
        if (null == fragmentList || fragmentList.isEmpty()) {
            return false;
        }
        return fragmentList.peek().equals(fragment);
    }

    public void closeToast(FragmentManager manager,String Fragment) {
        MLog.d (TAG,"closeToast " + Fragment);
    }

    public void showToast(FragmentManager manager, int type, Context context) {
        MLog.d (TAG,"showToast " + type);
        // close current toast
        View mToastWithIcon;
        switch (type) {
            case SurpriseEggFuncDef.SURPRISE_EGG_DELETE_SUCCESS:
                mToastWithIcon = new ToastWithIcon(context ,R.drawable.ic48_system_check2_n,
                        R.string.string_surprise_egg_list_item_del_success);
                showToast(mToastWithIcon, R.string.string_surprise_egg_list_item_del_success);
                break;
            case SurpriseEggFuncDef.SURPRISE_EGG_DELETE_FAILED:
                mToastWithIcon = new ToastWithIcon(context ,R.drawable.ic48_system_delete2_n,
                        R.string.string_surprise_egg_list_item_del_fail);
                showToast(mToastWithIcon, R.string.string_surprise_egg_list_item_del_fail);
                break;
            default:
                break;
        }

    }

    public void showToast(View view, int strId){
        new ToastUtil.ToastUtilBuilder(view.getResources().getString(strId)).setContext(view.getContext()).setView(view).build().showToast();
    }
}

