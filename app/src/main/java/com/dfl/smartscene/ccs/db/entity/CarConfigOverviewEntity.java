package com.dfl.smartscene.ccs.db.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "car_config_overview")
public class CarConfigOverviewEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "car_type", typeAffinity = ColumnInfo.TEXT)
    public String car_type;

    @ColumnInfo(name = "version", typeAffinity = ColumnInfo.TEXT)
    public String version;

    @ColumnInfo(name = "app_min_version", typeAffinity = ColumnInfo.TEXT)
    public String app_min_version;

    @ColumnInfo(name = "change_log", typeAffinity = ColumnInfo.TEXT)
    public String change_log;

    public CarConfigOverviewEntity(int pId, String car_type, String version, String app_min_version, String change_log) {
        this.pId = pId;
        this.car_type = car_type;
        this.version = version;
        this.app_min_version = app_min_version;
        this.change_log = change_log;
    }

    @NonNull
    @Override
    public String toString() {
        return "CarConfigOverview{" +
                "pId=" + pId +
                ", car_type='" + car_type + '\'' +
                ", version='" + version + '\'' +
                ", app_min_version='" + app_min_version + '\'' +
                ", change_log='" + change_log + '\'' +
                '}';
    }
}
