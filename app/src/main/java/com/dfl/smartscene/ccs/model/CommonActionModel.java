package com.dfl.smartscene.ccs.model;

import com.alibaba.fastjson.JSON;
import com.dfl.api.base.IBaseAPI;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.iauto.uibase.utils.MLog;

import java.lang.reflect.Method;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/9 11:39
 * @description ：走customApi执行的model层
 */
public class CommonActionModel implements DeviceBaseModel {
    private static final String TAG = "CommonActionModel";

    private static volatile CommonActionModel sInstance;

    public static CommonActionModel getInstance(){
        if (null == sInstance){
            synchronized (CommonActionModel.class){
                if (null == sInstance){
                    sInstance = new CommonActionModel();
                }
            }
        }
        return sInstance;
    }


    /**
     * 执行操作
     * @param settingOperation
     */
    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        MLog.d(TAG,"dispatchSingleActionOperation , id = " + settingOperation.getOperationId());
        SingleOperation singleOperation = CarConfigManager.getInstance().findSingleOperationById(settingOperation.getOperationId());
        if(singleOperation == null){
            MLog.e(TAG,"this car has not the operation!!! id = " + settingOperation.getOperationId());
            return false;
        }
        if(singleOperation.getArgsMap() == null || singleOperation.getListArgsMap() == null || singleOperation.getDyadicListArgsMap() == null){
            return false;
        }

        if((singleOperation.getViewType().equals(ConstantModelValue.VIEW_TYPE_LAYOUT_VERTICAL) || (singleOperation.getViewType().equals(ConstantModelValue.VIEW_TYPE_SEAT_ADJUST))) && !ListUtil.isEmpty(singleOperation.getSubOperations())){
            if(singleOperation.getSubOperations().size() != settingOperation.getListArgs().size()){
                return false;
            }
            //包含多个子操作的特殊视图
            int timeDelay = 0;
            for(int i = 0 ; i < singleOperation.getSubOperations().size() ; i ++){
                int pos = i;
                HandlerUtil.getActionHandler().excuteDelay(()->excuteSingleOperation(singleOperation.getSubOperations().get(pos),settingOperation.getListArgs().get(pos)),timeDelay);
//                excuteSingleOperation(singleOperation.getSubOperations().get(i),settingOperation.getListArgs().get(i));
                timeDelay+=ConstantModelValue.DELAY_CUSOMAPI_SUB_OPE;
            }

        }else {
            excuteSingleOperation(singleOperation, ListUtil.isEmpty(settingOperation.getListArgs()) ? null : settingOperation.getListArgs().get(0));
        }
        return true;
    }

    /**
     * 根据车辆配置和场景执行参数执行动作
     * @param singleOperation
     * @param value
     */
    private void excuteSingleOperation(SingleOperation singleOperation , String value){
        if(value == null || value.startsWith("null")){
            return;
        }
        List<String> apiNames = singleOperation.getListArg(ConstantModelValue.API_NAME_LIST);
        List<String> methodNames = singleOperation.getListArg(ConstantModelValue.ACTION_METHOD_NAME_LIST);
        List<String> valuePoss = singleOperation.getListArg(ConstantModelValue.ACTION_DATA_POS_LIST);
        List<List<String>> paramTypeLists = singleOperation.getDyadicListArg(ConstantModelValue.DATA_TYPE_DYADIC_LIST);
        List<List<String>> defaultLists = singleOperation.getDyadicListArg(ConstantModelValue.DATA_DEFAULT_DYADIC_LIST);
        if(!ListUtil.isSameSize(apiNames,methodNames,valuePoss,paramTypeLists,defaultLists)){
            LogUtil.e(TAG,"excuteSingleOperation error:");
            LogUtil.e(TAG,"------apiNames size:" + (apiNames == null ? "null" : ""+apiNames.size()));
            LogUtil.e(TAG,"------methodNames size:" + (methodNames == null ? "null" : ""+methodNames.size()));
            LogUtil.e(TAG,"------valuePoss size:" + (valuePoss == null ? "null" : ""+valuePoss.size()));
            LogUtil.e(TAG,"------paramTypeLists size:" + (paramTypeLists == null ? "null" : ""+paramTypeLists.size()));
            LogUtil.e(TAG,"------defaultLists size:" + (defaultLists == null ? "null" : ""+defaultLists.size()));
            return;
        }

        int delayTime = 0;
        for(int i = 0 ; i < methodNames.size() ; i++){
            int pos = i;
//            Message message1 = Message.obtain(HandlerUtil.getMainHandler(),()->invokeMethod(apiNames.get(pos) , methodNames.get(pos), valuePoss.get(pos) , paramTypeLists.get(pos) , defaultLists.get(pos) ,value));
            HandlerUtil.getActionHandler().excuteDelay(()->invokeMethod(apiNames.get(pos) , methodNames.get(pos), valuePoss.get(pos) , paramTypeLists.get(pos) , defaultLists.get(pos) ,value),delayTime);
            delayTime += ConstantModelValue.DELAY_CUSOMAPI_SUB_OPE_METHOD;
        }
    }

    /**
     * 反射执行方法
     * @param apiName api名字
     * @param methodName 方法名字
     * @param valuePos 执行参数所在方法位置
     * @param paramTypes 参数类型
     * @param dataDefaultList 默认参数列表
     * @param value 执行参数
     */
    private void invokeMethod(String apiName,String methodName,String valuePos,List<String> paramTypes,List<String> dataDefaultList,String value){

        MLog.e(TAG,"invokeMethod apiName = " + apiName + ", methodName = " + methodName +" value "+value +" valuePos "+valuePos +" dataList " +dataDefaultList);
        if(apiName.equals("com_dfl_api_da_setting_settingsoundeffect_service_adapter")){
            apiName = "com_dfl_api_da_setting_settingsoundeffect_service";
        }
        if(methodName.equals("setAkamysEffectMode")){
            methodName = "setEffectMode";
        }
        IBaseAPI iBaseAPI = CustomApiManager.getInstance().getIBaseApi(apiName);
//        if(iBaseAPI == null){
//            MLog.e(TAG,"this api name is invalid!!! name = " + apiName);
//            return;
//        }
        if(methodName == null){
            MLog.e(TAG,"this method is invalid!!!");
            return;
        }

        if(iBaseAPI == null) {
            MLog.e(TAG,"this method is invalid! iBaseAPI = null");
            return;
        }

        try {
            if(ListUtil.isEmpty(paramTypes)){
                Method actionMethod = iBaseAPI.getClass().getMethod(methodName);
                actionMethod.invoke(iBaseAPI);
            }else {
                if(!ListUtil.isSameSize(dataDefaultList,paramTypes)){
                    MLog.e(TAG,"params size is not match!!!");
                    return;
                }
                Object[] methodParams = makeMethodParams(paramTypes, dataDefaultList, value, valuePos);
                MLog.d(TAG,"reflect custom api , method name = " + methodName + ", params = " + JSON.toJSONString(methodParams));

                if(apiName.equals("com_dfl_api_da_setting_settingsoundeffect_service") && methodName.equals("setEffectMode")){
                    Method actionMethod = iBaseAPI.getClass().getMethod(methodName, int.class,int.class);
                    actionMethod.invoke(iBaseAPI,0,Integer.parseInt(value));
                }else {
                    Method actionMethod = iBaseAPI.getClass().getMethod(methodName, DataTypeFormatUtil.string2ClassList(paramTypes));
                    actionMethod.invoke(iBaseAPI, methodParams);
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 根据配置表的参数和执行参数生成方法参数
     * @param paramtypes 如["int","int"]
     * @param defaultParams 如["3",&]
     * @param value 如"4"
     * @param pos 如 1
     * @return 如[3,4]
     */
    private Object[] makeMethodParams(List<String> paramtypes,List<String> defaultParams , String value , String pos){
        int valuePos = DataTypeFormatUtil.string2Int(pos);
        if(valuePos == ConstantModelValue.ERROR_FORMAT_INT_FLOAT_DOUBLE){
            MLog.e(TAG , "makeMethodParams pos is invalid !!! , pos = " + pos);
            return null;
        }
        Object[] resultArray = new Object[paramtypes.size()];
        for(int i = 0 ;i < paramtypes.size() ; i++){
            if(i == valuePos){
                resultArray[i] = DataTypeFormatUtil.string2Object(value , paramtypes.get(i));
            }else {
                resultArray[i] = DataTypeFormatUtil.string2Object(defaultParams.get(i) , paramtypes.get(i));
            }
        }
        return resultArray;
    }
}
