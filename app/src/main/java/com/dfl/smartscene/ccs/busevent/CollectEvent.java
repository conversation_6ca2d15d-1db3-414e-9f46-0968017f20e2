package com.dfl.smartscene.ccs.busevent;

/**
 * <AUTHOR>
 * @date ：2022/9/7 17:05
 * @description ：
 */
public class CollectEvent {
    //单个场景收藏状态刷新：表示收藏或取消收藏动作
    public static final String COLLECT_EVENT_TYPE_SINGLE = "COLLECT_EVENT_TYPE_SINGLE";
    //整个场景分类收藏状态刷新:表示请求收藏列表的动作
    public static final String COLLECT_EVENT_TYPE_ALL = "COLLECT_EVENT_TYPE_ALL";
    private String eventType;
    private String mCategoryId;
    private String mSceneId;
    /**收藏行为的分类，true为收藏，false为取消收藏*/
    private boolean status;

    public String getCategoryId() {
        return mCategoryId;
    }

    public void setCategoryId(String categoryId) {
        mCategoryId = categoryId;
    }

    public String getSceneId() {
        return mSceneId;
    }

    public void setSceneId(String sceneId) {
        mSceneId = sceneId;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public CollectEvent(String categoryId, String sceneId, boolean status) {
        this(COLLECT_EVENT_TYPE_ALL,categoryId,sceneId,status);
    }

    public CollectEvent(String eventType, String categoryId, String sceneId, boolean status) {
        this.eventType = eventType;
        mCategoryId = categoryId;
        mSceneId = sceneId;
        this.status = status;
    }

    public CollectEvent(String eventType, String categoryId) {
        this.eventType = eventType;
        mCategoryId = categoryId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
}
