package com.dfl.smartscene.ccs.view.base;

import android.app.Application;

import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.interrupt.NewScenePatternInterrupt;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.iauto.uibase.utils.MLog;


public class SceneSmallViewViewModel extends ViewModel {

    private static final String TAG = "SceneSmallViewViewModel";

    private volatile static SceneSmallViewViewModel sSceneSmallViewViewModel;

    private boolean haveObservers = false;

    public static SceneSmallViewViewModel getInstance(Application application) {
        if (sSceneSmallViewViewModel == null) {
            synchronized (SceneSmallViewViewModel.class) {
                if (null == sSceneSmallViewViewModel) {
                    sSceneSmallViewViewModel = new ViewModelProvider.AndroidViewModelFactory(application).create(SceneSmallViewViewModel.class);
                }
            }
        }
        return sSceneSmallViewViewModel;
    }
    public void addObservers() {
        MLog.d(TAG, "addObservers: ");
        if(NewScenePatternInterrupt.getInstance() == null){
            NewScenePatternInterrupt.create(ScenePatternApp.getInstance());
        }
        NewScenePatternInterrupt.getInstance().registerScenePatternInterruptListener(mScenePatternInterruptListener);
        haveObservers = true;
    }

    public void removeObservers() {
        MLog.d(TAG, "removeObservers: ");
        if(NewScenePatternInterrupt.getInstance() == null){
            NewScenePatternInterrupt.create(ScenePatternApp.getInstance());
        }
        NewScenePatternInterrupt.getInstance().unregisterScenePatternInterruptListener(mScenePatternInterruptListener);
        haveObservers = false;
    }


    @Override
    protected void onCleared() {
        super.onCleared();
        removeObservers();
    }

    NewScenePatternInterrupt.ScenePatternInterruptListener mScenePatternInterruptListener = interruptType -> {
        MLog.d(TAG, "onInterrupt: " + interruptType);

        switch (interruptType) {
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL:
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_VR:
                MLog.d(TAG, "onInterrupt:bluetooth call");
                // 进入暂停态
                SceneControlManager.getInstance().pauseScene(false);
                // 退出全屏
                if (DAModel.getInstance().getVideoController() != null && DAModel.getInstance().getVideoController().isFullScreen()) {
                    DAModel.getInstance().exitFullScreenVideo();
                }
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P:
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_ECALL:
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_INSTRUMENT_WARNING:
                MLog.d(TAG, "onInterrupt:other");
                // 进入暂停态
                SceneControlManager.getInstance().exitScene();
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE:
                MLog.d(TAG, "onInterrupt:source change");
                // 退出全屏
//                if (DAModel.getInstance().getVideoController() != null && DAModel.getInstance().getVideoController().isFullScreen()) {
//                    DAModel.getInstance().getVideoController().requestDetachViewFromSysWindow();
//                }
                break;
            case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE:
                MLog.d(TAG, "SCENE_PATTERN_INTERRUPT_NONE");
                SceneControlManager.getInstance().resumeScene(false);
                break;
            default:
                break;
        }
    };

    public boolean haveObservers() {
        return haveObservers;
    }
}
