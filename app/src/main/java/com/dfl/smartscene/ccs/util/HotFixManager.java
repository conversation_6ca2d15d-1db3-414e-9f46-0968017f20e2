package com.dfl.smartscene.ccs.util;

import android.content.Context;
import android.os.RemoteException;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;

import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.Constants;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.patchmanager.PatchManager;
import com.dfl.patchmanager.config.PatchConfig;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.iauto.uibase.utils.MLog;

/**
 * <AUTHOR>
 * @date 2022/11/11/16:03
 */
public class HotFixManager {
    private static final String TAG = "HotFixManager";
    private static final byte[] mApiLampLock = new byte[0];
    private static IAppCommon mIAppCommon = null;
    private static String daId = null;
    private static boolean mPatchHasInited = false;
    private static final MutableLiveData<Boolean> isInitLiveData = new MutableLiveData<>();
    public static void initHotFix(Context context) {
        MLog.d(TAG, "initHotFix... mIAppCommon = " + mIAppCommon);
        if (mIAppCommon != null) {
            initPatch(mIAppCommon);
            return;
        }
        BaseManager.create(context, Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String requestName, IBaseAPI iBaseAPI) {
                MLog.d(TAG, "initHotFix requestName = " + requestName + ", apiState = " + apiStateType);
                if (!Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE.equals(requestName)) {
                    return;
                }
                if (apiStateType.equals(APIStateType.APIState.SUCCESS) && null != iBaseAPI) {
                    synchronized (mApiLampLock) {
                        mIAppCommon = (IAppCommon) iBaseAPI;
                        initPatch(mIAppCommon);
                    }
                }
            }
            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {
            }
        });
    }
    private static void initPatch(IAppCommon appCommon) {
        try {
            daId = appCommon.getDAId();
        } catch (NullServiceException | RemoteException e) {
            MLog.e(TAG, "getDAId failed !!! " + e.getMessage());
        }
        if (TextUtils.isEmpty(daId)) {
            MLog.d(TAG, "daId isEmpty... ");
            return;
        }
        if (mPatchHasInited) {
            MLog.d(TAG, "PatchHasInited ... " + mPatchHasInited);
            return;
        }
        mPatchHasInited = true;
        //联友SDK配置,包含SDK初始化+tinker
        MLog.d(TAG, "PatchManager init... daId = " + daId);


        PatchManager.getInstance().init(ScenePatternApp.getScenePatternApp(), new PatchConfig() {
            @Override
            public String getDaId() {
                return daId;
            }
            @Override
            public String getDaVersion() {
                return "daversion";
            }
            @Override
            public String getAppKey() {
//                return "524b757d9f884f73abe0e962d4efa814";
                return "fa66884df1fd48528bfa62098e87e146";
            }

            @Override
            public boolean isDebug() {
                return true;
            }
        });
        isInitLiveData.postValue(true);
    }
    public static void checkAndApplyPatch(FragmentActivity context) {
        MLog.d(TAG, "PatchManager checkAndApplyPatch... ");
        isInitLiveData.observeForever(isInit -> {
            MLog.d(TAG, "isInitLiveData onChanged... isInit : " + isInit);
            if (isInit) {
                PatchManager.getInstance().checkAndApplyPatch(context);
            }
        });
    }
}
