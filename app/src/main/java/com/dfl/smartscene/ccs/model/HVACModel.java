package com.dfl.smartscene.ccs.model;

import com.alibaba.fastjson.JSON;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.vehicle.hvac.IHvac;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.iauto.uibase.utils.MLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/9 11:16
 * @description ：
 */
public class HVACModel implements DeviceBaseModel, CarConfigResetModel {

    private static final String TAG = "HVACModel";
    private static volatile HVACModel sInstance;

    public static HVACModel getInstance() {
        if (null == sInstance) {
            synchronized (HVACModel.class) {
                if (null == sInstance) {
                    sInstance = new HVACModel();
                }
            }
        }
        return sInstance;
    }


    @Override
    public int resetSingleConfig(SingleOperation singleOperation) {
//        switch (singleOperation.getOperationId()) {
//            case ConstantModelValue.OPERATION_ID_ACTION_HVAC_BLOW_MODE:
//                return resetFlowModeConfig(singleOperation);
//            default:
//                return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }
        return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;

    }

//    /**
//     * 根据车型差异重设空调吹风模式
//     * @param singleOperation
//     * @return
//     */
//    public int resetFlowModeConfig(SingleOperation singleOperation) {
//        int flowType = 0;
//        try {
//            flowType = CustomApiManager.getInstance().getIHvac().getBlowingModeSupportType();
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }catch (NoSuchMethodError e){
//            e.printStackTrace();
//            MLog.d(TAG,"customapi version error !!!");
//            return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }
//        List<String> descList = new ArrayList<>();
//        List<String> valueList = new ArrayList<>();
//        switch (flowType) {
//            case ConstantModelValue.CONFIG_HVAC_BLOW_MODE0:
//                return ConstantModelValue.CONFIG_RESET_RESULT_NOCONFIG;
//            case ConstantModelValue.CONFIG_HVAC_BLOW_MODE1:
//                descList.add("吹脸");
//                descList.add("吹脚");
//                descList.add("除雾");
//                descList.add("吹脸吹脚");
//                descList.add("吹脚除雾");
//
//                valueList.add("2");
//                valueList.add("1");
//                valueList.add("4");
//                valueList.add("3");
//                valueList.add("5");
//
//                singleOperation.getListArgsMap().put(ConstantModelValue.VIEW_DESC_TEXT_LIST,descList);
//                singleOperation.getListArgsMap().put(ConstantModelValue.DATA_DESC_LIST,valueList);
//                return ConstantModelValue.CONFIG_RESET_RESULT_RESET;
//            case ConstantModelValue.CONFIG_HVAC_BLOW_MODE2:
//                descList.add("吹脸");
//                descList.add("吹脚");
//                descList.add("除雾");
//                descList.add("吹脸除雾");
//
//                descList.add("吹脸吹脚");
//                descList.add("吹脚除雾");
//
//                descList.add("吹脸吹脚除雾");
//
//                valueList.add("2");
//                valueList.add("1");
//                valueList.add("4");
//                valueList.add("6");
//
//                valueList.add("3");
//                valueList.add("5");
//                valueList.add("7");
//
//                singleOperation.getListArgsMap().put(ConstantModelValue.VIEW_DESC_TEXT_LIST,descList);
//                singleOperation.getListArgsMap().put(ConstantModelValue.DATA_DESC_LIST,valueList);
//
//                return ConstantModelValue.CONFIG_RESET_RESULT_RESET;
//
//            case ConstantModelValue.CONFIG_HVAC_BLOW_MODE3:
//                descList.add("吹脸");
//                descList.add("吹脚");
//                descList.add("吹脸除雾");
//
//                descList.add("吹脸吹脚");
//                descList.add("吹脚除雾");
//
//                descList.add("吹脸吹脚除雾");
//
//                valueList.add("2");
//                valueList.add("1");
//                valueList.add("6");
//
//                valueList.add("3");
//                valueList.add("5");
//                valueList.add("7");
//
//                singleOperation.getListArgsMap().put(ConstantModelValue.VIEW_DESC_TEXT_LIST,descList);
//                singleOperation.getListArgsMap().put(ConstantModelValue.DATA_DESC_LIST,valueList);
//
//                return ConstantModelValue.CONFIG_RESET_RESULT_RESET;
//            default:
//                return ConstantModelValue.CONFIG_RESET_RESULT_NOCHANGE;
//        }
//    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        switch (settingOperation.getOperationId()) {
            case ConstantModelValue.OID_ACT_SEAT_DRIVER_HEAT_VENTILATE:
                return excuteHeatVentilateOpe(1, settingOperation.getListArgs());
            case ConstantModelValue.OID_ACT_SEAT_PASSAGER_HEAT_VENTILATE:
                return excuteHeatVentilateOpe(2, settingOperation.getListArgs());
            default:
                return CommonActionModel.getInstance().dispatchSingleActionOperation(settingOperation);
        }
    }

    /**
     * type: 1-通风  2-加热
     * lavel 0-关闭 1-1挡 2-2挡 3-3挡
     *
     * @param pos  1-主驾 2-副驾
     * @param args
     * @return
     */
    private boolean excuteHeatVentilateOpe(int pos, List<String> args) {
        if (args == null || args.size() != 2) {
            return false;
        }
        if (CustomApiManager.getInstance().getIHvac() == null) {
            return false;
        }
        IHvac iHvac = CustomApiManager.getInstance().getIHvac();
        int type = DataTypeFormatUtil.string2Int(args.get(0));
        int level = DataTypeFormatUtil.string2Int(args.get(1));
        try {
            if (type == 1) {
                switch (level) {
                    case 0:
                        iHvac.setSeatHeatingVentilating(pos, 1);
                        break;
                    case 1:
                        iHvac.setSeatHeatingVentilating(pos, 5);
                        break;
                    case 2:
                        iHvac.setSeatHeatingVentilating(pos, 6);
                        break;
                    case 3:
                        iHvac.setSeatHeatingVentilating(pos, 7);
                        break;
                }
            } else if (type == 2) {
                switch (level) {
                    case 0:
                        iHvac.setSeatHeatingVentilating(pos, 1);
                        break;
                    case 1:
                        iHvac.setSeatHeatingVentilating(pos, 2);
                        break;
                    case 2:
                        iHvac.setSeatHeatingVentilating(pos, 3);
                        break;
                    case 3:
                        iHvac.setSeatHeatingVentilating(pos, 4);
                        break;
                }

            }

        } catch (NullServiceException e) {
            LogUtil.e(TAG, e.getMessage());
        }
        return true;
    }

    /**
     * 执行前后除雾操作
     *
     * @param settingOperation
     */
    private void excuteDefogOperation(SettingOperation settingOperation) {
        MLog.d(TAG, "excuteDefogOperation ,action value = " + JSON.toJSONString(settingOperation.getListArgs()));
        if (CustomApiManager.getInstance().getIHvac() == null) {
            return;
        }
        if (ListUtil.checkIndex(settingOperation.getListArgs(), 1)) {
            try {
                Boolean value = DataTypeFormatUtil.string2Boolean(settingOperation.getListArgs().get(0));
                if (value != null) {
                    CustomApiManager.getInstance().getIHvac().setFrontDefrostingState(value);
                }
                value = DataTypeFormatUtil.string2Boolean(settingOperation.getListArgs().get(1));
                if (value != null) {
                    CustomApiManager.getInstance().getIHvac().setRearDefrostingState(value);
                }
            } catch (Exception e) {

            }
        }
    }

}
