package com.dfl.smartscene.ccs.model;

import com.dfl.commonscenelibrary.bean.SceneBean;
import com.dfl.commonscenelibrary.bean.SettingOperation;

/**
 * <AUTHOR>
 * @date ：2022/12/13 14:48
 * @description ：
 */
public class LibraryNaviModel {
    private static final String TAG = "NaviModel";

    public void initListener() {

    }

    public void registerSceneCondition(SceneBean sceneBean, SettingOperation settingOperation) {

    }

    public void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation settingOperation) {
    }

    public interface NaviCollectPlaceListener{
        void onCollectPlaceChange(String json);
    }

    public void registerNaviCollectPlacesCallback(NaviCollectPlaceListener naviCollectPlaceListener ){

    }

    public String getNaviCollectPlace(){
        return null;
    }

    /**
     * 判断是否满足条件
     *
     * @param settingOperation 状态条件及参数
     * @return
     */
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {

        return true;
    }

    /**
     * 执行相应的操作
     *
     * @param settingOperation
     */
    public void dispatchSingleActionOperation(SettingOperation settingOperation) {

    }

}
