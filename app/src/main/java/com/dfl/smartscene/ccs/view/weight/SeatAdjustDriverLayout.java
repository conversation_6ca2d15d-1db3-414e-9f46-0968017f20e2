package com.dfl.smartscene.ccs.view.weight;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.dfl.smartscene.R;

public class SeatAdjustDriverLayout extends ConstraintLayout {

    private static final int DRIVER_PIVOT_X = 143;
    private static final int DRIVER_PIVOT_Y = 293;
    private static final int ANIMATION_TIME = 100; // ms
    private static final int HANDLER_ACTION_TIME = 100; // ms



    private static final int DRIVER_ROTATION_MAX = 20;
    private static final int DRIVER_ROTATION_MIN = -40;
    private static final int DRIVER_TRANSFORM_X_MAX = 30;
    private static final int DRIVER_TRANSFORM_X_MIN = -30;
    private static final int DRIVER_TRANSFORM_Y_MAX = 7;
    private static final int DRIVER_TRANSFORM_Y_MIN = -7;

    private int mWholeFrontRearValue = 0;
    private int mWholeUpDownValue = 0;
    private int mBackFrontRearValue = 0;

    private static final int WHOLE_FRONTREAR_INTERVAL_VALUE = -7;
    private static final int WHOLE_UPDOWN_INTERVAL_VALUE = -30;
    private static final int BACK_FRONTREAR_INTERVAL_VALUE = -40;

    private Button mBtnBackFront;
    private Button mBtnBackRear;
    private Button mBtnWholeUp;
    private Button mBtnWholeDown;
    private Button mBtnWholeFront;
    private Button mBtnWholeRear;
    private View mLayoutViewSeat;
    private View mViewSeatBack;


    private Handler mHandler = new Handler(Looper.getMainLooper());


    public SeatAdjustDriverLayout(@NonNull Context context) {
        this(context, null);
    }

    public SeatAdjustDriverLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SeatAdjustDriverLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        initView(context);
    }


    // 设置当前座椅值
    public void setBackFrontRearValue(int backFrontRearValue) {
        mBackFrontRearValue = backFrontRearValue; // -40
        int BackFrontRearAnimValue = mBackFrontRearValue + BACK_FRONTREAR_INTERVAL_VALUE;


        if (null != mViewSeatBack) {
            float rotation = mViewSeatBack.getRotation();
            ObjectAnimator rotate = ObjectAnimator
                    .ofFloat(mViewSeatBack, "rotation", rotation, BackFrontRearAnimValue)
                    .setDuration(10);
            rotate.setInterpolator(new LinearInterpolator());
            rotate.start();
        }
    }

    public void setWholeFrontRearValue(int wholeFrontRearValue) {
        mWholeFrontRearValue = wholeFrontRearValue; // -30
        int WholeFrontRearAnimValue = mWholeFrontRearValue + WHOLE_FRONTREAR_INTERVAL_VALUE;
    }

    public void setWholeUpDownValue(int wholeUpDownValue) {
        mWholeUpDownValue = wholeUpDownValue; // -7
        int WholeUpDownAnimValue = mWholeUpDownValue + WHOLE_UPDOWN_INTERVAL_VALUE;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        if (null != mHandler) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
    }

    static class LongClickRunnable implements Runnable {

        private View view;

        public LongClickRunnable(View view) {
            this.view = view;
        }

        public boolean isRunState() {
            return null != view && view.isPressed();
        }

        public View getView() {
            return this.view;
        }

        @Override
        public void run() {

        }
    }

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.layout_seat_adjuset_driver, this);
        mLayoutViewSeat = findViewById(R.id.framelayout_layout_seat_adjust_driver_back_layout);
        mViewSeatBack = findViewById(R.id.iv_back_view);

        mBtnBackFront = findViewById(R.id.button_layout_seat_adjust_driver_back_front);
        mBtnBackRear = findViewById(R.id.button_layout_seat_adjust_driver_back_rear);
        mBtnWholeUp = findViewById(R.id.button_layout_seat_adjust_driver_whole_up);
        mBtnWholeDown = findViewById(R.id.button_layout_seat_adjust_driver_whole_down);
        mBtnWholeFront = findViewById(R.id.button_layout_seat_adjust_driver_whole_right);
        mBtnWholeRear = findViewById(R.id.button_layout_seat_adjust_driver_whole_left);

        mBtnBackFront.setOnClickListener(v -> {
            actionRotationFront();
        });

        mBtnBackFront.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionRotationFront();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });

        mBtnBackRear.setOnClickListener(v -> {
            actionRotationRear();
        });

        mBtnBackRear.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionRotationRear();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });

        mBtnWholeUp.setOnClickListener(v -> {
            actionTransformYUp();
        });

        mBtnWholeUp.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionTransformYUp();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });

        mBtnWholeDown.setOnClickListener(v -> {
            actionTransformYDown();
        });

        mBtnWholeDown.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionTransformYDown();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });

        mBtnWholeFront.setOnClickListener(v -> {
            actionTransformXFront();
        });

        mBtnWholeFront.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionTransformXFront();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });

        mBtnWholeRear.setOnClickListener(v -> {
            actionTransformXRear();
        });

        mBtnWholeRear.setOnLongClickListener(v -> {
            final LongClickRunnable runnable = new LongClickRunnable(v) {
                @Override
                public void run() {
                    if (isRunState()) {
                        actionTransformXRear();
                        mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                    } else {
                        mHandler.removeCallbacksAndMessages(null);
                    }
                }
            };
            mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);
            return true;
        });
    }


    private void actionRotationFront() {
        if (null != mViewSeatBack) {
            float fromRotation = mViewSeatBack.getRotation();
            float toRotation = fromRotation + 1;
            showRotation(mViewSeatBack, fromRotation, toRotation);
        }

        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onBackFrontRear(1);
        }
    }

    private void actionRotationRear() {
        if (null != mViewSeatBack) {
            float fromRotation = mViewSeatBack.getRotation();
            float toRotation = fromRotation - 1;
            showRotation(mViewSeatBack, fromRotation, toRotation);
        }
        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onBackFrontRear(0);
        }
    }

    private void actionTransformXFront() {
        if (null != mViewSeatBack) {
            float fromValue = mLayoutViewSeat.getTranslationX();
            float toValue = fromValue + 1;
            showTransformX(mLayoutViewSeat, fromValue, toValue);
        }

        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onWholeFrontRear(1);
        }
    }


    private void actionTransformXRear() {
        if (null != mViewSeatBack) {
            float fromValue = mLayoutViewSeat.getTranslationX();
            float toValue = fromValue - 1;
            showTransformX(mLayoutViewSeat, fromValue, toValue);
        }

        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onWholeFrontRear(0);
        }
    }

    private void actionTransformYUp() {
        if (null != mViewSeatBack) {
            float fromValue = mLayoutViewSeat.getTranslationY();
            float toValue = fromValue - 1;
            showTransformY(mLayoutViewSeat, fromValue, toValue);
        }

        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onWholeUpDown(1);
        }
    }


    private void actionTransformYDown() {
        if (null != mViewSeatBack) {
            float fromValue = mLayoutViewSeat.getTranslationY();
            float toValue = fromValue + 1;
            showTransformY(mLayoutViewSeat, fromValue, toValue);
        }

        if (null != mISeatDriverTouchListener) {
            mISeatDriverTouchListener.onWholeFrontRear(0);
        }
    }

    private void showRotation(View view, float fromRotation, float toRotation) {
        if (toRotation > DRIVER_ROTATION_MAX) {
            toRotation = DRIVER_ROTATION_MAX;
        }

        if (toRotation < DRIVER_ROTATION_MIN) {
            toRotation = DRIVER_ROTATION_MIN;
        }

        if (toRotation == fromRotation) {
            return;
        }

        ObjectAnimator rotate = ObjectAnimator
                .ofFloat(view, "rotation", fromRotation, toRotation)
                .setDuration(100);
        rotate.setInterpolator(new LinearInterpolator());
        rotate.start();
    }

    private void showTransformX(View view, float fromX, float toX) {
        if (toX > DRIVER_TRANSFORM_X_MAX) {
            toX = DRIVER_TRANSFORM_X_MAX;
        }

        if (toX < DRIVER_TRANSFORM_X_MIN) {
            toX = DRIVER_TRANSFORM_X_MIN;
        }

        if (toX == fromX) {
            return;
        }

        ObjectAnimator rotate = ObjectAnimator
                .ofFloat(view, "translationX", fromX, toX)
                .setDuration(100);
        rotate.setInterpolator(new LinearInterpolator());
        rotate.start();
    }

    private void showTransformY(View view, float fromValue, float toValue) {
        if (toValue > DRIVER_TRANSFORM_Y_MAX) {
            toValue = DRIVER_TRANSFORM_Y_MAX;
        }

        if (toValue < DRIVER_TRANSFORM_Y_MIN) {
            toValue = DRIVER_TRANSFORM_Y_MIN;
        }

        if (toValue == fromValue) {
            return;
        }

        ObjectAnimator rotate = ObjectAnimator
                .ofFloat(view, "translationY", fromValue, toValue)
                .setDuration(100);
        rotate.setInterpolator(new LinearInterpolator());
        rotate.start();
    }


    public interface ISeatDriverTouchListener {
        void onBackFrontRear(int frontRear);

        void onWholeFrontRear(int frontRear);

        void onWholeUpDown(int upDown);
    }

    private ISeatDriverTouchListener mISeatDriverTouchListener;

    public void setISeatDriverTouchListener(ISeatDriverTouchListener listener) {
        mISeatDriverTouchListener = listener;
    }


}
