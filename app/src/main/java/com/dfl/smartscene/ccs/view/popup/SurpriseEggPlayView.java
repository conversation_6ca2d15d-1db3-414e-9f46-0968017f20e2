package com.dfl.smartscene.ccs.view.popup;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.http.SslError;
import android.os.Looper;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.JsResult;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.RelativeLayout;

import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.scenepattern.R;
import com.dfl.scenepattern.constant.SurpriseEggFuncDef;
import com.dfl.scenepattern.databinding.ViewSurpriseEggPlayBinding;
import com.dfl.scenepattern.model.SurpriseEggListModel;
import com.dfl.scenepattern.util.HandlerThreadProcessor;
import com.dfl.scenepattern.util.SysViewControl;
import com.dfl.scenepattern.viewmodel.SurpriseEggPlayVM;
import com.google.gson.Gson;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MViewBase;
import com.iauto.uicontrol.ButtonView;

/**
 * 惊喜彩蛋播放fragment
 * <AUTHOR>
 * @date 2022/06/10
 */
public class SurpriseEggPlayView extends MViewBase {

    private static final String TAG = "SurpriseEggPlayView";

    private WebView mWebView;

    private SurpriseEggPlayVM mViewModel = null;

    protected TTSPlayStatusChangeObserver mTTSPlayStatusChange = new TTSPlayStatusChangeObserver();
    protected VoiceFilePlayStatusChangeObserver mVoiceFilePlayStatusChange = new VoiceFilePlayStatusChangeObserver();
    private String mURL = "";
    private SurpriseEggToJS mSurpriseEggToJS = null;

    private ButtonView mCloseButton;
    private HandlerThreadProcessor handlerThreadProcessor = HandlerThreadProcessor.getInstance();

    private Context mContext = null;
    private RelativeLayout mRelativeLayout = null;
    private ViewSurpriseEggPlayBinding binding = null;


    public SurpriseEggPlayView(Context context) {
        super(context);
        mContext = context;
        mSurpriseEggToJS = new SurpriseEggToJS();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        MLog.d(TAG, "onCreateView");
        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.view_surprise_egg_play, this, true);
        mViewModel = new ViewModelProvider(this).get(SurpriseEggPlayVM.class);
        binding.setVm(mViewModel);
        binding.setLifecycleOwner(this);

        mURL = mViewModel.getSurpriseEggURL().getValue();
        mRelativeLayout = (RelativeLayout) binding.getRoot().findViewById(R.id.layout_webview);

        mCloseButton = (ButtonView) binding.getRoot().findViewById(R.id.button_base_close);
        mCloseButton.setOnBtnClickListener(new BtnClickListener());

        MLog.d(TAG, "onCreateView end");
    }

    private static class BtnClickListener implements ButtonView.OnBtnClickListener {
        @Override
        public void onBtnClick(View view) {
            SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
        }
    }

    /*
     * TTS播放状态
     * <AUTHOR>
     * @date 2022/7/1
     */
    public class TTSPlayStatusChangeObserver implements Observer<Integer> {
        public void onChanged(Integer playStatus) {
            MLog.d(TAG, "TTSPlayStatusChangeObserver " + playStatus);
            if(mViewModel.getSurpriseEggVoiceType().getValue() == SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT){
                if (SurpriseEggFuncDef.TTS_Playback_Status_Stop == playStatus) {
                    mSurpriseEggToJS.SurpriseEggStop(2);
                }
            }
        }
    }

    /*
     * 录音文件播放状态
     * <AUTHOR>
     * @date 2022/7/1
     */
    public class VoiceFilePlayStatusChangeObserver implements Observer<Integer> {
        public void onChanged(Integer playStatus) {
            MLog.d(TAG, "VoiceFilePlayStatusChangeObserver " + playStatus);
            if(mViewModel.getSurpriseEggVoiceType().getValue() == SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_VOICE) {
                if (SurpriseEggFuncDef.VOICE_FILE_Playback_Status_Stop == playStatus) {
                    mSurpriseEggToJS.SurpriseEggStop(2);
                }
            }
        }
    }

    @Override
    public void onStop() {
        MLog.d(TAG, "onStop");
        super.onStop();
        clearWebView();
    }

    @Override
    public void onPause() {
        MLog.d(TAG, "onPause");
        super.onPause();
        mSurpriseEggToJS.resetEndFlag();
        mViewModel.setSurpriseEggPlaybackStatus(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Stop);
        removeObserver();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onStart() {
        MLog.d(TAG, "onStart");
        super.onStart();
        LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT,LayoutParams.MATCH_PARENT);
//        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        mWebView = new WebView(mContext);
        mWebView.setLayoutParams(layoutParams);
        mRelativeLayout.addView(mWebView);
        ((ViewGroup) binding.getRoot()).bringChildToFront(mCloseButton);
        initWebView();
    }

    @Override
    public void onResume() {
        MLog.d(TAG, "onResume");
        super.onResume();
        mViewModel.resetTTSPlayStatus();
        mViewModel.resetVoiceFilePlayStatus();
        addObserver();

        int width = mWebView.getWidth();
        int height = mWebView.getHeight();
        MLog.d(TAG, "width,height:"+width+ ","+height);

        int fragmentWidth = this.getWidth();
        int fragmentHeight = this.getHeight();
        MLog.d(TAG, "fragmentWidth,fragmentHeight:"+fragmentWidth+ ","+fragmentHeight);
    }

    private void clearWebView() {
        MLog.d(TAG, "clearWebView");
        if (Looper.myLooper() != Looper.getMainLooper()) {
            return;
        }
        mWebView.loadUrl("about:blank");
        mWebView.stopLoading();
        if (mWebView.getHandler() != null)
            mWebView.getHandler().removeCallbacksAndMessages(null);
        mWebView.removeAllViews();
        ViewGroup mViewGroup = null;
        if ((mViewGroup = ((ViewGroup) mWebView.getParent())) != null) {
            mViewGroup.removeView(mWebView);
        }
        mWebView.setWebChromeClient(null);
        mWebView.setWebViewClient(null);
        mWebView.setTag(null);
        mWebView.clearHistory();
        mWebView.destroy();
        mWebView = null;
    }

    /** 展示网页界面 **/
    public void initWebView() {
        MLog.d(TAG,"initWebView");
        WebSettings webSettings = mWebView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setDomStorageEnabled(true);

        webSettings.setLoadsImagesAutomatically(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT); // 不加载缓存内容
        webSettings.setDomStorageEnabled(true);

        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);

//        webSettings.setMediaPlaybackRequiresUserGesture(false);
//        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        mWebView.setWebViewClient(new MyWebViewClient1());
        mWebView.setWebChromeClient(new WebStaticChromeClient());
        mSurpriseEggToJS.resetEndFlag();
        mWebView.addJavascriptInterface(mSurpriseEggToJS, "SurpriseEgg");
        mURL = mViewModel.getSurpriseEggURL().getValue();
        MLog.d(TAG," mURL = " + mURL);
        mWebView.loadUrl(mURL);
    }

    private static final class WebStaticChromeClient extends WebChromeClient {
        @Override
        public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
            super.onJsAlert(view, url, message, result);
            MLog.d("onJsAlert"," h5页面执行Alert对话框了");

            result.cancel();
            return true;//屏蔽弹框
        }
    }

    public class SurpriseEggToJS extends Object {
        boolean mImagePlayStatus = false;
        boolean mVoicePlayStatus = false;

        @JavascriptInterface
        public void SurpriseEggPlay() {
            handlerThreadProcessor.postToMainThread(new Runnable() {
                @Override
                public void run() {
                    MLog.d(TAG, "Log_SurpriseEggPlay");
                    MLog.d(TAG, "Log_SurpriseEggPlay TTS:" + mViewModel.getSurpriseEggTTSText().getValue());
                    MLog.d(TAG, "Log_SurpriseEggPlay voice File:" + mViewModel.getSurpriseEggVoiceFile().getValue());
                    MLog.d(TAG, "Log_SurpriseEggPlay type:" + mViewModel.getSurpriseEggVoiceType().getValue());
                    if(mViewModel.getSurpriseEggVoiceType().getValue() ==  SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT){
                        if (!mViewModel.getSurpriseEggTTSText().getValue().isEmpty()) {
                            MLog.d(TAG, "TTS is not empty");
                            mViewModel.setSurpriseEggPlaybackStatus(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Play);
                        }else {
//                            mVoicePlayStatus = true;
                            mSurpriseEggToJS.SurpriseEggStop(2);
                        }
                    }else {
                        if (!mViewModel.getSurpriseEggVoiceFile().getValue().isEmpty()) {
                            MLog.d(TAG, "voce file  is not empty");
                            mViewModel.setSurpriseEggPlaybackStatus(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Play);
                        }else {
//                            mVoicePlayStatus = true;
                            mSurpriseEggToJS.SurpriseEggStop(2);
                        }
                    }
                }
            });
        }

        @JavascriptInterface
        public void SurpriseEggStop(int endType) {
            handlerThreadProcessor.postToMainThread(new Runnable() {
                @Override
                public void run() {
                    MLog.d(TAG,"SurpriseEggStop:"+endType);
                    if (1==endType) {
                        mImagePlayStatus = true;
                    } else if (2==endType) {
                        mVoicePlayStatus = true;
                    } else {
                        // do nothing
                    }
                    if(mVoicePlayStatus && mImagePlayStatus) {
                        mWebView.evaluateJavascript("javascript:SurpriseStop()", new ValueCallback<String>() {
                            @Override
                            public void onReceiveValue(String s) {
                                MLog.d(TAG,"onReceiveValue");
                                mViewModel.setSurpriseEggPlaybackStatus(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Stop);
                                SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
                                mVoicePlayStatus = false;
                                mImagePlayStatus = false;
                            }
                        });

                    }
                }
            });
        }

        public void resetEndFlag() {
            mImagePlayStatus = false;
            mVoicePlayStatus = false;
        }

        @JavascriptInterface
        public void requestToken(String appId) {
            handlerThreadProcessor.postToMainThread(new Runnable() {
                @Override
                public void run() {
                    MLog.d(TAG, "requestToken");

                    SurpriseEggListModel.getInstance().requestSurpriseEggToken(appId, new SurpriseEggListModel.SurpriseEggTokenListener() {
                        @Override
                        public void onGetTokenResult(String token) {
                            MLog.d(TAG, "onGetTokenResult");
                            if (TextUtils.isEmpty(token)) {
                                MLog.d(TAG, "token is empty");
                                SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
                                mVoicePlayStatus = false;
                                mImagePlayStatus = false;
                                return;
                            }
                            mWebView.post(new Runnable() {
                                @Override
                                public void run() {
                                    String scriptText = "javascript:setToken('"+token+"')";
                                    MLog.d(TAG, "scriptText:"+scriptText);
                                    mWebView.evaluateJavascript(scriptText, new ValueCallback<String>() {
                                        @Override
                                        public void onReceiveValue(String s) {
                                            MLog.d(TAG,"setToken = " + s);
                                        }
                                    });
                                }
                            });

                        }
                    });
                }
            });
        }
    }

    public void addObserver() {
        MLog.d(TAG,"addObserver ");
        mViewModel.getTTSPlayStatus().observeForever(mTTSPlayStatusChange);
        mViewModel.getVoiceFilePlayStatus().observeForever(mVoiceFilePlayStatusChange);
    }

    public void removeObserver() {
        MLog.d(TAG,"removeObserver ");
        mViewModel.getTTSPlayStatus().removeObserver(mTTSPlayStatusChange);
        mViewModel.getVoiceFilePlayStatus().removeObserver(mVoiceFilePlayStatusChange);
    }

    
    class MyWebViewClient1 extends WebViewClient {

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            //favicon(网站图标)&#xff1a;如果这个favicon已经存储在本地数据库中;则会返回这个网页的favicon;否则返回为null
            MLog.d(TAG, "【onPageStarted】" + url);
            mViewModel.setSurpriseEggPlaybackStatus(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Start);
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            MLog.d("onPageFinished", "【onPageFinished】" + url);
            super.onPageFinished(view, url);
        }


        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            //访问指定的网址发生错误时回调;我们可以在这里做错误处理;比如再请求加载一次;或者提示404的错误页面
            MLog.d(TAG, "【onReceivedError】" + request.getUrl().toString() + "  " + error.getErrorCode() + "  " + error.getDescription());
            super.onReceivedError(view, request, error);
        }

        @Override
        public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
            //HTTP错误具有400的状态码。请注意;errorResponse参数中可能不提供服务器响应的内容。
            MLog.d(TAG, "【onReceivedHttpError】" + request.getUrl().toString() + "  " + errorResponse.getStatusCode()
                    + "  " + errorResponse.getEncoding() + "  " + errorResponse.getMimeType());
            super.onReceivedHttpError(view, request, errorResponse);
        }

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            //HTTP错误具有&gt; = 400的状态码。请注意;errorResponse参数中可能不提供服务器响应的内容。
            MLog.d(TAG, "【onReceivedSslError】" + error.getUrl() + "  " + error.getPrimaryError() + "  " + error.getCertificate().toString());
            handler.proceed();//忽略错误继续加载
        }


        @Override
        public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
            //给主机应用程序一次同步处理键事件的机会。如果应用程序想要处理该事件则返回true,否则返回false。
            MLog.d(TAG, "【shouldOverrideKeyEvent】" + event.getAction() + "  " + event.getKeyCode());
            return super.shouldOverrideKeyEvent(view, event);
        }


        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            String url = request.getUrl().toString();
            if (!url.startsWith("http")) {
                MLog.d("override", request.getUrl().toString() + " : " + new Gson().toJson(request.getRequestHeaders()) + " : true");
                return true;
            }
            MLog.d("override", request.getUrl().toString() + " : " + new Gson().toJson(request.getRequestHeaders())+ " : false");
            return super.shouldOverrideUrlLoading(view, request);

        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        MLog.d(TAG, "dispatchKeyEvent");
        if(event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }



}
