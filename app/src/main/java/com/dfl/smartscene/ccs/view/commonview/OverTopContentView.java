package com.dfl.smartscene.ccs.view.commonview;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.navigation.Navigation;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;


/**
 * <AUTHOR>
 * @date ：2022/9/17 13:50
 * @description ：具有返回和关闭按钮的顶部条
 */
public class OverTopContentView {
    private TextView mTextViewTitle;
    private ImageView mImageButtonBack;
    private ImageView mImageButtonClose;
    public void initView(View view){
        mTextViewTitle = view.findViewById(R.id.text_view_content_top_title);
        mImageButtonBack = view.findViewById(R.id.button_content_top_back);
        mImageButtonClose = view.findViewById(R.id.button_content_top_close);
        mImageButtonBack.setOnClickListener(v -> Navigation.findNavController(v).navigateUp());
        mImageButtonClose.setOnClickListener( v-> {
            SceneControlManager.getInstance().exitScene();
            ViewControlManager.closeApp();
        });
    }

    public View getBackButton(){
        return mImageButtonBack;
    }

    public View getCloseButton(){
        return mImageButtonClose;
    }

    public void setTitle(String text){
        mTextViewTitle.setText(text);
    }
}
