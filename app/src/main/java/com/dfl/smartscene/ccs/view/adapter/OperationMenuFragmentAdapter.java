package com.dfl.smartscene.ccs.view.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;


import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.fragment.OperationMenuAiotChildFragment;
import com.dfl.smartscene.ccs.fragment.OperationMenuChildFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/5 13:51
 * @description ：添加操作菜单里每一个分类的fragment的adapter
 */
public class OperationMenuFragmentAdapter extends FragmentStateAdapter {
    private List<DeviceOperation> mDeviceOperations;
    private int operationType;
    private OperationBaseDialogFragment.OperationChangeListerner mOperationChangeListerner;
    private List<String> unableList = new ArrayList<>();

    public void setUnableList(List<String> unableList) {
        this.unableList = unableList;
        notifyDataSetChanged();
    }

    public void setDeviceOperations(List<DeviceOperation> deviceOperations) {
        mDeviceOperations = deviceOperations;
        notifyDataSetChanged();
    }

    public void setData(List<DeviceOperation> deviceOperations,List<String> unableList){
        mDeviceOperations = deviceOperations;
        this.unableList = unableList;
        notifyDataSetChanged();

    }

    public OperationMenuFragmentAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle, int operationType) {
        super(fragmentManager, lifecycle);
        this.operationType = operationType;
    }

    public void setOperationChangeListerner(OperationBaseDialogFragment.OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        OperationMenuChildFragment fragment;
        if(mDeviceOperations.get(position).getDeviceId().equals(ConstantModelValue.DEVICE_ID_AIOT)){
            fragment = new OperationMenuAiotChildFragment(operationType,mDeviceOperations.get(position).getSingleOperations());
            fragment.setOperationChangeListerner(mOperationChangeListerner);

        }else {
            fragment = new OperationMenuChildFragment(operationType,mDeviceOperations.get(position).getSingleOperations(),unableList);
            fragment.setOperationChangeListerner(mOperationChangeListerner);

        }
        return fragment;
    }

    @Override
    public int getItemCount() {
        return mDeviceOperations == null ? 0 :mDeviceOperations.size();
    }
}
