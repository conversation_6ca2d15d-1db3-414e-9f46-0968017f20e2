package com.dfl.smartscene.ccs.util;

import android.os.Handler;
import android.os.Looper;

import com.dfl.smartscene.ccs.core.ActionHandler;


/**
 * <AUTHOR>
 * @date ：2022/12/31 14:20
 * @description ：
 */
public class HandlerUtil {
    private static Handler mainHandler = new Handler(Looper.getMainLooper());
    public static Handler getMainHandler() {
        return mainHandler;
    }

    private static ActionHandler actionHandler = new ActionHandler(Looper.getMainLooper());

    public static ActionHandler getActionHandler() {
        return actionHandler;
    }
}
