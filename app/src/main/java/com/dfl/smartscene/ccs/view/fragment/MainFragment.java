package com.dfl.smartscene.ccs.view.fragment;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.util.ThreadExecutor;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.MultiTitleBaseFragment;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.viewmodel.MainFragmentViewModel;
import com.google.android.material.tabs.TabLayout;
import com.iauto.uibase.utils.MLog;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date ：2022/8/29 10:43
 * @description ：
 */
public class MainFragment extends MultiTitleBaseFragment {
    private static final String TAG = "MainFragment";
    private MainFragmentViewModel mMainFragmentViewModel;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mMainFragmentViewModel = new ViewModelProvider(requireActivity()).get(MainFragmentViewModel.class);
    }

    @Override
    public String[] getPageNames() {
        return new String[]{ConstantViewValue.PAGE_MAIN_LIBRARY, ConstantViewValue.PAGE_MAIN_MY};
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mMainFragmentViewModel.getMainPagePos().observe(getViewLifecycleOwner(), new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                changePage(integer);
            }
        });

        //进入首页时埋点
        BigDataManager.getInstance().writeEventMod2(getString(R.string.scene_no_model), "");

        mTabLayoutTitle.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab != null && !TextUtils.isEmpty(tab.getText().toString())) {
                    BigDataManager.getInstance().writeEventMod3(tab.getText().toString());
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        ThreadExecutor.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                copyFilesAssets(getContext(),"Movies",getContext().getExternalFilesDir(Environment.DIRECTORY_MOVIES).getPath());
            }
        });
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        MLog.d("MainFragment", "onHiddenChanged hidden");
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
//        for(Fragment fragment : getChildFragmentManager().getFragments()){
//            if(fragment.getTag() != null && !fragment.getTag().contains(String.valueOf(mViewPager2Content.getCurrentItem()))){
//                fragment.onConfigurationChanged(newConfig);
//            }
//        }

    }

    /**
     *  从assets目录中复制整个文件夹内容
     *  @param  context  Context 使用CopyFiles类的Activity
     *  @param  oldPath  String  原文件路径  如：/aa
     *  @param  newPath  String  复制后路径  如：xx:/bb/cc
     */
    public static void copyFilesAssets(Context context, String oldPath, String newPath) {
        try {
            String[] fileNames = context.getAssets().list(oldPath);//获取assets目录下的所有文件及目录名
            if (fileNames != null){
                if (fileNames.length > 0) {//如果是目录
                    File file = new File(newPath);
                    if (!file.exists()){
                        file.mkdirs();//如果文件夹不存在，则递归
                    }
                    for (String fileName : fileNames) {
                        copyFilesAssets(context,oldPath + "/" + fileName,newPath+"/"+fileName);
                    }
                } else {//如果是文件
                    InputStream is = context.getAssets().open(oldPath);
                    File file = new File(newPath);
                    LogUtil.i(TAG, "performCopy oldPath:" + oldPath + ",,newPath :" + newPath + ",exist:" + file.exists());
                    if (!file.exists()){
                        FileOutputStream fos = new FileOutputStream(file);
                        byte[] buffer = new byte[1024];
                        int byteCount;
                        while((byteCount=is.read(buffer))!=-1) {//循环从输入流读取 buffer字节
                            fos.write(buffer, 0, byteCount);//将读取的输入流写入到输出流
                        }
                        fos.flush();//刷新缓冲区
                        fos.getFD().sync();//写入到磁盘
                        is.close();
                        fos.close();
                    }
                    LogUtil.i(TAG, "copy success");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
