package com.dfl.smartscene.ccs.view.operationview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.DateUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.CustomNumberPicker;

import java.util.ArrayList;
import java.util.List;

/**
 * 时间生效选择页面
 */
public class StateTimeClockOperation implements OperationBaseView {
    private static final String TAG = "StateTimeClockOperation";

    private final Switch mSwitchCompat;

    private final CustomNumberPicker mNumberPickerHour1;
    private final CustomNumberPicker mNumberPickerMinute1;
    private final CustomNumberPicker mNumberPickerHour2;
    private final CustomNumberPicker mNumberPickerMinute2;

    private final TextView mTvHourUnit1;
    private final TextView mTvMinuteUnit1;
    private final TextView mTvHourUnit2;
    private final TextView mTvMinuteUnit2;
    private final TextView mTvReach;

    private final ImageView bg1;
    private final ImageView bg2;

    private SingleOperation mSingleOperation;

    @Override
    public List<String> extractArgs() {

        ArrayList<String> values = new ArrayList<>(2);
        if (mSwitchCompat.isChecked()) {
            values.add("00:00");
            values.add("00:00");
            return values;
        } else {
            values.add(DataTypeFormatUtil.frontCompWithZero(mNumberPickerHour1.getValue(), 2) + ":" + DataTypeFormatUtil.frontCompWithZero(mNumberPickerMinute1.getValue(), 2));
            values.add(DataTypeFormatUtil.frontCompWithZero(mNumberPickerHour2.getValue(), 2) + ":" + DataTypeFormatUtil.frontCompWithZero(mNumberPickerMinute2.getValue(), 2));
        }

        return values;
    }

    @Override
    public String extractDesc() {
        String output = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);

        StringBuilder sb = new StringBuilder();

        if (mSwitchCompat.isChecked()) {
            sb.append(ScenePatternApp.getInstance().getResources().getString(R.string.string_state_time_clock_operation_all_day));
        } else {
            String time1 = DataTypeFormatUtil.frontCompWithZero(mNumberPickerHour1.getValue(), 2) + ":" + DataTypeFormatUtil.frontCompWithZero(mNumberPickerMinute1.getValue(), 2);
            String time2 = DataTypeFormatUtil.frontCompWithZero(mNumberPickerHour2.getValue(), 2) + ":" + DataTypeFormatUtil.frontCompWithZero(mNumberPickerMinute2.getValue(), 2);

            int i = DateUtil.compareTime(time1, time2);
            if(i > 0) {
                sb.append(time1)
                        .append(ScenePatternApp.getInstance().getResources().getString(R.string.string_state_time_clock_operation_reach))
                        .append(time2)
                        .append(ScenePatternApp.getInstance().getResources().getString(R.string.string_state_time_clock_operation_next_day_tip));
            } else if(i < 0) {
                sb.append(time1).append(ScenePatternApp.getInstance().getResources().getString(R.string.string_state_time_clock_operation_reach)).append(time2);
            } else {
                sb.append(ScenePatternApp.getInstance().getResources().getString(R.string.string_state_time_clock_operation_all_day));
            }
        }

        return output.replaceFirst("&" , sb.toString());
    }

    public StateTimeClockOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        LogUtil.d("0221", "StateTimeClockOperation: "+defaultStr);
        View targetView = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_custom_state_time_clock, parent, true);

        mSwitchCompat = targetView.findViewById(R.id.switchcompat_layout_custom_state_time_clock_all_day);
        mNumberPickerHour1 = targetView.findViewById(R.id.numberpicker_layout_custom_state_time_clock_hour_first);
        mNumberPickerMinute1 = targetView.findViewById(R.id.numberpicker_layout_custom_state_time_clock_minute_first);
        mNumberPickerHour2 = targetView.findViewById(R.id.numberpicker_layout_custom_state_time_clock_hour_second);
        mNumberPickerMinute2 = targetView.findViewById(R.id.numberpicker_layout_custom_state_time_clock_minute_second);

        bg1 = targetView.findViewById(R.id.switchcompat_layout_custom_state_time_picker_bg_1);
        bg2 = targetView.findViewById(R.id.switchcompat_layout_custom_state_time_picker_bg_2);

        mTvHourUnit1 = targetView.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_1);
        mTvMinuteUnit1 = targetView.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_2);
        mTvHourUnit2 = targetView.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_3);
        mTvMinuteUnit2 = targetView.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_4);
        mTvReach = targetView.findViewById(R.id.textview_layout_custom_state_time_clock_title_reach);

        refreshPickerState();

        mSwitchCompat.setOnCheckedChangeListener((buttonView, isChecked) -> {
            refreshPickerState();
        });

        mNumberPickerHour1.setMinValue(0);
        mNumberPickerHour1.setMaxValue(23);
        mNumberPickerHour1.setDisplayedValues(getDisplayArray(24));

        mNumberPickerMinute1.setMinValue(0);
        mNumberPickerMinute1.setMaxValue(59);
        mNumberPickerMinute1.setDisplayedValues(getDisplayArray(60));

        mNumberPickerHour2.setMinValue(0);
        mNumberPickerHour2.setMaxValue(23);
        mNumberPickerHour2.setDisplayedValues(getDisplayArray(24));

        mNumberPickerMinute2.setMinValue(0);
        mNumberPickerMinute2.setMaxValue(59);
        mNumberPickerMinute2.setDisplayedValues(getDisplayArray(60));
        if(defaultStr.contains("@")){
            String[] split = defaultStr.split("@");
            String time1 = split[0];
            String time2 = split[1];
            if(time1.equals(time2)){
                mSwitchCompat.setChecked(true);
                return;
            }
            String[] splitTime1 = time1.split(":");
            String[] splitTime2 = time2.split(":");
            mNumberPickerHour1.setValue(Integer.parseInt(splitTime1[0]));
            mNumberPickerMinute1.setValue(Integer.parseInt(splitTime1[1]));
            mNumberPickerHour2.setValue(Integer.parseInt(splitTime2[0]));
            mNumberPickerMinute2.setValue(Integer.parseInt(splitTime2[1]));
        }
    }


    private void refreshPickerState() {
        if (mSwitchCompat.isChecked()) {
            mNumberPickerHour1.setEnabled(false);
            mNumberPickerMinute1.setEnabled(false);
            mNumberPickerHour2.setEnabled(false);
            mNumberPickerMinute2.setEnabled(false);
            bg1.setVisibility(View.INVISIBLE);
            bg2.setVisibility(View.INVISIBLE);
            mTvHourUnit1.setTextColor(ContextCompat.getColor(mTvHourUnit1.getContext(), R.color.color_text_tertiary_label_gray));
            mTvMinuteUnit1.setTextColor(ContextCompat.getColor(mTvMinuteUnit1.getContext(), R.color.color_text_tertiary_label_gray));
            mTvHourUnit2.setTextColor(ContextCompat.getColor(mTvHourUnit2.getContext(), R.color.color_text_tertiary_label_gray));
            mTvMinuteUnit2.setTextColor(ContextCompat.getColor(mTvMinuteUnit2.getContext(), R.color.color_text_tertiary_label_gray));
            mTvReach.setTextColor(ContextCompat.getColor(mTvMinuteUnit2.getContext(), R.color.color_text_tertiary_label_gray));
        } else {
            mNumberPickerHour1.setEnabled(true);
            mNumberPickerMinute1.setEnabled(true);
            mNumberPickerHour2.setEnabled(true);
            mNumberPickerMinute2.setEnabled(true);
            bg1.setVisibility(View.VISIBLE);
            bg2.setVisibility(View.VISIBLE);
            mTvHourUnit1.setTextColor(ContextCompat.getColor(mTvHourUnit1.getContext(), R.color.color_text_tertiary_label));
            mTvMinuteUnit1.setTextColor(ContextCompat.getColor(mTvMinuteUnit1.getContext(), R.color.color_text_tertiary_label));
            mTvHourUnit2.setTextColor(ContextCompat.getColor(mTvHourUnit2.getContext(), R.color.color_text_tertiary_label));
            mTvMinuteUnit2.setTextColor(ContextCompat.getColor(mTvMinuteUnit2.getContext(), R.color.color_text_tertiary_label));
            mTvReach.setTextColor(ContextCompat.getColor(mTvMinuteUnit2.getContext(), R.color.color_text_tertiary_label));
        }

    }


    private String[] getDisplayArray(int maxValue) {
        String[] displayStr = new String[maxValue];
        for (int i = 0; i < maxValue; i++) {
            if (i < 10) {
                displayStr[i] = "0" + i;
            } else {
                displayStr[i] = String.valueOf(i);
            }
        }
        return displayStr;
    }

    @Override
    public void onConfigurationChanged() {

    }

}
