package com.dfl.smartscene.ccs.view.base;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;


/**
 * 主页面的基类
 * @author: huangzezheng
 * @date: 2021/10/18
 */
public abstract class BaseFragment extends SimpleBaseFragment {
    private static final String TAG = "BaseFragment";

    protected ConstraintLayout mLoadingLayout;      // 页面加载布局
    protected ConstraintLayout mNoContentLayout;    // 无内容布局
    protected ConstraintLayout mErrorLayout;        // 错误布局

    protected int requestCount = 0;


    protected int getNoResultLayoutId(){
        return R.layout.layout_fragment_none_result;
    }
    /**
     * onViewCreated在onCreateView执行完后立即执行,将findViewById和初始化一些数据放置其中可以避免出现如切换界面时出现卡顿的现象，提高用户体验
     * onCreateView是创建的时候调用，返回的就是fragment要显示的view。
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        LogUtil.d(TAG, "onViewCreated");
        //添加网络异常，空数据，加载中布局
        if(view instanceof ViewGroup){
            mLoadingLayout = (ConstraintLayout) (getLayoutInflater().inflate(R.layout.layout_page_loading,(ViewGroup)view,false));
            mNoContentLayout = (ConstraintLayout) (getLayoutInflater().inflate(getNoResultLayoutId(),(ViewGroup)view,false));
            mErrorLayout = (ConstraintLayout) (getLayoutInflater().inflate(R.layout.layout_fragment_network_abnormal,(ViewGroup)view,false));
            ViewGroup.LayoutParams layoutParams;
            if(view instanceof FrameLayout){
                layoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            }else if(view instanceof ConstraintLayout){
                layoutParams = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT);
            }else if(view instanceof LinearLayout){
                layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
            }else {
                layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            }
            mLoadingLayout.setLayoutParams(layoutParams);
            mNoContentLayout.setLayoutParams(layoutParams);
            mErrorLayout.setLayoutParams(layoutParams);
            ((ViewGroup) view).addView(mNoContentLayout);
            ((ViewGroup) view).addView(mLoadingLayout);
            ((ViewGroup) view).addView(mErrorLayout);
            LogUtil.d(TAG, "finish add View");
            LogUtil.d(TAG, "on content layout id = "+mNoContentLayout.getId());
        }
        View refreshButton = mErrorLayout.findViewById(R.id.text_view_network_retry);
        refreshButton.setOnClickListener(v -> {
            refreshData();
        });
        mNoContentLayout.findViewById(R.id.text_view_network_retry).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                refreshData();
            }
        });
        super.onViewCreated(view, savedInstanceState);
    }

    protected void showContentPage(){
        requestCount -= 1;
        if(requestCount > 0){
            return;
        }
        getContentPageView().setVisibility(View.VISIBLE);
        mNoContentLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mLoadingLayout.setVisibility(View.GONE);
        LogUtil.d(TAG, "show content");
    }

    protected void showNoContentPage(){
        requestCount -= 1;
        if(requestCount > 0){
            return;
        }
        getContentPageView().setVisibility(View.GONE);
        mNoContentLayout.setVisibility(View.VISIBLE);
        mErrorLayout.setVisibility(View.GONE);
        mLoadingLayout.setVisibility(View.GONE);
        LogUtil.d(TAG, "show no content");
    }

    protected void showLoadingPage(){
        requestCount += 1;
        getContentPageView().setVisibility(View.GONE);
        mNoContentLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.GONE);
        mLoadingLayout.setVisibility(View.VISIBLE);
        LogUtil.d(TAG, "show loading");
    }

    protected void showErrorPage(){
        requestCount -= 1;
        if(requestCount > 0){
            return;
        }
        getContentPageView().setVisibility(View.GONE);
        mNoContentLayout.setVisibility(View.GONE);
        mErrorLayout.setVisibility(View.VISIBLE);
        mLoadingLayout.setVisibility(View.GONE);
        LogUtil.d(TAG, "show error");
    }

    abstract protected View getContentPageView();

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        getChildFragmentManager().getFragments().forEach(fragment -> fragment.onHiddenChanged(hidden));

    }

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {

    }

    /**
     * 页面加载布局对象置空，无内容布局对象置空，错误布局对象置空
     */
    @Override
    public void onDestroyView() {
        mLoadingLayout = null;
        mNoContentLayout = null;
        mErrorLayout = null;
        requestCount = 0;
        super.onDestroyView();
    }
}
