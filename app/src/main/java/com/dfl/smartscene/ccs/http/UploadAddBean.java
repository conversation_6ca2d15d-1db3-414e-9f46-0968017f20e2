package com.dfl.smartscene.ccs.http;


import com.dfl.smartscene.ccs.bean.SceneBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/2/27 15:42
 * @description ：用户上传接口中无网络下添加的场景
 */
public class UploadAddBean {
    private String categoryId;
    private List<SceneBean> sceneBeanList = new ArrayList<>();

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public List<SceneBean> getSceneBeanList() {
        return sceneBeanList;
    }

    public void setSceneBeanList(List<SceneBean> sceneBeanList) {
        this.sceneBeanList = sceneBeanList;
    }
    public void add(SceneBean sceneBean){
        sceneBeanList.add(sceneBean);
    }
}
