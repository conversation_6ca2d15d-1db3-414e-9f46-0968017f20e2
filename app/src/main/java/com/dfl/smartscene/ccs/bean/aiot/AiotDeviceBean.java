package com.dfl.smartscene.ccs.bean.aiot;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/3 17:00
 * @description ：aiot连接设备信息原生数据
 */
public class AiotDeviceBean {
    /**
     * 产品id
     */
    private String pid;
    /**
     * 产品名
     */
    private String productName;
    /**
     * 设备id
     */
    private String did;
    /**
     * 设备名
     */
    private String deviceName;
    /**
     * 设备图片，http链接
     */
    private String image;
    /**
     * 是否在线
     */
    private boolean isOnline;
    /**
     * 操作列表
     */
    private List<AiotOperationBean> operationList;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public boolean isOnline() {
        return isOnline;
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public List<AiotOperationBean> getOperationList() {
        return operationList;
    }

    public void setOperationList(List<AiotOperationBean> operationList) {
        this.operationList = operationList;
    }
}
