package com.dfl.smartscene.ccs.model;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.RemoteException;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.MutableLiveData;

import com.dfl.api.app.vrfc.IVRStateCallback;
import com.dfl.api.app.vrfc.IVRStateControl;
import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.Constants;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.api.base.appcommon.ICommonCarStatusCallback;
import com.dfl.api.base.appcommon.RangeInfo;
import com.dfl.api.base.appcommon.WarningInfo;
import com.dfl.api.base.appcommon.media.AudioCp;
import com.dfl.api.base.appcommon.media.IMedia;
import com.dfl.api.base.appcommon.media.IMediaCallback;
import com.dfl.api.da.ecall.IEcall;
import com.dfl.api.da.ecall.IEcallCallback;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;

/*
 * 情景模式中断管理类
 * <AUTHOR>
 * @date 2022/5/15
 */
public class ScenePatternInterrupt {

    private static final String                TAG                    = "ScenePatternInterrupt";
    @SuppressLint("StaticFieldLeak")
    private static       ScenePatternInterrupt sScenePatternInterrupt = null;
    private              Context               mContext               = null;
    private              byte[]                mApiLock               = new byte[0];
    private              IEcall                mIEcall                = null;
    private              IMedia                mIMedia                = null;
    private IAppCommon             mAppCommonServiceManager = null;
    private IVRStateControl        mIVRStateControl         = null;
    private HandlerThreadProcessor handlerThreadProcessor   = HandlerThreadProcessor.getInstance();
    private int  mCurrentCallType = BluetoothHeadsetClientCall.CALL_STATE_TERMINATED;

    private ArrayList<ScenePatternInterruptListener> mInterruptListeners = new ArrayList<ScenePatternInterruptListener>(2);

    /**
     * ScenePatternInterrupt object constructor
     * @param context
     */
    private ScenePatternInterrupt(Context context) {
        mContext = context;
        // init handler
        IntentFilter intentFilter = new IntentFilter();

        intentFilter.addAction(BluetoothHeadsetClient.ACTION_CALL_CHANGED);
        mContext.registerReceiver(mCallStatusReceiver,intentFilter);

        // init livedata
        initLiveData();
    }

    /**
     * create ScenePatternInterrupt's instance
     * @param context
     */
    public static synchronized void create(Context context) {
        if(sScenePatternInterrupt == null){
            sScenePatternInterrupt = new ScenePatternInterrupt(context);
        }
    }

    /**
     * get ScenePatternInterrupt's instance
     */
    public static ScenePatternInterrupt getInstance(){
        return sScenePatternInterrupt;
    }

    /*
    * 中断通知接口类
    * <AUTHOR>
    * @date 2022/5/15
    */
    public interface ScenePatternInterruptListener {
        public void onInterrupt(int interruptType);
    }

    /*
    * 中断通知接口实例注册
    * @param listener 监听实例
    */
    public void registerScenePatternInterruptListener(ScenePatternInterruptListener listener) {
        mInterruptListeners.add(listener);
    }

    /*
    * 中断通知接口实例注销
    * @param listener 监听实例
    */
    public void unregisterScenePatternInterruptListener(ScenePatternInterruptListener listener) {
        mInterruptListeners.remove(listener);
    }

    /*
    * 设定中断类型
    * @param interruptType 中断类型
    */
    private void setScenePatternInterruptListener(int interruptType) {
        for (ScenePatternInterruptListener interruptListener : mInterruptListeners) {
            interruptListener.onInterrupt(interruptType);
        }
    }

    /**
     * 网络连接状态
     */
    private MutableLiveData<Integer> mNetWorkStatus = new MutableLiveData<>();
    public MutableLiveData<Integer> getNetWorkStatus() {
        MLog.d(TAG, "getInternetStatus from CustomApi" + mNetWorkStatus.getValue());
        return mNetWorkStatus;
    }

    /**
     * media source变更
     */
    private MutableLiveData<String> mOriginalSource = new MutableLiveData<>();
    public MutableLiveData<String> getOriginalSource() {
        MLog.d(TAG, "getOriginalSource from CustomApi" + mOriginalSource.getValue());
        return mOriginalSource;
    }

    /**
     * media source变更
     */
    private MutableLiveData<String> mChangedSource = new MutableLiveData<>();
    public MutableLiveData<String> getChangedSource() {
        MLog.d(TAG, "getChangedSource from CustomApi" + mChangedSource.getValue());
        return mChangedSource;
    }

    /**
     * 蓝牙电话状态
     */
    private MutableLiveData<Integer> mCallStatus = new MutableLiveData<>();
    public MutableLiveData<Integer> getCallStatus() {
        MLog.d(TAG, "getCallStatus: mCallStatus=" + mCallStatus.getValue());
        return mCallStatus;
    }

    /**
     * E-Call状态
     */
    private MutableLiveData<Integer> mECallStatus = new MutableLiveData<>();
    public MutableLiveData<Integer> getECallStatus() {
        MLog.d(TAG, "getECallState from CustomApi" + mECallStatus.getValue());
        return mECallStatus;
    }

    /**
     * 检查当前中断状态
     */ 
    private void checkInterruptStatus() {
//        if ((mNetWorkStatus.getValue() == ScenePatternFuncDef.INTERNET_STATUS_CONNECTED) &&
//            (mChangedSource.getValue().equals(RelieveStressFuncDef.MEDIA_SOURCE_MUSIC_PACKAGE_NAME)) &&
//            (mCallStatus.getValue() == ScenePatternFuncDef.CALL_STATUS_IDLE) &&
//            (mECallStatus.getValue() == ScenePatternFuncDef.E_CALL_STATUS_IDLE) &&
//            (mGearType.getValue() == ScenePatternFuncDef.CAR_GEAR_TYPE_P)) {
            setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
//        }
    }




    /**
     * 设定E-Call状态
     * @param status E-Call状态
     */
    public void setECallStatus(int status) {
        MLog.d(TAG, "setECallStatus from CustomApi" + mECallStatus.getValue());
        switch (status) {
            case ScenePatternFuncDef.E_CALL_CHANGE_DIAL_OUT:
            case ScenePatternFuncDef.E_CALL_CHANGE_DIALING_OUT:
            case ScenePatternFuncDef.E_CALL_CHANGE_DIALING_OUT_FAILED_RETRY:
            case ScenePatternFuncDef.E_CALL_CHANGE_ACNCALL_DIAL_OUT:
            case ScenePatternFuncDef.E_CALL_CHANGE_ACNCALL_ON_CALL:
            case ScenePatternFuncDef.E_CALL_CHANGE_ACNCALL_DIALING_OUT_FAILED_RETRY:
            case ScenePatternFuncDef.E_CALL_CHANGE_INCOMING:
                mECallStatus.setValue(ScenePatternFuncDef.E_CALL_STATUS_ACTIVE);
                setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_ECALL);
                break;
            case ScenePatternFuncDef.E_CALL_CHANGE_HANG_UP:
            case ScenePatternFuncDef.E_CALL_CHANGE_ABNORMAL_TERMINATION:
            default:
                mECallStatus.setValue(ScenePatternFuncDef.E_CALL_STATUS_IDLE);
                checkInterruptStatus();
                break;
        }
    }

    /**
     * 车辆挡位状态
     */
    private final MutableLiveData<Integer> mGearType = new MutableLiveData<>();
    public MutableLiveData<Integer> getGearType() {
        return mGearType;
    }

    /**
     * 设定挡位状态
     * @param carGearType 挡位状态
     */
    private void setGearType(int carGearType) {
        mGearType.setValue(carGearType);
        MLog.d(TAG, "setGearType:carGearType:"+carGearType);
        if (carGearType != ScenePatternFuncDef.CAR_GEAR_TYPE_P) {
            setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P);
        } else {
            checkInterruptStatus();
        }
    }

    /**
     * VR激活状态
     */
    private final MutableLiveData<Integer> mVRState = new MutableLiveData<>();
    public MutableLiveData<Integer> getVRState() {
        return mVRState;
    }

    /**
     * 设定VR激活状态
     * @param vrState VR激活状态
     */
    private void setVRState(int vrState) {
        mVRState.setValue(vrState);
        if (vrState == ScenePatternFuncDef.VR_STATE_ACTIVE) {
            setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_VR);
        } else {
            setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
        }
    }


    /**
     * 初始化处理
     */
    private void initLiveData() {
        mECallStatus.setValue(ScenePatternFuncDef.E_CALL_STATUS_IDLE);
        mCallStatus.setValue(ScenePatternFuncDef.CALL_STATUS_IDLE);
        mNetWorkStatus.setValue(ScenePatternFuncDef.INTERNET_STATUS_DISCONNECTED);
        mChangedSource.setValue("");
        mOriginalSource.setValue("");
        mGearType.setValue(ScenePatternFuncDef.CAR_GEAR_TYPE_P);
        initNetWorkCallBack();
        setECallNotify();
        setSourceChangeNotify();
        setAppCommonNotify();
        setVRStateChangeNotify();
    }

    /**
     * 初始化network callback方法
     */
    private void initNetWorkCallBack() {
        NetworkCallbackImpl networkCallback = new NetworkCallbackImpl();
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        NetworkRequest request = builder.build();
        ConnectivityManager connMgr = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr != null) {
            connMgr.registerNetworkCallback(request, networkCallback);
        }
    }

    /*
    * 网络状态变更回调接口
    * <AUTHOR>
    * @date 2022/5/15
    */
    public class NetworkCallbackImpl extends ConnectivityManager.NetworkCallback {
        /**
         * 网络已链接回调
         */
        @Override
        public void onAvailable(Network network) {
            super.onAvailable(network);
            MLog.d(TAG, "网络已链接");
            handlerThreadProcessor.postToMainThread(new Runnable() {
                @Override
                public void run() {
                    mNetWorkStatus.setValue(ScenePatternFuncDef.INTERNET_STATUS_CONNECTED);
                    checkInterruptStatus();
                }
            });
        }

        /**
         * 网络已断开回调
         */
        @Override
        public void onLost(Network network) {
            super.onLost(network);
            MLog.d(TAG, "网络已断开");
            handlerThreadProcessor.postToMainThread(new Runnable() {
                @Override
                public void run() {
                    mNetWorkStatus.setValue(ScenePatternFuncDef.INTERNET_STATUS_DISCONNECTED);
                    setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_DISCONNECT);
                }
            });
        }

        /**
         * 网络链接设备变更回调
         */
        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
            if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    MLog.d(TAG, "wifi已经连接");
                } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    MLog.d(TAG, "数据流量已经连接");
                } else {
                    MLog.d(TAG, "其他网络");
                }
            }
        }
    }


    /**
     *蓝牙电话状态广播
     */
    private final BroadcastReceiver mCallStatusReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            MLog.d(TAG, " onReceive getAction = " + intent.getAction());
            if (BluetoothHeadsetClient.ACTION_CALL_CHANGED.equals(intent.getAction())) {
                BluetoothHeadsetClientCall call = intent.getParcelableExtra(BluetoothHeadsetClient.EXTRA_CALL);
                MLog.d(TAG, "onReceive: [callstate : " + call.getState() + " ]");
                setCallStatus(call.getState());
            }
        }
    };

    /**
     * 设置蓝牙电话中断状态
     * @param status 蓝牙电话状态
     */
    private void setCallStatus(int status) {
        if (status == mCurrentCallType) {
            return;
        }
        switch (status) {
            case BluetoothHeadsetClientCall.CALL_STATE_HELD:
            case BluetoothHeadsetClientCall.CALL_STATE_ACTIVE:
            case BluetoothHeadsetClientCall.CALL_STATE_DIALING:
            case BluetoothHeadsetClientCall.CALL_STATE_ALERTING:
            case BluetoothHeadsetClientCall.CALL_STATE_INCOMING:
            case BluetoothHeadsetClientCall.CALL_STATE_WAITING:
            case BluetoothHeadsetClientCall.CALL_STATE_HELD_BY_RESPONSE_AND_HOLD:
                mCallStatus.setValue(ScenePatternFuncDef.CALL_STATUS_ACTIVE);
                setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL);
                break;
            case BluetoothHeadsetClientCall.CALL_STATE_TERMINATED:
                mCallStatus.setValue(ScenePatternFuncDef.CALL_STATUS_IDLE);
                checkInterruptStatus();
                break;
            default:
                break;
        }
        mCurrentCallType = status;
    }

    /**
     * 设置E-CALL回调接口
     */
    private void setECallNotify() {
        BaseManager.create(mContext, Constants.DFL_DA_ECALL_ECALL_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String requestName, IBaseAPI iBaseAPI) {
                MLog.d(TAG, "Type[" + apiStateType + "] Request Name:" + requestName);
                if (Constants.DFL_DA_ECALL_ECALL_SERVICE.equals(requestName)) {
                    synchronized(mApiLock) {
                        if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IEcall) {
                            mIEcall = (IEcall) iBaseAPI;
                            try {
                                mIEcall.registerEcallCallback(new IEcallCallback() {
                                    @Override
                                    public void onEcallStatusChange(int i) {
                                        final int iState = i;
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                setECallStatus(iState);
                                            }
                                        });
                                    }
                                });
                            } catch (NullServiceException e) {
                                mIEcall = null;
                                MLog.e(TAG, "->registerEcallCallback() catch NullServiceException!!!");
                            }

                            handlerThreadProcessor.postToMainThread(new Runnable() {
                                @Override
                                public void run() {
                                    int eCallStatus = 0;
                                    synchronized(mApiLock) {
                                        if (null != mIEcall) {
                                            try {
                                                eCallStatus = mIEcall.getEcallStatus();
                                                setECallStatus(eCallStatus);
                                            } catch (NullServiceException e) {
                                                mIEcall = null;
                                                MLog.e(TAG, "mIEcall.getEcallStatus catch NullServiceException");
                                            } catch (RemoteException e) {
                                                mIEcall = null;
                                                MLog.e(TAG, "mIEcall.getEcallStatus catch RemoteException");
                                            }
                                        }
                                    }
                                }
                            });

                        } else {
                            mIEcall = null;
                            MLog.d(TAG, "mIEcall is created failed");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

            }
        });
    }

    /**
     * 设置media source CustomApi回调接口
     */
    public void setSourceChangeNotify() {
        MLog.d(TAG, "setSourceChangeNotify");
        if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, "android.permission.VEHICLE_DATA")) {
            MLog.d(TAG, "PERMISSION_GRANTED");
            BaseManager.create(mContext, Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE, new APICreateCallback() {
                @Override
                public void onAPICreateCallback(APIStateType.APIState apiStateType, String requestName, IBaseAPI iBaseAPI) {
                    MLog.d(TAG, "Type[" + apiStateType + "] Request Name:" + requestName);
                    if (Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE.equals(requestName)) {
                        synchronized (mApiLock) {
                            if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IMedia) {
                                mIMedia = (IMedia) iBaseAPI;
                                try {
                                    mIMedia.registerMediaCallback(new IMediaCallback() {
                                        @Override
                                        public void onMediaAudioSourceChange(AudioCp audioCp) {
                                            AudioCp audioCpTemp = audioCp;
                                            handlerThreadProcessor.postToMainThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    MLog.d(TAG, "onMediaAudioSourceChange");
                                                    setActiveSource(audioCpTemp);
                                                }
                                            });
                                        }

                                        @Override
                                        public void onNullSourceSetDefaultSource(AudioCp audioCp) {

                                        }

                                        @Override
                                        public void onStreamTypeOfCurrentVolume(int i) {

                                        }


                                    });
                                } catch (ClassCastException e) {
                                    mIMedia = null;
                                    MLog.e(TAG, "registerMediaCallback catch ClassCastException:" + e.toString());
                                } catch (NullServiceException e) {
                                    mIMedia = null;
                                    MLog.e(TAG, "registerMediaCallback catch NullServiceException");
                                }

                                handlerThreadProcessor.postToMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        AudioCp audioCp = null;
                                        synchronized (mApiLock) {
                                            if (null != mIMedia) {
                                                try {
                                                    audioCp = mIMedia.getMediaAudioSource();
                                                    setActiveSource(audioCp);
                                                } catch (NullServiceException e) {
                                                    mIMedia = null;
                                                    MLog.e(TAG, "mIMedia.getMediaAudioSource catch NullServiceException");
                                                } catch (RemoteException e) {
                                                    mIMedia = null;
                                                    MLog.e(TAG, "mIMedia.getMediaAudioSource catch RemoteException");
                                                }
                                            }
                                        }
                                    }
                                });

                            } else {
                                mIMedia = null;
                                MLog.e(TAG, "Base API isn't IMedia");
                            }
                        }
                    }
                }

                @Override
                public void onMethodStateCallback(APIStateType.APIState apiState, String requestName) {
                    MLog.d(TAG, "Type[" + apiState + "] Request Name:" + requestName);
                }
            });
        }
    }

    /**
     * 设置media source中断状态
     * @param audioCp Audio状态
     */
    private void setActiveSource(AudioCp audioCp) {
        if (audioCp == null) {
            MLog.d(TAG, "AudioCp is null");
        }
        else {
            MLog.d(TAG, "Active Source Package[" + audioCp.getPkg()
                    + "] Class[" + audioCp.getCls() + "] ID:" + audioCp.getId());
            MLog.d(TAG, "mChangedSource:"+mChangedSource.getValue());
            if(mChangedSource.getValue().isEmpty() || !mChangedSource.getValue().equals(audioCp.getPkg())) {

                mOriginalSource.setValue(mChangedSource.getValue());
                MLog.d(TAG, "mOriginalSource:"+mOriginalSource.getValue());
                mChangedSource.setValue(audioCp.getPkg());
                MLog.d(TAG, "mChangedSource:"+mChangedSource.getValue());
                setScenePatternInterruptListener(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE);
            }
        }
    }

    /**
     * 设置app common回调接口(为了接收车辆挡位变更)
     */
    public void setAppCommonNotify() {
        MLog.d(TAG, "setAppCommonNotify");
        if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, "android.permission.VEHICLE_DATA")) {
            MLog.d(TAG, "PERMISSION_GRANTED");
            BaseManager.create(mContext, Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE, new APICreateCallback() {
                @Override
                public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                    if (Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE.equals(s)) {
                        synchronized (mApiLock) {
                            if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IAppCommon) {
                                mAppCommonServiceManager = (IAppCommon) iBaseAPI;
                                try {
                                    mAppCommonServiceManager.registerCommonCarStatusCallback(new ICommonCarStatusCallback() {
                                        @Override
                                        public void onCarGearChange(int i) {
                                            MLog.e(TAG, "onCarGearChange gear:" + i);
                                            final int iGearType = i;
                                            handlerThreadProcessor.postToMainThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    setGearType(iGearType);
                                                }
                                            });
                                        }

                                        @Override
                                        public void onReverseStatusChange(boolean b) {

                                        }

                                        @Override
                                        public void onPkbStatusChange(int i) {

                                        }

                                        @Override
                                        public void onCarSpeedChange(float v) {

                                        }

                                        @Override
                                        public void onMeterSpeedChange(float v) {

                                        }

                                        @Override
                                        public void onCarAccelerationValueChange(AccelerationInfo accelerationInfo) {

                                        }

                                        @Override
                                        public void onOdographChange(int i) {

                                        }


                                        @Override
                                        public void onFuelDrivingRange(int i) {

                                        }

                                        @Override
                                        public void onPercentRemainFuel(int i) {

                                        }

                                        @Override
                                        public void onPercentRemainPower(int i) {

                                        }

                                        @Override
                                        public void onPowerDrivingRange(int i) {

                                        }

                                        @Override
                                        public void onIllStatusChange(boolean b) {

                                        }

                                        @Override
                                        public void onTurnIndicateStatusChange(int i) {

                                        }

                                        @Override
                                        public void onScreenStatus(boolean b) {

                                        }

                                        @Override
                                        public void onAccStatus(boolean b) {

                                        }

                                        @Override
                                        public void onAutoACCStatusChange(boolean b) {

                                        }

                                        @Override
                                        public void onIGNStatus(boolean b) {

                                        }

                                        @Override
                                        public void onDriveStatusChanged(int i) {

                                        }

                                        @Override
                                        public void onVehicleStatusChange(int i) {

                                        }

                                        @Override
                                        public void onVehicleStatusTypeChange(int i, boolean b) {

                                        }

                                        @Override
                                        public void onDriveMode(int i) {

                                        }

                                        @Override
                                        public void onEnduranceCalculationStandardTypeChange(int i) {

                                        }

                                        @Override
                                        public void onEcuErrorStateChange(int i, int i1) {

                                        }

                                        @Override
                                        public void onCanIdErrorStateChange(int i, int i1) {

                                        }

                                        @Override
                                        public void onAlertWarningInfo(int i, int i1) {

                                        }

                                        @Override
                                        public void onVehicleWarningInfoChange(WarningInfo warningInfo) {

                                        }

                                        @Override
                                        public void onTotalRangeInfoChange(RangeInfo rangeInfo) {

                                        }

                                        @Override
                                        public void onPowerModeStatusChange(boolean b) {

                                        }

                                    });
                                } catch (NullServiceException e) {
                                    mAppCommonServiceManager = null;
                                    MLog.e(TAG, "->registerCommonCarStatusCallback() catch NullServiceException!!!");
                                }

                                handlerThreadProcessor.postToMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        synchronized (mApiLock) {
                                            if (null != mAppCommonServiceManager) {
                                                try {
                                                    int carGearType = mAppCommonServiceManager.getCarGear();
                                                    MLog.d(TAG, "setAppCommonNotify:carGearType:" + carGearType);
                                                    setGearType(carGearType);
                                                } catch (NullServiceException e) {
                                                    mAppCommonServiceManager = null;
                                                    e.printStackTrace();
                                                    MLog.e(TAG, "mAppCommonServiceManager.getCarGear() catch NullServiceException");
                                                } catch (RemoteException e) {
                                                    mAppCommonServiceManager = null;
                                                    e.printStackTrace();
                                                    MLog.e(TAG, "mAppCommonServiceManager.getCarGear() catch RemoteException");
                                                }
                                            }
                                        }
                                    }
                                });

                            } else {
                                mAppCommonServiceManager = null;
                                MLog.d(TAG, "mAppCommonServiceManager is created failed");
                            }
                        }
                    }
                }

                @Override
                public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

                }
            });
        }
    }

    /**
     * 设置VR回调接口(为了接收车辆挡位变更)
     */
    private void setVRStateChangeNotify() {
        BaseManager.create(mContext, Constants.DFL_APP_VRFC_VRSTATECONTROL_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                if (Constants.DFL_APP_VRFC_VRSTATECONTROL_SERVICE.equals(s)) {
                    synchronized(mApiLock) {
                        if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IVRStateControl) {
                            mIVRStateControl = (IVRStateControl) iBaseAPI;
                            try {
                                mIVRStateControl.registerCallback(new IVRStateCallback() {
                                    @Override
                                    public void onVRStateChanged(int i) {
                                        final int vrState = i;
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                setVRState(vrState);
                                            }
                                        });
                                    }
                                });
                            } catch (NullServiceException e) {
                                MLog.e(TAG, "registerMediaCallback catch NullServiceException");
                            }

                            handlerThreadProcessor.postToMainThread(new Runnable() {
                                @Override
                                public void run() {
                                    synchronized(mApiLock) {
                                        if (null != mIVRStateControl) {
                                            try {
                                                mVRState.setValue(mIVRStateControl.getVRState());
                                            } catch (NullServiceException e) {
                                                mIVRStateControl = null;
                                                MLog.e(TAG, "mIVRStateControl.getVRState() catch NullServiceException");
                                            } catch (RemoteException e) {
                                                mIVRStateControl = null;
                                                MLog.e(TAG, "mIVRStateControl.getVRState() catch RemoteException");
                                            }
                                        }
                                    }
                                }
                            });

                        } else {
                            mAppCommonServiceManager = null;
                            MLog.d(TAG, "mAppCommonServiceManager is created failed");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

            }
        });
    }


    public String getActiveSource() {
        String pkgName = "";
        synchronized (mApiLock) {
            if (null != mIMedia) {
                try {
                    AudioCp audioCp = mIMedia.getMediaAudioSource();
                    if (null != audioCp) {
                        pkgName = audioCp.getPkg();
                    }
                } catch (NullServiceException e) {
                    mIMedia = null;
                    MLog.e(TAG, "mIMedia.getMediaAudioSource() catch NullServiceException");
                } catch (RemoteException e) {
                    mIMedia = null;
                    MLog.e(TAG, "mIMedia.getMediaAudioSource() catch RemoteException");
                }
            }
        }
        return pkgName;
    }
}
