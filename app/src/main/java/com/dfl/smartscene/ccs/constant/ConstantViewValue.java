package com.dfl.smartscene.ccs.constant;

import androidx.annotation.DimenRes;

import com.dfl.smartscene.R;

/**
 * <AUTHOR>
 * @date ：2022/8/30 10:28
 * @description ：
 */
public class ConstantViewValue {

    public static final @DimenRes int COMMON_SHOW_SPACE = R.dimen.x_px_40;//item之间的间距



    public static final String KEY_MULTI_TITLE_FRAGMENTS = "KEY_MULTI_TITLE_FRAGMENTS";

    public static final String PAGE_MAIN_LIBRARY = "PAGE_MAIN_LIBRARY";//广场页面

    public static final String PAGE_MAIN_MY = "PAGE_MAIN_MY";//我的页面

    public static final String FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN = "SCENE_EDITOR_SCENE_BEAN";//传入场景编辑页的场景
    public static final String FRAGMENT_KEY_SCENE_EDITOR_TYPE = "FRAGMENT_KEY_SCENE_EDITOR_TYPE";//传入场景编辑页的操作类型，修改/添加
    public static final String FRAGMENT_KEY_SCENE_EDITOR_POS = "FRAGMENT_KEY_SCENE_EDITOR_POS";//传入场景编辑页修改的场景所在分类的位置

    public static final String SCENE_EDITOR_TYPE_SCENE_ADD = "SCENE_EDITOR_TYPE_SCENE_ADD";//添加场景
    public static final String SCENE_EDITOR_TYPE_SCENE_MODIFY = "SCENE_EDITOR_TYPE_SCENE_MODIFY";//修改场景
    public static final String SCENE_EDITOR_TYPE_SCENE_SHOW = "SCENE_EDITOR_TYPE_SCENE_SHOW";//展示场景

    public static final String FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID = "FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID";//传入场景编辑页编辑场景所属分类id
    public static final String FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_TITLE = "FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_TITLE";//传入场景编辑页所属分类名

    public static final String FRAGMENT_KEY_LIBRARY_CHILD_DATA = "FRAGMENT_KEY_LIBRARY_CHILD_DATA";

    public static final String FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_SCENE_BEAN = "FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_SCENE_BEAN";//传入首页场景详情页的场景
    public static final String FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_NAME = "FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_NAME";//传入首页场景详情页的场景
    public static final String FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_ID = "FRAGMENT_KEY_LIBRARY_SCENE_DETAIL_CATEGORY_ID";//传入首页场景详情页的场景

    public static final String FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN = "FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN";//传入服务态页面的场景
    public static final String FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN_NOW = "FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN_NOW";//是否立即执行场景

    public static final int SCENE_PATTERN_LAYER = 2430;//场景应用全屏画面使用的系统层级

    public static final int STATUS_ALL_SELECTED = 2;//我的页面编辑场景删除时的全选标识位
    public static final int STATUS_PART_SELECTED = 1;//我的页面编辑场景删除时的部分选中标识位
    public static final int STATUS_NONE_SELECTED = 0;//我的页面编辑场景删除时的无选中标识位

    public @interface SceneBeanStatus{

        int STATE_IDLE = 0;

        int STATE_START = 1;

        int STATE_PAUSE = 2;

        int STATE_STOP = 3;

        int STATE_DESTROY = 4;


    }

    /**AIOT广告的忽略时长, 为24h*/
    public static final long AIOT_AD_IGNORE_DURATIO = 24 * 60 * 60 * 1000;

    /**MMKV持久化存储的key设定*/
    public static final String AIOT_AD_IGNORE_TIME = "AIOT_AD_IGNORE_TIME";

    /**上拉加载和下拉刷新的布局收起时间*/
    public static final int REFRESH_REBOUND_TIMER = 1000;
    /**loading画面收起时间*/

    public static final int REFRESH_LOADING_TIMER = 800;
}
