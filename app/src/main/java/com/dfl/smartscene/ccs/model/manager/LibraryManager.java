package com.dfl.smartscene.ccs.model.manager;

import android.content.ActivityNotFoundException;
import android.content.Intent;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.core.LibraryModel;
import com.dfl.smartscene.ccs.core.LibraryModelCallback;
import com.dfl.smartscene.ccs.model.CCSDriveModel;
import com.dfl.smartscene.ccs.model.LibraryAiotModel;
import com.dfl.smartscene.ccs.model.LibraryCustomManager;
import com.dfl.smartscene.ccs.model.LibraryDAModel;
import com.dfl.smartscene.ccs.model.LibraryDimmingWindowModel;
import com.dfl.smartscene.ccs.model.LibraryDriveModel;
import com.dfl.smartscene.ccs.model.LibraryHvacModel;
import com.dfl.smartscene.ccs.model.LibraryLightModel;
import com.dfl.smartscene.ccs.model.LibraryNaviModel;
import com.dfl.smartscene.ccs.model.LibrarySeatModel;
import com.dfl.smartscene.ccs.model.LibraryVrModel;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/12/13 10:06
 * @description ：用于管理不同车型子模块的类，持用子模块的model对象，如果子模块没有特别实现，则使用通用的model
 */
public class LibraryManager {
    private static final String TAG = "LibraryManager";
    private static volatile LibraryManager sInstance;

    private LibraryModelParser mLibraryModelParser;
    public static LibraryManager getInstance(){
        if (null == sInstance){
            synchronized (LibraryManager.class){
                if (null == sInstance){
                    sInstance = new LibraryManager();
                }
            }
        }
        return sInstance;
    }

    private LibraryHvacModel mLibraryHvacModel;//子空调
    private LibraryNaviModel mLibraryNaviModel;//子导航
    private LibraryModel mLibraryModel;//子通用
    private LibraryCustomManager mLibraryCustomManager;//子customapi
    private LibraryVrModel mLibraryVrModel;//子语音
    private LibrarySeatModel mLibrarySeatModel;//子座椅
    private LibraryDriveModel mLibraryDriveModel;//子驾驶
    private LibraryAiotModel mLibraryAiotModel;//子aiot
    private LibraryDimmingWindowModel mLibraryDimmingWindowModel;//子调光玻璃
    private LibraryDAModel mLibraryDAModel;//子系统
    private LibraryLightModel mLibraryLightModel;//子氛围灯
    private CCSDriveModel mCCSDriveModel;//驾驶挡位监听

    private void init(){
        //注册和子模块交互的callback
        mLibraryModel.registerLibraryListener(mLibraryModelCallback);
        SceneDataModel.getInstance().registerDataListener(mLibraryModel);
    }
    private LibraryManager(){
        LogUtil.d(TAG,"init library manager");
        //解析子模块的model名
        mLibraryModelParser = new LibraryModelParser();
        mLibraryModelParser.parserModelName();

        //根据上面解析出来的全类名，生成对应的model类
        mLibraryHvacModel = getLibraryModel(LibraryHvacModel.class);
        mLibraryNaviModel = getLibraryModel(LibraryNaviModel.class);
        mLibraryModel = getLibraryModel(LibraryModel.class);
        mLibraryCustomManager = getLibraryModel(LibraryCustomManager.class);
        mLibraryVrModel = getLibraryModel(LibraryVrModel.class);
        mLibrarySeatModel = getLibraryModel(LibrarySeatModel.class);
        mLibraryDriveModel = getLibraryModel(LibraryDriveModel.class);
        mLibraryAiotModel = getLibraryModel(LibraryAiotModel.class);
        mLibraryDimmingWindowModel = getLibraryModel(LibraryDimmingWindowModel.class);
        mLibraryDAModel = getLibraryModel(LibraryDAModel.class);
        mLibraryLightModel = getLibraryModel(LibraryLightModel.class);
        mCCSDriveModel = getLibraryModel(CCSDriveModel.class);
        LogUtil.d(TAG,"end init library manager");
        if(null == mCCSDriveModel){
            LogUtil.d(TAG,"css drive model is null");
        }
        init();
    }

    /**
     * 根据传入的class类型，生成子模块对应的model
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> T getLibraryModel(Class<T> clazz){
        T result = null;
        if(mLibraryModelParser.containModelName(clazz.getSimpleName())){
            String fullClassName =  mLibraryModelParser.getModelName(clazz.getSimpleName());
            Class<?> realClazz = null;
            try {
                realClazz = Class.forName(fullClassName);
                Method method = realClazz.getMethod("getInstance");
                result = (T)method.invoke(null);
            } catch (ClassNotFoundException | NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        if(result == null){
            try {
                Method method = clazz.getMethod("getInstance");
                result = (T)method.invoke(null);
            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
                LogUtil.d(TAG,"use common model , no instance method" + clazz.getSimpleName());
            }
        }

        if(result == null){
            try {
                result = clazz.newInstance();
            } catch (IllegalAccessException | InstantiationException e) {
                e.printStackTrace();
            }
        }
        return result;

    }


    public LibraryCustomManager getCommonLibraryCustomManager() {
        return mLibraryCustomManager;
    }

    public LibraryHvacModel getLibraryHvacModel(){
        return mLibraryHvacModel;
    }

    public LibraryNaviModel getLibraryNaviModel() {
        return mLibraryNaviModel;
    }

    public LibraryDriveModel getLibraryDriveModel() {
        return mLibraryDriveModel;
    }

    public LibraryAiotModel getLibraryAiotModel() {
        return mLibraryAiotModel;
    }

    public LibraryModel getLibraryModel() {
        return mLibraryModel;
    }

    public void dispatchModelListenerInit(String apiName){
        mLibraryModel.initModelListener(apiName);
    }

    public LibraryDimmingWindowModel getLibraryDimmingWindowModel() {
        return mLibraryDimmingWindowModel;
    }

    public LibraryVrModel getLibraryVrModel() {
        return mLibraryVrModel;
    }

    public LibrarySeatModel getLibrarySeatModel() {
        return mLibrarySeatModel;
    }

    public LibraryDAModel getLibraryDAModel() {
        return mLibraryDAModel;
    }

    public LibraryLightModel getLibraryLightModel() {
        return mLibraryLightModel;
    }

    public CCSDriveModel getCCSDriveModel() {
        return mCCSDriveModel;
    }

    private LibraryModelCallback mLibraryModelCallback = new LibraryModelCallback() {
        @Override
        public void onSceneConditionMeet(String sceneId) {
            LogUtil.d(TAG,"scene sceneId : "+sceneId);
            SceneConditionManager.getInstance().onSceneConditionMeet(sceneId);
        }

        @Override
        public void onStartScene(String sceneId) {
            LogUtil.d(TAG,"scene condition : "+sceneId);
            SceneBean sceneBean = SceneDataModel.getInstance().findUserSceneBeanById(sceneId);
            if(sceneBean != null){
                LogUtil.d(TAG,"scene condition2 : "+sceneId);

                if (sceneBean.getEditType() > 0) {
                    Intent intent3 = new Intent();
                    intent3.setClassName("com.dfl.newscenepattern", "com.dfl.newscenepattern.MainActivity");
                    intent3.putExtra("ViewType", "Scene");
                    intent3.putExtra("INTENT_KEY_SCENE_ID", sceneId);
                    intent3.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    try {
                        ScenePatternApp.getInstance().startActivity(intent3);
                    } catch (ActivityNotFoundException e) {
                    }
                }else {
                    ViewControlManager.executeScene(sceneBean , "即将执行 ");
                }
            }
        }

        @Override
        public void onCarDataChange(String operationId, String value) {
            SceneConditionManager.getInstance().simpleConditionCheck(operationId, value);
        }
    };

}
