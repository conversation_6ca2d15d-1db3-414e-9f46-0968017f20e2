package com.dfl.smartscene.ccs.model;

import android.app.AlarmManager;
import android.content.Context;

import com.alibaba.fastjson.JSON;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.SceneConditionManager;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.OpeUtil;
import com.dfl.smartscene.ccs.wrapper.HttpWrapper;
import com.iauto.uibase.utils.MLog;
import com.tencent.mmkv.MMKV;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/11 11:41
 * @description ：时间相关的管理类
 */
public class TimeModel implements DeviceBaseModel, ConditionBaseModel, StateBaseModel {

    private static final String TAG = "TimeModel";
    private static volatile TimeModel sInstance;
    private AlarmManager mAlarmManager;//闹钟服务
    private String workHoliday;//工作日还是节假日，
    private static final String KEY_MMKV_ALL_HOLIDAY = "KEY_MMKV_ALL_HOLIDAY";
    private static final String KEY_MMKV_HOLIDAY_YEAR = "KEY_MMKV_HOLIDAY_YEAR";

    private MMKV mmkv = MMKV.defaultMMKV();
    public static TimeModel getInstance() {
        if (null == sInstance) {
            synchronized (TimeModel.class) {
                if (null == sInstance) {
                    sInstance = new TimeModel();
                }
            }
        }
        return sInstance;
    }

    /**
     * 初始化当天是工作日还是节假日
     */
    public void initWorkHoliday() {
        workHoliday = ConstantModelValue.VALUE_STATE_DAY_UNKNOW;
        Date d = new Date();
        SimpleDateFormat sf1 = new SimpleDateFormat("yyyy-MM-dd");
        LogUtil.d(TAG,"");
        HttpWrapper.getInstance().requestHolidayOrWorkday(sf1.format(d)).subscribe(new Observer<Boolean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Boolean aBoolean) {
                if (aBoolean) {
                    LogUtil.d(TAG, "today is holiday");
                    workHoliday = ConstantModelValue.VALUE_STATE_DAY_HOLIDAY;
                } else {
                    LogUtil.d(TAG, "today is workday");
                    workHoliday = ConstantModelValue.VALUE_STATE_DAY_WORKDAY;
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
        initAllHoliday();
    }

    public void initAllHoliday() {
        if(!workHoliday.equals(ConstantModelValue.VALUE_STATE_DAY_UNKNOW)){
            return;
        }

        //判断是否是周六周日
        Calendar calendar = Calendar.getInstance();
        int weekOriginDay = calendar.get(Calendar.DAY_OF_WEEK);
        if(weekOriginDay == 7 || weekOriginDay == 1){
            workHoliday = ConstantModelValue.VALUE_STATE_DAY_HOLIDAY;
        }else {
            workHoliday = ConstantModelValue.VALUE_STATE_DAY_WORKDAY;
        }

        Date d = new Date();
        SimpleDateFormat sfymd = new SimpleDateFormat("yyyy-MM-dd");
        String today = sfymd.format(d);
        String year = today.substring(0,4);
        MLog.d(TAG,"today : " + today);
        MLog.d(TAG,"this year : " + year);

        if(mmkv.containsKey(KEY_MMKV_HOLIDAY_YEAR) && mmkv.containsKey(KEY_MMKV_ALL_HOLIDAY) && year.equals(mmkv.decodeString(KEY_MMKV_HOLIDAY_YEAR))){
            List<String> holidays = JSON.parseArray(mmkv.decodeString(KEY_MMKV_ALL_HOLIDAY),String.class);
            if(holidays.contains(today)){
                //判断是否是节日
                LogUtil.d(TAG, "today is holiday2");
                workHoliday = ConstantModelValue.VALUE_STATE_DAY_HOLIDAY;
            }else {
                LogUtil.d(TAG, "today is workday2");
                workHoliday = ConstantModelValue.VALUE_STATE_DAY_WORKDAY;
            }
            return;
        }
        HttpWrapper.getInstance().requestAllHoliday().subscribe(new Observer<List<String>>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(List<String> strings) {
                if(!ListUtil.isEmpty(strings)){
                    mmkv.encode(KEY_MMKV_HOLIDAY_YEAR , year);

                    mmkv.encode(KEY_MMKV_ALL_HOLIDAY , JSON.toJSONString(strings));

                    if(workHoliday.equals(ConstantModelValue.VALUE_STATE_DAY_UNKNOW)){
                        if(strings.contains(today)){
                            LogUtil.d(TAG, "today is holiday3");
                            workHoliday = ConstantModelValue.VALUE_STATE_DAY_HOLIDAY;
                        }else {
                            LogUtil.d(TAG, "today is workday3");
                            workHoliday = ConstantModelValue.VALUE_STATE_DAY_WORKDAY;
                        }
                    }
                }

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        return false;
    }

    @Override
    public synchronized void registerSceneCondition(SceneBean sceneBean, SettingOperation condition) {
        MLog.d(TAG, "register = " + condition.getOperationId());
        if (condition.getOperationId().equals(ConstantModelValue.OID_CDT_EVENT_TIME)) {
            registerAlarmClock(sceneBean, condition);
        }
    }

    /**
     * 注册定时闹钟执行场景
     *
     * @param sceneBean
     * @param condition
     */
    public void registerAlarmClock(SceneBean sceneBean, SettingOperation condition) {
        if (mAlarmManager == null) {
            mAlarmManager = (AlarmManager) ScenePatternApp.getInstance().getSystemService(Context.ALARM_SERVICE);
        }
        LogUtil.d(TAG, "register timer : " + condition.getListArgs().get(0));
        String[] time = condition.getListArgs().get(0).split(":");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, DataTypeFormatUtil.string2Int(time[0]));
        calendar.set(Calendar.MINUTE, DataTypeFormatUtil.string2Int(time[1]));
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        if(calendar.getTimeInMillis() > Calendar.getInstance().getTimeInMillis()){
            mAlarmManager.setExact(AlarmManager.RTC, calendar.getTimeInMillis(), sceneBean.getSceneId(), new AlarmListener(sceneBean.getSceneId()), null);
        }
    }


    /**
     * 闹钟唤醒后执行的动作
     */
    static class AlarmListener implements AlarmManager.OnAlarmListener {
        private String mSceneId;

        public AlarmListener(String sceneId) {
            mSceneId = sceneId;
        }

        @Override
        public void onAlarm() {
            MLog.d(TAG, "AlarmListener onAlarm : " + mSceneId);
            SceneBean sceneBean = SceneConditionManager.getInstance().getSceneById(mSceneId);
            if (sceneBean != null) {
                MLog.d(TAG, "AlarmListener onSceneConditionMeet");
                SceneConditionManager.getInstance().onSceneConditionMeet(sceneBean);
            }
        }
    }


    @Override
    public synchronized void unRegisterSceneCondition(SceneBean sceneBean, SettingOperation condition) {

    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        switch (settingOperation.getOperationId()) {
            //检查时间是否满足状态
            case ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK:
                return checkTimeStateOperationMeet(settingOperation);
            //检查日期是否满足状态
            case ConstantModelValue.OPERATION_ID_STATE_TIME_DAY:
                return checkDayStateOperationMeet(settingOperation);
            default:
                return false;
        }
    }

    /**
     * 检查时间是否满足状态
     *
     * @param settingOperation
     * @return
     */
    public boolean checkTimeStateOperationMeet(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation,2)){
            return false;
        }
        String params1 = settingOperation.getListArgs().get(0);
        String params2 = settingOperation.getListArgs().get(1);
        if (params1.equals(params2)) {
            return true;
        }
        Calendar calendar = Calendar.getInstance();
        int nowHour = calendar.get(Calendar.HOUR_OF_DAY);
        int nowMinute = calendar.get(Calendar.MINUTE);
        String[] paramArray1 = params1.split(":");
        String[] paramArray2 = params2.split(":");

        try {
            int limitHour1 = DataTypeFormatUtil.string2Int(paramArray1[0]);
            int limitMinute1 = DataTypeFormatUtil.string2Int(paramArray1[1]);
            int limitHour2 = DataTypeFormatUtil.string2Int(paramArray2[0]);
            int limitMinute2 = DataTypeFormatUtil.string2Int(paramArray2[1]);
            boolean leftCheck = (nowHour * 60 + nowMinute) >= (limitHour1 * 60 + limitMinute1);
            if (limitHour2 * 60 + limitMinute2 > limitHour1 * 60 + limitMinute1) {
                return leftCheck && (limitHour2 * 60 + limitMinute2) >= (nowHour * 60 + nowMinute);
            } else {
                return leftCheck && ((limitHour2 + 24) * 60 + limitMinute2) >= (nowHour * 60 + nowMinute);
            }

        } catch (Exception e) {
            MLog.e(TAG, "checkTimeStateOperationMeet , try catch");
            return false;
        }
    }

    /**
     * 检查日期是否满足状态
     *
     * @param settingOperation
     * @return
     */
    public boolean checkDayStateOperationMeet(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return false;
        }
        String arg = settingOperation.getListArgs().get(0);
        //每一天
        if (ConstantModelValue.VALUE_STATE_DAY_EVERYDAY.equals(arg)) {
            return true;
        }
        //工作日，节假日
        if (workHoliday != null && workHoliday.equals(arg)) {
            return true;
        }
        //用户自定义
        int dayArg = DataTypeFormatUtil.string2Int(arg);
        Calendar calendar = Calendar.getInstance();
        int weekOriginDay = calendar.get(Calendar.DAY_OF_WEEK);
        LogUtil.d(TAG,"weekOriginDay : " +  weekOriginDay);
        //周日-1 周一-2 ->周一-1 周二-2
        int weekDay = weekOriginDay - 1 == 0 ? 7 : weekOriginDay - 1;
        LogUtil.d(TAG,"weekDay : " +  weekDay);

        return (dayArg & (1 << (weekDay - 1))) > 0;
    }
}
