package com.dfl.smartscene.ccs.viewmodel;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.api.app.usercenter.common.ICommon;
import com.dfl.api.base.APIStateType;

import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.busevent.CustomapiEvent;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.TimeModel;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.wrapper.MqttWrapper;

import java.util.concurrent.TimeUnit;

import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/12/31 14:21
 * @description ：
 */
public class MainFragmentViewModel extends ViewModel {
    private static final String TAG = "MainFragmentViewModel";
    //首页的轻松一刻和便捷服务
    private MutableLiveData<Integer> mainPagePos = new MutableLiveData<>();
    //我的分类位置id
    private MutableLiveData<String> myPageCategoryId = new MutableLiveData<>();

    public MutableLiveData<Integer> getMainPagePos() {
        return mainPagePos;
    }
    public void setMainPagePos(int pos) {
        mainPagePos.setValue(pos);
    }

    public MutableLiveData<String> getMyPageCategoryId() {
        return myPageCategoryId;
    }

    public void setMyPageCategoryId(String categoryId) {
        myPageCategoryId.setValue(categoryId);
    }

    public void init(){
        //判断是否拉起个人中心
        checkUserLogin();
    }

    /**
     * 判断是否拉起个人中心
     * */
    private void checkUserLogin(){
        LogUtil.d(TAG, "on service init checkUserLogin");
        ICommon iCommon = CustomApiManager.getInstance().getICommon();
        if(null == iCommon){
            LogUtil.e(TAG, "icommon api hasn't ready");
            //注册接收完成事件
            RxBus.getDefault().register(this);
        }else{
            boolean login = DAModel.getInstance().isUserCenterLogin();
            LogUtil.d(TAG, "login = "+login);
            if(!login) {
                DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                    @Override
                    public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return v -> {
                            Intent intent = new Intent(null, Uri.parse("usercenter://com.szlanyou/login?cp_type=24&target_page=1"));
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                            ScenePatternApp.getInstance().getApplicationContext().startActivity(intent);
                            libDialog.dismiss();
                        };
                    }

                    @Override
                    public int getDialogLayout() {
                        return R.layout.dialog_double_content_layout;
                    }

                    @Override
                    public View.OnClickListener getNegativeButtonListener(LibDialog dialog) {
                        return v -> {
                            dialog.dismiss();
                            System.exit(0);};
                    }

                    @Override
                    public String getDialogMessage() {
                        return "需要登录日产车联账号，是否前往登录";
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return "前往登录";
                    }
                });
            }else{
                Schedulers.computation().scheduleDirect(() -> {
                    initMqttConnect();
                }, 1, TimeUnit.SECONDS);
            }
        }
    }

    @Subscribe
    public void onServiceInitEvent(CustomapiEvent customapiEvent) {
        LogUtil.d(TAG, "on service init event");
        if (Constants.DFL_APP_USERCENTER_COMMON_COMMON_SERVICE.equals(customapiEvent.getCutomapiName())
        && customapiEvent.getState() == APIStateType.APIState.SUCCESS) {
            //取消注册监听
            RxBus.getDefault().unRegister(this);
            boolean login = DAModel.getInstance().isUserCenterLogin();
            LogUtil.d(TAG, "login = "+login);
            if(!login) {
                DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                    @Override
                    public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                        return v -> {
                            Intent intent = new Intent(null, Uri.parse("usercenter://com.szlanyou/login?cp_type=24&target_page=1"));
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                            ScenePatternApp.getInstance().getApplicationContext().startActivity(intent);
                            libDialog.dismiss();
                        };
                    }

                    @Override
                    public int getDialogLayout() {
                        return R.layout.dialog_double_content_layout;
                    }

                    @Override
                    public View.OnClickListener getNegativeButtonListener(LibDialog dialog) {
                        return v -> {
                            dialog.dismiss();
                            System.exit(0);};
                    }

                    @Override
                    public String getDialogMessage() {
                        return "需要登录日产车联账号，是否前往登录";
                    }

                    @Override
                    public String getPositiveButtonName() {
                        return "前往登录";
                    }
                });
            }else{
                Schedulers.computation().scheduleDirect(() -> {
                    initMqttConnect();
                }, 1, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 初始化MQTT连接
     */
    private void initMqttConnect(){
        if(MqttWrapper.getInstance().isConnected()){
            return;
        }
        MqttWrapper.getInstance().createConnect();
        //初始化节假日
        TimeModel.getInstance().initWorkHoliday();
    }

    /**
     * 初始化彩蛋项目的Toast组件
     * @param context
     */
    public void initToastManager(Context context){
        ToastManager.init(context);
    }
}
