package com.dfl.smartscene.ccs.constant;

import android.text.TextUtils;

import com.dfl.api.app.usercenter.weather.WeatherItems;

import java.util.HashMap;

/**
 * 个人中心拿到的天气值
 */
public class WeatherValue {

    //
    public static final String WEATHER_NONE = "";
    public static final String WEATHER_SUN = "sun";
    public static final String WEATHER_SNOW = "snow";
    public static final String WEATHER_RAIN = "rain";
    public static final String WEATHER_CLOUDY = "cloudy";
    public static final String WEATHER_HAZE = "haze";

    public static final String[] CONDITION_TYPE = {
            "晴天",                   //晴天
            "多云",                  //多云
            "阴天",                //阴天
            "阵雨",                  //阵雨
            "雷阵雨",                   //雷阵雨
            "雷阵雨伴有冰雹",    //雷阵雨伴有冰雹
            "雨夹雪",                   //雨夹雪
            "小雨",               //小雨
            "中雨",            //中雨
            "大雨",               //大雨
            "暴雨",               //暴雨
            "小雪",               //小雪
            "中雪",               //中雪
            "大雪",               //大雪
            "暴雪",            //暴雪
            "雾",                     //雾
            "沙尘暴",               //沙尘暴
            "浮尘",            //浮尘
            "扬沙",       //扬沙
            "扬沙",       //扬沙
            "霾",                    //霾
            "冰粒",            //冰粒
            "少云",                 //少云
            "尘卷风",             //尘卷风
            "雷暴",            //雷暴
    };

    public static final HashMap<String,String> AQI_BG = new HashMap<>();
    static {
        AQI_BG.put("优","非常好");
        AQI_BG.put("良","挺好的");
        AQI_BG.put("轻度","一般");
        AQI_BG.put("中度","较差");
        AQI_BG.put("重度","非常差");
    }


    public static String parseWeatherCondition(int condition) {
       switch (condition) {
           case 0:
               return WEATHER_SUN;
           case 2:
               return WEATHER_CLOUDY;
           case 3:
           case 4:
           case 5:
           case 6:
           case 7:
           case 8:
           case 9:
           case 10:
               return WEATHER_RAIN;
           case 11:
           case 12:
           case 13:
           case 14:
               return WEATHER_SNOW;
           case 15:
           case 20:
               return WEATHER_HAZE;
           default:
               return WEATHER_NONE;
       }
    }

    public static String packageWeatherInfo(WeatherItems weatherItems) {

        if(null == weatherItems) {
            return "";
        }

        int conditionIndex = Integer.parseInt(weatherItems.getConditionNum());

        String province = weatherItems.getProvince();
        String cityName = weatherItems.getCityName();
        String condition = weatherItems.getCondition();
        if(!TextUtils.isEmpty(province) && province.equals(cityName)) {
            cityName = "";
        }
        String realConditionDesc;
        if(conditionIndex >= WeatherValue.CONDITION_TYPE.length){
            realConditionDesc = condition;
        }else {
            realConditionDesc = WeatherValue.CONDITION_TYPE[conditionIndex];
        }

        return province +
                cityName +
                "今天" +
                realConditionDesc +
                "," +
                weatherItems.getMinTem() +
                "到" +
                weatherItems.getMaxTem() +
                "度,风力" +
                weatherItems.getWindLevel() +
                "级," +
                "空气质量" +
                WeatherValue.AQI_BG.get(weatherItems.getAqiValue());
    }

}
