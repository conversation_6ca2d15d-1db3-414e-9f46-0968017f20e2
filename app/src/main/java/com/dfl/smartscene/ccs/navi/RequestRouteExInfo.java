package com.dfl.smartscene.ccs.navi;

public class RequestRouteExInfo {
    //规划类型规划类型0：进⼊路线规划结果⻚（前台） 1：直接进⼊导航 2：不跳转⻚⾯，只透出信息（静默算路，不影响当前路线） 3：传⼊起点、终点进⾏路线规划；（必填）
    private int actionType;
    private ProtoocolPoi startProtocolPoi;
    private ProtoocolPoi endProtocolPoi;
    private ProtoocolPoi midProtocolPoi;
    int dev = 0;
    int strategy;


    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public ProtoocolPoi getStartProtocolPoi() {
        return startProtocolPoi;
    }

    public void setStartProtocolPoi(ProtoocolPoi startProtocolPoi) {
        this.startProtocolPoi = startProtocolPoi;
    }

    public ProtoocolPoi getEndProtocolPoi() {
        return endProtocolPoi;
    }

    public void setEndProtocolPoi(ProtoocolPoi endProtocolPoi) {
        this.endProtocolPoi = endProtocolPoi;
    }

    public ProtoocolPoi getMidProtocolPoi() {
        return midProtocolPoi;
    }

    public void setMidProtocolPoi(ProtoocolPoi midProtocolPoi) {
        this.midProtocolPoi = midProtocolPoi;
    }

    public int getDev() {
        return dev;
    }

    public void setDev(int dev) {
        this.dev = dev;
    }

    public int getStrategy() {
        return strategy;
    }

    public void setStrategy(int stategy) {
        this.strategy = stategy;
    }


    public static class ProtoocolPoi {
        String poiId;//poi唯一参数（必填）
        String poiName;//poi名称（没有的话不要填，不能写死）（可选）
        double longitude;//poi经度，经纬度小数点后不得超过6位（必填）
        double latitude;//poi纬度，经纬度小数点后不得超过6位（必填）
        double entryLongitude;//到达点经度，经纬度小数点后不得超过6位（可选）
        double entryLatitude;//到达点纬度，经纬度小数点后不得超过6位（可选）
        String nTypeCode;//poi类型（搜索结果会透出）（可选）
        int midTyp;//途经点类型，0:普通点，1：家，2：公司（可选）
        String address;//poi地点（可选）
        int index; //第几个途经点（可选）

        public String getPoiId() {
            return poiId;
        }

        public void setPoiId(String poiId) {
            this.poiId = poiId;
        }

        public String getPoiName() {
            return poiName;
        }

        public void setPoiName(String poiName) {
            this.poiName = poiName;
        }

        public double getLongitude() {
            return longitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }

        public double getLatitude() {
            return latitude;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }

        public double getEntryLongitude() {
            return entryLongitude;
        }

        public void setEntryLongitude(double entryLongitude) {
            this.entryLongitude = entryLongitude;
        }

        public double getEntryLatitude() {
            return entryLatitude;
        }

        public void setEntryLatitude(double entryLatitude) {
            this.entryLatitude = entryLatitude;
        }

        public String getnTypeCode() {
            return nTypeCode;
        }

        public void setnTypeCode(String nTypeCode) {
            this.nTypeCode = nTypeCode;
        }

        public int getMidTyp() {
            return midTyp;
        }

        public void setMidTyp(int midTyp) {
            this.midTyp = midTyp;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
}

