package com.dfl.smartscene.ccs.util;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 横向排列的gridlayout的recycleview的间距设置
 */
public class HorizontalGridItemDecorationUtil extends RecyclerView.ItemDecoration {
    private int lineSpace;//行间距
    private int columnSpace;//列间距
    private int itemCount;//

    private int divideH;//一个item在recycleview占用格子的高度
    private int itemH;//一个item实际的高度


    public HorizontalGridItemDecorationUtil(@DimenRes int lineSpace, @DimenRes int columnSpace, int column) {
        this.lineSpace = lineSpace;
        this.columnSpace = columnSpace;
        itemCount = column;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        if(divideH == 0){
            divideH = parent.getMeasuredHeight()/itemCount;
        }
        if(itemH == 0){
            //此时该view未测量，在布局里item不可使用matchparent或者wrapcontent
            itemH = view.getLayoutParams().height;
        }
        int pos = parent.getChildLayoutPosition(view);
        int columnPos = pos % itemCount;
        //计算移动位置
        if(columnPos == 0){
            outRect.top = 0;
        }else {
            outRect.top = columnPos * (itemH + columnSpace - divideH);
        }
        outRect.bottom = 0;
        outRect.right = lineSpace;
    }
}

