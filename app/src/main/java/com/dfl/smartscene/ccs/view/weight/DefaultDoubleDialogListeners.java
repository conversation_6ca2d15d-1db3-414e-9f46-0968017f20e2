package com.dfl.smartscene.ccs.view.weight;

import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;

import com.dfl.dflcommonlibs.appcommon.AppCommonManager;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;

public abstract class DefaultDoubleDialogListeners implements DoubleCountDownView.DoubleDialogListener {
    private final String TAG = "DefaultDoubleDialogListener";

    public int getPositiveButtonId() {
        return R.id.button_default_double_positive;
    }

    public String getPositiveButtonName() {
        return "确定";
    }

    public int getNegativeButtonId() {
        return R.id.button_default_double_negative;
    }

    public int getNegativeFrameId() {
        return R.id.frame_layout_button_negative;
    }

    public int getPositiveFrameId() {
        return R.id.frame_layout_button_positive;
    }

    public String getNegativeButtonName() {
        return "取消";
    }

    public View.OnClickListener getNegativeButtonListener(final FrameLayout dialog) {
        return new View.OnClickListener() {
            public void onClick(View v) {
                LogUtil.d("DefaultDoubleDialogListener", "NegativeButton onClick: ");
                AppCommonManager.getInstance().playBeepSpund();
            }
        };
    }

    public View.OnClickListener getPositiveButtonListener(final FrameLayout dialog) {
        return new View.OnClickListener() {
            public void onClick(View v) {
                AppCommonManager.getInstance().playBeepSpund();
            }
        };
    }

    public View.OnClickListener getCloseButtonListener(final FrameLayout dialog) {
        return new View.OnClickListener() {
            public void onClick(View v) {
                AppCommonManager.getInstance().playBeepSpund();
            }
        };
    }

    public int getTitleId() {
        return R.id.text_view_default_double_dialog_title;
    }

    public int getDialogLayout(DoubleCountDownView libDialog) {
        return TextUtils.isEmpty(this.getDialogTitle(libDialog)) ? R.layout.dialog_double_default_layout : R.layout.dialog_double_title_layout;
    }

    public int getMessageTextId() {
        return R.id.text_view_default_double_dialog_message;
    }

    public Boolean showCloseButton() {
        return false;
    }
}