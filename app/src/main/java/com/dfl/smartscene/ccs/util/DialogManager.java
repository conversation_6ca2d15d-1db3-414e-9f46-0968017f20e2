package com.dfl.smartscene.ccs.util;

import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.popup.IPopupCallback;
import com.dfl.api.da.popup.PopupDef;
import com.dfl.dflcommonlibs.dialog.DefaultDoubleCountDownListener;
import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DefaultSingerDialogListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.DialogUtilInterface;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.view.weight.DefaultDoubleCountDownListeners;
import com.dfl.smartscene.ccs.view.weight.DoubleCountDownView;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/10/14 13:57
 * @description ：
 */
public class DialogManager {

    /**
     *This method is show singal dialog
     *@param defaultSingerDialogListener
     */
    public static void showDefaultSingerDialog(DefaultSingerDialogListener defaultSingerDialogListener)
    {
        DialogUtil.showDefaultSingerDialog(defaultSingerDialogListener);
    }

    /**
     *This method is show dobule dialog
     *@param defaultDoubleDialogListener
     */
    public static void showDefaultDoubleDialog(DefaultDoubleDialogListener defaultDoubleDialogListener)
    {
        DialogUtil.showDefaultDoubleDialog(defaultDoubleDialogListener);
    }

    /**
     *This method is show dialog
     *@param dialogUtilInterface
     */
    public static void showDialog(DialogUtilInterface dialogUtilInterface)
    {
        DialogUtil.showDialog(dialogUtilInterface);
    }

    /**
     *This method is show list dialog
     *@param adapter
     */
    public static void showListDialog(RecyclerView.Adapter adapter)
    {
        DialogUtil.showListDialog(adapter);
    }
    private static final String TAG = "DialogManager";
    public static final String POPID_CONDITION_DIALOG = "PopId_NewScenepattern_Condition";
    /**
     * key:popId
     * value:status
     */
    private static Map<String, Integer> popStatusMap = new HashMap<>(1);
    private static TextView mTextView;
    private static DoubleCountDownView mview;
    private static String mViewId = "addMusic2ListFail";
    public static void initPopup() {
        if (CustomApiManager.getInstance().getIPopUp() != null) {
            try {
                CustomApiManager.getInstance().getIPopUp().registerPopupCallback(new IPopupCallback() {
                    @Override
                    public void onPopupStateChanged(String s, int i) {
                        LogUtil.d(TAG, "onPopupStateChanged  : " + s + i);
                        popStatusMap.put(s, i);
                    }

                    @Override
                    public void onPopupShowingChanged(boolean b) {
                        LogUtil.d(TAG, "onPopupShowingChanged  : " + b);

                    }
                });
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
    }

    public static void showExcuteSceneDialog(SceneBean sceneBean) {
        DialogUtil.showDefaultDoubleDialog(new DefaultDoubleCountDownListener() {
            @Override
            public int getCountDownNumber() {
                return 5;
            }

            @Override
            public int getCountDownTextId() {
                return R.id.button_default_double_positive;
            }


            @Override
            public void onCountDownFinish(LibDialog dialog) {
                getPositiveButtonListener(dialog).onClick(null);
                //埋点：点击取消试用
                LogUtil.d(TAG, "点击取消试用");
                BigDataManager.getInstance().writeEventMod3(dialog.getContext().getString(R.string.scene_click_un_use));
            }

            @Override
            public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                return v -> {
                    ViewControlManager.executeScene(sceneBean, "正在执行 ");
                    libDialog.dismiss();
                    //埋点：点击取消试用
                    LogUtil.d(TAG, "立即执行");
                    BigDataManager.getInstance().writeEventMod3(libDialog.getContext().getString(R.string.scene_click_un_use));
                };
            }

            @Override
            public String getDialogMessage() {
                return "开始试用 " + sceneBean.getSceneName() + "? 试用中请确保车辆及人员处于安全状态";
            }


            @Override
            public int getDialogLayout() {
                return R.layout.dialog_double_title_layout_2;
            }
        });

    }

    public synchronized static void showSceneConditionNotifyDialog(SceneBean sceneBean) {
        if (CustomApiManager.getInstance().getIPopUp() == null) {
            return;
        }

        DefaultDoubleCountDownListeners dialogParams = new DefaultDoubleCountDownListeners() {
            @Override
            public int getCountDownNumber() {
                return 10;
            }

            @Override
            public int getDialogLayout() {
                return R.layout.dialog_double_title_content_layout;
            }

            @Override
            public int getCountDownTextId() {
                return R.id.button_default_double_positive;
            }

            @Override
            public void onCountDownFinish(DoubleCountDownView dialog) {
                getPositiveButtonListener(dialog).onClick(null);
            }

            @Override
            public View.OnClickListener getPositiveButtonListener(DoubleCountDownView libDialog) {
                return v -> {
                    libDialog.clearTimerDisposable();
                    hideAndReleasePopup(POPID_CONDITION_DIALOG);
                    //如果全部条件都满足
                    LogUtil.d(TAG, "getPositiveButtonListener 测试执行");
                    ViewControlManager.executeScene(libDialog.getSceneBean(), "即将执行 ");
                };
            }

            @Override
            public View.OnClickListener getNegativeButtonListener(DoubleCountDownView libDialog) {
                return v -> {
                    libDialog.clearTimerDisposable();
                    LogUtil.d(TAG, "getNegativeButtonListener 测试执行");
                    hideAndReleasePopup(POPID_CONDITION_DIALOG);
                };
            }

            @Override
            public String getPositiveButtonName() {
                return "立即执行";
            }

            @Override
            public String getDialogTitle(DoubleCountDownView libDialog) {
                if (null == libDialog.getSceneBean()){
                    //第一次是空的
                    return "即将执行 " + sceneBean.getSceneName();
                }else {
                    return "即将执行 " + libDialog.getSceneBean().getSceneName();
                }
            }
        };
        if (mview == null) {
            mview = new DoubleCountDownView(dialogParams, ScenePatternApp.getInstance());
        }
        mview.setSceneBean(sceneBean);
        int x = ScenePatternApp.getInstance().getResources().getDimensionPixelSize(R.dimen.x_px_498);
        int y = ScenePatternApp.getInstance().getResources().getDimensionPixelSize(R.dimen.y_px_115);
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT, x, y,
                PopupDef.LAYOUTPARAMS_TYPE_POPUP,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
                PixelFormat.TRANSLUCENT);
        params.gravity = Gravity.TOP | Gravity.START;
        setAndShowPopUp(POPID_CONDITION_DIALOG,mview,PopupDef.Popup_Type_Timer,params);
    }

    public synchronized static void setAndShowPopUp(String popId, View view, int type, WindowManager.LayoutParams params) {
        if (CustomApiManager.getInstance().getIPopUp() == null) {
            return;
        }
        mViewId = popId;
        try {
            LogUtil.e(TAG,"dialog 状态显示 " + CustomApiManager.getInstance().getIPopUp().isPopupShowing());
            CustomApiManager.getInstance().getIPopUp().setPopup(popId, type, view, params);
            CustomApiManager.getInstance().getIPopUp().showPopup(popId);
        } catch (Exception e) {
            LogUtil.e(TAG, "setAndShowPopUp: " + e.getMessage());
        }
    }

    public static void hideAndReleasePopup(String popId) {
        LogUtil.e(TAG,"dialog hideAndReleasePopup " );
        if (CustomApiManager.getInstance().getIPopUp() == null) {
            return;
        }
        try {
            mview.setVisibility(View.GONE);
            CustomApiManager.getInstance().getIPopUp().hidePopup(popId);
            mViewId = null;
            mview = null;
//            CustomApiManager.getInstance().getIPopUp().releasePopup(popId);
        } catch (Exception e) {
            LogUtil.e(TAG, "hideAndReleasePopup: " + e.getMessage());
        }
    }

}
