package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.model.LibraryNaviModel;
import com.dfl.smartscene.ccs.model.NaviModel;

/**
 * <AUTHOR>
 * @date ：2023/6/2 9:18
 * @description ：
 */
public class NaviViewModel extends ViewModel {

    private MutableLiveData<String> collectPlace = new MutableLiveData<>();

    public MutableLiveData<String> getCollectPlace() {
        return collectPlace;
    }

    public NaviViewModel(){
        NaviModel.getInstance().registerNaviCollectPlacesCallback(new LibraryNaviModel.NaviCollectPlaceListener() {
            @Override
            public void onCollectPlaceChange(String json) {
                LogUtil.d("NaviViewModel" , "onCollectPlaceChange : " + json);
                collectPlace.postValue(json);
            }
        });
    }

    @Override
    protected void onCleared() {
        NaviModel.getInstance().unregisterNaviCollectPlacesCallback();
    }
}
