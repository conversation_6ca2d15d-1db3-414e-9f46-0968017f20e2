package com.dfl.smartscene.ccs.view.operationview;

import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.FragmentManager;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.view.adapter.OpeRecyclerAdapter;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.UserDayDialogFragment;
import com.iauto.uibase.utils.MLog;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/16 11:31
 * @description ：
 */
public class RecyclerStateDayOperation extends RecyclerButtonOperation {
    private static final String TAG = "RecyclerStateDayOperation";
    private String userValue;
    private String userDesc;

    public RecyclerStateDayOperation(SingleOperation singleOperation, ViewGroup parent) {
        super(singleOperation, parent);
    }

    @Override
    public void onItemClick(View view, int viewType, OpeRecyclerAdapter.DataBean dataBean, int position) {
        String defaultPos = mSingleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        LogUtil.d("RecyclerStateDayOperation","defaultPos-----:"+defaultPos);
        if (dataBean.getValue().equals(String.valueOf(ConstantModelValue.VALUE_STATE_DAY_USERDAY)) && ScenePatternApp.getFragmentManager() != null) {
            //点击自定义按钮
            UserDayDialogFragment userDayDialogFragment = new UserDayDialogFragment();
            if(!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_EVERYDAY)&&!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_WORKDAY)&&!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_HOLIDAY)){
                userDayDialogFragment.setInitValue(Integer.parseInt(defaultPos));
            }
            userDayDialogFragment.setUserDayClickListener(new UserDayDialogFragment.UserDayClickListener() {
                @Override
                public void onClickCancel() {
//                    ((OperationBaseDialogFragment) FragmentManager.findFragment(view)).onNegativeButtonClick();
                    if (mContentView != null) {
                        ((OperationBaseDialogFragment) FragmentManager.findFragment(mContentView)).onNegativeButtonClick();
                    } else {
                        MLog.d("TAG","mContentView==null");
                    }
                }

                @Override
                public void onClickConfirm(String value, String desc) {
                    onClickBack(value, desc);
//                    ((OperationBaseDialogFragment) FragmentManager.findFragment(view)).onPositiveButtonClick();
                    if (mContentView != null) {
                        ((OperationBaseDialogFragment) FragmentManager.findFragment(mContentView)).onPositiveButtonClick();
                    } else {
                        MLog.d("TAG","mContentView==null");
                    }
                }


                @Override
                public void onClickBack(String value, String desc) {
                    userValue = value;
                    userDesc = desc;
                    if (value.equals("127")) {
                        mOpeRecyclerAdapter.setSelesctedPos(0);
                    } else {
                        mOpeRecyclerAdapter.setSelesctedPos(3);
                    }

                }
            });
            userDayDialogFragment.show(ScenePatternApp.getFragmentManager(), null);
        } else {
            //点击其他按钮
            super.onItemClick(view, viewType, dataBean, position);
        }
    }

    @Override
    public void initData(SingleOperation singleOperation) {
        super.initData(singleOperation);
        String defaultPos = mSingleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        LogUtil.d("RecyclerStateDayOperation","defaultPos-----:"+defaultPos);
        if(!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_EVERYDAY)&&!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_WORKDAY)&&!defaultPos.equals(ConstantModelValue.VALUE_STATE_DAY_HOLIDAY)){
            if(userValue == null){
                userValue = defaultPos;
            }
            if(userValue!=null && userDesc == null){
                userDesc = getDescByDayValue(Integer.parseInt(userValue));
            }
        }
    }

    @Override
    public List<String> extractArgs() {
        if (mOpeRecyclerAdapter.getItemData(mOpeRecyclerAdapter.getSelectPos()).getValue().equals(ConstantModelValue.VALUE_STATE_DAY_USERDAY)) {
            return Arrays.asList(userValue);
        }
        return super.extractArgs();
    }

    @Override
    public String extractDesc() {
        if (mOpeRecyclerAdapter.getItemData(mOpeRecyclerAdapter.getSelectPos()).getValue().equals(ConstantModelValue.VALUE_STATE_DAY_USERDAY)) {
            String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
            if (null == userDesc){
                return desc;
            }
            return desc.replaceFirst("&", userDesc);
        }
        return super.extractDesc();
    }

    public String getDescByDayValue(int dayValue) {
        StringBuilder stringBuilder = new StringBuilder();
        List<String> list = Arrays.asList("周一","周二","周三","周四","周五","周六","周日");
        for(int i = 0 ; i < 7 ; i++){
            if(((dayValue >> i) & 1) != 0){
                if(!stringBuilder.toString().equals("")){
                    stringBuilder.append(",");
                }
                stringBuilder.append(list.get(i));
            }
        }
        return stringBuilder.toString();
    }
}
