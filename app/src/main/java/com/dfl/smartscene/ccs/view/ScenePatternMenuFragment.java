package com.dfl.smartscene.ccs.view;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.dflcommonlibs.dialog.DefaultDoubleDialogListener;
import com.dfl.dflcommonlibs.dialog.DialogUtil;
import com.dfl.dflcommonlibs.dialog.LibDialog;
import com.dfl.scenepattern.R;
import com.dfl.scenepattern.constant.RelieveStressFuncDef;
import com.dfl.scenepattern.constant.ScenePatternFuncDef;
import com.dfl.scenepattern.databinding.FragmentScenepatternMenuBinding;
import com.dfl.scenepattern.model.SurpriseEggListModel;
import com.dfl.scenepattern.util.BigDataControl;
import com.dfl.scenepattern.util.ScenePatternViewManager;
import com.dfl.scenepattern.viewmodel.ScenePatternMenuVM;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ButtonBase;
import com.iauto.uicontrol.ButtonView;

import java.util.Map;

public class ScenePatternMenuFragment extends ScenePatternBaseFragment {
    private static final String     TAG      = "ScenePatternMenuFragment";

    private FragmentScenepatternMenuBinding binding    = null;
    private ScenePatternMenuVM              mViewModel = null;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        MLog.d(TAG, "onCreateView");
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_scenepattern_menu, container, false);
        mViewModel = new ViewModelProvider(requireActivity()).get(ScenePatternMenuVM.class);
        binding.setVm(mViewModel);
        binding.setLifecycleOwner(this);
        initCCEventReceiver((ViewGroup) binding.getRoot());
        MLog.d(TAG, "onCreateView end");
        return binding.getRoot();
    }

    private void initCCEventReceiver(ViewGroup view) {
        for (int i = 0; i < view.getChildCount(); i++) {
            View childView = view.getChildAt(i);
            if (childView instanceof ButtonBase) {
                ButtonBase child = (ButtonBase) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ButtonView) {
                ButtonView child = (ButtonView) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ViewGroup) {
                initCCEventReceiver((ViewGroup) childView);
            }
        }
    }

    /**
     * Relieve Pattern Status relate button's event method
     * @param v Relieve stress pattern fragment view
     */
    public void onCCMethod_MenuClick(View v) {
        MLog.d(TAG, "onCCMethod_MenuClick: ");
        int RelievePatternStatus = RelieveStressFuncDef.Relieve_Stress_Status_Stop;
        int id = v.getId();
        if (id == R.id.relieve_stress_button) {
            onRelieveStressClick();
        } else if (id == R.id.surprise_egg_button) {
            onSurpriseEggClick();
        }
    }

    private void onRelieveStressClick() {
        MLog.d(TAG, "onRelieveStressClick: ");
        ScenePatternViewManager.getInstance().transFragment(ScenePatternFuncDef.RelieveStressPatternFragment_TAG,ScenePatternFuncDef.TransType.ScenePattern_Normal);
        makeBigData(ScenePatternFuncDef.BIGDATA_VALUE_PATTERN_SEL_RELIEVE);
        Map map = BigDataControl.getInstance().makeStartFrom(ScenePatternFuncDef.BIGDATA_VALUE_START_FROM_MENU);
        BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_1,
                ScenePatternFuncDef.BIGDATA_NAME_START,
                ScenePatternFuncDef.BIGDATA_EVENT_TYPE_PV,
                map);
    }

    private void onSurpriseEggClick() {
        MLog.d(TAG, "onSurpriseEggClick: ");
        if (PackageManager.PERMISSION_GRANTED != ContextCompat.checkSelfPermission(getContext(), "android.permission.INTERNET_CONTROL")) {
            requestPermissions(new String[]{"android.permission.INTERNET_CONTROL"}, ScenePatternFuncDef.PERMISSIONS_REQUEST_INTERNET_CODE);
            return;
        }

        if(!SurpriseEggListModel.getInstance().isUserCenterLogin()) {
            Intent intent=new Intent(null, Uri.parse("usercenter://com.szlanyou/login?cp_type=24&target_page=1"));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
            getActivity().getApplication().getApplicationContext().startActivity(intent);
            return;
        }

        ScenePatternViewManager.getInstance().transFragment(ScenePatternFuncDef.SurpriseEggListFragment_TAG,ScenePatternFuncDef.TransType.ScenePattern_Normal);
        makeBigData(ScenePatternFuncDef.BIGDATA_VALUE_PATTERN_SEL_SURPRISE);
    }

    private void makeBigData(String data) {
        Map map = BigDataControl.getInstance().makePatternSel(data);
        BigDataControl.getInstance().writeEventTrack(ScenePatternFuncDef.BIGDATA_EVENT_ID_6,
                ScenePatternFuncDef.BIGDATA_NAME_PATTERN_SEL,
                ScenePatternFuncDef.BIGDATA_EVENT_TYPE_CLICK,
                map);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        MLog.d(TAG, "onRequestPermissionsResult: ");
        switch (requestCode) {
            case ScenePatternFuncDef.PERMISSIONS_REQUEST_INTERNET_CODE:
                if (null != grantResults && grantResults.length > 0 && PackageManager.PERMISSION_GRANTED == grantResults[0]) {
                    MLog.d(TAG, "grantResults:" + requestCode);
                    if(!SurpriseEggListModel.getInstance().isUserCenterLogin()) {
                        DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
                            @Override
                            public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
                                return v -> {
                                    Intent intent = new Intent(null, Uri.parse("usercenter://com.szlanyou/login?cp_type=24&target_page=1"));
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                                    getActivity().getApplication().getApplicationContext().startActivity(intent);
                                    libDialog.dismiss();
                                };
                            }

                            @Override
                            public View.OnClickListener getNegativeButtonListener(LibDialog dialog) {
                                return v -> {
                                    dialog.dismiss();
                                    System.exit(0);};
                            }

                            @Override
                            public String getDialogMessage() {
                                return "需要登录日产车联账号，是否前往登录";
                            }

                            @Override
                            public String getPositiveButtonName() {
                                return "前往登录";
                            }
                        });
                        return;
                    }

                    ScenePatternViewManager.getInstance().transFragment(ScenePatternFuncDef.SurpriseEggListFragment_TAG,ScenePatternFuncDef.TransType.ScenePattern_Normal);
                    makeBigData(ScenePatternFuncDef.BIGDATA_VALUE_PATTERN_SEL_SURPRISE);
                } else {
                    MLog.d(TAG, "unGranted:" + requestCode);
                }
                break;
            default:
                break;
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }
}
