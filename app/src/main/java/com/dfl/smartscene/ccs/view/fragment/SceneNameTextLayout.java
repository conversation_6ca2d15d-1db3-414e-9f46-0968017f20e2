package com.dfl.smartscene.ccs.view.fragment;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.fragment.EditTextLayout;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.SceneConditionManager;
import com.dfl.smartscene.ccs.util.ToastManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：2022/12/7 11:35
 * @description ：
 */
public class SceneNameTextLayout extends EditTextLayout {

    private String mSceneName = "";
    private final List<String> mSceneNames = new ArrayList<>();
    public SceneNameTextLayout(@NonNull Context context) {
        super(context);
    }

    public SceneNameTextLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SceneNameTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void init() {
        canInputEnglish = true;
        super.init();
    }

    @Override
    public void show(String text) {
        super.show(text);
        if(mSceneName.isEmpty()){
            mSceneName = text;
        }
        mSceneNames.clear();
        List<SceneCategory> userScenes = SceneDataModel.getInstance().getUserScenes();
        if (null != userScenes){
            for (SceneCategory sceneCategory : SceneDataModel.getInstance().getUserScenes()) {
                for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                    SceneConditionManager.getInstance().registerVrCondition(sceneBean);
                }
            }
            for (SceneCategory category : userScenes){
                for (SceneBean sceneBean : category.getSceneBeanList()) {
                    //获取所有的场景名称
                    mSceneNames.add(sceneBean.getSceneName());
                }
            }
        }
    }

    @Override
    protected void onclickConfirmButton() {
        //新的场景名称和输入的不一样且和原有的相同
        if (!Objects.equals(mEditTextContent.getText().toString().trim(),mSceneName) && mSceneNames.contains(mEditTextContent.getText().toString().trim())){
            ToastManager.showToast("名称重复了，换一个吧");
            return;
        }
        if(mConfirmClickListener != null){
            mConfirmClickListener.onClickConfirm(mEditTextContent.getText().toString());
        }
        super.onclickConfirmButton();
    }

}
