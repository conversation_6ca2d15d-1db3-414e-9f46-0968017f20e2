package com.dfl.smartscene.ccs.model;

import com.dfl.api.vehicle.backdoor.IBackDoorCallback;
import com.dfl.api.vehicle.doorandtrunk.IDoorAndTrunkCallback;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModelImpl;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;

/**
 * 车门相关的管理类
 *
 * <AUTHOR>
 * @date ：2022/9/30 13:24
 * @description ：
 */
public class DoorModel extends ConditionBaseModelImpl implements StateBaseModel {

    private static final String TAG = "DoorModel";
    private static volatile DoorModel sInstance;

    public static DoorModel getInstance() {
        if (null == sInstance) {
            synchronized (DoorModel.class) {
                if (null == sInstance) {
                    sInstance = new DoorModel();
                }
            }
        }
        return sInstance;
    }

    public void testDoor(){
        onCarDataChange(ConstantModelValue.OID_CDT_DOOR_FRONT_LEFT, "2");
    }

    /**
     * 监听车门状态
     */
    public void listenDoorStatus() {
        try {
            LogUtil.d(TAG, "listenDoorStatus");
            //四个车门的状态监听
            CustomApiManager.getInstance().getIDoor().registerDoorAndTrunkCallback(new IDoorAndTrunkCallback() {
                @Override
                public void onDoorAndTrunkStatusChanged(int i, int i1) {
                    LogUtil.d(TAG, "stmtest:onDoorAndTrunkStatusChanged" + ",i:" + i + ",i1:" + i1);
                    //0:主驾；1：副驾；2：右后；3：左后
                    //状态：2：关；3：开
                    switch (i) {
                        case 0:
                            onCarDataChange(ConstantModelValue.OID_CDT_DOOR_FRONT_LEFT, String.valueOf(i1));
                            break;
                        case 1:
                            onCarDataChange(ConstantModelValue.OID_CDT_DOOR_FRONT_RIGHT, String.valueOf(i1));
                            break;
                        case 2:
                            onCarDataChange(ConstantModelValue.OID_CDT_DOOR_REAR_RIGHT, String.valueOf(i1));
                            break;
                        case 3:
                            onCarDataChange(ConstantModelValue.OID_CDT_DOOR_REAR_LEFT, String.valueOf(i1));
                            break;
                    }
                }

                @Override
                public void onElectricDoorStatusChange(int type, int position, int status) {

                }

                @Override
                public void onCarLockStatusChange(int i, int i1) {
                    LogUtil.d(TAG, "stmtest:onCarLockStatusChange" + ",i:" + i + ",i1:" + i1);

                }

                @Override
                public void onChildLockStatusChange(int type, int status) {

                }

                @Override
                public void onSeatBeltStatusChange(int position, int status) {

                }

                @Override
                public void onFuelTankLockStatusChange(int status) {

                }
            });
        } catch (Exception e) {
            LogUtil.d(TAG, e.getMessage());
            e.printStackTrace();
        }
    }

    public void listenBackDoorStatus() {
        try {
            //后背门状态监听
            CustomApiManager.getInstance().getIBackDoor().registerBackDoorCallback(new IBackDoorCallback() {
                @Override
                public void onBackDoorStatusChange(int i) {
                    onCarDataChange(ConstantModelValue.OID_CDT_DOOR_BACK, String.valueOf(i));
                }

                @Override
                public void onBackDoorAutoOnSenceStatusChange(int i) {

                }

                @Override
                public void onPBDHightChange(int hight) {

                }

                @Override
                public void onBackDoorHeightChange(int i) {

                }

                @Override
                public void onBackDoorErrorCodeChange(int i) {

                }

                @Override
                public void onBackDoorHoveringStatusChange(int i) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {
        try {
            //0:主驾；1：副驾；2：右后；3：左后
            switch (settingOperation.getOperationId()) {
                //检查主驾门状态是否满足条件
                case ConstantModelValue.OID_STA_DOOR_FRONT_LEFT:
                    int frontLeftDoorStatus = CustomApiManager.getInstance().getIDoor().getDoorAndTrunkStatus(0);
                    return simpleStateCheck(settingOperation.getListArgs(), String.valueOf(frontLeftDoorStatus));
                //检查副驾门状态是否满足条件
                case ConstantModelValue.OID_STA_DOOR_FRONT_RIGHT:
                    int frontRightDoorStatus = CustomApiManager.getInstance().getIDoor().getDoorAndTrunkStatus(1);
                    return simpleStateCheck(settingOperation.getListArgs(), String.valueOf(frontRightDoorStatus));
                //检查左后门状态是否满足条件
                case ConstantModelValue.OID_STA_DOOR_REAR_LEFT:
                    int rearLeftDoorStatus = CustomApiManager.getInstance().getIDoor().getDoorAndTrunkStatus(3);
                    return simpleStateCheck(settingOperation.getListArgs(), String.valueOf(rearLeftDoorStatus));
                //检查右后门状态是否满足条件
                case ConstantModelValue.OID_STA_DOOR_REAR_RIGHT:
                    int rearRightDoorStatus = CustomApiManager.getInstance().getIDoor().getDoorAndTrunkStatus(2);
                    return simpleStateCheck(settingOperation.getListArgs(), String.valueOf(rearRightDoorStatus));
                //检查后背门状态是否满足条件
                case ConstantModelValue.OID_STA_DOOR_BACK:
                    int backDoorStatus = CustomApiManager.getInstance().getIBackDoor().getBackDoorStatus();
                    return simpleStateCheck(settingOperation.getListArgs(), String.valueOf(backDoorStatus));
                default:
                    return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }


    @Override
    protected String[] getConditionIds() {
        return new String[]{
                ConstantModelValue.OID_CDT_DOOR_FRONT_LEFT,
                ConstantModelValue.OID_CDT_DOOR_FRONT_RIGHT,
                ConstantModelValue.OID_CDT_DOOR_REAR_LEFT,
                ConstantModelValue.OID_CDT_DOOR_REAR_RIGHT,
                ConstantModelValue.OID_CDT_DOOR_BACK
        };
    }
}
