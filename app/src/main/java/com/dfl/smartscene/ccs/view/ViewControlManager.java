package com.dfl.smartscene.ccs.view;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;

import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.busevent.CollectEvent;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.NetworkManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.util.PopupViewControl;
import com.dfl.smartscene.ccs.util.PopupViewDef;
import com.dfl.smartscene.ccs.util.SceneUtil;
import com.dfl.smartscene.ccs.util.ToastManager;

import java.util.Collections;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/11/1 9:28
 * @description ：给view使用的和model层交互的管理层
 */
public class ViewControlManager {


    public  static void executeCommonScene(SceneBean sceneBean){
        if(SceneUtil.checkSceneIsCard(sceneBean)){
            executeCardScene(sceneBean,false);
        }else{
            executeScene(sceneBean,"即将执行");
        }
    }

    public static void executeCardScene(SceneBean sceneBean,boolean executeNow){
        boolean result = SceneControlManager.getInstance().saveCurrentAndStartScene(sceneBean,executeNow);
    }
    /**
     * 执行场景
     * @param sceneBean
     */
    public static void executeScene(SceneBean sceneBean , String desc){
        if(CarConfigManager.getInstance().checkOperationPLimit(sceneBean.getActionOperations()) && !DriveModel.getInstance().isPGear()){
            PopupViewControl.getInstance().showMessagePopupView(PopupViewDef.PopupId_Scene_Massage_Execute_Info,"请在P挡下执行该场景");
//            ToastManager.showToast("请在P挡下执行该场景");
            return;
        }
//        if(ActivityManager.getInstance().getCurrentActivity() != null){
//            NavController navController = Navigation.findNavController(ActivityManager.getInstance().getCurrentActivity(), R.id.fragment_main_nav_host);
//            SettingOperation videoOp = sceneBean.findActionOperationById(ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO);
//            if(videoOp != null){
//                Bundle bundle = new Bundle();
//                bundle.putStringArrayList(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN,new ArrayList<>(videoOp.getListArgs()));
//                navController.navigate(R.id.action_global_sceneSmallViewFragment,bundle);
//            }
//        }

        LogUtil.d("ViewControlManager","sceneBean:"+sceneBean.toString());

        //触发模式时埋点
        BigDataManager.getInstance().writeEventMod2(sceneBean.getSceneName(),sceneBean.getActionString());

        boolean result = SceneControlManager.getInstance().saveCurrentAndStartScene(sceneBean,false);

        if(result){
            PopupViewControl.getInstance().showMessagePopupView(PopupViewDef.PopupId_Scene_Massage_Execute_Info,desc + sceneBean.getSceneName());
//            ToastManager.showToast(desc + sceneBean.getSceneName());
        }

    }

    /**
     * 收藏场景(UI上点击收藏按钮）
     * @param categoryId
     * @param sceneBean
     */
    public static void collectScene(String categoryId,SceneBean sceneBean){
        if(NetworkManager.getInstance().hasNetwork()){
            SceneDataModel.getInstance().addUserScene(categoryId,sceneBean);
            RxBus.getDefault().post(new CollectEvent(CollectEvent.COLLECT_EVENT_TYPE_SINGLE, sceneBean.getSceneCategoryId(), sceneBean.getSceneId(), true));
        }else{
            ToastManager.showToast(ScenePatternApp.getInstance().getString(R.string.string_no_network_notice));
        }
    }

    /**
     * 取消收藏场景(用于单条操作）
     * @param categoryId
     * @param sceneBean
     */
    public static void uncollectScene(String categoryId,SceneBean sceneBean){
        SceneDataModel.getInstance().deleteUserScenes(categoryId, Collections.singletonList(sceneBean));
    }

    /**
     * 关闭应用
     */
    public static void closeApp(){
        if(ActivityManager.getInstance().getCurrentActivity() == null){
            return;
        }
        Intent intent = new Intent();
        intent.setPackage("com.dfl.launcher");
        intent.setAction("ExitNormal");
        ActivityManager.getInstance().getCurrentActivity().sendBroadcast(intent);
    }

    public static void openAiotDevicePage(){
        Intent intent = new Intent();
        intent.setData(Uri.parse("aiot://aiot.dfl.com/main?tab=0"));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        try {
            ScenePatternApp.getInstance().startActivity(intent);
        }catch (ActivityNotFoundException e){

        }

    }

    public static void openAiotStorePage(){
        Intent intent = new Intent();
        intent.setData(Uri.parse("aiot://aiot.dfl.com/main?tab=1"));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            ScenePatternApp.getInstance().startActivity(intent);
        }catch (ActivityNotFoundException e){

        }

    }

}
