package com.dfl.smartscene.ccs.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.busevent.CollectEvent;

/**
 * <AUTHOR>
 * @date ：2022/9/7 16:27
 * @description ：
 */
public class CollectViewModel extends ViewModel {

    private MutableLiveData<CollectEvent> mCollectEventMutableLiveData = new MutableLiveData<>();

    public MutableLiveData<CollectEvent> getCollectEventMutableLiveData() {
        return mCollectEventMutableLiveData;
    }

    public CollectViewModel(){
        RxBus.getDefault().register(this);
    }

    @Subscribe
    public void collectSceneListener(CollectEvent collectEvent){
        LogUtil.d("CollectViewModel", "receive collect event");
        mCollectEventMutableLiveData.setValue(collectEvent);
        mCollectEventMutableLiveData.setValue(null);
    }


    @Override
    protected void onCleared() {
        super.onCleared();
        RxBus.getDefault().unRegister(this);
    }
}
