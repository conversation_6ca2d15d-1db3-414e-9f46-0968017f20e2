package com.dfl.smartscene.ccs.view.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.RecyclerView;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.bean.DeviceOperation;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.MyHeaderView;
import com.dfl.smartscene.ccs.view.adapter.MySceneAdapter;
import com.dfl.smartscene.ccs.view.base.BaseFragment;
import com.dfl.smartscene.ccs.viewmodel.MyViewModel;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20 13:30
 * @description 我的页面,我的收藏和自定义场景合并显示
 */
public class MineFragment extends BaseFragment {
    private MySceneAdapter mMySceneAdapter;
    protected SceneCategory mSceneCategory;//收藏数据
    protected String mCategoryId;
    private SmartRefreshLayout mRefreshLayout;
    //我的收藏数量
    private int collectSize;
    private RecyclerView mRecyclerView;
    private View layout_loading;
    private View none_result;
    private View net_error;

    private static final @DimenRes
    int COLUMN_SPACE = R.dimen.x_px_24;//item之间的间距
    int LINE_SPACE = R.dimen.y_px_24;//item之间的间距
    private MyViewModel mMyViewModel;

    private static final String TAG = "MineFragment";
    protected static final String CONSTRUCTOR_PARAM_KEY = "CONSTRUCTOR_PARAM_KEY";
    @Override
    protected View getContentPageView() {
        return getView().findViewById(R.id.smart_refresh_mine);
    }

    @Override
    protected void init() {
        mMyViewModel = new ViewModelProvider(this).get(MyViewModel.class);
        if(getArguments() != null){
            mCategoryId = getArguments().getString(CONSTRUCTOR_PARAM_KEY);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        LogUtil.d(TAG, "onCreateView");
        return inflater.inflate(R.layout.fragment_mine, container, false);
    }

    @Override
    protected void initObserver() {
        mMyViewModel.getSceneCategories().observe(getViewLifecycleOwner(), new Observer<List<SceneCategory>>() {

            @Override
            public void onChanged(List<SceneCategory> sceneCategories) {
                int cloudDataSize = getCategoriesSceneSize(sceneCategories);
                int localDataSize = getCategoriesSceneSize(mMyViewModel.getUserScenes());
                LogUtil.d(TAG, "getSceneCategories onChanged == "+ " cloudDataSize " + cloudDataSize + " localDataSize "+localDataSize);
                LogUtil.d(TAG, "getSceneCategories sceneCategories == "+ sceneCategories);

                if (!checkDataValid()) {
                    mRefreshLayout.finishRefresh(false);
                    return;
                }

                if (sceneCategories == null) {
                    mRefreshLayout.finishRefresh(false);
                    showError();
                    return;
                }
                if (sceneCategories.size() == 0) {
                    mRefreshLayout.finishRefreshWithNoMoreData();
                    showNoContent();
                    return;
                }
                if (sceneCategories.size() == 2 && sceneCategories.get(0).getSceneBeanList().size() == 0 && sceneCategories.get(1).getSceneBeanList().size() == 0){
                    mRefreshLayout.finishRefreshWithNoMoreData();
                    showNoContent();
                    return;
                }
                collectSize = sceneCategories.get(0).getSceneBeanList().size();
                mRefreshLayout.finishRefresh(true);
                List<SceneBean> beanList = new ArrayList<>();
                for(SceneCategory sceneCategory : sceneCategories){
                    beanList.addAll(sceneCategory.getSceneBeanList());
                }
                mMySceneAdapter.setDataList(beanList);
                SceneBean addScene = new SceneBean();
                addScene.setSceneName("");
                addScene.setSceneId(ConstantModelValue.SCENE_ID_ADD);
                mMySceneAdapter.addData(addScene);
                showContent();
            }
        });

        mMyViewModel.requestExecuteSceneData();
        mMyViewModel.getExecuteSceneBean().observe(getViewLifecycleOwner(), new Observer<SceneBean>() {
            @Override
            public void onChanged(SceneBean sceneBean) {
                updateExecuteStatus(sceneBean);
            }
        });
        mMyViewModel.requestLocalUserSceneData();
    }

    /**
     * 获取场景列表中的场景数量
     * @param list
     * @return
     */
    private int getCategoriesSceneSize(List<SceneCategory> list){
        int size = 0;
        if(list != null){
            for(SceneCategory sceneCategory :list){
                if(sceneCategory.getSceneBeanList() != null){
                    size = size +sceneCategory.getSceneBeanList().size();
                }
            }
        }
        return size;
    }

    @Override
    protected void initView(View view) {
        layout_loading = view.findViewById(R.id.layout_loading);
        none_result = view.findViewById(R.id.column_result_fragment_none_result);
        net_error = view.findViewById(R.id.menu_fragment_network_abnormal_layout);
        mRefreshLayout = view.findViewById(R.id.smart_refresh_mine);
        mRecyclerView = view.findViewById(R.id.rv_mine);
        mRefreshLayout.setRefreshHeader(new MyHeaderView(getContext()));
        mRefreshLayout.setEnableLoadMore(false);
        mRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mMySceneAdapter.hideAllDeleteButtons(mRecyclerView);
                mMyViewModel.requestUserScenes(true);
            }
        });
        net_error.findViewById(R.id.text_view_network_retry).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showLoading();
                mSceneCategory = SceneDataModel.getInstance().findUserCategoryById(mCategoryId);
                HandlerUtil.getMainHandler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mMyViewModel.requestUserScenes(true);
                    }
                }, ConstantViewValue.REFRESH_LOADING_TIMER);
            }
        });

        //创建场景
        none_result.findViewById(R.id.linear_create_scene).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN,new SceneBean());
                bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_CUSTOM);
                bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD);
                Navigation.findNavController(view).navigate(R.id.action_mainFragment_to_sceneEditorFragment,bundle);
            }
        });

        mMySceneAdapter = new MySceneAdapter();
        mRecyclerView.setAdapter(mMySceneAdapter);
        RecyclerViewUtil.setGridRecycleView(mRecyclerView,RecyclerView.VERTICAL,3,COLUMN_SPACE,LINE_SPACE);

        //收藏场景点击监听，跳转场景编辑页面
        mMySceneAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SceneBean>() {
            @Override
            public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                mMySceneAdapter.hideAllDeleteButtons(mRecyclerView);
                //编辑状态下
                if(mMySceneAdapter.isEditMode()){
                    boolean oldStatus = mMySceneAdapter.isAllSelected();
                    mMySceneAdapter.changeItemSelected(position);
                    if(mMySceneAdapter.isAllSelected()){
                        mMyViewModel.setAllSelected(ConstantViewValue.STATUS_ALL_SELECTED);
                    }else if(mMySceneAdapter.isAllUnSelected()){
                        mMyViewModel.setAllSelected(ConstantViewValue.STATUS_NONE_SELECTED);
                    }else {
                        mMyViewModel.setAllSelected(ConstantViewValue.STATUS_PART_SELECTED);
                    }
                    return;
                }
                //新建场景
                Bundle bundle = new Bundle();
                if(sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_ADD)){
                    bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN,new SceneBean());
                    bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_CUSTOM);
                    bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD);
                }else {
                    //编辑场景
                    bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN,sceneBean);
                    if (position < collectSize){
                        bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_COLLECT);
                        bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_TITLE, "编辑场景");
                    }else {
                        bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_CUSTOM);
                        bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_TITLE, "编辑场景");
                    }
                    bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_MODIFY);
                    bundle.putInt(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_POS,position);
                }
                Navigation.findNavController(view).navigate(R.id.action_mainFragment_to_sceneEditorFragment,bundle);
            }
        });
//        mMySceneAdapter.setItemLongClickListener(new BaseAdapter.OnItemLongClickListener<SceneBean>() {
//            @Override
//            public void onItemLongClick(View view, int viewType, SceneBean sceneBean, int position) {
//                if (ConstantModelValue.CATEGORY_ID_CUSTOM.equals(sceneBean.getSceneCategoryId())){
//                    DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
//                        @Override
//                        public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
//                            return v -> {
//                                LogUtil.d(TAG, "getPositiveButtonListener 删除此场景");
//                                SceneDataModel.getInstance().deleteUserScenes(ConstantModelValue.CATEGORY_ID_CUSTOM, Collections.singletonList(sceneBean));
//                                mMySceneAdapter.removeData(sceneBean);
//                                libDialog.dismiss();
//                            };
//                        }
//
//                        @Override
//                        public String getDialogMessage() {
//                            return "是否删除此场景";
//                        }
//                    });
//                }
//            }
//        });

    }

    /**
     * 更新执行按钮状态
     * @param sceneBean
     */
    private void updateExecuteStatus(SceneBean sceneBean){
        int pos = -1;
        if(mMySceneAdapter == null){
            return;
        }
        for(int i = 0 ; i < mMySceneAdapter.getItemCount() ; i++){
            if(mMySceneAdapter.getItemData(i).equals(sceneBean)){
                pos = i;
                break;
            }
        }
        if(pos > -1){
            sceneBean.setExecuteStatus(false);
            mMySceneAdapter.modifyData(pos,sceneBean);
        }
    }

    @Override
    protected void initData() {
        LogUtil.d(TAG, "init data show loading page");
        showLoading();
        mSceneCategory = SceneDataModel.getInstance().findUserCategoryById(mCategoryId);
    }

    @Override
    public void onResume() {
        super.onResume();
        LogUtil.e(TAG, "onResume");
        LogUtil.d(TAG, "onResume requestUserBean");
        mMyViewModel.requestUserScenes(false);
        checkDataValid();
    }

    @Override
    public void onPause() {
        super.onPause();
        mMySceneAdapter.hideAllDeleteButtons(mRecyclerView);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        LogUtil.e(TAG, "onHiddenChanged " + hidden);
        if (!hidden){
            mMyViewModel.requestUserScenes(false);
        }
        checkDataValid();
    }

    private void showLoading(){
        layout_loading.setVisibility(View.VISIBLE);
        net_error.setVisibility(View.GONE);
        none_result.setVisibility(View.GONE);
        getContentPageView().setVisibility(View.GONE);
    }

    private void showError(){
        layout_loading.setVisibility(View.GONE);
        net_error.setVisibility(View.VISIBLE);
        none_result.setVisibility(View.GONE);
        getContentPageView().setVisibility(View.GONE);
    }

    private void showNoContent(){
        layout_loading.setVisibility(View.GONE);
        net_error.setVisibility(View.GONE);
        none_result.setVisibility(View.VISIBLE);
        getContentPageView().setVisibility(View.GONE);
    }

    private void showContent(){
        layout_loading.setVisibility(View.GONE);
        net_error.setVisibility(View.GONE);
        none_result.setVisibility(View.GONE);
        getContentPageView().setVisibility(View.VISIBLE);
    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }
    private boolean checkDataValid(){
        LogUtil.d(TAG, "checkDataValid: ");
        List<DeviceOperation> dataList1 = CarConfigManager.getInstance().getLocalConditionOperations();
        List<DeviceOperation> dataList2 = CarConfigManager.getInstance().getLocalActionOperations();
        List<DeviceOperation> dataList3 = CarConfigManager.getInstance().getLocalStateOperations();
        if(ListUtil.isEmpty(dataList1)||ListUtil.isEmpty(dataList2)|| ListUtil.isEmpty(dataList3)){
            LogUtil.d(TAG, "checkDataValid: data null");
            showError();
            return false;
        }
        return true;
    }
}
