package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.navi.FavoriteItemBean;


/**
 * <AUTHOR>
 * @date ：2022/9/20
 * @description ：导航收藏点列表适配器
 */
public class NaviCollectionRecyclerAdapter extends BaseAdapter<FavoriteItemBean> {

    private int selectPos = 0;

    public void setSelectedPos(int pos) {
        if (mDataList == null || pos < 0 || pos >= getItemCount()) {
            return;
        }
        int temp = selectPos;

        if(pos == selectPos) {
            selectPos = pos;
            notifyItemChanged(temp);
        } else {
            selectPos = pos;
            notifyItemChanged(temp);
            notifyItemChanged(selectPos);
        }
    }

    public int getSelectPos() {
        return selectPos;
    }

    public FavoriteItemBean getSelectData() {
        if (selectPos >= 0 && selectPos < mDataList.size()) {
            return mDataList.get(selectPos);
        }
        return null;
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_ope_navi_favorite_recycler;
    }

    @Override
    protected BaseHolder<FavoriteItemBean> getViewHolder(View view, int viewType) {
        return new NaviCollectionRecyclerHolder(view);
    }

    class NaviCollectionRecyclerHolder extends BaseHolder<FavoriteItemBean> {
        TextView mTextViewName;
        TextView mTextViewAddress;
        ImageView mImageView;
        CheckBox mCheckBox;

        public NaviCollectionRecyclerHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_item_ope_navi_favorite_pos_name);
            mTextViewAddress = itemView.findViewById(R.id.textview_item_ope_navi_favorite_pos_address);
            mImageView = itemView.findViewById(R.id.imageview_item_ope_navi_favorite_pos_recycler);
            mCheckBox = itemView.findViewById(R.id.checkbox_item_ope_navi_favorite_pos_select_state);
        }

        @Override
        public void setupData(FavoriteItemBean dataBean, int position) {

            int common_name = dataBean.getPoiType();
            if (common_name == 1) {
                // 家
                mImageView.setImageResource(R.drawable.ic_home);
            } else if (common_name == 2) {
                // 公司
                mImageView.setImageResource(R.drawable.ic_company);
            } else {
                mImageView.setImageResource(R.drawable.ic_pin);
            }

            mTextViewName.setText(dataBean.getFavoriteItemName());
            mTextViewAddress.setText(dataBean.getAddress());

            boolean selected = selectPos == position;
//            mTextViewName.setTextColor(ContextCompat.getColor(mTextViewName.getContext(), selected ? R.color.color_text_selected : R.color.color_text_unselected));
//            mTextViewAddress.setTextColor(ContextCompat.getColor(mTextViewName.getContext(), selected ? R.color.color_text_selected : R.color.color_text_unselected));
            mCheckBox.setChecked(selected);

        }
    }

}
