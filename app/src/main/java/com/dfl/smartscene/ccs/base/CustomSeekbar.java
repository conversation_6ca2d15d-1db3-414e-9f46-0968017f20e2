package com.dfl.smartscene.ccs.base;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;

import androidx.core.content.res.ResourcesCompat;

import com.dfl.dflcommonlibs.uimodeutil.UISeekBar;
import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;

/**
 * 自定义seekbar，
 * 1.thumb的图片成为任意大小
 * 2.通过自定义属性custom_seek_bar_progress_ratio，放大progress范围。实现实际范围较小时的无级滑动
 * <AUTHOR>
 * @date 2021/12/25
 *
 */

public class CustomSeekbar extends UISeekBar {
    private final String TAG="CustomSeekbar";
    private OnSeekBarChangeListener mOnSeekBarChangeListener;
    /**
     * 为了实现进度条细腻的调整，设置的刻度放大系数
     */
    private int RATIO = 1;

    /**
     * 记录真实的最大值和最小值，和放大后的进度
     */
    private int mActualMax, mActualMin, mScaleProgress;

    /**
     * 进度条宽，视图高，进度球宽度，进度球高度
     */
    private int mWidth, mHeight, mThumbWidth, mThumbHeight;

    /**
     * 圆球x位置, y位置
     */
    private int mThumbX, mThumbY;


    /**
     * 进度条
     */
    private Drawable mDrawableBackground;

    /**
     * 滑块
     */
    private Bitmap mThumb;

    public CustomSeekbar(Context context) {
        super(context);
    }

    public CustomSeekbar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);

    }


    /**
     * 对seekbar做属性设置
     */
    private void init (Context context, AttributeSet attrs) {
        //读取进度球图片
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inMutable = true;
        options.outWidth = getResources().getDimensionPixelSize(R.dimen.x_px_60);
        options.outHeight = getResources().getDimensionPixelSize(R.dimen.y_px_60);

        TypedArray styledAttrs = context.obtainStyledAttributes(attrs,R.styleable.CustomSeekbar);
        RATIO = styledAttrs.getInt(R.styleable.CustomSeekbar_custom_seek_bar_progress_ratio,1);
        MLog.d(TAG,"RATIO" +RATIO);
        int thumbDrawableId = styledAttrs.getResourceId(R.styleable.UISeekbar_android_thumb, R.drawable.ic_volume_seekbar_thumb);
        mThumb = BitmapFactory.decodeResource(getResources(), thumbDrawableId, options);
        mThumbWidth = mThumb.getWidth();
        mThumbHeight = mThumb.getHeight();
        reviseProgressScale();
        setThumb(null);
        //设置padding
        setPadding(options.outWidth / 2
                ,getResources().getDimensionPixelSize(R.dimen.y_px_48)
                ,options.outWidth / 2
                ,getResources().getDimensionPixelSize(R.dimen.y_px_48));
        int progressDrawableId = styledAttrs.getResourceId(R.styleable.UISeekbar_android_progressDrawable, R.drawable.seekbar_bg_progress_volume);
        mDrawableBackground = ResourcesCompat.getDrawable(this.getResources(), progressDrawableId, null);
        //设置progress的drawable
        setProgressDrawable(mDrawableBackground);
        setProgressDrawableId(progressDrawableId);
        styledAttrs.recycle();
    }

    private void reviseProgressScale() {
        mActualMax = getMax();
        mActualMin = getMin();
        setMax(mActualMax);
        setMin(mActualMin);
    }

    /**
     * 计算圆球的x坐标，在setProgress时调用
     */
    private void calculateThumbX(){
        if(RATIO > 1){
            mThumbX = getPaddingLeft() + (int)(mWidth * (mScaleProgress - mActualMin * RATIO) / (mActualMax - mActualMin) / RATIO ) - mThumbWidth / 2;
        }else{
            mThumbX = getPaddingLeft() + (int)(mWidth * (getProgress() - mActualMin) / (mActualMax - mActualMin)) - mThumbWidth / 2;
        }
    }


    @Override
    public synchronized void setProgress(int progress) {
        int reviseProgress;
        if(RATIO > 1){
            reviseProgress = progress * RATIO;
        }else{
            reviseProgress = progress;
        }
        mScaleProgress = reviseProgress;
        calculateThumbX();
        //针对L12C这类min值不为0的情况，可能会出现底层透传的音量值为0的情况，增加此限制
        if(reviseProgress <= getMin()){
            super.setProgress(getMin());
            return;
        }
        super.setProgress(reviseProgress);
    }

    @Override
    public void setOnSeekBarChangeListener(OnSeekBarChangeListener l) {
        mOnSeekBarChangeListener = l;
        super.setOnSeekBarChangeListener(mReviseListener);
    }

   private OnSeekBarChangeListener mReviseListener = new OnSeekBarChangeListener () {
        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if(null != mOnSeekBarChangeListener){
                mOnSeekBarChangeListener.onProgressChanged(seekBar,Math.round(progress/RATIO),fromUser);
                mScaleProgress = progress;
                calculateThumbX();
            }
        }
        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
            if(null != mOnSeekBarChangeListener){
                mOnSeekBarChangeListener.onStartTrackingTouch(seekBar);
            }
        }
        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
            if(null != mOnSeekBarChangeListener){
                mOnSeekBarChangeListener.onStopTrackingTouch(seekBar);
            }
        }
    };

    @Override
    public synchronized int getProgress() {
        if(RATIO > 1){
            return (Math.round(super.getProgress()/RATIO));
        }else{
            return super.getProgress();
        }
    }
    @Override
    public synchronized void setMax(int max) {
        MLog.d(TAG,"setMax:" + max);
        mActualMax = max;
        if(RATIO > 1) {
            super.setMax(max * RATIO);
        } else {
            super.setMax(max);
        }
    }

    @Override
    public synchronized void setMin(int min) {
        MLog.d(TAG,"setMin:" + min +",Ratio = "+RATIO);
        mActualMin = min;
        if(RATIO > 1) {
            super.setMin(min * RATIO);
        } else {
            super.setMin(min);
        }
    }

    @Override
    protected synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        //画圆球
        canvas.drawBitmap(mThumb, mThumbX, mThumbY , null);
    }

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();
        mHeight = getMeasuredHeight();
        mThumbY = mHeight / 2 - mThumbHeight / 2;
    }
}
