package com.dfl.smartscene.ccs.base;

import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.ccs.util.AntiShakeUtil;


/**
 * ViewHolder 基类，只需要实现setupData()就可以填充数据。
 */

public abstract class BaseHolder<T> extends RecyclerView.ViewHolder implements View.OnClickListener, View.OnLongClickListener {

    private OnViewClickListener mViewClickListener;

    private OnViewLongClickListener mViewLongClickListener;

    public BaseHolder(final View itemView) {
        super(itemView);
        itemView.setOnClickListener(this);
        itemView.setOnLongClickListener(this);
    }

    @Override
    public void onClick(final View v) {

        if ((!AntiShakeUtil.check(v.getId())) && mViewClickListener != null) {
            mViewClickListener.onViewClick(v, getAdapterPosition());
        }
    }

    @Override
    public boolean onLongClick(View v) {
        if(mViewLongClickListener != null){
            mViewLongClickListener.onViewLongClick(v, getAdapterPosition());
            return true;
        }
        return false;
    }

    /**
     * 释放holder中的资源
     */
    public void onRelease() {

    }

    void setOnViewHolderClickListener(OnViewClickListener listener) {
        mViewClickListener = listener;
    }

    public void setViewLongClickListener(OnViewLongClickListener viewLongClickListener) {
        mViewLongClickListener = viewLongClickListener;
    }

    interface OnViewClickListener {

        /**
         * 点击事件
         *
         * @param position 可能会返回-1，当改条目被remove后会返回-1.
         */
        void onViewClick(View view, int position);
    }

    interface OnViewLongClickListener{
        void onViewLongClick(View view, int position);
    }

    /**
     * 填充数据
     */
    public abstract void setupData(T t, int position);
}