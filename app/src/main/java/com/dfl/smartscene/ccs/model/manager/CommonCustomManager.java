package com.dfl.smartscene.ccs.model.manager;

import android.content.Context;
import android.content.Intent;

import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.api.da.setting.ISettingSoundEffect;
import com.dfl.api.vehicle.hvac.IHvac;
import com.dfl.api.vehicle.seat.ISeat;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.ccs.busevent.CustomapiEvent;
import com.dfl.smartscene.ccs.constant.ConstantValue;
import com.dfl.smartscene.ccs.library.app.LibraryApp;

import java.util.HashSet;
import java.util.Hashtable;

/**
 * <AUTHOR>
 * @date ：2022/12/13 10:19
 * @description ：管理子模块的customapi，具体由子模块实现
 */
public class CommonCustomManager {
    private static final String TAG = "CustomApiManager";
    /**用于保存key和CustomAPI*/
    protected Hashtable<String , IBaseAPI> otherApiMap = new Hashtable<>();
    protected HashSet<String> otherApiName = new HashSet<>();

    public IBaseAPI getIBaseApi(String name){
        return otherApiMap.get(name);
    }

    public IHvac getIHvac(){
        return null;
    }

    public ISeat getISeat(){
        return null;
    }

    public IAppCommon getIAppCommon(){
        return null;
    }

    public ISettingSoundEffect getISettingSoundEffect(){return null;}

    /**
     * 初始化customapi的具体对象类
     */
    public void registerCustomApi(){
    }

    /**
     * 非预置功能
     */
    public void initUnknownApi(String name){
        otherApiName.add(name);
        LogUtil.d(TAG,"initUnknownApi,name = " + name);
        Context context = LibraryApp.getApp();
        BaseManager.create(context, name, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiState, String requestName, IBaseAPI iBaseAPI) {
                LogUtil.d(TAG, "initUnknownApi onAPICreateCallback,apiState : " + apiState + ",requestName = " + requestName + ",name : " + name);
                if(apiState == APIStateType.APIState.SUCCESS){
                    otherApiMap.put(name,iBaseAPI);
                    Intent intent = new Intent();
                    intent.setClassName("com.dfl.newscenepattern","com.dfl.newscenepattern.service.ScenePatternService");
                    intent.setAction(ConstantValue.ACTION_INTENT_CUSTOMAPI_CONDITION_INIT);
                    intent.putExtra(ConstantValue.KEY_SERVICE_INTENT_CUSTOMAPI_NAME,name);
                    LibraryApp.getApp().startService(intent);
                }
                RxBus.getDefault().post(new CustomapiEvent(requestName,apiState));
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {

            }
        });
    }

}
