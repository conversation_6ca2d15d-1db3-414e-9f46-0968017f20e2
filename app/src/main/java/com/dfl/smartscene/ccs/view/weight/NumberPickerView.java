package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.NumberPicker;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;


import com.dfl.smartscene.R;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：数值选取的自定义控件
 */
public class NumberPickerView extends ConstraintLayout {

    private NumberPicker mNumberPickerValue;
    private TextView mTextViewUnit;

    public NumberPickerView(Context context) {
        this(context, null);
    }

    public NumberPickerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    private void initView(Context context) {
        View inflate = LayoutInflater.from(context).inflate(R.layout.layout_custom_number_picker, this);
        mNumberPickerValue = inflate.findViewById(R.id.numberpicker_layout_custom_number_picker_value);
        mTextViewUnit = inflate.findViewById(R.id.textview_layout_custom_number_picker_unit);
    }

    public void setValue(String valueType, Object min, Object max, Object step, String defaultValue, String unitStr) {
        ArrayList<String> displayValues = new ArrayList<>();
        if ("int".equals(valueType)) {
            for (int i = (int) min; i <= (int) max; i = i + (int) step) {
                displayValues.add(i + "");
            }
        } else {
            for (float i = (float) min; i <= (float) max; i = i + (float) step) {
                displayValues.add(i + "");
            }
        }

        mNumberPickerValue.setMinValue(0);
        mNumberPickerValue.setMaxValue(displayValues.size() - 1);
        mNumberPickerValue.setDisplayedValues(displayValues.toArray(new String[0]));
        mNumberPickerValue.setValue(displayValues.indexOf(defaultValue));
        mTextViewUnit.setText(unitStr);
    }

    public String getValue() {
        int value = mNumberPickerValue.getValue();
        String[] displayedValues = mNumberPickerValue.getDisplayedValues();
        if (null != displayedValues) {
            if (value >= 0 && value < displayedValues.length) {
                return displayedValues[value];
            }
        }
        return null;
    }

}
