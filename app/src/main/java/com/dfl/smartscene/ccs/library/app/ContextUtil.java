package com.dfl.smartscene.ccs.library.app;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;

import androidx.core.content.res.ResourcesCompat;

public class ContextUtil {

    /**
     * 根据图片名获取图片
     * @param resName
     * @return
     */
    public static Drawable getDrawableByName(String resName) {
        Resources resources = ScenePatternApp.getInstance().getResources();
        int identifier = resources.getIdentifier(resName, "mipmap", ScenePatternApp.getInstance().getPackageName());
        if (identifier <= 0) {
            identifier = resources.getIdentifier(resName, "drawable", ScenePatternApp.getInstance().getPackageName());
        }

        if (identifier > 0) {
            return ResourcesCompat.getDrawable(resources, identifier, null);
        } else {
            return null;
        }
    }

}
