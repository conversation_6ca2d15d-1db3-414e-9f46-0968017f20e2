package com.dfl.smartscene.ccs.base;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.media.session.PlaybackState;
import android.os.RemoteException;
import android.support.v4.media.MediaBrowserCompat;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.session.MediaControllerCompat;
import android.support.v4.media.session.MediaControllerCompat.TransportControls;
import android.support.v4.media.session.MediaSessionCompat;
import android.support.v4.media.session.PlaybackStateCompat;

import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.model.MediaSyncModel;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.iauto.uibase.utils.MLog;


public class MediaSessionControl {
    private static final String TAG = "MediaSessionControl";

    private MediaBrowserCompat mMediaBrowserCompat;
    private   MediaControllerCompat                   mMediaControllerCompat;
    protected TransportControls mTransportControls = null;

    private Context mContext;
    private String mConnectSource;
    private int    m_PlayState   = PlaybackState.STATE_NONE;

    public MediaSessionControl(Context context, String packageName, String name) {
        mContext = context;
        mConnectSource = packageName;

        mMediaBrowserCompat = new MediaBrowserCompat(context, new ComponentName(packageName,
                name), mConnectionCallback, null);
        connectMediaSession();
    }

    /**
     * 连接mediaSession
     */
    private void connectMediaSession() {
        if (!mMediaBrowserCompat.isConnected()) {
            MLog.d(TAG, "connectMediaSession mConnectSource== " + mConnectSource);
            mMediaBrowserCompat.connect();
            MLog.d(TAG, "request mMediaBrowserCompat connect mConnectSource== " + mConnectSource);
        }
    }


    /**
     * 获取多媒体信息
     */
    @SuppressLint("WrongConstant")
    public void getMediaInfo() {
        MLog.d(TAG, "getMediaInfo from " + mConnectSource);

        if (mMediaBrowserCompat.isConnected() && mMediaControllerCompat != null) {
            final PlaybackStateCompat playbackState = mMediaControllerCompat.getPlaybackState();
            if (playbackState != null) {
                m_PlayState = playbackState.getState();
            }
            else {
                MLog.d(TAG, "getPlaybackState is null ");
                m_PlayState = PlaybackStateCompat.STATE_NONE;
            }

        }
    }

    /**
     * MediaSession browser connect callback
     */
    MediaBrowserCompat.ConnectionCallback mConnectionCallback = new MediaBrowserCompat.ConnectionCallback() {
        @Override
        public void onConnected() {
            super.onConnected();
            MLog.d(TAG, "MediaBrowserCompat onConnected == " + mConnectSource);
            if (null != mMediaBrowserCompat) {
                try {
                    MediaSessionCompat.Token token = mMediaBrowserCompat.getSessionToken();
                    mMediaControllerCompat = new MediaControllerCompat(mContext, token);
                    mMediaControllerCompat.registerCallback(mMediaCallBack);
                    mTransportControls = mMediaControllerCompat.getTransportControls();
                    getMediaInfo();
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onConnectionSuspended() {
            super.onConnectionSuspended();
            MLog.d(TAG, "MediaBrowserCompat onConnection Suspended == " + mConnectSource);
        }

        @Override
        public void onConnectionFailed() {
            super.onConnectionFailed();
            MLog.e(TAG, "MediaBrowserCompat onConnection Failed == " + mConnectSource);
            // 连接时失败，再次连接
            HandlerThreadProcessor.getInstance().postDelayedToMainThread(new Runnable() {
                @Override
                public void run() {
                    connectMediaSession();
                }
            }, 5000);
        }
    };

    /**
     * MediaSession callback
     */
    private MediaControllerCompat.Callback mMediaCallBack = new MediaControllerCompat.Callback() {
        @SuppressLint("WrongConstant")
        @Override
        public void onPlaybackStateChanged(PlaybackStateCompat state) {
            super.onPlaybackStateChanged(state);

            if (state == null) {
                MLog.w(TAG, "PlaybackState is null");
            }
            else {
                MLog.d(TAG, "State :" + state.toString()+" mConnectSource "+mConnectSource) ;
                if (m_PlayState == state.getState()) {
                    MLog.d(TAG, "State not change");
                    return;
                }
                m_PlayState = state.getState();
                if(mConnectSource != null && mConnectSource.equals(ScenePatternFuncDef.MEDIA_SOURCE_ONLINE_PACKAGE_NAME)){
                    MLog.d(TAG, "sync play state" +state.getState());
                    MediaSyncModel.getInstance().getPlayStatus().postValue(state.getState());
                }
                MLog.d(TAG, "onPlaybackStateChanged status == " + m_PlayState);
            }
        }

        @Override
        public void onMetadataChanged(MediaMetadataCompat metadata) {
            super.onMetadataChanged(metadata);
        }
    };

    public TransportControls getTransportControls() {
        return mTransportControls;
    }

    public int getPlayState() {
        return m_PlayState;
    }

}
