package com.dfl.smartscene.ccs.db.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "device")
public class DeviceEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "id", typeAffinity = ColumnInfo.TEXT)
    public String id;
    @ColumnInfo(name = "configType", typeAffinity = ColumnInfo.TEXT)
    public String configType;
    @ColumnInfo(name = "mDeviceIcon", typeAffinity = ColumnInfo.TEXT)
    public String mDeviceIcon;
    @ColumnInfo(name = "mDeviceId", typeAffinity = ColumnInfo.TEXT)
    public String mDeviceId;
    @ColumnInfo(name = "mDeviceName", typeAffinity = ColumnInfo.TEXT)
    public String mDeviceName;
    @ColumnInfo(name = "relationId", typeAffinity = ColumnInfo.TEXT)
    public String relationId;
    @ColumnInfo(name = "priority", typeAffinity = ColumnInfo.INTEGER)
    public int priority;


    @Ignore
    public DeviceEntity() {

    }

    public DeviceEntity(String id, String configType, String mDeviceIcon, String mDeviceId, String mDeviceName, String relationId, int priority) {
        this.id = id;
        this.configType = configType;
        this.mDeviceIcon = mDeviceIcon;
        this.mDeviceId = mDeviceId;
        this.mDeviceName = mDeviceName;
        this.relationId = relationId;
        this.priority = priority;
    }


    @NonNull
    @Override
    public String toString() {
        return "DeviceTable{" +
                "pId=" + pId +
                ", id='" + id + '\'' +
                ", configType='" + configType + '\'' +
                ", mDeviceIcon='" + mDeviceIcon + '\'' +
                ", mDeviceId='" + mDeviceId + '\'' +
                ", mDeviceName='" + mDeviceName + '\'' +
                ", relationId='" + relationId + '\'' +
                ", priority=" + priority +
                '}';
    }
}
