package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SeatMemoryDataBean;


/**
 * <AUTHOR>
 * @date ：2022/9/2 14:02
 * @description ：座椅记忆adapter
 */
public class SeatMemoryRecyclerAdapter extends BaseAdapter<SeatMemoryDataBean> {

    private int selectPos;

    public void setSelectedPos(int pos) {
        if (mDataList == null || pos < 0 || pos >= getItemCount()) {
            return;
        }

        SeatMemoryDataBean seatMemoryDataBean = mDataList.get(pos);
        if (seatMemoryDataBean.isHasValue()) {
            int temp = selectPos;
            selectPos = pos;
            notifyItemChanged(temp);
            notifyItemChanged(selectPos);
        } else {
            // TODO: 页面跳转
        }
    }

    public int getSelectPos() {
        return selectPos;
    }

    public SeatMemoryDataBean getSelectData() {
        if (selectPos >= 0 && selectPos < mDataList.size()) {
            return mDataList.get(selectPos);
        }
        return null;
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_ope_add_recycler;
    }

    @Override
    protected BaseHolder<SeatMemoryDataBean> getViewHolder(View view, int viewType) {
        return new SeatMemoryRecyclerHolder(view);
    }

    class SeatMemoryRecyclerHolder extends BaseHolder<SeatMemoryDataBean> {
        TextView mTextViewName;
        ImageView mImageView;

        public SeatMemoryRecyclerHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_item_ope_add_recycler);
            mImageView = itemView.findViewById(R.id.imageview_item_ope_add_recycler);
        }

        @Override
        public void setupData(SeatMemoryDataBean dataBean, int position) {
            mTextViewName.setText(dataBean.getDesc());
            mTextViewName.setTextColor(ContextCompat.getColor(mTextViewName.getContext(), selectPos == position ? R.color.color_text_selected : R.color.color_text_unselected));
            mTextViewName.setBackgroundResource(selectPos == position ? R.drawable.bg_checkbox_button_selected : R.drawable.bg_checkbox_button_normal);
            mImageView.setVisibility(dataBean.isHasValue() ? View.INVISIBLE : View.VISIBLE);
        }
    }

}
