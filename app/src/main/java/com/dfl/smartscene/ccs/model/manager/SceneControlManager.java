package com.dfl.smartscene.ccs.model.manager;

import android.os.Bundle;
import android.os.Message;

import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.dfl.dflcommonlibs.commonclass.ActivityManager;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.core.SceneStateListener;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.SaveStateModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.SceneEditor;
import com.dfl.smartscene.ccs.model.factory.SceneModelFactory;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.PopupViewControl;
import com.dfl.smartscene.ccs.util.PopupViewDef;
import com.dfl.smartscene.ccs.util.SceneUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 执行场景的管理类
 * <AUTHOR> Zezheng
 * @date ：2022/7/13 10:51
 * @description ：
 */
public class SceneControlManager {
    private static final String TAG = "SceneControlManager";

    private static volatile SceneControlManager sInstance;

    /**记录是否为用户自己暂停*/
    private boolean mIsPauseByUser = false;

    private SceneController mSceneController;

    private SceneBean mSceneBean;

    public SceneController getSceneController() {
        return mSceneController == null ? new SceneController(mSceneBean) : mSceneController;
    }


    private List<SceneStateListener> mSceneStateListeners = new ArrayList<>();
    public static SceneControlManager getInstance(){
        if (null == sInstance){
            synchronized (SceneControlManager.class){
                if (null == sInstance){
                    sInstance = new SceneControlManager();
                }
            }
        }
        return sInstance;
    }

    private SceneControlManager(){
        LibraryManager.getInstance().getLibraryModel().setActionHandler(HandlerUtil.getActionHandler());
    }


    public void registerSceneStateListener(SceneStateListener sceneStateListener){
        mSceneStateListeners.add(sceneStateListener);
    }

    public void unregisterSceneStateListener(SceneStateListener sceneStateListener){
        mSceneStateListeners.remove(sceneStateListener);
    }
    /**
     * 执行单个操作
     * @param settingOperation
     */
    public void executeOperation(SettingOperation settingOperation){
        LogUtil.d(TAG,"excuteOperation : " + settingOperation.getOperationId());
        //如果车型特殊模块处理了该操作
        if(LibraryManager.getInstance().getLibraryModel().executeOperation(settingOperation)){
            LogUtil.i(TAG,"excuteOperation by module");
            return;
        }
        //否则转入通用处理模块
        DeviceBaseModel deviceBaseModel = SceneModelFactory.productDeviceModel(settingOperation.getDeviceId(), settingOperation.getOperationId());
        if(deviceBaseModel != null){
            if(!deviceBaseModel.dispatchSingleActionOperation(settingOperation)){
                LogUtil.i(TAG,"excute Operation fail");
            }
        }else {
            LogUtil.i(TAG,"startScene deviceBaseModel == null");
        }
    }


    /**
     * 点击卡片或触发成功后调用
     * 先保存当前状态到mPreSceneStart
     * 启动场景
     * @param sceneBean
     * @param executeNow 立即执行
     */
    public boolean saveCurrentAndStartScene(SceneBean sceneBean,boolean executeNow){
        LogUtil.d(TAG,"saveCurrentAndStartScene : " + sceneBean.getSceneId());
        mSceneBean = sceneBean;
        if(mSceneController != null){
            exitScene();
            MLog.d(TAG, "controller is not null");
            ToastManager.showToast(ScenePatternApp.getInstance().getString(R.string.string_notice_stop_scene_first));
            return false;
        }

        if(HandlerUtil.getActionHandler().isBusy()){
            MLog.d(TAG, "handler is busy");
            ToastManager.showToast(ScenePatternApp.getInstance().getString(R.string.string_notice_scene_busy));
            return false;
        }

        if(HandlerUtil.getActionHandler().hasMessage(sceneBean.getSceneId())){
            MLog.d(TAG,"上个相同场景还在执行");
            return false;
        }


        if(SceneUtil.checkSceneIsCard(sceneBean)){
            mSceneController = new SceneController(sceneBean);
            registerSceneStateListener(mSceneController);
            SettingOperation videoOp = sceneBean.findActionOperationById(ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO);
            DAModel.getInstance().initVideoController(videoOp);

            if(ActivityManager.getInstance().getCurrentActivity() != null){
                NavController navController = Navigation.findNavController(ActivityManager.getInstance().getCurrentActivity(), R.id.fragment_main_nav_host);
                if(videoOp != null){
                    Bundle bundle = new Bundle();
                    bundle.putStringArrayList(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN,new ArrayList<>(videoOp.getListArgs()));
                    bundle.putBoolean(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN_NOW,executeNow);
                    navController.navigate(R.id.action_global_sceneSmallViewFragment,bundle);
                }
            }
            notifyScenePrepare();
        }else {
            startSceneActionOperation(sceneBean);
        }
        return true;

        //todo 复原方案
//        SceneBean preScene = JSON.parseObject(JSON.toJSONString(sceneBean), SceneBean.class);
//        modifySceneByCurrentStatus(preScene);
//        mPreSceneStart = preScene;
    }

    /**
     * 将当前状态保存至传入的sceneBean中
     * @param sceneBean
     */
    public void modifySceneByCurrentStatus(SceneBean sceneBean){
        SceneEditor sceneEditor = new SceneEditor(sceneBean);
        for(SettingOperation actionOperation : sceneBean.getActionOperations()){
            SaveStateModel saveStateModel = SceneModelFactory.productSaveStateModel(actionOperation.getDeviceId(), actionOperation.getOperationId());
            if(saveStateModel != null){
                saveStateModel.saveCurrentActionToSceneBean(sceneEditor,actionOperation);
            }else {
                MLog.e(TAG,"modifySceneByCurrentStatus deviceBaseModel == null , deviceid = " + actionOperation.getDeviceId());
            }
        }
    }

    /**
     * 开始执行传入场景的动作
     * @param sceneBean
     */
    public void startSceneActionOperation(SceneBean sceneBean){
        MLog.d(TAG,"startScene , id = " + sceneBean .getSceneId());
        clearLastSceneOperation();
        if(sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_SLEEP)){
            return;
        }else if(sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_RELAX)){
            return;
        }
        HandlerUtil.getActionHandler().startScene(sceneBean.getSceneId());
        int timeDelay = 0;
        for(SettingOperation actionOperation : sceneBean.getActionOperations()){
            //时间延迟
            if(ConstantModelValue.OPERATION_ID_ACTION_MORE_TIME_DELAY.equals(actionOperation.getOperationId())){
                if(!ListUtil.isEmpty(actionOperation.getListArgs())){
                    int addDelay = DataTypeFormatUtil.string2Int(actionOperation.getListArgs().get(0));
                    if(addDelay != ConstantModelValue.ERROR_FORMAT_INT_FLOAT_DOUBLE){
                        timeDelay += addDelay * 1000;
                        MLog.d(TAG , "TIME DELAY : "+ addDelay);
                    }
                }

            }else {
                //分发任务
                Message message = Message.obtain();
                message.obj = actionOperation;
                HandlerUtil.getActionHandler().excuteDelay(()-> executeOperation(actionOperation),timeDelay);
            }
            timeDelay+=ConstantModelValue.DELAY_CUSOMAPI_USE;
        }
        MLog.d(TAG , "最终耗时----TIME DELAY : "+ timeDelay);
        //场景执行完成需要toast
        HandlerUtil.getActionHandler().excuteDelay(()-> {
            if (!"scene_card_relax".equals(sceneBean.getSceneId())) {
                SceneDataModel.getInstance().notifySceneBeanExecuteFinish(sceneBean);
                PopupViewControl.getInstance().showMessagePopupView(PopupViewDef.PopupId_Scene_Massage_Execute_Info,sceneBean.getSceneName() + " 执行完成");
//                ToastManager.showToast(sceneBean.getSceneName() + " 执行完成");
            }
        },timeDelay + 1000);
    }

    /**
     * 当下一个场景开始执行时，取消上一个场景的遗留操作
     */
    private void clearLastSceneOperation(){
        HandlerUtil.getActionHandler().removeAllMessage();

    }



    /**
     * 退出当前场景
     * 将当前状态保存至启动的场景执行动作中
     * 复位至场景启动前的状态
     */
    public void exitScene(){
        if(mSceneController == null) {
            MLog.d(TAG,"exitScene mCurrentSceneStart == null!!!");
            return;
        }

//        modifySceneByCurrentStatus(mCurrentSceneStart);
//        SceneDataModel.getInstance().replaceUserScene(mCurrentSceneStart.getSceneCategoryId(),mCurrentSceneStart);
//
        //执行保存的场景
//        if(mPreSceneStart != null){
//            startScene(mPreSceneStart);
//        }
        MLog.d(TAG,"mSceneController.getsState() = " + mSceneController.getsState());
        if(mSceneController.getsState() != SceneController.STATE_IDLE){
            notifySceneStop();
        }
        notifySceneDestroy();
        MLog.d(TAG, "finish notify scene destroy");
        unregisterSceneStateListener(mSceneController);
        DAModel.getInstance().clearVideoController();
        mSceneController = null;
    }

    public void stopScene(){
        if(mSceneController == null) {
            MLog.d(TAG,"exitScene mCurrentSceneStart == null!!!");
            return;
        }
        MLog.d(TAG,"mSceneController.getsState() = " + mSceneController.getsState());
        if(mSceneController.getsState() != SceneController.STATE_IDLE){
            notifySceneStop();
        }
        notifySceneDestroy();
        MLog.d(TAG, "finish notify scene destroy");
    }

    public void pauseScene(boolean pauseByUser){
        if(pauseByUser){
            mIsPauseByUser = pauseByUser;
        }
        notifyScenePause();
    }

    public void resumeScene(boolean resumeByUser){
        if(resumeByUser){
            mIsPauseByUser = false;
            notifySceneResume();
        }else{
            if(mIsPauseByUser){
                mIsPauseByUser = false;
                notifySceneResume();
            }
        }
    }

    public void startScene() {
        startSceneActionOperation(mSceneController.getSceneBean());
        notifySceneStart();
    }
    /**
     * 获取当前场景id
     * @return
     */
    public String getCurrentSceneId(){
        if(mSceneController != null){
            return mSceneController.getSceneId();
        }
        return null;
    }

    public void notifyScenePrepare(){
        for(SceneStateListener sceneStateListener : mSceneStateListeners){
            sceneStateListener.onScenePrepare();
        }
    }

    public void notifySceneStart(){
        for(SceneStateListener sceneStateListener : mSceneStateListeners){
            sceneStateListener.onSceneStart();
        }
    }

    public void notifyScenePause(){
        for(SceneStateListener sceneStateListener : mSceneStateListeners){
            sceneStateListener.onScenePause();
        }
    }

    public void notifySceneResume(){
        for(SceneStateListener sceneStateListener : mSceneStateListeners){
            sceneStateListener.onSceneResume();
        }
    }

    public void notifySceneStop(){
        for(SceneStateListener sceneStateListener : mSceneStateListeners){
            sceneStateListener.onSceneStop();
        }
    }

    public void notifySceneDestroy(){
        LogUtil.d(TAG, "notify scene destroy, listener size = "+mSceneStateListeners.size());
        List<SceneStateListener> listTemp = new ArrayList<>(mSceneStateListeners);
        for(int i = 0 ; i < listTemp.size() ; i++){
            listTemp.get(i).onSceneDestroy();
        }
    }






}
