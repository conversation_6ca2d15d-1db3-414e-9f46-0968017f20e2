package com.dfl.smartscene.ccs.util;


import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/3/13 13:54
 * @description ：
 */
public class SceneUtil {
    /**
     * 判断是否为带UI卡片类型的模式
     */
    public static boolean checkSceneIsCard(SceneBean sceneBean){
        return sceneBean.getEditType() == ConstantModelValue.SCENE_EDIT_TYPE_EDIT_CARD ||
                sceneBean.getEditType() == ConstantModelValue.SCENE_EDIT_TYPE_UNEDIT_CARD;
    }

    /**
     * 为了确保sort的顺序，重新排序模式列表数据，确保sort值小的模式在前
     * @param fromPosition 原位置
     * @param toPosition 移动后的目标位置
     * @param sceneBeanList 模式列表
     */
    public static void reSortScene(int fromPosition, int toPosition, List<SceneBean> sceneBeanList){
        if(toPosition > fromPosition){
            int tempSort = 0;
            for(int i = fromPosition ; i < toPosition ; i++){
                //交换数据内容
                Collections.swap(sceneBeanList, i, i+1);
                //再交换一次sort值
                tempSort = sceneBeanList.get(i).getSort();
                sceneBeanList.get(i).setSort(sceneBeanList.get(i+1).getSort());
                sceneBeanList.get(i+1).setSort(tempSort);
            }
        }else if(fromPosition > toPosition){
            int tempSort = 0;
            for(int i = fromPosition ; i > toPosition ; i--){
                //交换数据内容
                Collections.swap(sceneBeanList, i, i-1);
                //keep原位次的sort值,需要交换i和i-1位置的sort值
                tempSort = sceneBeanList.get(i).getSort();
                sceneBeanList.get(i).setSort(sceneBeanList.get(i-1).getSort());
                sceneBeanList.get(i-1).setSort(tempSort);
            }
        }

    }
}
