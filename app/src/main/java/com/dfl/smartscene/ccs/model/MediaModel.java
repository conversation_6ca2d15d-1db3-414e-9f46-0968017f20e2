package com.dfl.smartscene.ccs.model;

import android.content.ComponentName;
import android.os.RemoteException;

import com.dfl.api.base.NullServiceException;
import com.dfl.api.base.appcommon.media.AudioCp;
import com.dfl.api.base.appcommon.media.IMediaCallback;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.player.FullMusicPlayer;
import com.dfl.smartscene.ccs.model.player.MusicPlayer;
import com.dfl.smartscene.ccs.model.player.Player;
import com.dfl.smartscene.ccs.model.player.PlayerFactory;
import com.dfl.smartscene.ccs.util.OpeUtil;
import com.iauto.uibase.utils.MLog;

import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/10/26 10:14
 * @description ：多媒体控制model层
 */
public class MediaModel implements DeviceBaseModel, CarConfigManager.CarConfigInitListener {
    private static final String TAG = "MediaModel";

    /**
     * 多媒体的传参为method@param,如special@recommend表示播放推荐
     */
    private static final String METHOD_LOCAL = "local";//本地音乐资源播放

    private static volatile MediaModel sInstance;

    public static MediaModel getInstance() {
        if (null == sInstance) {
            synchronized (MediaModel.class) {
                if (null == sInstance) {
                    sInstance = new MediaModel();
                }
            }
        }
        return sInstance;
    }

    private Player currentPlayer;
    /**
     * 注册获取车辆配置成功监听，从车辆配置中拿到多媒体包名和服务名后，再进行mediasession的连接
     */
    public void init() {
        CarConfigManager.getInstance().registerInitListener(this);
    }

    /**
     * 注册当前音源监听。并连接当前音源
     */
    public void initAudioListener() {
        try {
//            AudioCp nowAudio = CustomApiManager.getInstance().getIMedia().getMediaAudioSource();
//            if (nowAudio != null) {
//                currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(new ComponentName(nowAudio.getPkg(), nowAudio.getCls()));
//            }
            CustomApiManager.getInstance().getIMedia().registerMediaCallback(new IMediaCallback() {
                @Override
                public void onMediaAudioSourceChange(AudioCp audioCp) {
                    MLog.d(TAG, "onMediaAudioSourceChange : " + audioCp.getPkg() + audioCp.getCls());
                    if(currentPlayer == null){
//                        currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(new ComponentName(audioCp.getPkg(), audioCp.getCls()));
                    }else if(!currentPlayer.getTag().equals(audioCp.getPkg())){
                        currentPlayer.destroy();
                        currentPlayer = null;
//                        currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(new ComponentName(audioCp.getPkg(), audioCp.getCls()));
                    }
                }

                @Override
                public void onMediaAudioSourceListChange(List<AudioCp> cpTypes) {

                }

            });
        } catch (NullServiceException | NullPointerException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onCarConfigInit(CarConfig carConfig) {
        PlayerFactory.getInstance().init();
    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        switch (settingOperation.getOperationId()) {
            case ConstantModelValue.OID_ACT_APP_MEDIA_END:
                pauseCurrentMedia();
                return true;
            case ConstantModelValue.OID_ACT_APP_MEDIA_RADIO:
                excuteRadioOperation(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_APP_MEDIA_MUSIC:
                excuteMusicOperation(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_APP_MEDIA_BOOK:
                excuteBookOperation(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_MUSIC:
                executeFullMusic(settingOperation);
                return true;
            default:
                return false;
        }

    }

    /**
     * 处理电台操作
     *
     * @param settingOperation
     */
    private void excuteRadioOperation(SettingOperation settingOperation) {
        if(currentPlayer == null){
            currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getRadioComponent());
        }else{
            String pkgName = "unKnown1";
            if(PlayerFactory.getInstance().getRadioComponent() != null){
                pkgName = PlayerFactory.getInstance().getRadioComponent().getPackageName();
            }
            if(!currentPlayer.getTag().equals(pkgName)){
                currentPlayer.destroy();
                currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getRadioComponent());
            }
        }
        if(currentPlayer != null){
            currentPlayer.start(settingOperation);
        }

    }

    /**
     * 处理音乐操作
     *
     * @param settingOperation
     */
    private void excuteMusicOperation(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }
        String arg = settingOperation.getListArgs().get(0);
        String[] split = arg.split("@");

        if (METHOD_LOCAL.equals(split[0])) {

        }else {
            if(currentPlayer == null){
                currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getMusicComponent());
            }else {
                String pkgName = "unKnown2";
                if(PlayerFactory.getInstance().getMusicComponent() != null){
                    pkgName = PlayerFactory.getInstance().getMusicComponent().getPackageName();
                }
                if(!currentPlayer.getTag().equals(pkgName)){
                    currentPlayer.destroy();
                    currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getMusicComponent());
                }
            }
            if(currentPlayer != null){
                currentPlayer.start(settingOperation);
            }

        }
    }

    private void excuteBookOperation(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }

        if(currentPlayer == null){
            currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getBookComponent());
        }else{
            String pkgName = "unKnown3";
            if(PlayerFactory.getInstance().getBookComponent() != null){
                pkgName = PlayerFactory.getInstance().getBookComponent().getPackageName();
            }
            if(!currentPlayer.getTag().equals(pkgName)){
                currentPlayer.destroy();
                currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(PlayerFactory.getInstance().getBookComponent());
            }
        }
        if(currentPlayer != null){
            currentPlayer.start(settingOperation);
        }
    }

    private void executeFullMusic(SettingOperation settingOperation){
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }

        if(currentPlayer == null){
            currentPlayer = PlayerFactory.getInstance().creatFullMusicPlayer();
        }else if(!(currentPlayer instanceof  FullMusicPlayer)){
            if(!(currentPlayer instanceof MusicPlayer)){
                currentPlayer.destroy();
            }
            currentPlayer = PlayerFactory.getInstance().creatFullMusicPlayer();
        }
        if(currentPlayer != null){
            currentPlayer.start(settingOperation);
        }
    }


    /**
     * 暂停当前音源
     */
    public void pauseCurrentMedia() {
        syncCurrentPlayer();
        if(currentPlayer != null){
            currentPlayer.pause();
        }
    }

    public void resumeCurrentMedia() {
        syncCurrentPlayer();
        if(currentPlayer != null){
            currentPlayer.resume();
        }
    }

    public void syncCurrentPlayer(){
        AudioCp nowAudio = null;
        try {
            nowAudio = CustomApiManager.getInstance().getIMedia().getMediaAudioSource();
        } catch (NullServiceException | RemoteException e) {
            e.printStackTrace();
        }
        if (nowAudio == null || "com.dfl.vr".equals(nowAudio.getPkg())) {
            return;
        }
        ComponentName componentName = new ComponentName(nowAudio.getPkg(), nowAudio.getCls());

        if(currentPlayer == null){
            LogUtil.d(TAG,"syncCurrentPlayer currentPlayer == null");
            currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(componentName);
        }else if(!currentPlayer.getTag().equals(componentName.getPackageName())){
            LogUtil.d(TAG,"syncCurrentPlayer !currentPlayer.getTag().equals(componentName.getPackageName()) : " + componentName.getPackageName());

            currentPlayer.destroy();
            currentPlayer = PlayerFactory.getInstance().creatCommonPlayer(componentName);
        }

    }

    public boolean isCurrentFullMusic(){
        return currentPlayer != null && currentPlayer instanceof FullMusicPlayer;
    }

    public void disconnectPlayer(){
        if(currentPlayer != null){
            currentPlayer.destroy();
            currentPlayer = null;
        }
    }

    public void playFullNext(){
        if(currentPlayer != null && currentPlayer instanceof FullMusicPlayer){
            ((FullMusicPlayer) currentPlayer).playNext();
        }
    }
}
