package com.dfl.smartscene.ccs.view.weight;

import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.iauto.uibase.utils.MLog;

/**
 * <AUTHOR>
 * @date ：2022/9/16 14:09
 * @description ：周一，周二....周日的
 */
public class UserDayDialogAdapter extends BaseAdapter<String> {
    private int dayValue = 127;//二进制，第一位表示周一，第七位表示周日，默认全部选中，周一选中为0x 0000001 周一周五选中为 0x 0010001
    @Override
    protected BaseHolder<String> getViewHolder(View view, int viewType) {
        return new UserDayHolder(view);
    }

    public int getDayValue() {
        MLog.d("UserDayDialogAdapter" , String.valueOf(dayValue));

        return dayValue;
    }

    public void setDayValue(int value) {
        MLog.d("UserDayDialogAdapter setDayValue " , String.valueOf(value));
        dayValue = value;
        notifyDataSetChanged();
    }

    public String getDesc() {
        StringBuilder stringBuilder = new StringBuilder();
        for(int i = 0 ; i < 7 ; i++){
            if(((dayValue >> i) & 1) != 0){
                if(!stringBuilder.toString().equals("")){
                    stringBuilder.append(",");
                }
                stringBuilder.append(getItemData(i));
            }
        }
        return stringBuilder.toString();
    }

    public void changeCheckStatus(int pos){
        if(((dayValue >> pos) & 1) == 0){
            dayValue = dayValue | (1 << pos);
        }else {
            dayValue = dayValue & (~(1 << pos));
        }
        notifyItemChanged(pos);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_user_day;
    }

    class UserDayHolder extends BaseHolder<String> {
        private TextView mTextViewTitle;
        private CheckBox mCheckBox;
        private ConstraintLayout mLayout;
        public UserDayHolder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_user_day_title);
            mCheckBox = itemView.findViewById(R.id.checkbox_item_user_day);
            mLayout = itemView.findViewById(R.id.ccs_item_user_day);
        }

        @Override
        public void setupData(String s, int position) {
            mTextViewTitle.setText(s);
            if(((dayValue >> position) & 1) == 0){
//                mTextViewTitle.setTextColor(mTextViewTitle.getResources().getColor(R.color.color_text_unselected));
                mCheckBox.setChecked(false);
                mLayout.setSelected(false);

            }else {
//                mTextViewTitle.setTextColor(mTextViewTitle.getResources().getColor(R.color.color_text_selected));
                mCheckBox.setChecked(true);
                mLayout.setSelected(true);
            }
        }

//        @Override
//        public void onClick(View v) {
//            int pos = getAdapterPosition();
//            if(((dayValue >> pos) & 1) == 0){
//                dayValue = dayValue | (1 << pos);
//            }else {
//                dayValue = dayValue & (~(1 << pos));
//            }
//            notifyItemChanged(pos);
//            super.onClick(v);
//
//        }
    }
}
