package com.dfl.smartscene.ccs.bean;

import androidx.annotation.NonNull;

import java.io.Serializable;
import java.util.List;

/**
 * 场景的动作，触发，状态存储参数
 * <AUTHOR>
 * @date ：2022/8/2 13:49
 * @description ：
 */
public class SettingOperation implements Serializable {
    /**
     * 设备id
     */
    String deviceId = "";
    /**
     * 操作id
     */
    String operationId = "";
    /**
     * 操作描述
     */
    String desc = "";
    /**
     * 参数列表
     */
    List<String> listArgs;

    /**
     * 从属场景id
     */
    String sceneId;

    /**
     * 展示类型
     */
    int showType;

    public SettingOperation() {
    }

    public SettingOperation(String deviceId, String operationId, List<String> listArgs) {
        this.deviceId = deviceId;
        this.operationId = operationId;
        this.listArgs = listArgs;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public List<String> getListArgs() {
        return listArgs;
    }

    public void setListArgs(List<String> listArgs) {
        this.listArgs = listArgs;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public int getShowType() {
        return showType;
    }

    public void setShowType(int showType) {
        this.showType = showType;
    }

    @NonNull
    @Override
    public String toString() {
        return "SettingOperation{" +
                "mDeviceId='" + deviceId + '\'' +
                ", mOperationId='" + operationId + '\'' +
                ", mDesc='" + desc + '\'' +
                ", mListArgs=" + listArgs +
                '}';
    }
}
