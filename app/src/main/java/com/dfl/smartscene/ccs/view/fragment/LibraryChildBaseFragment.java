package com.dfl.smartscene.ccs.view.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.base.SimpleBaseFragment;
import com.dfl.smartscene.ccs.view.commonview.OverTopContentView;


/**
 * <AUTHOR>
 * @date ：2022/12/5 9:34
 * @description ：
 */
public abstract class LibraryChildBaseFragment<T extends BaseAdapter<SceneBean>> extends SimpleBaseFragment {
    protected SceneCategory mSceneCategory;//页面数据
    private T mLibrarySceneAdapter;
    int COLUMN_SPACE = ConstantViewValue.COMMON_SHOW_SPACE;//item之间的间距
    int LINE_SPACE = R.dimen.y_px_24;//item之间的间距
    private int SPAN_NUM = 2;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_library_child,container,false);
    }

    @Override
    protected void init() {
        if(getArguments() != null){
            mSceneCategory = (SceneCategory) getArguments().getSerializable(ConstantViewValue.FRAGMENT_KEY_LIBRARY_CHILD_DATA);
        }
    }

    @Override
    protected void initView(View view) {
        RecyclerView recyclerView = view.findViewById(R.id.recyclerview_library_child);
        RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL,  SPAN_NUM , COLUMN_SPACE,LINE_SPACE,false);
        mLibrarySceneAdapter = createAdapter();
        recyclerView.setAdapter(mLibrarySceneAdapter);
        initAdapterItemClickEvent(mLibrarySceneAdapter);
        //顶部条
        OverTopContentView overTopContentView = new OverTopContentView();
        overTopContentView.initView(view);
        overTopContentView.setTitle(mSceneCategory.getCategoryName());
    }

    protected void initAdapterItemClickEvent(T adapter){

    }

    protected abstract T createAdapter();

    protected T getAdapter(){
        return mLibrarySceneAdapter;
    }
    @Override
    protected void initObserver() {


    }

    @Override
    protected void initData() {
        mLibrarySceneAdapter.setDataList(mSceneCategory.getSceneBeanList());

    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {

    }
}
