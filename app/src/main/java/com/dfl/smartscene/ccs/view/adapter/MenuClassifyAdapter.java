package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.CategoryBean;
import com.dfl.smartscene.ccs.util.DescriptionUtil;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.view.baseadapter.ClassifyAdapter;


/**
 * <AUTHOR>
 * @date ：2022/11/2 17:55
 * @description ：添加操作选择菜单的adapter，特点带一个icon
 */
public class MenuClassifyAdapter extends ClassifyAdapter {

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_menu_classify;
    }

    @Override
    protected BaseHolder<CategoryBean> getViewHolder(View view, int viewType) {
        return new MenuClassifyHolder(view);
    }

    //选定分类，更新旧的被选定与新的被选定
    public void setSelectedPos(int selectedPos) {
        if(selectedPos != this.mSelectedPos){
            int oldPos = this.mSelectedPos;
            this.mSelectedPos = selectedPos;
            notifyItemChanged(oldPos);
            notifyItemChanged(selectedPos);
        }
    }

    public void setSelectedPosById(String id){
        if(getDataList() == null){
            return;
        }
        int newpos = 0;
        for(int i = 0 ; i < getItemCount() ; i ++){
            if(getItemData(i).getId().equals(id)){
                newpos = i;
                break;
            }
        }
        setSelectedPos(newpos);
    }

    public int getSelectedPos() {
        return mSelectedPos;
    }

    class MenuClassifyHolder extends ClassifyHolder {
        private ImageView mImageViewIcon;
        private TextView mTextView;
        private ImageView mImageView;
        public MenuClassifyHolder(View itemView) {
            super(itemView);
            mImageViewIcon = itemView.findViewById(R.id.imageview_item_menu_classify_icon);
            mTextView = itemView.findViewById(R.id.textview_base_category_item_classify);
            mImageView = itemView.findViewById(R.id.imageview_item_menu_classify);
        }

        @Override
        public void setupData(CategoryBean categoryBean, int position) {
//            super.setupData(categoryBean, position);
            ImageUtil.loadIcon(categoryBean.getImageUrl(),mImageViewIcon);
            //选定的Tab展示不同UI
            if(position == mSelectedPos){
                mTextView.setTextColor(mTextView.getResources().getColor(R.color.color_tab_selected));
                mImageView.setBackgroundResource(R.drawable.bg_classify_item_selected);
                mImageViewIcon.setColorFilter(ContextCompat.getColor(mImageViewIcon.getContext(),R.color.color_tab_selected));
            }else {
                mTextView.setTextColor(mTextView.getResources().getColor(R.color.color_tab_unselected));
                mImageView.setBackgroundResource(R.color.transparent);
                mImageViewIcon.setColorFilter(ContextCompat.getColor(mImageViewIcon.getContext(),R.color.color_tab_unselected));
            }
            mTextView.setText(categoryBean.getName());
            //设置标题的可见即可说
            DescriptionUtil.generateButtonDescriptionVisibleToSay(categoryBean.getName(), itemView);

//            ImageUtil.loadIcon(categoryBean.getImageUrl(),mImageViewIcon);
//            if(position == mSelectedPos){
//                mImageViewIcon.setColorFilter(ContextCompat.getColor(mImageViewIcon.getContext(),R.color.color_text_selected));
//            }else {
//                mImageViewIcon.setColorFilter(ContextCompat.getColor(mImageViewIcon.getContext(),R.color.color_text_secondary_label));
//            }
        }
    }
}
