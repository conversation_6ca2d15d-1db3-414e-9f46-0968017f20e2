package com.dfl.smartscene.ccs.view.dialogfragment;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/29 17:29
 * @description ：操作视图需具备的方法
 */
public interface OperationBaseView {
    /**
     * 提取对应的操作参数
     * @return
     */
    List<String> extractArgs();

    /**
     * 提取对应的场景描述
     * @return
     */
    String extractDesc();

    /**
     * 黑夜模式变更通知
     */
    void onConfigurationChanged();
}
