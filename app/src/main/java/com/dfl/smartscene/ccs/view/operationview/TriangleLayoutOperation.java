package com.dfl.smartscene.ccs.view.operationview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.factory.OperationBaseViewFactory;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

/**
 * <AUTHOR>
 * @date ：2022/11/9 14:54
 * @description ：
 */
public class TriangleLayoutOperation extends VerticalLayoutOperation{
    public TriangleLayoutOperation(SingleOperation singleOperation, ViewGroup parent) {
        super(singleOperation, parent);
    }

    @Override
    protected ViewGroup creatViewContainer(ViewGroup parent) {
        View root = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_triangle_operation,parent,true);
        ViewGroup container = root.findViewById(R.id.layout_triangle_operation_container);
        return container;
    }

    @Override
    protected void layoutChild(ViewGroup container, SingleOperation singleOperation) {
        for(int i = 0 ; i < 3 ; i ++){
            //包含title的view
            ViewGroup itemContainer;
            SingleOperation itemData = singleOperation.getSubOperations().get(i);
            if(i == 0){
                itemContainer = container.findViewById(R.id.layout_triangle_operation_item_top);
            }else if(i == 1){
                itemContainer = container.findViewById(R.id.layout_triangle_operation_item_left);
            }else {
                itemContainer = container.findViewById(R.id.layout_triangle_operation_item_right);
            }
            LayoutInflater.from(container.getContext()).inflate(R.layout.item_vertical_operation,itemContainer,true);
            //实际包裹的单元view
            ViewGroup itemContentViewContainer = itemContainer.findViewById(R.id.layout_vertical_operation_content);
            TextView textViewTitle = itemContainer.findViewById(R.id.textview_item_vertical_operation_title);
            textViewTitle.setText(itemData.getOperationName());
            OperationBaseView itemView = OperationBaseViewFactory.addOperationBaseView(itemData, itemContentViewContainer);
            onItemViewAdd(itemData,itemView,itemContainer);
        }
    }
}
