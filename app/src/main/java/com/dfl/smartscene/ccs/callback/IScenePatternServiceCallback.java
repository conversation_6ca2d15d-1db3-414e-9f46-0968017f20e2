/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.dfl.smartscene.ccs.callback;
public interface IScenePatternServiceCallback extends android.os.IInterface
{
  /** Default implementation for IScenePatternServiceCallback. */
  public static class Default implements IScenePatternServiceCallback
  {
    /**
         * 监听模式打开状态
    	 * 
         */
    @Override public void onRelaxStatusChange(int status) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements IScenePatternServiceCallback
  {
    private static final String DESCRIPTOR = "com.dfl.api.app.scenepattern.IScenePatternServiceCallback";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.dfl.api.app.scenepattern.IScenePatternServiceCallback interface,
     * generating a proxy if needed.
     */
    public static IScenePatternServiceCallback asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof IScenePatternServiceCallback))) {
        return ((IScenePatternServiceCallback)iin);
      }
      return new Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_onRelaxStatusChange:
        {
          data.enforceInterface(descriptor);
          int _arg0;
          _arg0 = data.readInt();
          this.onRelaxStatusChange(_arg0);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements IScenePatternServiceCallback
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /**
           * 监听模式打开状态
      	 * 
           */
      @Override public void onRelaxStatusChange(int status) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(status);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onRelaxStatusChange, _data, null, android.os.IBinder.FLAG_ONEWAY);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onRelaxStatusChange(status);
            return;
          }
        }
        finally {
          _data.recycle();
        }
      }
      public static IScenePatternServiceCallback sDefaultImpl;
    }
    static final int TRANSACTION_onRelaxStatusChange = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(IScenePatternServiceCallback impl) {
      // Only one user of this interface can use this function
      // at a time. This is a heuristic to detect if two different
      // users in the same process use this function.
      if (Proxy.sDefaultImpl != null) {
        throw new IllegalStateException("setDefaultImpl() called twice");
      }
      if (impl != null) {
        Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static IScenePatternServiceCallback getDefaultImpl() {
      return Proxy.sDefaultImpl;
    }
  }
  /**
       * 监听模式打开状态
  	 * 
       */
  public void onRelaxStatusChange(int status) throws android.os.RemoteException;
}
