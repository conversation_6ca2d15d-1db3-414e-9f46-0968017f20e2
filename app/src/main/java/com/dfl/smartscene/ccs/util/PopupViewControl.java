package com.dfl.smartscene.ccs.util;

import static com.dfl.api.da.popup.PopupDef.LAYOUTPARAMS_TYPE_POPUP;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.util.ArrayMap;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.popup.IPopup;
import com.dfl.api.da.popup.IPopupCallback;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.view.MessageToast;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MPopupViewBase;

public class PopupViewControl {
    private static final String TAG = "PopupViewControl";
    @SuppressLint("StaticFieldLeak")
    private static PopupViewControl sPopupViewControl = null;
    private Context mContext = null;
    private final ArrayMap<String, MPopupViewBase> mPopupViews = new ArrayMap<>();
    private IPopup mIPopup = null;
    public boolean mIsSeatPopCanShowed = true;
    public boolean mPopupServiceRetry = false;

    private PopupViewControl() {
        init(ScenePatternApp.getInstance());
    }

    public static PopupViewControl getInstance() {
        if (null == sPopupViewControl) {
            sPopupViewControl = new PopupViewControl();
        }
        return sPopupViewControl;
    }

    public void init(Context context) {
        mContext = context;
        if (null == mIPopup) {
            BaseManager.create(mContext, Constants.DFL_DA_POPUP_POPUP_SERVICE, new APICreateCallback() {
                @Override
                public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                    if (APIStateType.APIState.SUCCESS == apiStateType && iBaseAPI instanceof IPopup) {
                        MLog.d(TAG, "onAPICreateCallback");
                        mIPopup = (IPopup) iBaseAPI;
                        mPopupServiceRetry = false;
                        try {
                            mIPopup.registerPopupCallback(mIPopupCallback);
                            registerPopup();
                        } catch (NullServiceException e) {
                            e.printStackTrace();
                            mIPopup = null;
                        }
                        MLog.d(TAG, "IPopup create success");
                    } else if (!mPopupServiceRetry
                            && (APIStateType.APIState.ERROR_SERVICE_EXCEPTION == apiStateType
                            || APIStateType.APIState.ERROR_TRY_TO_CONNECT_SERVICE_TIMEOUT == apiStateType)) {
                        mPopupServiceRetry = true;
                        BaseManager.create(mContext, Constants.DFL_DA_POPUP_POPUP_SERVICE, this);
                    } else {
                        mPopupServiceRetry = false;
                        mIPopup = null;
                        MLog.d(TAG, "IPopup create failed");
                    }
                }

                @Override
                public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

                }
            });
        }
    }

    private final IPopupCallback mIPopupCallback = new IPopupCallback() {
        @Override
        public void onPopupStateChanged(String s, int i) {
            MLog.d(TAG, "onPopupStateChanged: " + s + ", " + i);
        }

        @Override
        public void onPopupShowingChanged(boolean b) {
            MLog.d(TAG, "onPopupShowingChanged: " + b);
        }
    };

    /**
     * 获取指定的SysView对象
     * @param viewId viewId
     * @return SysView对象
     */
    private MPopupViewBase getPopupView(String viewId) {
        MPopupViewBase popup = mPopupViews.get(viewId);
        if (null == popup) {
            popup = createPopupView(viewId);
            if (null != popup) {
                mPopupViews.put(viewId, popup);
            }
        }
        return popup;
    }

    private void registerPopup() {
        MLog.d(TAG, "registerPopup");
        getPopupView(PopupViewDef.PopupId_Scene_Massage_Execute_Info);
    }

    /**
     * 创建SysView对象
     * @param viewId viewId
     * @return SysView对象
     */
    private MPopupViewBase createPopupView(String viewId) {
        MLog.d(TAG, "createPopupView: " + viewId);
        MPopupViewBase popup = null;
        WindowManager.LayoutParams params = null;
        int type = -1;

        switch (viewId) {
            case PopupViewDef.PopupId_Scene_Massage_Execute_Info:{
                popup = new MessageToast(mContext, viewId);
                break;
            }
            default:{}
        }

        if (null != popup) {
            type = 0;
            switch (viewId){
                case PopupViewDef.PopupId_Scene_Massage_Execute_Info: {
                    type = 0;
                    int xpos = 239;
                    int ypos = 50;
                    params = new WindowManager.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            xpos,
                            ypos,
                            LAYOUTPARAMS_TYPE_POPUP,
                            WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
                            PixelFormat.TRANSLUCENT);
                    params.windowAnimations = R.style.style_scene_message_toast;
                    params.gravity = Gravity.CENTER | Gravity.BOTTOM;
                    break;
                }
                default:
                    params = new WindowManager.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            0, 0,
                            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
                            WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM,
                            PixelFormat.TRANSLUCENT);
                    params.gravity = Gravity.TOP | Gravity.START;
                    break;
            }
        }

        if (null != mIPopup && null != popup && null != params) {
            try {
                //MLog.d(TAG, "setPopup viewId:"+viewId+" type:"+type+" popup:"+popup+" params:"+params);
                mIPopup.setPopup(viewId, type, popup, params);
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
        return popup;
    }

    /**
     *
     * @param viewId
     * @param text
     */
    public void showMessagePopupView(String viewId, String text) {
        MLog.d(TAG, "showMessagePopupView: message: " + text);
        MessageToast popup = (MessageToast) getPopupView(viewId);
        int time = 2500;
        popup.setParams(text, time);

        if (null != mIPopup && null != popup) {
            try {
                mIPopup.showPopup(viewId);
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
    }

    public void closePopup(String popId) {
        MLog.d(TAG, "closePopup: ["+popId+"]");
        if (popId ==null || popId.equals("") || mIPopup == null){
            return;
        }

        try {
            switch (popId){
                case PopupViewDef.PopupId_Scene_Massage_Execute_Info:
                    mIPopup.hidePopup(PopupViewDef.PopupId_Scene_Massage_Execute_Info);
                    break;
                default:
                    break;
            }
        }catch (Exception e) {
            MLog.d(TAG, "closePopup ex:"+e);
        }
    }
}
