package com.dfl.smartscene.ccs.bean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2022/7/12 14:25
 * @description ：一个具体的操作
 */
public class SingleOperation {
    /**
     * 操作id
     */
    String operationId = "";

    /**
     * 设备id
     */
    String deviceId = "";
    /**
     * 操作名
     */
    String operationName = "";

    /**
     * 操作icon
     */
    String operationIcon = "";

    int showMode = 0;

    /**
     * 展示类型
     */
    String viewType = "";
    /**
     * 参数map
     */
    Map<String,String> argsMap = new HashMap<>();
    /**
     * 列表参数map
     */
    Map<String,List<String>> listArgsMap = new HashMap<>();
    /**
     * 二维列表参数map
     */
    Map<String,List<List<String>>> dyadicListArgsMap = new HashMap<>();

    /**
     *子操作列表
     */
    List<SingleOperation> subOperations;

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Map<String, String> getArgsMap() {
        return argsMap;
    }

    public void setArgsMap(Map<String, String> argsMap) {
        this.argsMap = argsMap;
    }

    public List<SingleOperation> getSubOperations() {
        return subOperations;
    }

    public void setSubOperations(List<SingleOperation> subOperations) {
        this.subOperations = subOperations;
    }

    public String getViewType() {
        return viewType;
    }

    public void setViewType(String viewType) {
        this.viewType = viewType;
    }

    public Map<String, List<String>> getListArgsMap() {
        return listArgsMap;
    }

    public void setListArgsMap(Map<String, List<String>> listArgsMap) {
        this.listArgsMap = listArgsMap;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public void setMapArgs(String key, String value){
        if(argsMap == null){
            argsMap = new HashMap<>();
        }
        if(value == null || "".equals(value)){
            return;
        }
        argsMap.put(key,value);
    }

    public void setMapArgs(String key,List<String> value){
        if(listArgsMap == null){
            listArgsMap = new HashMap<>();
        }
        if(value == null){
            return;
        }
        listArgsMap.put(key,value);
    }

    public void setDyadicMapArgs(String key,List<List<String>> value){
        if(dyadicListArgsMap == null){
            dyadicListArgsMap = new HashMap<>();
        }
        if(value == null){
            return;
        }
        dyadicListArgsMap.put(key,value);
    }

    public String getArg(String key){
        return argsMap.get(key);
    }
    public String getArg(String key,String defaultValue){
        return argsMap.get(key) == null ? defaultValue : argsMap.get(key);
    }

    public List<String> getListArg(String key){
        return listArgsMap.get(key);
    }

    public List<List<String>> getDyadicListArg(String key){
        return dyadicListArgsMap.get(key);
    }

    public Map<String, List<List<String>>> getDyadicListArgsMap() {
        return dyadicListArgsMap;
    }

    public void setDyadicListArgsMap(Map<String, List<List<String>>> dyadicListArgsMap) {
        this.dyadicListArgsMap = dyadicListArgsMap;
    }

    public String getOperationIcon() {
        return operationIcon;
    }

    public void setOperationIcon(String operationIcon) {
        this.operationIcon = operationIcon;
    }

    public int getShowMode() {
        return showMode;
    }

    public void setShowMode(int mShowType) {
        this.showMode = mShowType;
    }
}
