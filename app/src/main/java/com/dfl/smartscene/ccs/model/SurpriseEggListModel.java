package com.dfl.smartscene.ccs.model;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.dfl.api.app.usercenter.common.ICommon;
import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.library.app.LibraryApp;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.dfl.smartscene.ccs.util.SurpriseEggJsonDataManager;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.iauto.uibase.utils.MLog;
import com.szlanyou.gwsdk.LanYouRequest;
import com.szlanyou.gwsdk.RequestCallback;
import com.szlanyou.gwsdk.RequestEnvironment;
import com.szlanyou.gwsdk.bean.AppInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

/*
 * 彩蛋list model类
 * <AUTHOR>
 * @date 2022/5/15
 */
public class SurpriseEggListModel {
    private static final String TAG = "SurpriseEggListModel";
    @SuppressLint("StaticFieldLeak")
    private static SurpriseEggListModel  sSurpriseEggListModel = null;
    private Context mContext = null;
    private SurpriseEggList  mSurpriseEggList = new SurpriseEggList();
    private boolean mStopDel = false;
    private byte[] mApiLock = new byte[0];
    /**是否删除成功标识*/
    private boolean mIsDeletedSuc = true;
    private String mDaID = "";
    private IAppCommon mAppCommonServiceManager  = null;
    private HandlerThreadProcessor handlerThreadProcessor    = HandlerThreadProcessor.getInstance();
    protected   NetConnectChangeObserver mNetConnectChangeObserver = new NetConnectChangeObserver();        // 网络状态变化观察者对象

    private final MutableLiveData<Boolean> mSurpriseEggListChanged   = new MutableLiveData<>();         // 彩蛋list变化live data
    private final MutableLiveData<Integer> mSurpriseEggListDspStatus = new MutableLiveData<>();         // 表示状态变化live data
    private final MutableLiveData<Integer> mSurpriseEggListDelStatus   = new MutableLiveData<>();       // 彩蛋list变化live data
    private final MutableLiveData<Integer> mSurpriseEggListDelCount   = new MutableLiveData<>();        // 彩蛋list变化live data
    private ArrayList<Integer> mDeleteListID = new ArrayList<Integer>();
    /**记录删除失败的彩蛋id列表*/
    private HashSet<Integer> mDeleteErrorList = new HashSet<>();

    /**
     * get SurpriseEggListModel's instance
     */
    public static SurpriseEggListModel getInstance() {
        return sSurpriseEggListModel;
    }

    /**
     * SurpriseEggListModel object constructor
     *
     * @param context
     */
    private SurpriseEggListModel(Context context) {
        mContext = context;
        // init livedata
        initLiveData();
        initAppCommon();
        initICommonApi();
        ScenePatternInterrupt.getInstance().setAppCommonNotify();
        ScenePatternInterrupt.getInstance().setSourceChangeNotify();
    }

    /**
     * create SurpriseEggListModel's instance
     *
     * @param context
     */
    public static synchronized void create(Context context) {
        if (sSurpriseEggListModel == null) {
            sSurpriseEggListModel = new SurpriseEggListModel(context);
        }
    }

    /*
    * 网络状态变化观察者类
    * <AUTHOR>
    * @date 2022/5/15
    */
    public class NetConnectChangeObserver implements Observer<Integer> {
        @Override
        public void onChanged(Integer changed) {
            MLog.d(TAG,"SurpriseEggListModel:NetConectChangeObserver ");
            if(ScenePatternFuncDef.INTERNET_STATUS_CONNECTED == changed) {
                MLog.d(TAG,"ScenePatternFuncDef.INTERNET_STATUS_CONNECTED");

                if (isUserCenterLogin()) {
                    mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_LOADING);
//                    requestSurpriseEggListData();
                } else {
                    mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
                    mSurpriseEggList.clearSurpriseEggList();
                }
            } else {
                MLog.d(TAG,"ScenePatternFuncDef.INTERNET_STATUS_DISCONNECTED");
//                mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_DISCONNECT);
//                mSurpriseEggList.clearSurpriseEggList();
            }
        }
    }

    public void addObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:addObserver ");
        ScenePatternInterrupt.getInstance().getNetWorkStatus().observeForever(mNetConnectChangeObserver);
    }

    public void removeObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:removeObserver ");
        ScenePatternInterrupt.getInstance().getNetWorkStatus().removeObserver(mNetConnectChangeObserver);
    }

    public MutableLiveData<Integer> getSurpriseEggListDspStatus() {
        return mSurpriseEggListDspStatus;
    }

    public MutableLiveData<Boolean> getSurpriseEggListChanged() {
        return mSurpriseEggListChanged;
    }
    public void changeSurpriseEggListChanged() {
        mSurpriseEggListChanged.postValue(!mSurpriseEggListChanged.getValue());
    }

    public MutableLiveData<Integer> getSurpriseEggListDelStatus() {
        return mSurpriseEggListDelStatus;
    }

    public MutableLiveData<Integer> getSurpriseEggListDelCount() {
        return mSurpriseEggListDelCount;
    }

    public String getDaID() {
        return mDaID;
    }

    public void setStopDel(boolean stopDel) {
        this.mStopDel = stopDel;
    }

    public void setSurpriseEggListDspStatus(int status) {
        mSurpriseEggListDspStatus.setValue(status);
    }


    /**
     * init live data
     */
    private void initLiveData() {
        mSurpriseEggListDelStatus.setValue(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_NON);
        mSurpriseEggListChanged.setValue(false);
        mSurpriseEggListDspStatus.setValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
        mSurpriseEggListDelCount.setValue(0);

        addObserver();
    }

    /**
     * 请求彩蛋list
     * demo
     */
    public void requestSurpriseEggListData() {
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("api", "ly.ccsv.da.surprise.list");
        LanYouRequest.request(map, new RequestCallback() {
            @Override
            public void onStart() {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onSuccess(String json) {
                mSurpriseEggList.clearSurpriseEggList();
                boolean result = SurpriseEggJsonDataManager.getSurpriseEggListReqResult(json,mSurpriseEggList);
                int listSize = mSurpriseEggList.getSurpriseEggListSize();
                MLog.d(TAG,"listSize:"+ listSize );
                if (true == result && 0 != listSize) {
                    if (mSurpriseEggListDspStatus.getValue() != SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_COMMON) {
                        mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_COMMON);
                    }
                } else {
                    mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
                }
            }

            @Override
            public void onError(Throwable throwable) {
                MLog.d(TAG,"requestSurpriseEggListData onErrorCallBack");
                MLog.d(TAG,"requestSurpriseEggListData throwable:"+throwable.getLocalizedMessage());
                mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_DISCONNECT);
            }

            @Override
            public void onFail(String json) {
                MLog.d(TAG,"requestSurpriseEggListData onFailCallBack");
                SurpriseEggJsonDataManager.getSurpriseEggListReqResult(json,mSurpriseEggList);
                mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
            }
        },true);

    }

    private static final class SurpriseEggDisplayDataRequest implements RequestCallback{
        @Override
        public void onStart() {

        }

        @Override
        public void onComplete() {

        }

        @Override
        public void onSuccess(String json) {
            MLog.d(TAG,"requestSurpriseEggDisplayData onSuccess");
            SurpriseEgg egg = new SurpriseEgg();
            boolean result = SurpriseEggJsonDataManager.getSurpriseEggKeywordReqResult(json,egg);
            if (result) {
                SurpriseEggPlayModel.getInstance().setSurpriseEggURL(egg.getURL());
                SurpriseEggPlayModel.getInstance().setSurpriseEggTTSText(egg.getTTSText());
                SurpriseEggPlayModel.getInstance().setSurpriseEggVoiceFile(egg.getVoiceFile());
                SurpriseEggPlayModel.getInstance().setSurpriseEggVoiceTyp(egg.getVoiceType());
                SysViewControl.getInstance().reqOpenSurpriseEggPlayFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_SURPRISE);
            }
        }

        @Override
        public void onError(Throwable throwable) {
            MLog.d(TAG,"requestSurpriseEggDisplayData onErrorCallBack");
            MLog.d(TAG,"requestSurpriseEggListData throwable:"+throwable.getLocalizedMessage());
        }

        @Override
        public void onFail(String json) {
            MLog.d(TAG,"requestSurpriseEggDisplayData onFailCallBack");
            MLog.d(TAG,"json:"+json);
        }
    }


    /**
     * 请求彩蛋list
     * demo
     */
    public boolean requestSurpriseEggDisplayData(int keyCode) {
        // 测试代码
        MLog.d(TAG,"requestSurpriseEggDisplayData");

        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("api","ly.ccsv.da.surprise.getbykeyword");
        map.put("triggerKeyword", keyCode);
        LanYouRequest.request(map, new SurpriseEggDisplayDataRequest(),true);

        return true;
    }

    /**
     * 取得彩蛋list
     * @return SurpriseEggList 彩蛋list
     */
    public SurpriseEggList getSurpriseEggList() {
        return mSurpriseEggList;
    }

    /**
     * 删除指定管理id的彩蛋item
     * @return id 彩蛋item管理id
     */
    public void deleteSurpriseEggList(ArrayList<Integer> listID) {
        mIsDeletedSuc = true;
        if (isUserCenterLogin()) {
            mDeleteListID.addAll(listID);
            mSurpriseEggListDelStatus.setValue(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_BEG);
            mDeleteErrorList.clear();
            deleteSurpriseEggListItem();
        } else {
            MLog.d(TAG,"deleteSurpriseEggList:delete is error for user logout");
            mSurpriseEggListDelStatus.setValue(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_ERR);
        }
    }

    /**
     * 删除终了处理单个彩蛋
     * 内部使用迭代，每删除一个，在删除成功回调处删除下一个，直到删除完了
     * <注意事项>如外部调用此函数，请务必先清理mDeleteErrorList列表
     */
    public void deleteSurpriseEggListItem() {
        // 迭代推出条件，所有删除list中的数据被删除，或者主动停止删除。
        MLog.d(TAG, "delete surprise egg list item, size = "+mDeleteListID.size()+", mStopDel = "+mStopDel);
        if(0 == mDeleteListID.size() || mStopDel) {
            MLog.d(TAG, "delete surprise egg list, mIsDeletedSuc = "+mIsDeletedSuc);
            if (mIsDeletedSuc) {
                if(mDeleteErrorList.size() == 0){
                    deleteEndHandler(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_END);
                }else{
                    deleteEndHandler(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_ERR);
                }
            } else {
                deleteEndHandler(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_ERR);
            }
            mIsDeletedSuc = true;
            mStopDel = false;
            return;
        }
        // 取得要删除的一个彩蛋，并
        Integer id= mDeleteListID.get(0);
        mDeleteListID.remove(id);
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("api", "ly.ccsv.da.surprise.delete");
        map.put("id", id);
        LanYouRequest.request(map, new RequestCallback() {
            private final int mID = id;
            @Override
            public void onStart() {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onSuccess(String json) {
                boolean result = SurpriseEggJsonDataManager.getSurpriseEggDelReqResult(json);
                MLog.d(TAG, "on success , result = "+result);
                if (true == result) {
                    mSurpriseEggListDelCount.postValue(mSurpriseEggListDelCount.getValue()+1);
                    mSurpriseEggList.deleteSurpriseEgg(mID);
                    deleteSurpriseEggListItem();
                }else{
                    mDeleteErrorList.add(mID);
                }
            }

            @Override
            public void onError(Throwable throwable) {
                MLog.d(TAG,"deleteSurpriseEggListItem onErrorCallBack");
                MLog.d(TAG,"requestSurpriseEggListData throwable:"+throwable.getLocalizedMessage());
                mDeleteErrorList.add(mID);
                mSurpriseEggListDelCount.postValue(mSurpriseEggListDelCount.getValue()+1);
                deleteSurpriseEggListItem();
                mIsDeletedSuc = false;
            }

            @Override
            public void onFail(String json) {
                MLog.d(TAG,"deleteSurpriseEggListItem onFailCallBack");
                mDeleteErrorList.add(mID);
                SurpriseEggJsonDataManager.getSurpriseEggDelReqResult(json);
                mSurpriseEggListDelCount.postValue(mSurpriseEggListDelCount.getValue()+1);
                mIsDeletedSuc = false;
                deleteSurpriseEggListItem();
            }
        },true);
        mSurpriseEggListDelStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DEL_STATUS_ING);
    }

    /**
     * 删除终了处理
     * @param endStatus 终了状态
     */
    private void deleteEndHandler(int endStatus){
        MLog.d(TAG, "deleteEndHandler, endStatus = "+endStatus);
        mSurpriseEggListDelStatus.postValue(endStatus);
        mDeleteListID.clear();
        mSurpriseEggListDelCount.postValue(0);
        if (0 == mSurpriseEggList.getSurpriseEggListSize()) {
            mSurpriseEggListDspStatus.postValue(SurpriseEggFuncDef.SURPRISE_EGG_DSP_STATUS_NO_FILES);
        }
        changeSurpriseEggListChanged();
    }

    /**
     * 初始化app common service manager(为了取得daID)
     */
    public void initAppCommon() {
        MLog.d(TAG,"initAppCommon");
        if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, "android.permission.VEHICLE_DATA")) {
            MLog.d(TAG, "PERMISSION_GRANTED");
            BaseManager.create(mContext, Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE, new APICreateCallback() {
                @Override
                public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                    if (Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE.equals(s)) {
                        synchronized (mApiLock) {
                            if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IAppCommon) {
                                mAppCommonServiceManager = (IAppCommon) iBaseAPI;
                                handlerThreadProcessor.postToMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        synchronized (mApiLock) {
                                            if (null != mAppCommonServiceManager) {
                                                try {
                                                    MLog.d(TAG, "run:request data");
                                                    // 取得daID，并初始化联友SDK，根据网络链接状态请求彩蛋list数据
                                                    mDaID = mAppCommonServiceManager.getDAId();
                                                    MLog.d(TAG, "mAppCommonServiceManager.getDAId():" + mDaID);
                                                    //initLanYouRequest(mDaID);
                                                } catch (NullServiceException e) {
                                                    mAppCommonServiceManager = null;
                                                    MLog.e(TAG, "mAppCommonServiceManager.getDAId() catch NullServiceException");
                                                } catch (RemoteException e) {
                                                    mAppCommonServiceManager = null;
                                                    MLog.e(TAG, "mAppCommonServiceManager.getDAId() catch RemoteException");
                                                }
                                            }
                                        }
                                    }
                                });
                                handlerThreadProcessor.postDelayedToMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        synchronized (mApiLock) {
                                            if (null != mAppCommonServiceManager) {
                                               requestSurpriseEggListData();
                                            }
                                        }
                                    }
                                },3000);

                            } else {
                                mAppCommonServiceManager = null;
                                MLog.d(TAG, "mAppCommonServiceManager is created failed");
                            }
                        }
                    }
                }

                @Override
                public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

                }
            });
        }
    }

    /**
     * 初始化联友SDK
     *
     * @param daID daID
     */
    public boolean initLanYouRequest(String daID) {
        MLog.d(TAG,"initLanYouRequest" + daID);
        if(daID.isEmpty()) {
            return false;
        }
        AppInfo appInfo = new AppInfo(SurpriseEggFuncDef.APP_INFO_APP_ID,
                                    SurpriseEggFuncDef.APP_INFO_APP_KEY,
                                    SurpriseEggFuncDef.APP_INFO_APP_CODE,
                                    SurpriseEggFuncDef.APP_INFO_PROJECT_TYPE,
                                    SurpriseEggFuncDef.APP_INFO_APP_VERSION_NAME,
                                    daID,
                                    SurpriseEggFuncDef.APP_INFO_SERVER_PUBLIC_KEY,
                                    SurpriseEggFuncDef.APP_INFO_CUSTOM_PRIVATE_KEY);
        LanYouRequest.init((Application) mContext, appInfo, RequestEnvironment.TEST);
        MLog.d(TAG,"initLanYouRequest end");
        return true;
    }

    /**
     * 初始化联友SDK
     *
     */
    public void deInitLanYouRequest() {
        MLog.d(TAG,"deInitLanYouRequest");
        LanYouRequest.deInit();
    }

    /**
     * 请求彩蛋list
     * demo
     */
    public boolean requestSurpriseEggToken(String appID, @Nullable SurpriseEggTokenListener listener) {

        if (TextUtils.isEmpty(appID) || TextUtils.isEmpty(mDaID)) {
            return false;
        }

        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("api", "ly.ccsv.da.cptoken.query");
        map.put("appId", appID);
        map.put("daId", mDaID);
        LanYouRequest.request(map, new RequestCallback() {
            @Override
            public void onStart() {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onSuccess(String json) {
                String token = SurpriseEggJsonDataManager.getSurpriseEggTokenResult(json);
                MLog.d(TAG,"requestSurpriseEggToken onSuccess token:"+token);
                listener.onGetTokenResult(token);
            }

            @Override
            public void onError(Throwable throwable) {
                MLog.d(TAG,"requestSurpriseEggToken onErrorCallBack");
                MLog.d(TAG,"requestSurpriseEggToken throwable:"+throwable.getLocalizedMessage());
                listener.onGetTokenResult("");
            }

            @Override
            public void onFail(String json) {
                MLog.d(TAG,"requestSurpriseEggToken onFailCallBack" +json);
                listener.onGetTokenResult("");
            }
        },true);

        return true;
    }

    /*
     * 中断通知接口类
     * <AUTHOR>
     * @date 2022/5/15
     */
    public interface SurpriseEggTokenListener {
        public void onGetTokenResult(String token);
    }

    private ICommon mICommon;

    public boolean isUserCenterLogin(){
        if(null != mICommon){
            try{
                String status = mICommon.getLoginStatus();
                if("unKnow".equals(status)){
                    return false;
                }else{
                    return true;
                }
            }catch (RemoteException|NullServiceException e){
                LogUtil.e(TAG, "while check user enter login throw exception");
            }
        }
        MLog.d(TAG, "isUserCenterLogin : false");
        return true;
    }

    /**
     * 初始化登录接口相关的CustomAPI
     */
    private void initICommonApi(){
        Context context = LibraryApp.getApp();
        BaseManager.create(context, Constants.DFL_APP_USERCENTER_COMMON_COMMON_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiState, String requestName, IBaseAPI iBaseAPI) {
                LogUtil.d(TAG, "init ICommon Api onAPICreateCallback,apiState : " + apiState + ",requestName = " + requestName);
                if(apiState == APIStateType.APIState.SUCCESS){
                    mICommon = (ICommon) iBaseAPI;
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {

            }
        });
    }
}