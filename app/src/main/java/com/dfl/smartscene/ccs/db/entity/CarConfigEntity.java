package com.dfl.smartscene.ccs.db.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "car_config")
public class CarConfigEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "car_type", typeAffinity = ColumnInfo.TEXT)
    public String car_type;

    @ColumnInfo(name = "device_id", typeAffinity = ColumnInfo.TEXT)
    public String device_id;

    @ColumnInfo(name = "operation_id", typeAffinity = ColumnInfo.TEXT)
    public String operation_id;



    @Ignore
    public CarConfigEntity() {

    }

    public CarConfigEntity(int pId, String car_type, String device_id, String operation_id) {
        this.pId = pId;
        this.car_type = car_type;
        this.device_id = device_id;
        this.operation_id = operation_id;
    }

    @NonNull
    @Override
    public String toString() {
        return "CarConfigEntity{" +
                "pId=" + pId +
                ", car_type='" + car_type + '\'' +
                ", device_id='" + device_id + '\'' +
                ", operation_id='" + operation_id + '\'' +
                '}';
    }
}
