package com.dfl.smartscene.ccs.model;

import android.content.Context;
import android.media.AudioManager;
import android.media.AudioSystem;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;

import com.dfl.api.app.usercenter.common.ICommon;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.setting.ISetting;
import com.dfl.api.da.systemview.ISystemView;
import com.dfl.api.da.systemview.ISystemViewCallback;
import com.dfl.api.da.systemview.SystemViewIdDef;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.OpeUtil;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/9/27 9:31
 * @description ：
 */
public class DAModel implements DeviceBaseModel {

    private static final String TAG = "DAModel";
    private static volatile DAModel sInstance;
    private AudioManager mAudioManager;

    public static DAModel getInstance() {
        if (null == sInstance) {
            synchronized (DAModel.class) {
                if (null == sInstance) {
                    sInstance = new DAModel();
                }
            }
        }
        return sInstance;
    }

    private DAModel() {
        mAudioManager = (AudioManager) ScenePatternApp.getInstance().getSystemService(Context.AUDIO_SERVICE);
    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        switch (settingOperation.getOperationId()) {
            case ConstantModelValue.OID_ACT_APP_NAVI_VOLUME:
                setNaviVolume(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_APP_MEDIA_VOLUME:
                setMediaVolume(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_APP_VR_VOLUME:
                setTtsVolume(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_MORE_SCREEN_LIGHT:
                setScreenLight(settingOperation);
                return true;
            case ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO:
                //setScreenVideo(settingOperation);
                return false;
            default:
                return false;
        }

    }

    private Handler mHandler = new Handler(Looper.getMainLooper());

    /**
     * 注册窗口竞合逻辑监听（请求显示舒压模式全屏时调用）
     * */
    public void registerSystemViewListener() {
        List<Integer> list = new ArrayList<>();
        list.add(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0);
        list.add(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);

        try {
            ISystemView systemView = CustomApiManager.getInstance().getISystemView();
            if(null == systemView){
                return;
            }
            MLog.d(TAG, "register system view listener");
            systemView.registerSysViewCallback(mSystemViewCallback, list);
        } catch (NullServiceException e) {
            LogUtil.e(TAG, "throw null service exception while register system view listener");
        }
    }

    /**
     * 反注册窗口竞合逻辑监听（关闭舒压模式全屏时调用）
     * */
    private void unregisterSystemViewListener(){
        try {
            ISystemView systemView = CustomApiManager.getInstance().getISystemView();
            if(null == systemView){
                return;
            }
            MLog.d(TAG, "unregister system view listener");
            systemView.unregisterSysViewCallback(mSystemViewCallback);
        } catch (NullServiceException e) {
            LogUtil.e(TAG, "throw null service exception while unregister system view listener");
        }
    }

    /**
     *窗口竞合逻辑的结果监听
     * */
    ISystemViewCallback mSystemViewCallback = new ISystemViewCallback() {
        @Override
        public void onCompeteFinished(int i) {
            LogUtil.d(TAG,"onCompeteFinished : " + i);
            if (i == SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1) {
                Message message = Message.obtain(mHandler, () -> openFullScreenVideo());
                message.sendToTarget();
            }
        }

        @Override
        public void onSysViewChanged(List<Integer> list) {

        }
    };

    /**
     * 调节导航音量
     * @param settingOperation
     */
    public void setNaviVolume(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }
        int pos = DataTypeFormatUtil.string2Int(settingOperation.getListArgs().get(0));
        mAudioManager.setStreamVolume(AudioSystem.STREAM_TTS, pos, 0);
    }

    /**
     * 调节tts播报音量
     * @param settingOperation
     */
    public void setTtsVolume(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }

        int pos = DataTypeFormatUtil.string2Int(settingOperation.getListArgs().get(0));
        mAudioManager.setStreamVolume(AudioManager.STREAM_ACCESSIBILITY, pos, 0);

    }

    /**
     * 获取多媒体音量
     * @return
     */
    public int getMediaVolume() {
        return mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
    }

    /**
     * 设置多媒体音量
     * @param pos
     */
    public void setMediaVolume(int pos) {
        LogUtil.d(TAG,"setMediaVolumeThread = "+pos);
        if(mAudioManager != null){
            mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, pos, 0);
        }
    }

    public void setMediaVolume(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }

        int pos = DataTypeFormatUtil.string2Int(settingOperation.getListArgs().get(0));
        setMediaVolume(pos);
    }

    /**
     * 设置屏幕亮度
     * @param settingOperation
     */
    public void setScreenLight(SettingOperation settingOperation) {
        if(!OpeUtil.checkOpeArgLegal(settingOperation)){
            return;
        }

        ISetting iSetting = CustomApiManager.getInstance().getISetting();
        if (iSetting == null) {
            return;
        }
    }

    private VideoController mVideoController;

    public VideoController getVideoController() {
        return mVideoController;
    }

    public void initVideoController(SettingOperation settingOperation){
        if(null == mVideoController){
            mVideoController = new VideoController(settingOperation);
        }
    }
    /**
     * 新建并请求打开全屏视图
     * @param settingOperation
     */
    public void setScreenVideo(SettingOperation settingOperation) {
        MLog.d(TAG, "setScreenVideo");
        if(null == mVideoController){
            mVideoController = new VideoController(settingOperation);
        }
        mVideoController.requestOpenSystemWindow();
        if(BuildConfig.DEBUG){
            SceneControlManager.getInstance().executeOperation(new SettingOperation(ConstantModelValue.DEVICE_ID_MORE,ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_MUSIC, Arrays.asList("scene@17")));
        }
    }

    public void clearVideoController(){
        mVideoController = null;
    }

    /**
     * 请求成功后，添加全屏视图至系统视图
     */
    private void openFullScreenVideo() {
        MLog.d(TAG, "openFullScreenVideo");

        if (mVideoController != null) {
            mVideoController.requestAttachViewToSysWindow();
        }
    }

    /**
     * 请求舒压模式全屏弹窗的窗口竞合
     * */
    public void requestFullScreenVideo(){
        MLog.d(TAG, "requestFullScreenVideo");
        registerSystemViewListener();
        if (mVideoController != null) {
            mVideoController.reqOpenRelieveStressPatternFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_RELIEVE);
        }
    }

    /**
     * 舒压模式全屏弹窗退出全屏模式
     * */
    public void exitFullScreenVideo(){
        MLog.d(TAG, "exitFullScreenVideo");
        unregisterSystemViewListener();
        if (mVideoController != null) {
            mVideoController.requestDetachViewFromSysWindow();
        }
    }

    public boolean isUserCenterLogin(){
        ICommon iCommon = CustomApiManager.getInstance().getICommon();
        if(null != iCommon){
            try{
                String status = iCommon.getLoginStatus();
                LogUtil.e(TAG, "login status = "+status);
                if("unKnow".equals(status)){
                    return false;
                }else{
                    return true;
                }
            }catch (RemoteException|NullServiceException e){
                LogUtil.e(TAG, "while check user enter login throw exception");
            }
        }else{
            LogUtil.e(TAG, "ICommon is null");
        }
        MLog.d(TAG, "isUserCenterLogin : false");
        return false;
    }

}
