package com.dfl.smartscene.ccs.model;


/**
 * <AUTHOR>
 * @date ：2022/12/13 19:09
 * @description ：
 */
public class CCSCustomApiManager extends LibraryCustomManager {
    private static final String TAG = "CCSCustomApiManager";
    private static volatile CCSCustomApiManager sInstance;

    public static CCSCustomApiManager getInstance() {
        if (null == sInstance) {
            synchronized (CCSCustomApiManager.class) {
                if (null == sInstance) {
                    sInstance = new CCSCustomApiManager();
                }
            }
        }
        return sInstance;
    }

}
