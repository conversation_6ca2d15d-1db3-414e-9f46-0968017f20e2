package com.dfl.smartscene.ccs.db.entity.scene;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "scene_state_list")
public class SceneStateListEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    @ColumnInfo(name = "mSceneId", typeAffinity = ColumnInfo.TEXT)
    public String mSceneId;

    @ColumnInfo(name = "priority", typeAffinity = ColumnInfo.TEXT)
    public String priority;

    @ColumnInfo(name = "mDeviceId", typeAffinity = ColumnInfo.TEXT)
    public String mDeviceId;

    @ColumnInfo(name = "mOperationId", typeAffinity = ColumnInfo.TEXT)
    public String mOperationId;

    @ColumnInfo(name = "mListArgs", typeAffinity = ColumnInfo.TEXT)
    public String mListArgs;

    @ColumnInfo(name = "mDesc", typeAffinity = ColumnInfo.TEXT)
    public String mDesc;

    @ColumnInfo(name = "mShowType", typeAffinity = ColumnInfo.TEXT)
    public String mShowType;

    @Ignore
    public SceneStateListEntity() {

    }

    public SceneStateListEntity(int pId, String mSceneId, String priority, String mDeviceId, String mOperationId, String mListArgs, String mDesc, String mShowType) {
        this.pId = pId;
        this.mSceneId = mSceneId;
        this.priority = priority;
        this.mDeviceId = mDeviceId;
        this.mOperationId = mOperationId;
        this.mListArgs = mListArgs;
        this.mDesc = mDesc;
        this.mShowType = mShowType;
    }

    @Override
    public String toString() {
        return "SceneStateListEntity{" +
                "pId=" + pId +
                ", mSceneId='" + mSceneId + '\'' +
                ", priority='" + priority + '\'' +
                ", mDeviceId='" + mDeviceId + '\'' +
                ", mOperationId='" + mOperationId + '\'' +
                ", mListArgs='" + mListArgs + '\'' +
                ", mDesc='" + mDesc + '\'' +
                ", mShowType='" + mShowType + '\'' +
                '}';
    }
}
