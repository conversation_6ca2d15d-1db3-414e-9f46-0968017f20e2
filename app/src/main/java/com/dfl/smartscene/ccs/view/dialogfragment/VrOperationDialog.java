package com.dfl.smartscene.ccs.view.dialogfragment;


import static com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment.EDIT_TYPE_ADD;
import static com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment.EDIT_TYPE_MODIFY;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseDialogFragment;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.fragment.SceneEditorFragment;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.util.ExpandTouchArea;
import com.iauto.uibase.utils.MLog;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/12/6 18:56
 * @description ：设置触发语音功能的弹窗
 */
public class VrOperationDialog extends BaseDialogFragment {
    private static final String TAG = "VrOperationDialog";
    /**
     * 由EditFragment定义，详见该类
     */
    private int operationType = SceneEditorFragment.OPERATION_TYPE_VR;
    /**
     * 0：修改操作
     * 1：新增操作
     */
    private int editType = 0;
    /**
     * 修改位置
     */
    private int pos = 0;

    private ImageView mImageViewCancel;
    private EditText mEditTextContent;
    private  TextView mTextViewConfirm;
    private ImageView mImageViewClear;

    private SettingOperation mSettingOperation;
    private OperationBaseDialogFragment.OperationChangeListerner mOperationChangeListerner;

    public void setOperationChangeListerner(OperationBaseDialogFragment.OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
    }

    @Override
    protected void setCommonStyle() {
        setStyle(STYLE_NO_FRAME, R.style.Theme_Acvtivity);
    }

    public VrOperationDialog() {
        this.editType = EDIT_TYPE_ADD;
    }

    public VrOperationDialog(int pos, SettingOperation settingOperation) {
        this.editType = EDIT_TYPE_MODIFY;
        this.pos = pos;
        mSettingOperation = settingOperation;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_fragment_vr_operation,container,false);
    }

    @Override
    protected void init() {

    }

    @Override
    protected void initView(View view) {
        mEditTextContent = view.findViewById(R.id.edittext_scene_editor_vr_content);
        mImageViewCancel = view.findViewById(R.id.imageView_vr_operation_dialog_cancel);
        mTextViewConfirm = view.findViewById(R.id.textview_vr_operation_dialog_confirm);
        mImageViewClear = view.findViewById(R.id.imageview_vr_operation_clear);
        mImageViewClear.setOnClickListener(v -> mEditTextContent.setText(""));
        mImageViewCancel.setOnClickListener(v -> dismiss());
        ExpandTouchArea.expandTouchArea(mImageViewCancel, 16);
        mTextViewConfirm.setOnClickListener(v -> {
            if(editType == EDIT_TYPE_ADD){
                mOperationChangeListerner.onOperationAdd(SceneEditorFragment.OPERATION_TYPE_VR,creatOperation());
            }else {
                mOperationChangeListerner.onOperationModify(SceneEditorFragment.OPERATION_TYPE_VR,pos,creatOperation());
            }
            dismiss();
        });

        setEditTextFilter(mEditTextContent,getInputEnChatFilter(),getInputSpaceFilter(),getInputSpeChatFilter());
        mEditTextContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                MLog.d(TAG,"beforeTextChanged : " + s);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                MLog.d(TAG,"onTextChanged : " + s);

            }

            @Override
            public void afterTextChanged(Editable s) {
                MLog.d(TAG,"afterTextChanged : " + s.toString());
                if("".equals(s.toString())){
                    mImageViewClear.setVisibility(View.GONE);
                    mTextViewConfirm.setEnabled(false);
                }else {
                    mImageViewClear.setVisibility(View.VISIBLE);
                    mTextViewConfirm.setEnabled(true);

                }


            }
        });
    }



    public SettingOperation creatOperation(){
        SettingOperation settingOperation = new SettingOperation();
        settingOperation.setOperationId(ConstantModelValue.OPERATION_ID_CONDITION_VR);
        settingOperation.setDesc(mEditTextContent.getText().toString());
        settingOperation.setListArgs(Arrays.asList(mEditTextContent.getText().toString()));
        return settingOperation;
    }

    @Override
    protected void initObserver() {

    }

    @Override
    protected void initData() {
        if(mSettingOperation != null){
            mEditTextContent.setText(mSettingOperation.getListArgs().get(0));
        }else {
            mEditTextContent.setText("");
        }
        mEditTextContent.requestFocus();
        mEditTextContent.postDelayed(this::showInputMethod,300);

    }

    /**
     * 显示软键盘（输入法）
     *
     */
    public void showInputMethod() {
        InputMethodManager inputMethodManager = (InputMethodManager) ScenePatternApp.getInstance().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.showSoftInput(mEditTextContent, 0,null);
    }

    public InputFilter getInputSpaceFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (source.equals(" ") || source.toString().contentEquals("\n")) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }

    public InputFilter getInputSpeChatFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                String speChat = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
                Pattern pattern = Pattern.compile(speChat);
                Matcher matcher = pattern.matcher(source.toString());
                if (matcher.find()) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }

    public InputFilter getInputEnChatFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                String speChat = "[abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ]";
                Pattern pattern = Pattern.compile(speChat);
                Matcher matcher = pattern.matcher(source.toString());
                if (matcher.find()) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }


    public void setEditTextFilter(EditText editText , InputFilter... inputFilters){
        editText.setFilters(inputFilters);
    }
}
