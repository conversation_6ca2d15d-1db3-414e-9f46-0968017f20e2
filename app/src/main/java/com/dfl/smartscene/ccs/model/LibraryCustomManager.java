package com.dfl.smartscene.ccs.model;

import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.api.da.setting.ISettingSoundEffect;
import com.dfl.api.vehicle.hvac.IHvac;
import com.dfl.api.vehicle.seat.ISeat;
import com.dfl.smartscene.ccs.model.manager.CommonCustomManager;

/**
 * <AUTHOR>
 * @date ：2022/12/13 15:54
 * @description ：
 */
public class LibraryCustomManager extends CommonCustomManager {
    protected CommonCustomManager mainCustomApi;

    public void registerCommonManager(CommonCustomManager mainManager){
        mainCustomApi = mainManager;
    }

    @Override
    public IHvac getIHvac() {
        return mainCustomApi.getIHvac();
    }

    @Override
    public ISeat getISeat() {
        return mainCustomApi.getISeat();
    }

    @Override
    public IAppCommon getIAppCommon() {
        return mainCustomApi.getIAppCommon();
    }

    @Override
    public ISettingSoundEffect getISettingSoundEffect() {
        return mainCustomApi.getISettingSoundEffect();
    }


}
