package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.NumberPicker;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;


import com.dfl.smartscene.R;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：数值大于/小于选取的自定义控件
 */
public class NumberCompareView extends ConstraintLayout {

    private NumberPicker mNumberPickerCondition;
    private NumberPicker mNumberPickerValue;
    private TextView mTextViewUnit;

    public NumberCompareView(Context context) {
        this(context, null);
    }

    public NumberCompareView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);

        initView(context);
    }

    private void initView(Context context) {
        View inflate = LayoutInflater.from(context).inflate(R.layout.layout_custom_number_compare, this);
        mNumberPickerCondition = inflate.findViewById(R.id.numberpicker_layout_custom_number_compare_condition);
        mNumberPickerValue = inflate.findViewById(R.id.numberpicker_layout_custom_number_compare_value);
        mTextViewUnit = inflate.findViewById(R.id.textview_layout_custom_number_compare_unit);
    }

    public void setValue(String[] displays,int defaultPos) {
        mNumberPickerCondition.setMinValue(0);
        mNumberPickerCondition.setMaxValue(displays.length - 1);
        mNumberPickerCondition.setDisplayedValues(displays);
        mNumberPickerCondition.setValue(defaultPos);
    }

    public void setValue2(String valueType, Object min, Object max, Object step, int defaultValue, String unitStr) {
        ArrayList<String> displayValues = new ArrayList<>();
        if ("int".equals(valueType)) {
            for (int i = (int) min; i <= (int) max; i = i + (int) step) {
                displayValues.add(i + "");
            }
        } else {
            for (float i = (float) min; i <= (float) max; i = i + (float) step) {
                displayValues.add(i + "");
            }
        }

        mNumberPickerValue.setMinValue(0);
        mNumberPickerValue.setMaxValue(displayValues.size() - 1);
        mNumberPickerValue.setDisplayedValues(displayValues.toArray(new String[0]));
        mNumberPickerValue.setValue(defaultValue);
        mTextViewUnit.setText(unitStr);
    }

    public void setValue2WithDefaultValue(String valueType, Object min, Object max, Object step, String defaultValue, String unitStr) {
        ArrayList<String> displayValues = new ArrayList<>();
        if ("int".equals(valueType)) {
            for (int i = (int) min; i <= (int) max; i = i + (int) step) {
                displayValues.add(i + "");
            }
        } else {
            for (float i = (float) min; i <= (float) max; i = i + (float) step) {
                displayValues.add(i + "");
            }
        }

        mNumberPickerValue.setMinValue(0);
        mNumberPickerValue.setMaxValue(displayValues.size() - 1);
        mNumberPickerValue.setDisplayedValues(displayValues.toArray(new String[0]));
        mNumberPickerValue.setValue(displayValues.indexOf(defaultValue));
        mTextViewUnit.setText(unitStr);
    }

    public String getValue1() {
        int value = mNumberPickerCondition.getValue();
        String[] displayedValues = mNumberPickerCondition.getDisplayedValues();
        if (null != displayedValues) {
            if (value >= 0 && value < displayedValues.length) {
                return displayedValues[value];
            }
        }
        return null;
    }

    public String getValue2() {
        int value = mNumberPickerValue.getValue();
        String[] displayedValues = mNumberPickerValue.getDisplayedValues();
        if (null != displayedValues) {
            if (value >= 0 && value < displayedValues.length) {
                return displayedValues[value];
            }
        }
        return null;
    }

}
