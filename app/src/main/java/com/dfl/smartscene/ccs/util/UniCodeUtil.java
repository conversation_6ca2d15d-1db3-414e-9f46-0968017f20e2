package com.dfl.smartscene.ccs.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：2023/5/5 11:12
 * @description ：
 */
public class UniCodeUtil {
    private static final Pattern PATTERN_UNICODE = Pattern.compile("\\\\u[a-f0-9A-F]{1,4}");
    /**
     * unicode串转字符串
     *
     * @param unicode unicode串
     * @return 字符串
     */
    public static String unicodeToChar(String unicode) {
        if (unicode == null || unicode.isEmpty()) {
            return unicode;
        }
        StringBuffer str = new StringBuffer();
        String[] hex = unicode.split("\\\\u");
        for (int index = 1; index < hex.length; index++) {
            int data = Integer.parseInt(hex[index], 16);
            str.append((char) data);
        }
        return str.toString();
    }

    /**
     * 字符串转unicode串
     *
     * @param str 字符串
     * @return unicode串
     */
    public static String charToUnicode(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuffer unicode = new StringBuffer();
        for (int index = 0; index < str.length(); index++) {
            char c = str.charAt(index);
            unicode.append("\\u").append(Integer.toHexString(c));
        }
        return unicode.toString();
    }


    /**
     * 混合串转普通字符串
     * 混合串指的是包含unicode和普通字符的字符串
     *
     * @param mixStr 混合串
     * @return 普通字符串
     */
    public static String mixStrToString(String mixStr) {
        if (mixStr == null || mixStr.isEmpty()) {
            return mixStr;
        }
        int start = 0;
        StringBuffer result = new StringBuffer();
        Matcher matcher = PATTERN_UNICODE.matcher(mixStr);
        while (matcher.find()) {
            String oldChar = matcher.group();
            result.append(mixStr.substring(start, matcher.start()));
            result.append(unicodeToChar(oldChar));
            start = matcher.start() + oldChar.length();
        }
        result.append(mixStr.substring(start));
        return result.toString();
    }

    /**
     * 混合串转unicode串
     * 混合串指的是包含unicode和普通字符的字符串
     *
     * @param mixStr 混合串
     * @return unicode串
     */
    public static String mixStrToUnicode(String mixStr) {
        if (mixStr == null || mixStr.isEmpty()) {
            return mixStr;
        }
        int start = 0;
        StringBuffer result = new StringBuffer();
        Matcher matcher = PATTERN_UNICODE.matcher(mixStr);
        while (matcher.find()) {
            String oldChar = matcher.group();
            result.append(charToUnicode(mixStr.substring(start, matcher.start())));
            result.append(oldChar);
            start = matcher.start() + oldChar.length();
        }
        result.append(charToUnicode(mixStr.substring(start)));
        return result.toString();
    }
}
