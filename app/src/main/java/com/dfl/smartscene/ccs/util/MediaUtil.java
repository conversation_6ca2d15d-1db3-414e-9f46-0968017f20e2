package com.dfl.smartscene.ccs.util;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.provider.MediaStore;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

/**
 * description:
 * author:huangzezheng
 * Data:2023/3/29
 **/
public class MediaUtil {
    private static final String TAG = "MediaUtil";

    public static Uri getMusicUri(String name){
        Uri uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        ContentResolver contentResolver = ScenePatternApp.getInstance().getContentResolver();
        Cursor query = contentResolver.query(uri, new String[]{MediaStore.Audio.Media._ID}, MediaStore.Audio.Media.DISPLAY_NAME + "='"+ name + "'", null, null);
        long id = query.getLong(query.getColumnIndexOrThrow(MediaStore.Audio.Media._ID));
        query.close();
        return ContentUris.withAppendedId(uri, id);
    }

    public static Uri getVideoUri(String name , int width , int height){
        LogUtil.d(TAG , "getVideoUri : " + name + width + height);
        Uri uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
        Uri result = null;
        ContentResolver contentResolver = ScenePatternApp.getInstance().getContentResolver();
        String command = MediaStore.Video.Media.DISPLAY_NAME + "='"+ name + "'" + " AND " + MediaStore.Video.Media.HEIGHT + "=" + height + " AND " + MediaStore.Video.Media.WIDTH + "=" + width;
//        String command = MediaStore.Video.Media.DISPLAY_NAME + "='"+ name + "'" + " AND " + MediaStore.Video.Media.RELATIVE_PATH + " like " + "'%" +height + "%'";

        Cursor query = contentResolver.query(uri, new String[]{MediaStore.Video.Media._ID}, command, null, null);

        if(query != null && query.moveToFirst()){
            long id = query.getLong(query.getColumnIndexOrThrow(MediaStore.Video.Media._ID));
            result = ContentUris.withAppendedId(uri, id);
        }
        if(query != null){
            query.close();
        }
        return result;
    }

    public static Bitmap getVideoFirstFrame(Uri uri){
        LogUtil.d(TAG , "getVideoFirstFrame : " + uri.toString());

        if(uri == null){
            return null;
        }
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        retriever.setDataSource(ScenePatternApp.getInstance(),uri);
        Bitmap frameAtIndex = retriever.getFrameAtIndex(0);
        Matrix matrix = new Matrix();
        matrix.setScale(0.5f, 0.5f);
        return Bitmap.createBitmap(frameAtIndex, 0, 0, frameAtIndex.getWidth(), frameAtIndex.getHeight(), matrix, true);
    }
}
