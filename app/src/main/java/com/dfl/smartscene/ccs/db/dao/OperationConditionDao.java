package com.dfl.smartscene.ccs.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.dfl.smartscene.ccs.db.entity.OperationConditionEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface OperationConditionDao {

    @Insert
    void insertStudent(OperationConditionEntity item);

    @Delete
    void deleteStudent(OperationConditionEntity item);

    @Update
    void updateStudent(OperationConditionEntity item);

    @Query("SELECT * FROM operation_condition")
    Observable<List<OperationConditionEntity>> queryAll();

    @Query("SELECT * FROM operation_condition WHERE :id='' or id= :id")
    Observable<List<OperationConditionEntity>> queryById(String id);


    @Query("SELECT * FROM operation_condition WHERE id in (:ids)")
    Observable<List<OperationConditionEntity>> queryByIds(String[] ids);

}
