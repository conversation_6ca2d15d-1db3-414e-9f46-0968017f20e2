package com.dfl.smartscene.ccs.view.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.util.DescriptionUtil;

/**
 * 首页卡片状列表的adapter
 * <AUTHOR>
 * @date ：2022/10/10 13:47
 * @description ：
 */
public class LibraryCardAdapter extends BaseAdapter<SceneBean> {

    @Override
    protected BaseHolder<SceneBean> getViewHolder(View view, int viewType) {
        return new LibraryCardHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_library_card;
    }

    class LibraryCardHolder extends BaseHolder<SceneBean> {
        private TextView mTextViewTitle;//卡片标题
        private TextView mTextViewDesc;//卡片描述
        private ImageView bgView;
        public LibraryCardHolder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_library_card_title);
            mTextViewDesc = itemView.findViewById(R.id.textview_library_card_desc);
            bgView = itemView.findViewById(R.id.item_libray_card_bg);
        }

        @Override
        public void setupData(SceneBean sceneBean, int position) {
            mTextViewTitle.setText(sceneBean.getSceneName());
            mTextViewDesc.setText(sceneBean.getSceneDesc());
            boolean isUrlValid = !TextUtils.isEmpty(sceneBean.getScenePic());
            if ("舒压模式".equals(sceneBean.getSceneName())){
                Glide.with(itemView.getContext()).load(isUrlValid?sceneBean.getScenePic():R.drawable.icon_scene_relax)
                        .apply(RequestOptions.bitmapTransform(
                                new MultiTransformation<>(
                                        new RoundedCorners(16)
                                )
                        ).error(R.drawable.icon_scene_relax))
                        .into(bgView);
            }else if ("惊喜彩蛋".equals(sceneBean.getSceneName())){
                Glide.with(itemView.getContext()).load(isUrlValid?sceneBean.getScenePic():R.drawable.icon_scene_egg)
                        .apply(RequestOptions.bitmapTransform(
                                new MultiTransformation<>(
                                        new RoundedCorners(16)
                                )
                        ).error(R.drawable.icon_scene_egg))
                        .into(bgView);
            }else {
                Glide.with(itemView.getContext()).load(sceneBean.getScenePic())
                        .apply(RequestOptions.bitmapTransform(
                                new MultiTransformation<>(
                                        new RoundedCorners(16)
                                )
                        ).error(R.drawable.drawable_card_default_bg))
                        .into(bgView);
            }
//            ImageUtil.loadBackGroundUrl(itemView,sceneBean.getScenePic());
            //设置卡片的可见即可说
            DescriptionUtil.generateButtonDescriptionVisibleToSay(sceneBean.getSceneName(), itemView);
        }
    }
}
