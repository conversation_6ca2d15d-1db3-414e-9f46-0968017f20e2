package com.dfl.smartscene.ccs.db.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

@Entity(tableName = "operation_state")
public class OperationStateEntity {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "pId", typeAffinity = ColumnInfo.INTEGER)
    public int pId;

    public String id;
    @ColumnInfo(name = "relationId", typeAffinity = ColumnInfo.TEXT)
    public String relationId;
    @ColumnInfo(name = "mOperationId", typeAffinity = ColumnInfo.TEXT)
    public String mOperationId;
    @ColumnInfo(name = "mOperationName", typeAffinity = ColumnInfo.TEXT)
    public String mOperationName;

    @ColumnInfo(name = "mOperationIcon", typeAffinity = ColumnInfo.TEXT)
    public String mOperationIcon;

    @ColumnInfo(name = "mShowType", typeAffinity = ColumnInfo.TEXT)
    public String mShowType;

    @ColumnInfo(name = "mViewType", typeAffinity = ColumnInfo.TEXT)
    public String mViewType;

    @ColumnInfo(name = "VIEW_DESC_TEXT", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_TEXT;
    @ColumnInfo(name = "VIEW_DESC_TEXT_END", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_TEXT_END;
    @ColumnInfo(name = "VIEW_DESC_TEXT_BUTTOM", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_TEXT_BUTTOM;
    @ColumnInfo(name = "VIEW_DESC_PIC_START", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_PIC_START;
    @ColumnInfo(name = "VIEW_DESC_PIC_END", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_PIC_END;
    @ColumnInfo(name = "OUTPUT_DESC_TEXT", typeAffinity = ColumnInfo.TEXT)
    public String OUTPUT_DESC_TEXT;
    @ColumnInfo(name = "VIEW_DESC_PIC", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_PIC;
    @ColumnInfo(name = "VIEW_DEFAULT_POS", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DEFAULT_POS;
    @ColumnInfo(name = "GEAR_P_LIMIT", typeAffinity = ColumnInfo.TEXT)
    public String GEAR_P_LIMIT;
    @ColumnInfo(name = "VIEW_DESC_RANGE_HIGH", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_RANGE_HIGH;
    @ColumnInfo(name = "VIEW_DESC_RANGE_LOW", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_RANGE_LOW;
    @ColumnInfo(name = "VIEW_DESC_RANGE_STEP", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_RANGE_STEP;
    @ColumnInfo(name = "VIEW_DESC_RANGE_TYPE", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_RANGE_TYPE;
    @ColumnInfo(name = "RELATED_DATA_CULCULATE", typeAffinity = ColumnInfo.TEXT)
    public String RELATED_DATA_CULCULATE;

    @ColumnInfo(name = "DATA_DESC_LIST", typeAffinity = ColumnInfo.TEXT)
    public String DATA_DESC_LIST;
    @ColumnInfo(name = "ACTION_DATA_POS_LIST", typeAffinity = ColumnInfo.TEXT)
    public String ACTION_DATA_POS_LIST;
    @ColumnInfo(name = "ACTION_METHOD_NAME_LIST", typeAffinity = ColumnInfo.TEXT)
    public String ACTION_METHOD_NAME_LIST;
    @ColumnInfo(name = "API_NAME_LIST", typeAffinity = ColumnInfo.TEXT)
    public String API_NAME_LIST;
    @ColumnInfo(name = "VIEW_DESC_TEXT_LIST", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_TEXT_LIST;
    @ColumnInfo(name = "VIEW_DESC_PIC_LIST", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_DESC_PIC_LIST;
    @ColumnInfo(name = "CHILD_OPERATION_ID_LIST", typeAffinity = ColumnInfo.TEXT)
    public String CHILD_OPERATION_ID_LIST;
    @ColumnInfo(name = "VIEW_CONFLICT_ARG_LIST", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_CONFLICT_ARG_LIST;
    @ColumnInfo(name = "VIEW_CONFLICT_ID_LIST", typeAffinity = ColumnInfo.TEXT)
    public String VIEW_CONFLICT_ID_LIST;

    @ColumnInfo(name = "DATA_TYPE_DYADIC_LIST", typeAffinity = ColumnInfo.TEXT)
    public String DATA_TYPE_DYADIC_LIST;
    @ColumnInfo(name = "DATA_DEFAULT_DYADIC_LIST", typeAffinity = ColumnInfo.TEXT)
    public String DATA_DEFAULT_DYADIC_LIST;

    @Ignore
    public OperationStateEntity() {
    }

    public OperationStateEntity(int pId, String id, String relationId, String mOperationId, String mOperationName, String mOperationIcon, String mShowType, String mViewType, String VIEW_DESC_TEXT, String VIEW_DESC_TEXT_END, String VIEW_DESC_TEXT_BUTTOM, String VIEW_DESC_PIC_START, String VIEW_DESC_PIC_END, String OUTPUT_DESC_TEXT, String VIEW_DESC_PIC, String VIEW_DEFAULT_POS, String GEAR_P_LIMIT, String VIEW_DESC_RANGE_HIGH, String VIEW_DESC_RANGE_LOW, String VIEW_DESC_RANGE_STEP, String VIEW_DESC_RANGE_TYPE, String RELATED_DATA_CULCULATE, String DATA_DESC_LIST, String ACTION_DATA_POS_LIST, String ACTION_METHOD_NAME_LIST, String API_NAME_LIST, String VIEW_DESC_TEXT_LIST, String VIEW_DESC_PIC_LIST, String CHILD_OPERATION_ID_LIST, String VIEW_CONFLICT_ARG_LIST, String VIEW_CONFLICT_ID_LIST, String DATA_TYPE_DYADIC_LIST, String DATA_DEFAULT_DYADIC_LIST) {
        this.pId = pId;
        this.id = id;
        this.relationId = relationId;
        this.mOperationId = mOperationId;
        this.mOperationName = mOperationName;
        this.mOperationIcon = mOperationIcon;
        this.mShowType = mShowType;
        this.mViewType = mViewType;
        this.VIEW_DESC_TEXT = VIEW_DESC_TEXT;
        this.VIEW_DESC_TEXT_END = VIEW_DESC_TEXT_END;
        this.VIEW_DESC_TEXT_BUTTOM = VIEW_DESC_TEXT_BUTTOM;
        this.VIEW_DESC_PIC_START = VIEW_DESC_PIC_START;
        this.VIEW_DESC_PIC_END = VIEW_DESC_PIC_END;
        this.OUTPUT_DESC_TEXT = OUTPUT_DESC_TEXT;
        this.VIEW_DESC_PIC = VIEW_DESC_PIC;
        this.VIEW_DEFAULT_POS = VIEW_DEFAULT_POS;
        this.GEAR_P_LIMIT = GEAR_P_LIMIT;
        this.VIEW_DESC_RANGE_HIGH = VIEW_DESC_RANGE_HIGH;
        this.VIEW_DESC_RANGE_LOW = VIEW_DESC_RANGE_LOW;
        this.VIEW_DESC_RANGE_STEP = VIEW_DESC_RANGE_STEP;
        this.VIEW_DESC_RANGE_TYPE = VIEW_DESC_RANGE_TYPE;
        this.RELATED_DATA_CULCULATE = RELATED_DATA_CULCULATE;
        this.DATA_DESC_LIST = DATA_DESC_LIST;
        this.ACTION_DATA_POS_LIST = ACTION_DATA_POS_LIST;
        this.ACTION_METHOD_NAME_LIST = ACTION_METHOD_NAME_LIST;
        this.API_NAME_LIST = API_NAME_LIST;
        this.VIEW_DESC_TEXT_LIST = VIEW_DESC_TEXT_LIST;
        this.VIEW_DESC_PIC_LIST = VIEW_DESC_PIC_LIST;
        this.CHILD_OPERATION_ID_LIST = CHILD_OPERATION_ID_LIST;
        this.VIEW_CONFLICT_ARG_LIST = VIEW_CONFLICT_ARG_LIST;
        this.VIEW_CONFLICT_ID_LIST = VIEW_CONFLICT_ID_LIST;
        this.DATA_TYPE_DYADIC_LIST = DATA_TYPE_DYADIC_LIST;
        this.DATA_DEFAULT_DYADIC_LIST = DATA_DEFAULT_DYADIC_LIST;
    }

    @NonNull
    @Override
    public String toString() {
        return "OperationStateEntity{" +
                "pId=" + pId +
                ", id='" + id + '\'' +
                ", relationId='" + relationId + '\'' +
                ", mOperationId='" + mOperationId + '\'' +
                ", mOperationName='" + mOperationName + '\'' +
                ", mOperationIcon='" + mOperationIcon + '\'' +
                ", mShowType='" + mShowType + '\'' +
                ", mViewType='" + mViewType + '\'' +
                ", VIEW_DESC_TEXT='" + VIEW_DESC_TEXT + '\'' +
                ", VIEW_DESC_TEXT_END='" + VIEW_DESC_TEXT_END + '\'' +
                ", VIEW_DESC_TEXT_BUTTOM='" + VIEW_DESC_TEXT_BUTTOM + '\'' +
                ", VIEW_DESC_PIC_START='" + VIEW_DESC_PIC_START + '\'' +
                ", VIEW_DESC_PIC_END='" + VIEW_DESC_PIC_END + '\'' +
                ", OUTPUT_DESC_TEXT='" + OUTPUT_DESC_TEXT + '\'' +
                ", VIEW_DESC_PIC='" + VIEW_DESC_PIC + '\'' +
                ", VIEW_DEFAULT_POS='" + VIEW_DEFAULT_POS + '\'' +
                ", GEAR_P_LIMIT='" + GEAR_P_LIMIT + '\'' +
                ", VIEW_DESC_RANGE_HIGH='" + VIEW_DESC_RANGE_HIGH + '\'' +
                ", VIEW_DESC_RANGE_LOW='" + VIEW_DESC_RANGE_LOW + '\'' +
                ", VIEW_DESC_RANGE_STEP='" + VIEW_DESC_RANGE_STEP + '\'' +
                ", VIEW_DESC_RANGE_TYPE='" + VIEW_DESC_RANGE_TYPE + '\'' +
                ", RELATED_DATA_CULCULATE='" + RELATED_DATA_CULCULATE + '\'' +
                ", DATA_DESC_LIST='" + DATA_DESC_LIST + '\'' +
                ", ACTION_DATA_POS_LIST='" + ACTION_DATA_POS_LIST + '\'' +
                ", ACTION_METHOD_NAME_LIST='" + ACTION_METHOD_NAME_LIST + '\'' +
                ", API_NAME_LIST='" + API_NAME_LIST + '\'' +
                ", VIEW_DESC_TEXT_LIST='" + VIEW_DESC_TEXT_LIST + '\'' +
                ", VIEW_DESC_PIC_LIST='" + VIEW_DESC_PIC_LIST + '\'' +
                ", CHILD_OPERATION_ID_LIST='" + CHILD_OPERATION_ID_LIST + '\'' +
                ", VIEW_CONFLICT_ARG_LIST='" + VIEW_CONFLICT_ARG_LIST + '\'' +
                ", VIEW_CONFLICT_ID_LIST='" + VIEW_CONFLICT_ID_LIST + '\'' +
                ", DATA_TYPE_DYADIC_LIST='" + DATA_TYPE_DYADIC_LIST + '\'' +
                ", DATA_DEFAULT_DYADIC_LIST='" + DATA_DEFAULT_DYADIC_LIST + '\'' +
                '}';
    }
}
