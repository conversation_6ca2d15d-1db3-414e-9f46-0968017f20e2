package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;


import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/4/8 10:32
 * @description ：
 */
public abstract class SelectBaseAdapter<T> extends BaseAdapter<T> {

    private int selectPos;

    public void setSelesctedPos(int pos){
//        int temp = selectPos;
        selectPos = pos;
//        notifyItemChanged(temp);
//        notifyItemChanged(selectPos);
        notifyDataSetChanged();
    }

    public int getSelectPos() {
        return selectPos;
    }

    public void setDataList(List<T> dataList, int defaultPos) {
        selectPos = defaultPos;
        notifyItemChanged(defaultPos);
        super.setDataList(dataList);
    }

    public abstract class SelectBaseHolder<T> extends BaseHolder<T> {

        public SelectBaseHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(T t, int position) {
            if(position == selectPos){
                setupSelectedData(t, position);
            }else {
                setupUnSelectedData(t,position);
            }
        }

        protected void setupSelectedData(T t, int position){

        }
        protected void setupUnSelectedData(T t, int position){

        }

    }

}
