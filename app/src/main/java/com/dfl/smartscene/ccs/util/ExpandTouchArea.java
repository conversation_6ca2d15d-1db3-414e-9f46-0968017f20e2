package com.dfl.smartscene.ccs.util;

import android.graphics.Rect;
import android.view.TouchDelegate;
import android.view.View;

/**
 * 扩大点击区域的工具类
 *
 * @author: huangzezheng
 * @date: 2021/11/16
 */
public class ExpandTouchArea {
    /**
     * 扩大点击范围，长宽各增加两个size
     * @param view 需增加点击范围的view
     * @param size
     */
    public static void expandTouchArea(View view, int size) {
        View parentView = (View) view.getParent();
        parentView.post(new Runnable() {
            @Override
            public void run() {
                Rect rect = new Rect();
                view.getHitRect(rect);
                rect.top -= size;
                rect.bottom += size;
                rect.left -= size;
                rect.right += size;
                parentView.setTouchDelegate(new TouchDelegate(rect, view));
            }
        });
    }

    public static void expandTouchAreaHeight(View view, int size) {
        View parentView = (View) view.getParent();
        parentView.post(new Runnable() {
            @Override
            public void run() {
                Rect rect = new Rect();
                view.getHitRect(rect);
                rect.top -= size;
                rect.bottom += size;
                parentView.setTouchDelegate(new TouchDelegate(rect, view));
            }
        });
    }
}
