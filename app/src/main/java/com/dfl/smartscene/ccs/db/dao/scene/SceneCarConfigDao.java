package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneCarConfigEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneCarConfigDao {

    @Insert
    void insertStudent(SceneCarConfigEntity item);

    @Delete
    void deleteStudent(SceneCarConfigEntity item);

    @Update
    void updateStudent(SceneCarConfigEntity item);

    @Query("SELECT * FROM scene_car_config")
    Observable<List<SceneCarConfigEntity>> queryAll();

    @Query("SELECT * FROM scene_car_config WHERE :car_type='' or car_type= :car_type")
    Observable<List<SceneCarConfigEntity>> queryByCarType(String car_type);
}
