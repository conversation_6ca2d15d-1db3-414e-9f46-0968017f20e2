package com.dfl.smartscene.ccs.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.animation.LinearInterpolator;

/**
 * <AUTHOR>
 * @date ：2022/12/7 18:00
 * @description ：动画管理类
 */
public class AnimManager {

    /**
     * 开始抖动动画
     * @param view
     */
    public static void startShakeAnim(View view){
        if(view.getTag() != null && view.getTag() instanceof ObjectAnimator){
            ((ObjectAnimator) view.getTag()).start();
        }else {
            ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(view, "rotation", 0f, 2f, -2f, 0f)
                    .setDuration(300);
            objectAnimator.setRepeatMode(ValueAnimator.RESTART);
            objectAnimator.setRepeatCount(ValueAnimator.INFINITE);
            objectAnimator.setInterpolator(new LinearInterpolator());
            objectAnimator.setupEndValues();
            objectAnimator.start();
            view.setTag(objectAnimator);
        }

    }

    /**
     * 停止抖动动画
     * @param view
     */
    public static void stopShakeAnim(View view){
        if(view.getTag() != null && view.getTag() instanceof ObjectAnimator){
            ((ObjectAnimator) view.getTag()).cancel();
            view.setRotation(0f);
        }

    }
}
