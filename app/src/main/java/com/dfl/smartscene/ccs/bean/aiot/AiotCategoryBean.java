package com.dfl.smartscene.ccs.bean.aiot;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/8/3 16:58
 * @description ：aiot类别，原生数据
 */
public class AiotCategoryBean {
    /**
     * 类别id
     */
    private String csId;
    /**
     * 类别名
     */
    private String csName;
    /**
     * 该类别下设备列表
     */
    private List<AiotDeviceBean> deviceList = new ArrayList<>();

    public String getCsId() {
        return csId;
    }

    public void setCsId(String csId) {
        this.csId = csId;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public List<AiotDeviceBean> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<AiotDeviceBean> deviceList) {
        this.deviceList = deviceList;
    }
}
