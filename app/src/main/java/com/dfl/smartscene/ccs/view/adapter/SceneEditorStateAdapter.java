package com.dfl.smartscene.ccs.view.adapter;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.fragment.AutoPStateOperation;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.util.ListUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：2022/12/6 17:32
 * @description ：编辑页面状态条件的adapter，特点有个数限制，特殊p挡条件
 */
public class SceneEditorStateAdapter extends SceneEditorAdapter{

    private static String mSceneType = ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD;

    public void setSceneType(String sceneType) {
        mSceneType = sceneType;
    }

    public static final int VIEW_TYPE_AUTO_P = 2;
    @Override
    public int getItemViewType(int position) {
        if(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR.equals(getItemData(position).getOperationId())){
            return VIEW_TYPE_AUTO_P;
        }
        return super.getItemViewType(position);
    }

    @Override
    protected BaseHolder<SettingOperation> getViewHolder(View view, int viewType) {
        if(viewType == VIEW_TYPE_AUTO_P){
            return new AutoPHolder(view);
        }
        return super.getViewHolder(view, viewType);
    }

    @Override
    public void setDataList(List<SettingOperation> dataList) {
        mDataList = new ArrayList<>(dataList);
        if(dataList.size() < 3 && !checkAddExist(getDataList())){
            if (creatAddOperation() != null) {
                mDataList.add(creatAddOperation());
            }
        }
        notifyDataSetChanged();
    }

    protected SettingOperation creatAddOperation(){
        return SpecialBeanFactory.productAddStateOperation();
    }

    private boolean checkAddExist(List<SettingOperation> settingOperations){
        return !ListUtil.isEmpty(settingOperations) && settingOperations.get(settingOperations.size() - 1).getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD);
    }

    private boolean checkAutoPExist(List<SettingOperation> settingOperations){
        for (SettingOperation settingOperation : settingOperations){
            if(settingOperation instanceof AutoPStateOperation){
                return true;
            }
        }
        return false;
    }

    @Override
    public void removeData(int position) {
        mDataList.remove(position);
        if(!checkAddExist(mDataList) && getNormalStateCount() < 3){
            if (creatAddOperation() != null) {
                mDataList.add(creatAddOperation());
            }
        }
        notifyDataSetChanged();
    }

    @Override
    public void addData(SettingOperation data) {
        if(checkAddExist(mDataList)){
            if(checkAutoPExist(mDataList)){
                mDataList.add(getItemCount() - 2 , data);
            }else {
                mDataList.add(getItemCount() - 1 , data);
            }
        }else {
            if(checkAutoPExist(mDataList)){
                return;
            }else {
                if(data instanceof AutoPStateOperation){
                    mDataList.add(data);
                }
            }

        }
        if(getNormalStateCount() == 3 && getItemData(getItemCount() - 1).getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)){
            mDataList.remove(getItemCount() - 1);
        }
        notifyDataSetChanged();
    }

//    @Override
//    public void addData(int pos, SettingOperation data) {
//        mDataList.add(pos, data);
//        if(getItemCount() > 3){
//            mDataList.remove(3);
//        }
//        notifyDataSetChanged();
//    }

    @Override
    public List<SettingOperation> getValidData() {
        List<SettingOperation> settingOperations = new ArrayList<>(getDataList());
        if(checkAddExist(settingOperations)){
            settingOperations.remove(settingOperations.size() - 1);
        }
        return settingOperations;
    }

    public int getNormalStateCount(){
        int num = 0;
        for(SettingOperation settingOperation : mDataList){
            if(!settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD) && !(ConstantModelValue.OPERATION_ID_STATE_DRIVE_GEAR.equals(settingOperation.getOperationId()))){
                num += 1;
            }
        }
        return num;
    }

    /**
     * P挡状态条件子视图Holder
     * */
    public class AutoPHolder extends BaseHolder<SettingOperation>{

        private TextView mTextViewDesc;
        protected ImageView mImageButtonDelete;
        protected ImageView mImageViewIcon;

        public AutoPHolder(View itemView) {
            super(itemView);
            mTextViewDesc = itemView.findViewById(R.id.textview_item_scene_editor_desc);
            mImageButtonDelete = itemView.findViewById(R.id.imagebutton_item_scene_editor_delete);
            mImageViewIcon = itemView.findViewById(R.id.imageview_scene_editor_item_icon);
        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            setTitle(settingOperation.getDesc());
            if(settingOperation instanceof AutoPStateOperation){
                mImageButtonDelete.setVisibility(View.GONE);
            }else{
                if (Objects.equals(mSceneType,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW)){
                    mImageButtonDelete.setVisibility(View.GONE);
                }else {
                    mImageButtonDelete.setVisibility(View.VISIBLE);
                    mImageButtonDelete.setOnClickListener(v -> {
                        if(mOnButtonClickListener != null){
                            mOnButtonClickListener.onItemClick(mImageButtonDelete,getItemViewType(),getItemData(position),position);
                        }
                    });
                }
            }
            ImageUtil.loadIcon(CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId()),mImageViewIcon);

        }

        protected void setTitle(String value){

            if(value.contains(" ")){
                SpannableString spannableString = new SpannableString(value);
                int index = value.indexOf(" ");
                spannableString.setSpan(new ForegroundColorSpan(mTextViewDesc.getResources().getColor(R.color.color_text_selected)),index,value.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                mTextViewDesc.setText(spannableString);
            }else {
                mTextViewDesc.setText(value);
            }

        }

    }
}
