package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;

import com.dfl.dflcommonlibs.uimodeutil.UITextView;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseHolder;

/**
 * <AUTHOR>
 * @date ：2023/4/8 10:54
 * @description ：
 */
public class NaviRouteChooseAdapter extends OpeRecyclerAdapter{
    @Override
    protected BaseHolder<DataBean> getViewHolder(View view, int viewType) {
        return new NaviRouteChooseHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_navi_route;
    }

    class NaviRouteChooseHolder extends OpeRecyclerHolder{

        private UITextView mTextView;
        ImageView shade;

        public NaviRouteChooseHolder(View itemView) {
            super(itemView);
            mTextView = itemView.findViewById(R.id.textview_item_ope_recycler);
            shade = itemView.findViewById(R.id.imageview_item_ope_recycler_shade);
        }

        @Override
        protected void setupSelectedData(DataBean dataBean, int position) {
            shade.setVisibility(View.VISIBLE);
            itemView.setBackgroundResource(R.color.transparent);
            mTextView.setTextColorResource(R.color.color_text_route_preference);
        }

        @Override
        protected void setupUnSelectedData(DataBean dataBean, int position) {
            shade.setVisibility(View.GONE);
            itemView.setBackgroundResource(R.drawable.bg_unselect_route);
            mTextView.setTextColorResource(R.color.color_text_route_preference);
        }
    }
}
