package com.dfl.smartscene.ccs.util;

import android.content.res.AssetManager;

import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.iauto.uibase.utils.MLog;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;

public class FileUtils {

    private static final String TAG = FileUtils.class.getSimpleName();

    public static boolean copyFileToLocal(String assetsFileName, String path) {

        MLog.d(TAG, "copyFileToLocal assetsFileName = " + assetsFileName + ", path = " + path);

        AssetManager am = ScenePatternApp.getInstance().getApplicationContext().getAssets();
        try {
            File dir = new File(path.substring(0, path.lastIndexOf("/")));
            if (!dir.exists()) {
                boolean mkdirs = dir.mkdirs();
                if(!mkdirs) {
                    return false;
                }
                MLog.d(TAG, "dir is not exist and make config dir =" + dir.getAbsolutePath());
            }
            if (!dir.exists()) {
                return false;
            }
            InputStream in = am.open(assetsFileName);
            FileOutputStream fos = new FileOutputStream(path);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }
            fos.flush();
            fos.close();
            in.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }


    //向文件里写入内容
    public static void saveAsFileWriter(String path, String content){
        MLog.d(TAG,"path = " + path);
        File file = new File(path);
        //如果目录不存在 创建目录
        try {
            if(!file.getParentFile().exists()){
                file.getParentFile().mkdirs();
            }
            file.createNewFile();
        }catch (IOException e){
            e.printStackTrace();
        }
        FileWriter fileWriter = null;
        try {
            // true表示不覆盖原来的内容，而是加到文件的后面。若要覆盖原来的内容，直接省略这个参数就好
            fileWriter = new FileWriter(path, false);
            fileWriter.write(content);
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取文件md5值
     */
    public String getFileMD5(File file) {
        if (!file.isFile()) {
            return null;
        }
        MessageDigest digest;
        FileInputStream in = null;
        byte[] buffer = new byte[1024];
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
            in = new FileInputStream(file);
            while ((len = in.read(buffer, 0, 1024)) != -1) {
                digest.update(buffer, 0, len);
            }
        } catch (Exception e) {
            MLog.e(TAG, "getFileMD5, error = ", e);
            return null;
        } finally {
            safetyClose(in);
        }
        return bytesToHexString(digest.digest());
    }

    private void safetyClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length == 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
}
