package com.dfl.smartscene.ccs.view.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DescriptionUtil;
import com.dfl.smartscene.ccs.util.ImageUtil;

/**
 * <AUTHOR>
 * @date ：2022/8/30 10:48
 * @description ：用于广场场景卡片的adapter
 */
public class LibrarySceneAdapter extends BaseAdapter<SceneBean> {
    private static final String TAG = "LibrarySceneAdapter";
    @Override
    protected BaseHolder<SceneBean> getViewHolder(View view, int viewType) {
        return new LibrarySceneHolder(view);
    }

    /**
     * 外部通知收藏状态变化
     * @param id
     * @param status
     */
    public void onCollectChange(String id , boolean status){
        if(getDataList() == null){
            return;
        }

        int pos = -1;
        for(int i = 0 ; i < getItemCount() ; i ++){
            if(getItemData(i).getSceneId().equals(id)){
                pos = i;
                break;
            }
        }
        if(pos > -1){
            getItemData(pos).setCollectState(status);
            notifyItemChanged(pos);
        }
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_library_scene;
    }

    class LibrarySceneHolder extends BaseHolder<SceneBean>{
        TextView mTextViewTitle;//场景名称
        TextView mTextViewDesc;//场景描述
        ImageView mImageButtonCollect;
        TextView mTextViewCollectDesc;//收藏状态描述
        ImageView mImageViewExcute;
        ImageView mImageViewNew;
        ImageView mImageViewPic;
        TextView mTextViewCorner;
        ImageView mImageViewBg;//背景


        public LibrarySceneHolder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_item_library_scene_title);
            mImageButtonCollect = itemView.findViewById(R.id.imagebutton_item_library_scene_collect);
            mTextViewDesc = itemView.findViewById(R.id.text_view_item_library_scene_desc);
            mTextViewCollectDesc = itemView.findViewById(R.id.textview_item_library_scene_collect_desc);
            mImageViewExcute = itemView.findViewById(R.id.image_view_item_library_scene_excute);
            mImageViewNew = itemView.findViewById(R.id.imageview_item_library_scene_new);
            mImageViewPic = itemView.findViewById(R.id.image_view_item_library_scene_src);
            mTextViewCorner = itemView.findViewById(R.id.textview_library_item_corner);
            mImageViewBg = itemView.findViewById(R.id.item_libray_card_bg);
            mImageButtonCollect.setOnClickListener(v -> {
                if(mOnButtonClickListener != null){
                    int pos = getAdapterPosition();
                    mOnButtonClickListener.onItemClick(mImageButtonCollect,getItemViewType(),getItemData(pos),pos);
                }
            });
            mImageViewExcute.setOnClickListener(v -> {
                if(mOnButtonClickListener != null){
                    int pos = getAdapterPosition();
                    mOnButtonClickListener.onItemClick(mImageViewExcute,getItemViewType(),getItemData(pos),pos);
                }

            });

        }

        @Override
        public void setupData(SceneBean sceneBean, int position) {
            mTextViewTitle.setText(sceneBean.getSceneName());
            mTextViewDesc.setText(sceneBean.getSceneDesc());
            //设置醒神模式，上班模式，回家模式的icon
            if (ConstantModelValue.SCENE_ID_OLD_SLEEP.equals(sceneBean.getSceneId())) {
                loadBackground(mImageViewBg,sceneBean.getScenePic(),R.drawable.icon_scene_sleep_mode);
            } else if (ConstantModelValue.SCENE_ID_OLD_WAKE.equals(sceneBean.getSceneId())) {
                loadBackground(mImageViewBg,sceneBean.getScenePic(),R.drawable.icon_scene_wake_mode);
            } else if (ConstantModelValue.SCENE_ID_OLD_WORK.equals(sceneBean.getSceneId())) {
                loadBackground(mImageViewBg,sceneBean.getScenePic(),R.drawable.icon_scene_work_mode);
            } else if (ConstantModelValue.SCENE_ID_OLD_HOME.equals(sceneBean.getSceneId())) {
                loadBackground(mImageViewBg,sceneBean.getScenePic(),R.drawable.icon_scene_home_mode);
            } else {
                loadBackground(mImageViewBg,sceneBean.getScenePic(),R.drawable.icon_scene_qinzi_mode);
            }

            if(sceneBean.isCollectState()){
                mImageButtonCollect.setBackground(mImageButtonCollect.getResources().getDrawable(R.drawable.icon_like));
                mTextViewCollectDesc.setText(mImageButtonCollect.getResources().getString(R.string.string_item_library_scene_collected));
                mImageButtonCollect.setContentDescription(mImageButtonCollect.getResources().getString(R.string.string_visible_able_say_uncollect)+sceneBean.getSceneName());
            }else {
                mImageButtonCollect.setBackground(mImageButtonCollect.getResources().getDrawable(R.drawable.icon_unlike));
                mTextViewCollectDesc.setText(mImageButtonCollect.getResources().getString(R.string.string_item_library_scene_uncollected));
                mImageButtonCollect.setContentDescription(mImageButtonCollect.getResources().getString(R.string.string_visible_able_say_collect)+sceneBean.getSceneName());
            }
//            if(sceneBean.getSceneCorner() == null || "".equals(sceneBean.getSceneCorner()) || !SceneCornerManager.shouldShowBadge(sceneBean.getSceneId(),sceneBean.getSceneCorner())){
//                mImageViewNew.setVisibility(View.GONE);
//                mTextViewCorner.setVisibility(View.GONE);
//            }else if(sceneBean.getSceneCorner().startsWith("text")){
//                mImageViewNew.setVisibility(View.VISIBLE);
//                mTextViewCorner.setVisibility(View.VISIBLE);
//                mImageViewNew.setImageResource(R.drawable.icon_scene_corner_bg);
//                Uri parse = Uri.parse(sceneBean.getSceneCorner());
//                String text = parse.getHost();
//                mTextViewCorner.setText(text);
//            }else {
//                mImageViewNew.setVisibility(View.VISIBLE);
//                mTextViewCorner.setVisibility(View.GONE);
//                ImageUtil.loadImageUrlNoCorner(mImageViewNew,sceneBean.getSceneCorner(),R.color.transparent);
//            }
            ImageUtil.loadImageUrlNoCorner(mImageViewPic, sceneBean.getScenePic(),R.drawable.drawable_library_scene_default);
            //设置执行按钮的可见即可说
            mImageViewExcute.setContentDescription(mImageViewExcute.getResources().getString(R.string.string_visible_able_say_execute)+sceneBean.getSceneName());
            DescriptionUtil.generateItemViewExceptButtonDescriptionVisibleToSay(sceneBean.getSceneName(), itemView);

        }

        private void loadBackground(ImageView imageView,String url, int defaultId) {
            LogUtil.d(TAG, "loadBackground: "+url);
            boolean isUrlValid = !TextUtils.isEmpty(url);
            Glide.with(itemView.getContext()).load(isUrlValid?url:defaultId)
                    .apply(RequestOptions.bitmapTransform(new RoundedCorners(16)).error(defaultId))
                    .into(imageView);
        }
    }

}
