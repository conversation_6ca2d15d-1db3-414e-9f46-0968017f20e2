package com.dfl.smartscene.ccs.util;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 纵向排列
 */
public class VerticalGridItemDecorationUtil extends RecyclerView.ItemDecoration {
    private int lineSpace;
    private int columnSpace;
    private int itemCount;

    private int itemW;//一个item实际的宽度

    private int needWidth = 0;

    public VerticalGridItemDecorationUtil(int lineSpace, int columnSpace, int column) {
        this.lineSpace = lineSpace;
        this.columnSpace = columnSpace;
        itemCount = column;
    }

    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(c, parent, state);
        if(needWidth != 0 && parent.getMeasuredWidth() != needWidth){
            ViewGroup.LayoutParams layoutParams = parent.getLayoutParams();
            layoutParams.width = needWidth;
            parent.setLayoutParams(layoutParams);
            Log.d("VerticalGridItemDecorationUtil" , "reset width");
        }
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        if (itemW == 0) {
            //此时该view未测量，在布局里item不可使用matchparent或者wrapcontent
            itemW = view.getLayoutParams().width;
        }
        if(needWidth == 0){
            needWidth = itemCount * (itemW + columnSpace);
        }

        outRect.bottom = lineSpace;
        outRect.left = 0;
        outRect.right = 0;
    }
}

