package com.dfl.smartscene.ccs.service;

import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;

import androidx.annotation.NonNull;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.busevent.CustomapiEvent;
import com.dfl.smartscene.ccs.busevent.ServiceInitEvent;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DoorModel;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.MediaModel;
import com.dfl.smartscene.ccs.model.SeatModel;
import com.dfl.smartscene.ccs.model.VRModel;
import com.dfl.smartscene.ccs.model.WeatherModel;
import com.dfl.smartscene.ccs.model.interrupt.NewScenePatternInterrupt;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.NetworkManager;
import com.iauto.uibase.utils.MLog;

public class ScenePatternService extends Service {
    private static final String TAG = "ScenePatternService";
    private HandlerThread conditionHandlerThread;
    private Handler conditionHandler;


    @Override
    public void onCreate() {
        super.onCreate();
        RxBus.getDefault().register(this);
        MLog.d(TAG, "ScenePatternService onCreate()");
        conditionHandlerThread = new HandlerThread("condition");
        conditionHandlerThread.start();
        //初始化执行动作handler
        MediaModel.getInstance().init();

        //初始化customapi
        CustomApiManager.getInstance().registerCustomApi();

        if(!initDaidRelateFlag && DriveModel.getInstance().getDaid() != null){
            initDaidRelateFlag = true;
            initDaidRelate();
        }
//        //初始化节假日
//        TimeModel.getInstance().initWorkHoliday();
//        copyAssertFile();
        //初始化网络状态监听
        NetworkManager.getInstance().initNetworkListener();
        NewScenePatternInterrupt.create(ScenePatternApp.getInstance());

//        MqttWrapper.getInstance().createConnect(this);
    }

    private volatile boolean initDaidRelateFlag = false;

    /**
     * 检讨个人中心登录服务的准备完成情况
     * */
    @Subscribe
    public void onServiceInitEvent(CustomapiEvent customapiEvent) {
        LogUtil.d(TAG, "on service init event");
//        if (Constants.DFL_APP_USERCENTER_COMMON_COMMON_SERVICE.equals(customapiEvent.getCutomapiName())
//                && customapiEvent.getState() == APIStateType.APIState.SUCCESS) {
//            boolean login = DAModel.getInstance().isUserCenterLogin();
//            LogUtil.d(TAG, "login = "+login);
//            if(!login) {
//                Intent intent=new Intent(null, Uri.parse("usercenter://com.szlanyou/login?cp_type=24&target_page=1"));
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
//                ScenePatternApp.getInstance().getApplicationContext().startActivity(intent);
//            }
//            //初始化用户场景与车辆配置
//            Schedulers.computation().scheduleDirect(() -> {
//                SceneDataModel.getInstance().requestUserSceneList(true).subscribe();
//                MqttWrapper.getInstance().createConnect();
//            }, 1, TimeUnit.SECONDS);
//        }
    }

    //初始化和daid相关的操作，有可能此时customapi获取daid还没初始化完成
    public void initDaidRelate(){
        LogUtil.d(TAG,"initDaidRelate");
        (ScenePatternApp.getScenePatternApp()).initLanYouParas();
        RxBus.getDefault().post(new ServiceInitEvent());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (conditionHandler == null) {
            conditionHandler = new Handler(conditionHandlerThread.getLooper(), new Handler.Callback() {
                @Override
                public boolean handleMessage(@NonNull Message msg) {
                    String apiName = (String) msg.obj;
                    MLog.d(TAG, "customapi init listener:" + apiName);
                    switch (apiName) {
                        case Constants.DFL_VEHICLE_BACKDOOR_BACKDOOR_SERVICE:
                            DoorModel.getInstance().listenBackDoorStatus();
                            break;
                        case Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE:
                            DriveModel.getInstance().listenCarStatus();
                            if(!initDaidRelateFlag && DriveModel.getInstance().getDaid() != null){
                                initDaidRelateFlag = true;
                                initDaidRelate();
                            }
                            break;
                        case Constants.DFL_VEHICLE_DOORANDTRUNK_DOORANDTRUNK_SERVICE:
                            DoorModel.getInstance().listenDoorStatus();
                            break;
                        case Constants.DFL_VEHICLE_SEAT_SEAT_SERVICE:
                            SeatModel.getInstance().registerSeatCallBack();
                            break;
                        case Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE:
                            MediaModel.getInstance().initAudioListener();
                            break;
                        case Constants.DFL_APP_VR_UPLOADHOTWORDS_UPLOADHOTWORDS_SERVICE:
                            VRModel.getInstance().initVrListener();
                            break;
                        case Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE:
//                            DAModel.getInstance().initSystemViewListener();
                            break;
                        case Constants.DFL_APP_USERCENTER_WEATHER_WEATHER_SERVICE:
                            WeatherModel.getInstance().init();
                            break;
                        case Constants.DFL_APP_VR_TTS_TTSAIDL_SERVICE:
                            VRModel.getInstance().initTtsListener();
                            break;
                        case Constants.DFL_DA_POPUP_POPUP_SERVICE:
                            DialogManager.initPopup();
                            break;
                        case Constants.DFL_APP_NAVI_NAVI_NAVISCENE_SERVICE:
                            NaviModel.getInstance().initListener();
                            break;
                        default:
                            break;
                    }
                    LibraryManager.getInstance().dispatchModelListenerInit(apiName);
                    return false;
                }

            });

        }
        if (null != intent && ConstantModelValue.ACTION_INTENT_CUSTOMAPI_CONDITION_INIT.equals(intent.getAction())) {
            Message obtain = Message.obtain(conditionHandler);
            obtain.obj = intent.getStringExtra(ConstantModelValue.KEY_SERVICE_INTENT_CUSTOMAPI_NAME);
            conditionHandler.sendMessage(obtain);
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return ScenePatternServiceStub.getInstance();
    }


}