package com.dfl.smartscene.ccs.fragment;

import android.content.Context;
import android.content.res.Configuration;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.util.ExpandTouchArea;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：2022/12/7 11:08
 * @description ：
 */
public class EditTextLayout extends FrameLayout {

    private ImageView mImageViewCancel;
    protected EditText mEditTextContent;
    private  TextView mTextViewConfirm;
    private ImageView mImageViewClear;
    public ConfirmClickListener mConfirmClickListener;
    protected boolean canInputEnglish = false;

    public interface ConfirmClickListener{
        void onClickConfirm(String text);
    }

    public void setConfirmClickListener(ConfirmClickListener confirmClickListener) {
        mConfirmClickListener = confirmClickListener;
    }

    public EditTextLayout(@NonNull Context context) {
        super(context);
        init();
    }

    public EditTextLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();

    }

    public EditTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();

    }

    public EditTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();

    }

    protected void init(){
        initView(this);

    }

    protected void initView(View view){
        LayoutInflater.from(view.getContext()).inflate(R.layout.dialog_fragment_vr_operation,this,true);
        mEditTextContent = view.findViewById(R.id.edittext_scene_editor_vr_content);
        mImageViewCancel = view.findViewById(R.id.imageView_vr_operation_dialog_cancel);
        mTextViewConfirm = view.findViewById(R.id.textview_vr_operation_dialog_confirm);
        mImageViewClear = view.findViewById(R.id.imageview_vr_operation_clear);
        mImageViewClear.setOnClickListener(v -> mEditTextContent.setText(""));
        mImageViewCancel.setOnClickListener(v -> onclickCancelButton());
        ExpandTouchArea.expandTouchArea(mImageViewCancel, 16);
        mTextViewConfirm.setOnClickListener(v -> {
            onclickConfirmButton();
        });

        mEditTextContent.setHint(canInputEnglish ? getResources().getText(R.string.string_scene_editor_scene_name_hint) : getResources().getText(R.string.string_scene_editor_vr_hint));

//        setEditTextFilter(mEditTextContent,getInputEnChatFilter(),getInputSpaceFilter(),getInputSpeChatFilter(),getInputLengthFilter());
        mEditTextContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 过滤非中文和数字的字符
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < s.length(); i++) {
                    char c = s.charAt(i);
                    if(canInputEnglish){
                        if (Character.toString(c).matches("[\\u4E00-\\u9FA5a-zA-Z0-9]")) {
                            sb.append(c);
                        }
                    }else {
                        if (Character.toString(c).matches("[\\u4E00-\\u9FA50-9]")) {
                            sb.append(c);
                        }
                    }
                }
                // 如果过滤后有变化，则更新EditText的内容
                if (!sb.toString().equals(s.toString())) {
                    mEditTextContent.setText(sb.toString());
                    mEditTextContent.setSelection(sb.length()); // 保持光标位置
                }
                if("".equals(sb.toString())){
                    mImageViewClear.setVisibility(View.GONE);
                    mTextViewConfirm.setEnabled(false);
                }else {
                    mImageViewClear.setVisibility(View.VISIBLE);
                    mTextViewConfirm.setEnabled(true);

                }


            }
        });
        mEditTextContent.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if(mTextViewConfirm.isEnabled()){
                    onclickConfirmButton();
                }
                return false;
            }
        });
    }

    protected void onclickCancelButton(){
        // 隐藏键盘
        mEditTextContent.clearFocus();
        InputMethodManager inputMethodManager = (InputMethodManager)mEditTextContent.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(mEditTextContent.getRootView().getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
        hide();
    }

    protected void onclickConfirmButton(){
        if(mConfirmClickListener != null){
            mConfirmClickListener.onClickConfirm(mEditTextContent.getText().toString());
        }
        // 隐藏键盘
        mEditTextContent.clearFocus();
        InputMethodManager inputMethodManager = (InputMethodManager)mEditTextContent.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(mEditTextContent.getRootView().getWindowToken(),  InputMethodManager.HIDE_NOT_ALWAYS);

        hide();
    }




    protected void initData(String text){
        if(text == null){
            mEditTextContent.setText("");
        }else {
            mEditTextContent.setText(text);
        }
        mEditTextContent.requestFocus();
        mEditTextContent.postDelayed(this::showInputMethod,300);

    };

    public void show(){
        show(null);
    }

    public void show(String text){
        setVisibility(VISIBLE);
        initData(text);
    }

    public void hide(){
        setVisibility(GONE);
        mConfirmClickListener = null;
    }

    public void showInputMethod() {
        InputMethodManager inputMethodManager = (InputMethodManager) ScenePatternApp.getInstance().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.showSoftInput(mEditTextContent, 0,null);
    }

    public InputFilter getInputSpaceFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (source.equals(" ") || source.toString().contentEquals("\n")) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }

    public InputFilter getInputSpeChatFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                String speChat = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
                Pattern pattern = Pattern.compile(speChat);
                Matcher matcher = pattern.matcher(source.toString());
                if (matcher.find()) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }

    public InputFilter getInputLengthFilter(){
        return new InputFilter.LengthFilter(getMaxLength());
    }

    protected int getMaxLength(){
        return 15;
    }

    public InputFilter getInputEnChatFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                String speChat = "[abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ]";
                Pattern pattern = Pattern.compile(speChat);
                Matcher matcher = pattern.matcher(source.toString());
                if (matcher.find()) {
                    return "";
                } else {
                    return null;
                }
            }
        };
    }


    public void setEditTextFilter(EditText editText , InputFilter... inputFilters){
        editText.setFilters(inputFilters);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if(mEditTextContent != null){
            mEditTextContent.setTextColor(getResources().getColor(R.color.color_text_first_label));
            mEditTextContent.setHintTextColor(getResources().getColor(R.color.color_text_tertiary_label));
        }
    }
}
