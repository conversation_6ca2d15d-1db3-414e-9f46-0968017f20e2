package com.dfl.smartscene.ccs.view.dialogfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.Switch;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;

/**
 * <AUTHOR>
 * @date ：2022/12/31 8:39
 * @description ：和普通操作ui不同
 */
public class OperationTimeStateDialog extends OperationBaseDialogFragment{
    private static final String TAG = "OperationTimeStateDialog";
    public OperationTimeStateDialog(int operationType, SingleOperation singleOperation) {
        super(operationType, singleOperation);
    }

    public OperationTimeStateDialog(int operationType, int pos, SettingOperation settingOperation, SingleOperation singleOperation) {
        super(operationType, pos, settingOperation, singleOperation);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_fragment_operation_base_time,container,false);
       return view;
    }

    @Override
    protected void initView(View view) {
        super.initView(view);
        FrameLayout frameLayout = view.findViewById(R.id.layout_operation_dialog_content);
        View open = view.findViewById(R.id.view_open_day_switch);
        View close = view.findViewById(R.id.view_close_day_switch);
        Switch contentSwitch = frameLayout.findViewById(R.id.switchcompat_layout_custom_state_time_clock_all_day);
        LogUtil.d(TAG, "onCreateView: "+contentSwitch);
        if(contentSwitch != null){
            open.setOnClickListener(v -> {contentSwitch.setChecked(true);});
            close.setOnClickListener(v -> {contentSwitch.setChecked(false);});
        }
    }
}
