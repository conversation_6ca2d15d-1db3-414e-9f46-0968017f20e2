package com.dfl.smartscene.ccs.constant;

/*
 * 彩蛋模式功能定义
 * <AUTHOR>
 * @date 2022/4/15
 */
public class SurpriseEggFuncDef {
    //彩蛋模式list item类型
    public static final int ITEM_TYPE_TITLE = 0;
    public static final int ITEM_TYPE_DATA = 1;
    public static final int ITEM_TYPE_OTHER = 2;

    //彩蛋模式JSON解析名称定义
    public static final String JSON_ITEM_RESULT = "result";
    public static final String JSON_ITEM_TITLE = "title";
    public static final String JSON_ITEM_ROW = "rows";
    public static final String JSON_ITEM_CREATE_TIME = "createTime";
    public static final String JSON_ITEM_SURPRISE_INFO_FILES = "surpriseInfoFiles";
    public static final String JSON_ITEM_FILE_PATH = "filePath";
    public static final String JSON_ITEM_ID = "id";
    public static final String JSON_ITEM_URL = "h5link";
    public static final String JSON_ITEM_MESSAGE = "msg";
    public static final String JSON_ITEM_VOICE_TYPE = "voiceType";
    public static final String JSON_ITEM_VOICE_TEXT = "voiceText";
    public static final String JSON_ITEM_VOICE_FILE = "voiceFile";
    public static final String JSON_ITEM_TIME_START= "startTime";
    public static final String JSON_ITEM_TIME_END = "endTime";
    public static final String JSON_TOKEN = "rows";
    public static final int SURPRISE_EGG_LIST_REQ_SUCCESS = 1;

    public static final int JSON_ITEM_VOICE_TYPE_TEXT = 1;
    public static final int JSON_ITEM_VOICE_TYPE_VOICE = 2;

    //彩蛋模式list表示状态定义
    public static final int SURPRISE_EGG_DSP_STATUS_COMMON = 0;     //正常表示
    public static final int SURPRISE_EGG_DSP_STATUS_DISCONNECT = 1; //网络断开表示
    public static final int SURPRISE_EGG_DSP_STATUS_NO_FILES = 2;   //无内容表示
    public static final int SURPRISE_EGG_DSP_STATUS_LOADING = 3;    //加载中状态
    public static final int SURPRISE_EGG_DSP_STATUS_REFRESH_LOADING = 4;    //加载中状态

    //彩蛋模式list删除定义
    public static final int SURPRISE_EGG_DEL_STATUS_END = 2;        //完了
    public static final int SURPRISE_EGG_DEL_STATUS_ING = 1;        //删除中
    public static final int SURPRISE_EGG_DEL_STATUS_BEG = 0;        //开始
    public static final int SURPRISE_EGG_DEL_STATUS_NON = -1;       //未执行
    public static final int SURPRISE_EGG_DEL_STATUS_ERR = 3;        //未执行

    // 联友通信用app info
    public static final String APP_INFO_APP_ID = "appcxr9Dmh5mav117vHF5Lh4L0Wjt2xcWPl";
    public static final String APP_INFO_APP_KEY = "RDGJpK8TEtIARlsrzKW58l2zijUUQIaKYH0ck8haREIa1WM6HrMG4Bu94Xx2PWCdV5dHJ3C7lhMeZK5D1dYyDwydWC7J8LjKjefNjFf83VzgjZhzB8xXEUNOORoT198z";
    public static final String APP_INFO_APP_CODE = "nissan";
    public static final String APP_INFO_PROJECT_TYPE = "nissan";
    public static final String APP_INFO_APP_VERSION_NAME = "1.3.1";
    public static final String APP_INFO_SERVER_PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEmPfMOr62FBjQjQ2gFoerOIL3tBRCOetlgXgvXBX/va6sCvgroVP/4bracI9UojuZlCtzNmsclMrOjnqx7/hL3g==";
    public static final String APP_INFO_CUSTOM_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgflVLqqeAxP/qhsPc5pocsm1BpsVF9gUwMAF2Abc6HhegCgYIKoEcz1UBgi2hRANCAASqb0sWHL+7Sc/ET9JPWWZhJCfMy+oGhtJtbFFuSmOx5hy+fivjKMbbbbfZQf4QrCDa/adXFYEqDX8fpQfOvgST";


    // 舒压模式播放状态
    public static final int Surprise_Egg_Playback_Status_None = -1;
    public static final int Surprise_Egg_Playback_Status_Stop = 0;
    public static final int Surprise_Egg_Playback_Status_Play = 1;
    public static final int Surprise_Egg_Playback_Status_Start = 2;

    // 彩蛋模式TTS播放状态
    public static final int TTS_Playback_Status_None = -1;
    public static final int TTS_Playback_Status_Stop = 0;
    public static final int TTS_Playback_Status_Play = 1;
    public static final int TTS_Playback_Status_Interrupt = 2;

    // 彩蛋模式音频文件播放状态
    public static final int VOICE_FILE_Playback_Status_None = -1;
    public static final int VOICE_FILE_Playback_Status_Stop = 0;
    public static final int VOICE_FILE_Playback_Status_Play = 1;
    public static final int VOICE_FILE_Playback_Status_Interrupt = 2;

    public static final int SURPRISE_EGG_DELETE_SUCCESS = 1; // 删除成功
    public static final int SURPRISE_EGG_DELETE_FAILED = 2;  // 删除失败
}
