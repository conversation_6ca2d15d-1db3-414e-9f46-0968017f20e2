package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneListEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneListDao {

    @Insert
    void insertStudent(SceneListEntity item);

    @Delete
    void deleteStudent(SceneListEntity item);

    @Update
    void updateStudent(SceneListEntity item);

    @Query("SELECT * FROM scene_list")
    Observable<List<SceneListEntity>> queryAll();

    @Query("SELECT * FROM scene_list WHERE :mSceneId='' or mSceneId= :mSceneId")
    Observable<List<SceneListEntity>> queryBySceneId(String mSceneId);

    @Query("SELECT * FROM scene_list WHERE :mSceneCategoryId='' or mSceneCategoryId= :mSceneCategoryId")
    Observable<List<SceneListEntity>> queryBySceneCategoryId(String mSceneCategoryId);

    @Query("SELECT * FROM scene_list WHERE mSceneId in (:sceneIds)")
    Observable<List<SceneListEntity>> queryBySceneIds(String[] sceneIds);
}
