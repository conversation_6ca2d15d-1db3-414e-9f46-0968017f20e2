package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.NumberPicker;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.dfl.smartscene.R;


public class CustomTimePicker extends ConstraintLayout {

    private NumberPicker mNumberPicker1;
    private NumberPicker mNumberPicker2;

    private TextView mTextView1;
    private TextView mTextView2;

    private int mMinValue1 = 0;
    private int mMaxValue1 = 0;
    private int mValue1 = 0;

    private int mMinValue2 = 0;
    private int mMaxValue2 = 0;
    private int mValue2 = 0;

    private String mText1 = "时";
    private String mText2 = "分";

    private String[] mDisplayArray1;
    private String[] mDisplayArray2;

    public CustomTimePicker(Context context) {
        this(context, null);
    }

    public CustomTimePicker(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    private void initView(Context context) {

        View inflate = LayoutInflater.from(context).inflate(R.layout.layout_custom_timepicker, this);

        mNumberPicker1 = inflate.findViewById(R.id.numberpicker_layout_custom_state_time_clock_hour_first);
        mNumberPicker2 = inflate.findViewById(R.id.numberpicker_layout_custom_state_time_clock_minute_first);
        mTextView1 = inflate.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_1);
        mTextView2 = inflate.findViewById(R.id.textview_layout_custom_state_time_clock_value_unit_2);

        mNumberPicker1.setOnValueChangedListener((picker, oldVal, newVal) -> {

        });

        mNumberPicker2.setOnValueChangedListener((picker, oldVal, newVal) -> {

        });
    }


    public int[] getData() {
        int value1 = mNumberPicker1.getValue();
        int value2 = mNumberPicker2.getValue();
        int[] ints = new int[2];
        ints[0] = value1;
        ints[1] = value2;
        return ints;
    }


    public void refreshView() {
        mNumberPicker1.setMinValue(mMinValue1);
        mNumberPicker1.setMaxValue(mMaxValue1);
        mNumberPicker1.setValue(mValue1);
        mNumberPicker1.setDisplayedValues(mDisplayArray1);

        mNumberPicker2.setMinValue(mMinValue2);
        mNumberPicker2.setMaxValue(mMaxValue2);
        mNumberPicker2.setValue(mValue2);
        mNumberPicker2.setDisplayedValues(mDisplayArray2);


        mTextView1.setText(mText1);
        mTextView2.setText(mText2);
    }

    public void setDisplayArray1(String[] mDisplayArray1) {
        this.mDisplayArray1 = mDisplayArray1;
    }

    public void setDisplayArray2(String[] mDisplayArray2) {
        this.mDisplayArray2 = mDisplayArray2;
    }

    public void setMinValue1(int mMinValue1) {
        this.mMinValue1 = mMinValue1;
    }

    public void setMaxValue1(int mMaxValue1) {
        this.mMaxValue1 = mMaxValue1;
    }

    public void setValue1(int mValue1) {
        this.mValue1 = mValue1;
    }

    public void setMinValue2(int mMinValue2) {
        this.mMinValue2 = mMinValue2;
    }

    public void setMaxValue2(int mMaxValue2) {
        this.mMaxValue2 = mMaxValue2;
    }

    public void setValue2(int mValue2) {
        this.mValue2 = mValue2;
    }

    public void setText1(String mText1) {
        this.mText1 = mText1;
    }

    public void setText2(String mText2) {
        this.mText2 = mText2;
    }


    public int getValue1() {
        return mNumberPicker1.getValue();
    }

    public int getValue2() {
        return mNumberPicker2.getValue();
    }

    public String getText1() {
        return mText1;
    }

    public String getText2() {
        return mText2;
    }
}
