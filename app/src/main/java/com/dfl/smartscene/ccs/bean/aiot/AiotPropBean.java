package com.dfl.smartscene.ccs.bean.aiot;

/**
 * <AUTHOR>
 * @date ：2022/8/3 17:07
 * @description ：aiot操作参数的原生信息
 */
public class AiotPropBean {
    /**
     * 服务id
     */
    private int siid;
    /**
     * 属性id
     */
    private int piid;
    /**
     * 数据类型,enum, int8, bool,string 等
     */
    private String dataType;
    /**
     * viewType==0   {"0":"关闭","1":"打开"}
     * viewType==1 且 dataType != string  {"max":"100","min":"0","step":"1"}
     * 如果 dataType == string, specs= {"length":"10"}  length 为字符串最大长度
     */
    private String specs;
    /**
     * 1：作为设备能力返回且是枚举类型时有值，用于view展示与操作执行时传输
     * 2：操作执行时传输
     */
    private String value;

    public int getSiid() {
        return siid;
    }

    public void setSiid(int siid) {
        this.siid = siid;
    }

    public int getPiid() {
        return piid;
    }

    public void setPiid(int piid) {
        this.piid = piid;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
