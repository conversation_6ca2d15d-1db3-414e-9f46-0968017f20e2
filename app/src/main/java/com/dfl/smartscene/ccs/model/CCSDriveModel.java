package com.dfl.smartscene.ccs.model;

import com.dfl.api.base.APIStateType;
import com.dfl.api.base.Constants;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.api.base.appcommon.ICommonCarStatusCallback;
import com.dfl.api.base.appcommon.RangeInfo;
import com.dfl.api.base.appcommon.WarningInfo;
import com.dfl.api.base.carinfospeedrate.AccelerationInfo;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.busevent.CustomapiEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;

/**
 * <AUTHOR>
 * @date ：2022/12/13 19:08
 * @description ：车辆驾驶状态的监听，包括P挡状态、续航、电量情况<P/>
 */
public class CCSDriveModel extends LibraryDriveModel {
    private static final String TAG = "CCSDriveModel";
    private static volatile CCSDriveModel sInstance;

    private CCSDriveModel() {
        RxBus.getDefault().register(this);
    }

    public static CCSDriveModel getInstance() {
        if (null == sInstance) {
            synchronized (CCSDriveModel.class) {
                if (null == sInstance) {
                    sInstance = new CCSDriveModel();
                }
            }
        }
        return sInstance;
    }

    @Subscribe
    public void onServiceInitEvent(CustomapiEvent customapiEvent) {
        LogUtil.d(TAG, "on service init event " + customapiEvent.getCutomapiName() + ",apiState:" + customapiEvent.getState());
        if (Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE.equals(customapiEvent.getCutomapiName())
                && customapiEvent.getState() == APIStateType.APIState.SUCCESS) {
            listenCarStatus();
//            RxBus.getDefault().unRegister(this);以防断开重连后不接收了出现问题
        }
    }

    @Override
    protected String[] getConditionIds() {
        return new String[]{ConstantModelValue.OID_CDT_DRIVE_RANGE_FUEL,ConstantModelValue.OID_CDT_DRIVE_ELECTRICITY,
                ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER};
    }

    /**
     * 注册车辆挡位监听
     * */
    @Override
    public void listenCarStatus() {
        LogUtil.d(TAG,"listenCarStatus");
        IAppCommon appCommon = CCSCustomApiManager.getInstance().getIAppCommon();
        if(null != appCommon){
            try {
                LogUtil.d(TAG,"appCommon registerCommonCarStatusCallback");
                appCommon.registerCommonCarStatusCallback(new ICommonCarStatusCallback() {
                    @Override
                    public void onCarGearChange(int i) {
                        CCSDriveModel.this.onCarGearChange(i);
                    }

                    @Override
                    public void onReverseStatusChange(boolean b) {

                    }

                    @Override
                    public void onPkbStatusChange(int i) {

                    }

                    @Override
                    public void onCarSpeedChange(float v) {

                    }

                    @Override
                    public void onMeterSpeedChange(float v) {

                    }


                    @Override
                    public void onOdographChange(int i) {

                    }


                    @Override
                    public void onFuelDrivingRange(int i) {
                        LogUtil.d(TAG,"onFuelDrivingRange  " + i);
                        //监听燃油续航里程
                        //电续航里程已经弃用，改用OID_CDT_DRIVE_RANGE_POWER作为operationId
                        onCarDataChange(ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER, String.valueOf(i));
                    }

                    @Override
                    public void onPercentRemainFuel(int i) {

                    }

                    @Override
                    public void onPercentRemainPower(int i) {
                        LogUtil.d(TAG,"onPercentRemainPower  " + i);
                        //监听电量百分比
                        onCarDataChange(ConstantModelValue.OID_CDT_DRIVE_ELECTRICITY, String.valueOf(i));
                    }

                    @Override
                    public void onPowerDrivingRange(int i) {
                        LogUtil.d(TAG,"onPowerDrivingRange  " + i);
                        //监听电续航里程
                        onCarDataChange(ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER, String.valueOf(i));
                    }

                    @Override
                    public void onIllStatusChange(boolean b) {

                    }

                    @Override
                    public void onTurnIndicateStatusChange(int i) {

                    }

                    @Override
                    public void onScreenStatus(boolean b) {

                    }

                    @Override
                    public void onAccStatus(boolean b) {

                    }

                    @Override
                    public void onAutoACCStatusChange(boolean b) {

                    }

                    @Override
                    public void onIGNStatus(boolean b) {

                    }

                    @Override
                    public void onDriveStatusChanged(int i) {

                    }

                    @Override
                    public void onVehicleStatusChange(int i) {

                    }

                    @Override
                    public void onVehicleStatusTypeChange(int i, boolean b) {

                    }

                    @Override
                    public void onDriveMode(int i) {

                    }

                    @Override
                    public void onEnduranceCalculationStandardTypeChange(int i) {

                    }

                    @Override
                    public void onEcuErrorStateChange(int i, int i1) {

                    }

                    @Override
                    public void onCanIdErrorStateChange(int i, int i1) {

                    }

                    @Override
                    public void onAlertWarningInfo(int i, int i1) {

                    }

                    @Override
                    public void onVehicleWarningInfoChange(WarningInfo warningInfo) {

                    }

                    @Override
                    public void onTotalRangeInfoChange(RangeInfo rangeInfo) {

                    }

                    @Override
                    public void onPowerModeStatusChange(boolean b) {

                    }

                    @Override
                    public void onRemainFuel(float value) {

                    }


                });
            } catch (Exception e) {
                e.printStackTrace();
                LogUtil.d(TAG,"appCommon registerCommonCarStatusCallback Exception " + e);
            }
        }
    }
}
