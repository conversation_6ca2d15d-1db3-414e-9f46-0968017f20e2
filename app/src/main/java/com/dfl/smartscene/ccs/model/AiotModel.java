package com.dfl.smartscene.ccs.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.bean.aiot.AiotCategoryBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotCategorySaleBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotCommandBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotDeviceBean;
import com.dfl.smartscene.ccs.bean.aiot.AiotOperationBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.wrapper.AiotWrapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.functions.BiFunction;
import io.reactivex.functions.Function;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/9/21 14:33
 * @description ：aiot相关的管理类
 */
public class AiotModel implements DeviceBaseModel,StateBaseModel{
    private static final String TAG = "AiotModel";

    private static volatile AiotModel sInstance;

    private List<AiotCategoryBean> mAiotCategoryBeans = new ArrayList<>();

    private static final String MMKV_KEY_CSID = "MMKV_KEY_CSID";
    private static final String MMKV_KEY_PID = "MMKV_KEY_PID";

    private Map<String,String> mIconMap = new HashMap<>();
    public static AiotModel getInstance(){
        if (null == sInstance){
            synchronized (AiotModel.class){
                if (null == sInstance){
                    sInstance = new AiotModel();
                }
            }
        }
        return sInstance;
    }

    private AiotModel(){
        mIconMap.put("香氛","icon_fragrance");
        mIconMap.put("净化","icon_air_cleaner");
        mIconMap.put("颈枕","icon_pillow");
        mIconMap.put("车位锁","icon_p_lock");
        mIconMap.put("儿童座椅","icon_child_seat");
        mIconMap.put("投影","icon_projection");
    }

    /**
     * 获取当前连接的aiot设备及具有的操作
     * @return
     */
    public Observable<List<SingleOperation>> requestAiotDevices(){
        return AiotWrapper.getInstance().requestAiotDevices().map(aiotCategoryBeans -> {
            mAiotCategoryBeans = aiotCategoryBeans;
            List<SingleOperation> result  = new ArrayList<>();

            aiotCategoryBeans.forEach(aiotCategoryBean -> {
                aiotCategoryBean.getDeviceList().forEach(aiotDeviceBean -> {

                    result.add(originToSingleOperation(aiotCategoryBean.getCsId(),aiotDeviceBean));
                });
            });

            if(result.size() != 0){
                result.add(0,SpecialBeanFactory.productTitleOperation("已有设备"));
            }
            return result;
        }).doOnError(e ->{
            LogUtil.d(TAG, "request aiot devices error");
        });
    }

    public Observable<SingleOperation> requestOperationByOpe(SettingOperation settingOperation){
        return requestAiotDevices().map(new Function<List<SingleOperation>, SingleOperation>() {
            @Override
            public SingleOperation apply(List<SingleOperation> singleOperations) throws Exception {
                if(ListUtil.isEmpty(settingOperation.getListArgs())){
                    return null;
                }
                JSONObject jsonObject = JSON.parseObject(settingOperation.getListArgs().get(0));
                String csid = jsonObject.getString("csId");
                String pid = jsonObject.getString("pid");
                SingleOperation result = null;
                for(SingleOperation singleOperation : singleOperations){
                    if(pid != null && !"".equals(pid) && pid.equals(singleOperation.getArg(MMKV_KEY_PID))){
                        result = singleOperation;
                        break;
                    }else if(csid != null && !"".equals(csid) && csid.equals(singleOperation.getArg(MMKV_KEY_CSID))){
                        result = singleOperation;
                    }
                }
                return result;
            }
        });
    }

    /**
     * 查询预设场景中未购买的aiot设备名称
     * @param ops
     * @return
     */
    public Observable<String> requestSceneUnbuyDevices(List<SettingOperation> ops){
        return AiotWrapper.getInstance().requestAiotDevices().onErrorReturn(new Function<Throwable, List<AiotCategoryBean>>() {
            @Override
            public List<AiotCategoryBean> apply(Throwable throwable) throws Exception {
                return new ArrayList<>();
            }
        })
                .map(new Function<List<AiotCategoryBean>, String>() {
            @Override
            public String apply(List<AiotCategoryBean> aiotCategoryBeans) throws Exception {
                Map<String , String> sceneMap = new HashMap<>();
                for (SettingOperation so : ops){
                    if(ConstantModelValue.DEVICE_ID_AIOT.equals(so.getDeviceId())){
                        JSONObject jsonObject = JSON.parseObject(so.getListArgs().get(0));
                        sceneMap.put(jsonObject.getString("csId") , jsonObject.getString("csName"));
                    }
                }

                if(aiotCategoryBeans != null){
                    for (AiotCategoryBean aiotCategoryBean : aiotCategoryBeans){
                        sceneMap.remove(aiotCategoryBean.getCsId());
                    }
                }
                String result = "";
                for(String value : sceneMap.values()){
                    if("".equals(result)){
                        result  = value + ",";
                    }else {
                        result = result + value + ",";
                    }

                }
                return result;
            }
        });
    }



    /**
     * 获取aiot设备及售卖中设备，并转换为操作的格式
     * @return
     */
    public Observable<List<SingleOperation>> requestAiotBindAndSaleDevices(){
        return Observable.zip(requestAiotDevices(),AiotWrapper.getInstance().requestAiotSaleDevices(), new BiFunction<List<SingleOperation>, List<AiotCategorySaleBean>, List<SingleOperation>>() {
            @Override
            public List<SingleOperation> apply(List<SingleOperation> operations, List<AiotCategorySaleBean> aiotCategorySaleBeans) throws Exception {
                List<SingleOperation> result = new ArrayList<>(operations);
                aiotCategorySaleBeans.forEach(aiotCategorySaleBean -> {
                    if(ListUtil.isEmpty(aiotCategorySaleBean.getProductList())){
                        return;
                    }
                    for(AiotCategoryBean aiotCategoryBean : mAiotCategoryBeans){
                        if(aiotCategoryBean.getCsId().equals(aiotCategorySaleBean.getCsId())){
                            return;
                        }
                    }
                    result.add(saleBeanToSingleOperation(aiotCategorySaleBean));
                });
                if(operations.size() != result.size()){
                    result.add(operations.size(),SpecialBeanFactory.productTitleOperation("其他设备"));
                }
                return result;
            }
        }).doOnError( e->{
            LogUtil.d(TAG, "doOnError");
        });
    }

    //用户本地添加场景能力json
    public void sendUserCommand(String json){
        AiotWrapper.getInstance().sendUserCommand(json);
    }

    //云端预设场景能力json
    public void sendCloudCommand(String json){
        AiotWrapper.getInstance().sendCloudCommand(json);
    }

    private SingleOperation saleBeanToSingleOperation(AiotCategorySaleBean aiotCategorySaleBean){
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setDeviceId(aiotCategorySaleBean.getCsId());
        singleOperation.setOperationName(aiotCategorySaleBean.getCsName());
        singleOperation.setViewType(ConstantModelValue.VIEW_TYPE_AIOT_SALE_DEVICE);
        singleOperation.setOperationIcon(aiotCategorySaleBean.getProductList().get(0).getImage());
        return singleOperation;
    }

    private SingleOperation originToSingleOperation(String csId , AiotDeviceBean aiotDeviceBean){
        List<String> descs = new ArrayList<>();
        List<String> datas = new ArrayList<>();
        SingleOperation singleOperation = new SingleOperation();
        singleOperation.setViewType(ConstantModelValue.VIEW_TYPE_SINGLE_CHOOSE);
        singleOperation.setOperationId(ConstantModelValue.OID_ACT_DEVICE_AIOT_USER);
        singleOperation.setOperationName(aiotDeviceBean.getProductName());
        for(AiotOperationBean aiotOperationBean : aiotDeviceBean.getOperationList()){
            descs.add(aiotOperationBean.getOpName());
            datas.add(JSON.toJSONString(originToAiotCommandBean(csId,aiotDeviceBean.getPid(), aiotOperationBean)));
        }
        singleOperation.setMapArgs(MMKV_KEY_CSID,csId);
        singleOperation.setMapArgs(MMKV_KEY_PID,aiotDeviceBean.getPid());
        singleOperation.setMapArgs(ConstantModelValue.DATA_DESC_LIST,datas);
        singleOperation.setMapArgs(ConstantModelValue.VIEW_DESC_TEXT_LIST,descs);
        singleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT,aiotDeviceBean.getProductName() + " &");
        singleOperation.setDeviceId(ConstantModelValue.DEVICE_ID_AIOT);
        if(!aiotDeviceBean.isOnline()){
            singleOperation.setShowMode(ConstantModelValue.CAR_OPERATION_SHOW_TYPE_AIOT_OFFLINE);
        }
        return singleOperation;
    }

    private AiotCommandBean originToAiotCommandBean(String csId , String pid , AiotOperationBean aiotOperationBean){
        AiotCommandBean aiotCommandBean = new AiotCommandBean();
        aiotCommandBean.setAiid(aiotOperationBean.getAiid());
        aiotCommandBean.setCmd(aiotOperationBean.getCmd());
        aiotCommandBean.setPid(pid);
        aiotCommandBean.setPropList(aiotOperationBean.getPropList());
        aiotCommandBean.setSiid(aiotOperationBean.getSiid());
        aiotCommandBean.setCsId(csId);
        return aiotCommandBean;
    }

    @Override
    public boolean dispatchSingleActionOperation(SettingOperation settingOperation) {
        //用户本地添加的操作
        if(ConstantModelValue.OID_ACT_DEVICE_AIOT_USER.equals(settingOperation.getOperationId())){
            sendUserCommand(settingOperation.getListArgs().get(0));
        }else {
            //云端预设的操作
            sendCloudCommand(settingOperation.getListArgs().get(0));
        }
        return true;
    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {

        return false;
    }

    public String  findAiotIconByDesc(String desc){
        for(String aiotName : mIconMap.keySet()){
            if(desc.contains(aiotName)){
                return mIconMap.get(aiotName);
            }
        }
        return null;
    }

}
