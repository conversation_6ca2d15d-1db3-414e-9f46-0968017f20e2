package com.dfl.smartscene.ccs.model;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.WindowManager;

import androidx.lifecycle.MutableLiveData;

import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.systemview.ISystemView;
import com.dfl.api.da.systemview.SystemViewIdDef;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.core.SceneStateListener;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MWindowManager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/3/10 10:48
 * @description ：
 */
public class VideoController implements SceneStateListener {
    private static final String TAG = "VideoController";
    //全屏视频的操作
    private SettingOperation mSettingOperationVideo;
    private VideoFullView mImageOrVideoFullView;
    private boolean fullScreen;
    private List<String> videoNames;

    public boolean isFullScreen() {
        return fullScreen;
    }

    public VideoController(SettingOperation settingOperation){
        mSettingOperationVideo = settingOperation;
        SceneControlManager.getInstance().registerSceneStateListener(this);
    }

    public void requestOpenSystemWindow(){
        if (CustomApiManager.getInstance().getISystemView() != null) {
            try {
                CustomApiManager.getInstance().getISystemView().requestOpenSysView(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);
                CustomApiManager.getInstance().getIWideMode().reqWideMode(7);
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 扩展成全屏
     */
    public void requestAttachViewToSysWindow(){
        if(fullScreen){
            LogUtil.e(TAG, "current is already full screen");
            return;
        }
        fullScreen = true;
        RxBus.getDefault().post(new ImageOrVideoFullViewEvent(1));

        if (mImageOrVideoFullView == null) {
            mImageOrVideoFullView = new VideoFullView(ScenePatternApp.getInstance());
        }

        int type = CarConfigUtil.isCCU() || CarConfigUtil.isCCS() ? ConstantViewValue.SCENE_PATTERN_LAYER : WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                0, 0,
                type,
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,

                PixelFormat.TRANSLUCENT);

        params.gravity = Gravity.TOP | Gravity.START;
        if(!mImageOrVideoFullView.isAttachedToWindow()){
            WindowManager windowManager = (WindowManager) ScenePatternApp.getInstance().getSystemService(Context.WINDOW_SERVICE);

            windowManager.addView(mImageOrVideoFullView, params);
        }

        if(videoNames == null){
            List<String> listArgs = mSettingOperationVideo.getListArgs();
            videoNames = new ArrayList<>(listArgs);
        }

        HandlerUtil.getMainHandler().post(dataRunable);
    }

    public void reqOpenRelieveStressPatternFullView(int type) {
        ISystemView iSystemView = CustomApiManager.getInstance().getISystemView();
        MLog.d(TAG, "reqOpenRelieveStressPatternFullView: ");
        if (null == iSystemView) {
            MLog.e(TAG, "reqOpenRelieveStressPatternFullView: Error");
            return;
        }
        try {
            iSystemView.requestOpenSysView(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);
        } catch (NullServiceException e) {
            MLog.e(TAG, "requestOpenSysView catch NullServiceException");
        }
    }

    private final Runnable dataRunable = new Runnable() {
        @Override
        public void run() {
            if (mSettingOperationVideo != null) {
                mImageOrVideoFullView.setData(videoNames,DAModel.getInstance().getVideoController().getPlayIndex());
            }

        }
    };



    public void requestDetachViewFromSysWindow(){
        LogUtil.d(TAG,"requestDetachViewFromSysWindow");
        ISystemView iSystemView = CustomApiManager.getInstance().getISystemView();
        if (iSystemView != null) {
            try {
                iSystemView.requestSysViewClose(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0);
                CustomApiManager.getInstance().getIWideMode().reqWideMode(0);
            } catch (NullServiceException e) {
                e.printStackTrace();
            }
        }
        if (mImageOrVideoFullView != null) {
            if(mImageOrVideoFullView.isAttachedToWindow()){
                WindowManager windowManager = (WindowManager) ScenePatternApp.getInstance().getSystemService(Context.WINDOW_SERVICE);
                windowManager.removeView(mImageOrVideoFullView);

            }
            MWindowManager.getInstance().removeView(mImageOrVideoFullView.getContext(),mImageOrVideoFullView);
            mImageOrVideoFullView = null;
        }
        fullScreen = false;
        HandlerUtil.getMainHandler().removeCallbacks(dataRunable);
        RxBus.getDefault().post(new ImageOrVideoFullViewEvent(0));
    }

    private MutableLiveData<Integer> mPlayIndex = new MutableLiveData<>(); // 播放的数据索引
    private int mPlayProgress = 0; // 播放的进度

    public int getPlayIndex() {
        return mPlayIndex.getValue() == null ? 0 : mPlayIndex.getValue();
    }

    public MutableLiveData<Integer> getPlayIndexLiveData(){
        return mPlayIndex;
    }
    public void setPlayIndex(int index) {
        mPlayIndex.setValue(index);
    }
    public int getPlayProgress() {
        return mPlayProgress;
    }
    public void setPlayProgress(int mPlayProgress) {
        this.mPlayProgress = mPlayProgress;
    }

    @Override
    public void onScenePrepare() {

    }

    @Override
    public void onSceneStart() {

    }

    @Override
    public void onSceneStop() {

    }

    @Override
    public void onScenePause() {

    }

    @Override
    public void onSceneResume() {

    }

    @Override
    public void onSceneDestroy() {
        LogUtil.d(TAG, "on scene destroy");
        SceneControlManager.getInstance().unregisterSceneStateListener(this);
    }


}
