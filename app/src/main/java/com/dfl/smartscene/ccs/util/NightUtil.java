package com.dfl.smartscene.ccs.util;

import android.content.res.Configuration;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil;

/**
 * <AUTHOR>
 * @date ：2023/4/19 9:06
 * @description ：
 */
public class NightUtil {

    public static void notifyViewConfigChange(View view , Configuration configuration){
        refresh(view,configuration);
    }
    private static void refresh(@NonNull View view,Configuration configuration) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup)view;
            if (viewGroup.getChildCount() > 0) {
                for(int i = 0; i < viewGroup.getChildCount(); ++i) {
                    View child = viewGroup.getChildAt(i);
                    if (child instanceof ViewGroup) {
                        ResourceTagLoadUtil.refreshChildView(child);
                        refresh(child,configuration);
                    } else {
                        child.dispatchConfigurationChanged(configuration);
                    }
                }
            }
        }

    }

}
