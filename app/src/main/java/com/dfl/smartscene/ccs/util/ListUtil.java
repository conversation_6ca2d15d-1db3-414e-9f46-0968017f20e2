package com.dfl.smartscene.ccs.util;

import android.util.SparseArray;

import java.util.Collection;
import java.util.List;
import java.util.Map;


public class ListUtil {
    public ListUtil() {
    }

    public static boolean checkIndex(Collection data , int index){
        return data != null && index >= 0 && index < data.size();
    }

    public static boolean checkIndex(int[] objects, int index){
            return objects != null && index >= 0 && index < objects.length;
    }

    public static boolean isEmpty(SparseArray data) {
        return data == null || data.size() == 0;
    }

    public static boolean isEmpty(Collection data) {
        return data == null || data.isEmpty();
    }

    public static boolean isEmpty(Map data) {
        return data == null || data.isEmpty();
    }

    public static boolean isEmpty(Object[] data) {
        return data == null || data.length == 0;
    }

    public static boolean isSameSize(List<?>... data){
        if(isEmpty(data)){
            return false;
        }
        if(data[0] == null){
            return false;
        }
        int size = data[0].size();
        for(List<?> d : data){
            if (d == null || d.size() != size){
                return false;
            }
        }
        return true;
    }
}
