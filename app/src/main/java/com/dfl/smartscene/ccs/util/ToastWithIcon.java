package com.dfl.smartscene.ccs.util;

import android.content.Context;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ImageBase;
import com.iauto.uicontrol.TextBase;

/**
 * 
 */
public class ToastWithIcon extends LinearLayout {
    private static final String s_TAG = "ToastWithIcon";
    private int                  mImageID;
    private  int mStrID;
    TextBase mText = null;
    ImageBase mImageBase = null;


    public ToastWithIcon(Context context, int imageID, int strID){
        super(context);
        MLog.d(s_TAG, "crete");
        mImageID = imageID;
        mStrID = strID;
        initView(context);
    }

    /**
     * 初始化操作
     * 1.加载布局，创建View
     * 2.创建ViewModel对象
     * 3.DataBinding设置ViewModel
     * 4、DataBinding生命周期设置
     * 5.子View添加（按需操作）
     */
    private void initView(Context context){
        MLog.d(s_TAG, "onCreateView");
        LayoutInflater.from(context).inflate(R.layout.toast_with_icon, this);
        mText = findViewById(R.id.text_view_toast_with_icon);
        mText.setText(mStrID);
        mImageBase = findViewById(R.id.image_view_toast_with_icon);
        mImageBase.setImageString(mImageID);

        MLog.d(s_TAG, "onCreateView end");
    }

//    @Override
//    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
//        MLog.d(s_TAG, "onCreateAnimation transit = " + transit + " enter = " + enter + " nextAnim = " + nextAnim);
//        //表示是一个进入动作，比如add.show等
//        if (transit == FragmentTransaction.TRANSIT_FRAGMENT_OPEN) {
//            Animation enter_anim = AnimationUtils.loadAnimation(getContext(), R.anim.toast_fade_in);
//            return enter_anim;
//        }
//        //表示一个退出动作，比如出栈，hide，detach等
//        else if (transit == FragmentTransaction.TRANSIT_FRAGMENT_CLOSE) {
//            Animation exit_anim = AnimationUtils.loadAnimation(getContext(), R.anim.toast_fade_out);
//            exit_anim.setAnimationListener(new Animation.AnimationListener() {
//                @Override
//                public void onAnimationStart(Animation animation) {
//                    MLog.d(s_TAG, "onAnimationStart animation = " + animation);
//                    //动画开始
//                }
//                @Override
//                public void onAnimationRepeat(Animation animation) {
//                    MLog.d(s_TAG, "onAnimationRepeat");
//                    //动画循环
//                }
//                @Override
//                public void onAnimationEnd(Animation animation) {
//                    MLog.d(s_TAG, "onAnimationEnd animation = " + animation);
//                    //动画结束，移除当前的Toast
//                    getActivity().getSupportFragmentManager().beginTransaction().remove(ToastWithIcon.this).commit();
//                }
//            });
//            return exit_anim;
//        }
//        return null;
//    }

    public void setText(int strID) {
        if (null != mText) {
            mText.setText(strID);
        }
    }

    public void setIcon(int imageID) {
        if (null != mImageBase) {
            mImageBase.setImageString(imageID);
        }
    }
}
