package com.dfl.smartscene.ccs.view.baseadapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/11/15 9:36
 * @description ：
 */
public class ViewPagerAdapter extends PagerAdapter {
    private List<View> mViews;

    public ViewPagerAdapter() {
    }

    public ViewPagerAdapter(List<View> views) {
        mViews = views;
    }

    public void setViews(List<View> views) {
        mViews = views;

    }

    @Override   //返回要滑动的VIew的个数
    public int getCount() {
        return mViews.size();
    }

    @Override  //来判断pager的一个view是否和instantiateItem方法返回的object有关联
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view==object;
    }


    @Override  //从当前container中删除指定位置（position）的View;
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView(mViews.get(position));
    }

    @NonNull
    @Override  //第一：将当前视图添加到container中，第二：返回当前View
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        container.addView(mViews.get(position));
        return mViews.get(position);
    }
}
