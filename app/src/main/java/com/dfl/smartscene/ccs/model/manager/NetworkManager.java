package com.dfl.smartscene.ccs.model.manager;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.network.NetBean;
import com.dfl.dflcommonlibs.network.NetworkUtils;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.ccs.busevent.NetWorkEvent;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

/**
 * <AUTHOR>
 * @date ：2023/2/27 16:39
 * @description ：网络状态监听管理类
 */
public class NetworkManager {
    private static final String TAG = "NetworkManager";
    private static volatile NetworkManager sInstance;

    public static NetworkManager getInstance() {
        if (null == sInstance) {
            synchronized (NetworkManager.class) {
                if (null == sInstance) {
                    sInstance = new NetworkManager();
                }
            }
        }
        return sInstance;
    }

    public void initNetworkListener(){
        //初始化网络监听
        NetworkUtils.registerNetWorkListener(ScenePatternApp.getInstance(), new NetworkUtils.OnNetWorkChangeListener() {
            @Override
            protected void onNetWorkChanged(NetBean netBean) {
                super.onNetWorkChanged(netBean);
            }

            @Override
            protected void onNetWorkChanged(boolean status) {
                LogUtil.d(TAG,"onNetWorkChanged : " + status);
                super.onNetWorkChanged(status);
                if(netStatus != status){
                    netStatus = status;
                    RxBus.getDefault().post(new NetWorkEvent(status));
                }
            }
        });

    }

    public boolean hasNetwork(){
        return NetworkUtils.getNetworkAvailable(ScenePatternApp.getInstance());
    }

    private boolean netStatus;

}
