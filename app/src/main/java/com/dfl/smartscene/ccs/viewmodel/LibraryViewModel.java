package com.dfl.smartscene.ccs.viewmodel;


import static com.dfl.smartscene.ccs.model.SceneDataModel.KEY_MMKV_SQUARE_SCENE;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.dflcommonlibs.rxbus.Subscribe;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.busevent.ServiceInitEvent;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.util.PermissionManager;
import com.tencent.mmkv.MMKV;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/8/30 9:00
 * @description ：
 */
public class LibraryViewModel extends ViewModel {
    private static final String TAG = "LibraryViewModel";
    // 防止内存泄漏
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private MutableLiveData<List<SceneCategory>> mSceneCategories = new MutableLiveData<>();

    public MutableLiveData<List<SceneCategory>> getSceneCategories() {
        return mSceneCategories;
    }

    public MMKV mMMKV = MMKV.defaultMMKV();

    public LibraryViewModel() {
        RxBus.getDefault().register(this);
    }

    private boolean requestButDaidNull = false;

    /**
     * 请求首页的场景数据
     */
    public void requestLibraryScenes() {
        if (DriveModel.getInstance().getDaid() == null || "".equals(DriveModel.getInstance().getDaid()) || !PermissionManager.checkLocalPermission()) {
            requestButDaidNull = true;
            LogUtil.d(TAG, "requestLibraryScenes: requestButDaidNull");
            mSceneCategories.setValue(null);
            return;
        }
        SceneDataModel.getInstance().requestCloudSceneList().observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<List<SceneCategory>>() {
            @Override
            public void onSubscribe(Disposable d) {
                mCompositeDisposable.add(d);
            }

            @Override
            public void onNext(List<SceneCategory> sceneCategories) {
                mSceneCategories.setValue(sceneCategories);
            }

            @Override
            public void onError(Throwable e) {
                mSceneCategories.setValue(null);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    /**
     * 刷新首页广场的数据
     */
    public void refreshLibraryScenes(){
        //先清理本地数据
        mMMKV.remove(KEY_MMKV_SQUARE_SCENE);
        requestLibraryScenes();
    }


    @Subscribe
    public void onServiceInitEvent(ServiceInitEvent serviceInitEvent) {
        if (mSceneCategories.getValue() == null
                && requestButDaidNull
                && PermissionManager.checkLocalPermission()) {
            LogUtil.d(TAG, "onCustomapiEvent");
            requestButDaidNull = false;
            requestLibraryScenes();
        }
    }

    /**
     * 判断AIOT的广告显示时间是否在忽略的有效期内
     * @return true需要忽略
     */
    public boolean isAdDisplayIgnore(){
        if(mMMKV.containsKey(ConstantViewValue.AIOT_AD_IGNORE_TIME)){
            boolean atIgnoreDuration = (System.currentTimeMillis() - mMMKV.decodeLong(ConstantViewValue.AIOT_AD_IGNORE_TIME)) < ConstantViewValue.AIOT_AD_IGNORE_DURATIO;
            if(atIgnoreDuration){
                return true;
            }
        }
        return false;
    }

    /**
     * 保存AIOT广告被忽略的时间
     */
    public void encodeAdDisplayIgnoreTime(){
        mMMKV.encode(ConstantViewValue.AIOT_AD_IGNORE_TIME , System.currentTimeMillis());
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        mCompositeDisposable.dispose();
        RxBus.getDefault().unRegister(this);

    }
}
