package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneConditionListEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneConditionListDao {

    @Insert
    void insertStudent(SceneConditionListEntity item);

    @Delete
    void deleteStudent(SceneConditionListEntity item);

    @Update
    void updateStudent(SceneConditionListEntity item);

    @Query("SELECT * FROM scene_condition_list")
    Observable<List<SceneConditionListEntity>> queryAll();

    @Query("SELECT * FROM scene_condition_list WHERE :mSceneId='' or mSceneId= :mSceneId")
    Observable<List<SceneConditionListEntity>> queryBySceneId(String mSceneId);

    @Query("SELECT * FROM scene_condition_list WHERE :mDeviceId='' or mSceneId= :mDeviceId")
    Observable<List<SceneConditionListEntity>> queryByDeviceId(String mDeviceId);

    @Query("SELECT * FROM scene_condition_list WHERE :mOperationId='' or mOperationId= :mOperationId")
    Observable<List<SceneConditionListEntity>> queryByOperationId(String mOperationId);

    @Query("SELECT * FROM scene_condition_list WHERE mSceneId in (:mSceneIds)")
    Observable<List<SceneConditionListEntity>> queryBySceneIds(String[] mSceneIds);

}
