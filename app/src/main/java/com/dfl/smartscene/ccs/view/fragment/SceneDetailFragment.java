package com.dfl.smartscene.ccs.view.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.dfl.dflcommonlibs.log.LogUtil;
import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.RecyclerView;


import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.MyClickableSpan;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.busevent.CollectEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.util.AntiShakeUtil;
import com.dfl.smartscene.ccs.util.CloneUtil;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.util.ImageUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.SceneDetailVrAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorActionAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorAdapter;
import com.dfl.smartscene.ccs.view.adapter.SceneEditorTimeStateAdapter;
import com.dfl.smartscene.ccs.view.base.SimpleBaseFragment;
import com.dfl.smartscene.ccs.view.commonview.OverTopContentView;
import com.dfl.smartscene.ccs.viewmodel.CollectViewModel;
import com.dfl.smartscene.ccs.viewmodel.LibraryDetailViewModel;
import com.dfl.smartscene.ccs.viewmodel.MenuAiotViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2024/11/22 10:48
 * @description ：场景详情页
 */
public class SceneDetailFragment extends SimpleBaseFragment {
    private static final String TAG = SceneDetailFragment.class.getSimpleName();
    private ImageView mImageViewSceneIcon;//场景图标
    private TextView mTextViewTitle;//场景名称
    private TextView mTextViewAiotNotice;
    private View mViewAiotNotice;//未购买设备提示语-可见即可说触发
    private TextView mTextViewDes;//场景描述
    private SceneBean mSceneBean;//当前展示的场景
    RecyclerView recyclerViewState;//且满足全部条件列表
    private LinearLayout mConstraintLayoutConditionState;//触发条件的layout，可被收起
    private TextView mConditionState;//触发条件内容
    private ImageView mConditionStateDel;//触发条件内容删除按钮
    private LinearLayout layoutState;//状态条件的layout，可被收起
    private SceneEditorAdapter mAdapterTimeState;//包括日期和时间的状态条件
    private SceneDetailVrAdapter mAdapterState;//状态条件
    private SceneEditorActionAdapter mAdapterAction;//执行动作
    private SceneDetailVrAdapter mAdapterVr;
    private SettingOperation conditionOperation;//触发条件数据
    private SettingOperation dayOperation;//日期状态条件数据
    private SettingOperation timeOperation;//时间状态条件数据
    List<SettingOperation> unEditActionOperation = new ArrayList<>();//不可见的执行操作
    private MenuAiotViewModel mMenuAiotViewModel;
    private CollectViewModel mCollectViewModel;

    private static final @DimenRes int COLUMN_SPACE = ConstantViewValue.COMMON_SHOW_SPACE;
    private static final @DimenRes int LINE_SPACE = R.dimen.y_px_20;
    private TextView mTextViewCollect;//收藏描述
    private View content_vr;
    private LibraryDetailViewModel mLibraryDetailViewModel;

    private int mTextViewTitleMagrinTopInitalValue;
    private View mLayoutCollect;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_scene_detail, container, false);
    }

    @Override
    protected void init() {
        if (getArguments() == null) {
            mSceneBean = new SceneBean();
        } else {
            mSceneBean = (SceneBean) CloneUtil.cloneObject(getArguments().getSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN));
            if (mSceneBean == null) {
                mSceneBean = new SceneBean();
            }
        }
        mMenuAiotViewModel = new ViewModelProvider(this).get(MenuAiotViewModel.class);
        mLibraryDetailViewModel = new ViewModelProvider(this).get(LibraryDetailViewModel.class);
        mCollectViewModel = new ViewModelProvider(this).get(CollectViewModel.class);
    }

    @Override
    protected void initView(View view) {
        OverTopContentView overTopContentView = new OverTopContentView();
        overTopContentView.initView(view);
        overTopContentView.setTitle("场景详情");
        overTopContentView.getBackButton().setOnClickListener(v -> {
            Navigation.findNavController(v).navigateUp();
        });
        content_vr = view.findViewById(R.id.content_vr);
        //执行按钮
        ConstraintLayout mLayoutExecute = view.findViewById(R.id.layout_library_detail_execute_button);
        mTextViewTitle = view.findViewById(R.id.textview_scene_editor_name);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mTextViewTitle.getLayoutParams();
        mTextViewTitleMagrinTopInitalValue = params.topMargin;
        mTextViewDes = view.findViewById(R.id.textview_scene_editor_describe);
        //收藏按钮
        mLayoutCollect = view.findViewById(R.id.clickarea_library_detail_collect_button);
        mConstraintLayoutConditionState = view.findViewById(R.id.layout_scene_editor_condition_state);
        mConditionState = view.findViewById(R.id.textview_item_scene_editor_desc);
        mConditionStateDel = view.findViewById(R.id.imagebutton_item_scene_editor_delete);
        layoutState = view.findViewById(R.id.layout_scene_editor_state);
        mTextViewCollect = view.findViewById(R.id.textview_library_detail_collect_button);
        mImageViewSceneIcon = view.findViewById(R.id.imageview_scene_editor_icon);
        mTextViewAiotNotice = view.findViewById(R.id.textview_libary_detail_buy_notice);
        mViewAiotNotice = view.findViewById(R.id.view_buy_notice);
        initVrRecyclerView(view);
        initTimeStateRecyclerView(view);
        initStateRecyclerView(view);
        initActionRecyclerView(view);
        if(mSceneBean.isCollectState()){
            mTextViewCollect.setText(getString(R.string.string_library_detail_uncollect_button));
        }else {
            mTextViewCollect.setText(getString(R.string.string_library_detail_collect_button));
        }
        mLibraryDetailViewModel.requestSceneUnbuyDevices(mSceneBean.getActionOperations());
        //设置可见即可说标签
        mLayoutCollect.setContentDescription(mTextViewCollect.getText().toString());
        mLayoutExecute.setContentDescription(getResources().getString(R.string.string_library_detail_excute_button));
        //执行按钮点击监听
        mLayoutExecute.setOnClickListener(v-> {
            //埋点：点击场景模式
            LogUtil.d("LibrarySceneDetailDialog", "点击试用");
            BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_use));
            DialogManager.showExcuteSceneDialog(mSceneBean);
        });
        //收藏按钮点击监听
        mLayoutCollect.setOnClickListener(v -> {
            if(AntiShakeUtil.check(v)){
                return;
            }
            if(mSceneBean.isCollectState()){
                ViewControlManager.uncollectScene(mSceneBean.getSceneCategoryId(), mSceneBean);
                LogUtil.d("LibrarySceneDetailDialog", "点击取消收藏");
                BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_un_collect));
            }else {
                ViewControlManager.collectScene(mSceneBean.getSceneCategoryId(), mSceneBean);
                //埋点：点击场景模式
                LogUtil.d("LibrarySceneDetailDialog", "点击收藏");
                BigDataManager.getInstance().writeEventMod3(view.getContext().getString(R.string.scene_click_collect));
            }
        });
    }

    /**
     * 初始化执行动作视图
     *
     * @param view 布局
     */
    private void initActionRecyclerView(View view) {
        RecyclerView recyclerViewAction = view.findViewById(R.id.recyclerview_scene_editor_action);
        RecyclerViewUtil.setGridRecycleView(recyclerViewAction, RecyclerView.VERTICAL, 4, R.dimen.x_px_24, R.dimen.y_px_24);
        mAdapterAction = new SceneEditorActionAdapter();
        mAdapterAction.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
        recyclerViewAction.setAdapter(mAdapterAction);
        recyclerViewAction.setHasFixedSize(true);
    }

    /**
     * 初始化时间状态条件视图
     *
     * @param view 布局
     */
    private void initTimeStateRecyclerView(View view) {
        RecyclerView recyclerViewTimeState = view.findViewById(R.id.recyclerview_scene_editor_time_state);
        RecyclerViewUtil.setGridRecycleView(recyclerViewTimeState, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterTimeState = new SceneEditorTimeStateAdapter();
        mAdapterTimeState.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
        recyclerViewTimeState.setAdapter(mAdapterTimeState);
    }

    private void initVrRecyclerView(View view) {
        RecyclerView recyclerViewVr = view.findViewById(R.id.recyclerview_scene_editor_vr);
        RecyclerViewUtil.setGridRecycleView(recyclerViewVr, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterVr = new SceneDetailVrAdapter();
        mAdapterVr.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
        recyclerViewVr.setAdapter(mAdapterVr);
    }

    /**
     * 初始化状态条件视图
     *
     * @param view 布局
     */
    private void initStateRecyclerView(View view) {
        recyclerViewState = view.findViewById(R.id.recyclerview_scene_editor_state);
        RecyclerViewUtil.setGridRecycleView(recyclerViewState, RecyclerView.VERTICAL, 2, COLUMN_SPACE, LINE_SPACE);
        mAdapterState = new SceneDetailVrAdapter();
        mAdapterState.setSceneType(ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
        recyclerViewState.setAdapter(mAdapterState);
    }

    @Override
    protected void initObserver() {
        mMenuAiotViewModel.getAiotModifyOperation().observe(getViewLifecycleOwner(), new Observer<MenuAiotViewModel.PosSingleOperation>() {
            @Override
            public void onChanged(MenuAiotViewModel.PosSingleOperation posSingleOperation) {
                if (posSingleOperation == null){
                    String toastText = "您还没有此设备" + ",前往购买";
                    ToastManager.showSpanToast(toastText, "前往购买", new MyClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            ViewControlManager.openAiotStorePage();
                        }
                    },getView());

                    return;
                }
//                OperationBaseDialogFragment operationBaseDialogFragment = OperationBaseViewFactory.creatOperationContainer(OPERATION_TYPE_ACTION, posSingleOperation.getPos(), posSingleOperation.getSettingOperation(), posSingleOperation.getSingleOperation());
//                operationBaseDialogFragment.setOperationChangeListerner(SceneDetailFragment.this);
//                operationBaseDialogFragment.show(getChildFragmentManager(), null);
            }
        });

        mLibraryDetailViewModel.getAiotDesc().observe(getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if(s != null && !"".equals(s)){
                    SpannableString spannableString = ToastManager.getSpanString("您还没有" + s + "点击前往购买,或打开手机端智联日产APP选购", "前往购买", new MyClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            ViewControlManager.openAiotStorePage();
                        }
                    });
                    mTextViewAiotNotice.setVisibility(View.VISIBLE);
                    mTextViewAiotNotice.setText(spannableString);
                    mTextViewAiotNotice.setMovementMethod(LinkMovementMethod.getInstance());
                    mViewAiotNotice.setVisibility(View.VISIBLE);
                    mViewAiotNotice.setOnClickListener(v -> {ViewControlManager.openAiotStorePage();});
                }
            }
        });
        mCollectViewModel.getCollectEventMutableLiveData().observe(getViewLifecycleOwner(), new Observer<CollectEvent>() {
            @Override
            public void onChanged(CollectEvent collectEvent) {
                if(collectEvent != null){
                    LogUtil.d(TAG, "getCollectEventMutableLiveData : "+collectEvent.isStatus());
                    if (collectEvent.getSceneId().equals(mSceneBean.getSceneId()) && (collectEvent.getCategoryId().equals(mSceneBean.getSceneCategoryId()) || ConstantModelValue.CATEGORY_ID_COLLECT.equals(collectEvent.getCategoryId()))) {
                        mSceneBean.setCollectState(collectEvent.isStatus());
                        if (mSceneBean.isCollectState()) {
                            mTextViewCollect.setText(getString(R.string.string_library_detail_uncollect_button));
                        } else {
                            mTextViewCollect.setText(getString(R.string.string_library_detail_collect_button));
                        }
                        mLayoutCollect.setContentDescription(mTextViewCollect.getText().toString());
                    }
                }
            }
        });

    }

    @Override
    protected void initData() {
        if (mSceneBean.getSceneName() != null && !mSceneBean.getSceneName().equals("")) {
            mTextViewTitle.setText(mSceneBean.getSceneName());
        } else {
            mTextViewTitle.setText(SpecialBeanFactory.productSceneName());
        }
        mTextViewDes.setText(mSceneBean.getSceneDesc());
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mTextViewTitle.getLayoutParams();
        if(TextUtils.isEmpty(mSceneBean.getSceneDesc())){
            params.topMargin = mTextViewTitleMagrinTopInitalValue *2;
        }else {
            params.topMargin = mTextViewTitleMagrinTopInitalValue;
        }
        mTextViewTitle.setLayoutParams(params);
        if (mSceneBean.getSceneIcon() != null){
            if (ConstantModelValue.SCENE_ID_OLD_WORK.equals(mSceneBean.getSceneId())){
                mImageViewSceneIcon.setImageResource(R.drawable.icon_scene_company);
            }else if (ConstantModelValue.SCENE_ID_OLD_HOME.equals(mSceneBean.getSceneId())){
                mImageViewSceneIcon.setImageResource(R.drawable.icon_scene_home);
            }else if (ConstantModelValue.SCENE_ID_OLD_CHILD.equals(mSceneBean.getSceneId())){
                mImageViewSceneIcon.setImageResource(R.drawable.icon_scene_qinzi);
            }else if (ConstantModelValue.SCENE_ID_OLD_SLEEP.equals(mSceneBean.getSceneId())){
                mImageViewSceneIcon.setImageResource(R.drawable.icon_scene_sleep);
            }else {
                mImageViewSceneIcon.setImageResource(R.drawable.icon_scene_wake);
            }
        }else {
            ImageUtil.loadImageUrlNoCorner(mImageViewSceneIcon, "", R.drawable.icon_edit_scene_64x64);
        }

        List<SettingOperation> vrOperations = new ArrayList<>();

        //初始化展示触发条件，筛选出语音触发条件
        for (SettingOperation settingOperation : mSceneBean.getConditionOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)) {
                vrOperations.add(settingOperation);
            } else {
                conditionOperation = settingOperation;
            }
        }
        mAdapterVr.setDataList(vrOperations);
        if (vrOperations.isEmpty()){
            content_vr.setVisibility(View.GONE);
        }else {
            content_vr.setVisibility(View.VISIBLE);
        }
        List<SettingOperation> stateOperations = new ArrayList<>();
        List<SettingOperation> timeStateOperations = new ArrayList<>();
        //筛选出时间状态条件和日期状态条件
        for (SettingOperation settingOperation : mSceneBean.getStateOperations()) {
            if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK)) {
                timeOperation = settingOperation;
            } else if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY)) {
                dayOperation = settingOperation;
            } else {
                stateOperations.add(settingOperation);
            }
        }
        //如果时间状态为空，如新建自定义时，添加默认的时间和日期状态，全天+每天
        if (timeOperation == null) {
            timeOperation = SpecialBeanFactory.productTimeOperation();
        }
        if (dayOperation == null) {
            dayOperation = SpecialBeanFactory.productDayOperation();
        }
        timeStateOperations.add(dayOperation);
        timeStateOperations.add(timeOperation);
        //状态条件
        if (conditionOperation == null){
            mConstraintLayoutConditionState.setVisibility(View.GONE);
        }else {
            mConditionStateDel.setVisibility(View.GONE);
            mConditionState.setText(conditionOperation.getDesc());
            mConstraintLayoutConditionState.setVisibility(View.VISIBLE);
        }
        //触发条件
        if (stateOperations.isEmpty()){
            layoutState.setVisibility(View.GONE);
        }else {
            layoutState.setVisibility(View.VISIBLE);
            mAdapterState.setDataList(stateOperations);
        }
        mAdapterTimeState.setDataList(timeStateOperations);

        setActionDataList(mSceneBean.getActionOperations());
    }


    /**
     * 向执行动作视图设置数据时需添加“添加操作”卡片
     *
     * @param operations 执行操作
     */
    private void setActionDataList(List<SettingOperation> operations) {
        unEditActionOperation = new ArrayList<>();
        List<SettingOperation> editOperations = new ArrayList<>();
        for (SettingOperation settingOperation : operations) {
            if (settingOperation.getShowType() == ConstantModelValue.SCENE_OPERATION_SHOW_TYPE_NORMAL) {
                editOperations.add(settingOperation);
            } else {
                unEditActionOperation.add(settingOperation);
            }
        }
        mAdapterAction.setDataList(editOperations);
    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {}

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {}

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {}

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {}

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if(getView() == null){
            return;
        }
        RecyclerView recyclerViewVr = getView().findViewById(R.id.recyclerview_scene_editor_vr);
        RecyclerView recyclerViewTimeState = getView().findViewById(R.id.recyclerview_scene_editor_time_state);
        RecyclerView recyclerViewState = getView().findViewById(R.id.recyclerview_scene_editor_state);
        RecyclerView recyclerViewAction = getView().findViewById(R.id.recyclerview_scene_editor_action);

        if (recyclerViewVr!=null) {
            recyclerViewVr.removeAllViews();
            recyclerViewVr.removeAllViewsInLayout();
            recyclerViewVr.setAdapter(mAdapterVr);
            recyclerViewVr.getRecycledViewPool().clear();
            mAdapterVr.setDataList(mAdapterVr.getDataList());
        }

        if (recyclerViewTimeState!=null) {
            recyclerViewTimeState.removeAllViews();
            recyclerViewTimeState.removeAllViewsInLayout();
            recyclerViewTimeState.setAdapter(mAdapterTimeState);
            recyclerViewTimeState.getRecycledViewPool().clear();
            mAdapterTimeState.setDataList(mAdapterTimeState.getDataList());
        }

        if (recyclerViewState!=null) {
            recyclerViewState.removeAllViews();
            recyclerViewState.removeAllViewsInLayout();
            recyclerViewState.setAdapter(mAdapterState);
            recyclerViewState.getRecycledViewPool().clear();
            mAdapterState.setDataList(mAdapterState.getDataList());
        }

        if (recyclerViewAction!=null) {
            recyclerViewAction.removeAllViews();
            recyclerViewAction.removeAllViewsInLayout();
            recyclerViewAction.setAdapter(mAdapterAction);
            recyclerViewAction.getRecycledViewPool().clear();
            mAdapterAction.setDataList(mAdapterAction.getDataList());
        }

        ConstraintLayout collectBtn = getView().findViewById(R.id.layout_library_detail_collect_button);
        if (collectBtn != null) {
            collectBtn.setBackground(getResources().getDrawable(R.drawable.bg_button_positive));
        }
        ConstraintLayout tryBtn = getView().findViewById(R.id.layout_library_detail_execute_button);
        if (tryBtn != null) {
            tryBtn.setBackground(getResources().getDrawable(R.drawable.bg_button_negative));
        }
    }
}
