package com.dfl.smartscene.ccs.constant;
//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

import com.dfl.api.util.VehicleType_623;
import com.dfl.api.util.VehicleType_ALL;
import com.dfl.api.util.VehicleType_CCS5;

public class Constants {
    public static final String FS_ACTIVE = "active";
    public static final String FS_UNACTIVE = "unactive";
    public static final String FS_UNAVAILABLE = "unavailable";
    public static final String FS_ERROR = "error";
    public static final String ACTION_SHUT_DOWN_PREPARE = "com.hsae.service.ACTION_SHUT_DOWN_PREPARE";
    public static final String VEHICLETYPE_ALL = "VEHICLETYPE_ALL";
    public static final String VEHICLETYPE_CCS5 = "VEHICLETYPE_CCS5";
    public static final String VEHICLETYPE_623 = "VEHICLETYPE_623";
    public static final String CPSP = "_cpsp";
    public static final String ADAPTER = "_adapter";
    public static final String APICHECKER = "_apichecker";
    public static final String DFL_BASE = "com_dfl_api_base";
    public static final String DFL_VEHICLE = "com_dfl_api_vehicle";
    public static final String DFL_CAR_ADAPTER_SERVICE = "com_dfl_api_iadapter_service";
    public static final String DFL_API_CHECKER_SERVICE = "com_dfl_api_iapichecker_service";
    public static final String DFL_API_CARTYPECONFIGURATION_SERVICE = "com_dfl_api_cartypeconfiguration_service";
    @VehicleType_ALL
    public static final String DFL_BASE_APPCOMMON_APPCOMMON_SERVICE = "com_dfl_api_base_appcommon_appcommon_service";
    @VehicleType_CCS5
    public static final String DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE = "com_dfl_api_base_appcommon_media_media_service";
    @VehicleType_CCS5
    public static final String DFL_BASE_APPCOMMON_GESTURE_SERVICE = "com_dfl_api_base_appcommon_gesture_service";
    @VehicleType_CCS5
    public static final String DFL_BASE_APPCOMMON_CONFIGURATION_SERVICE = "com_dfl_api_base_appcommon_configuration_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_DOORANDTRUNK_DOORANDTRUNK_SERVICE = "com_dfl_api_vehicle_doorandtrunk_doorandtrunk_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_BACKDOOR_BACKDOOR_SERVICE = "com_dfl_api_vehicle_backdoor_backdoor_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_MIRRORFOLDER_MIRRORFOLDER_SERVICE = "com_dfl_api_vehicle_mirrorfolder_mirrorfolder_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_HVAC_HVAC_SERVICE = "com_dfl_api_vehicle_hvac_hvac_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_CHARGE_CHARGE_SERVICE = "com_dfl_api_vehicle_charge_charge_service";
    @VehicleType_CCS5
    public static final String DFL_VEHICLE_ENERGYCONSUMPTION_FUELCONSUMPTION_SERVICE = "com_dfl_api_vehicle_energyconsumption_fuelconsumption_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_ENERGYCONSUMPTION_HISTORY_SERVICE = "com_dfl_api_vehicle_energyconsumption_history_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_BCM_ATMOSPHERELAMP_ATMOSPHERELAMP_SERVICE = "com_dfl_api_vehicle_bcm_atmospherelamp_atmospherelamp_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_SEAT_SEAT_SERVICE = "com_dfl_api_vehicle_seat_seat_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_BCM_WINDOW_WINDOW_SERVICE = "com_dfl_api_vehicle_bcm_window_window_service";
    @VehicleType_CCS5
    public static final String DFL_VEHICLE_VEHICLESTATUS_VEHICLESTATUS_SERVICE = "com_dfl_api_vehicle_vehiclestatus_vehiclestatus_service";
    @VehicleType_CCS5
    public static final String DFL_VEHICLE_METER_METERDISPLAY_SERVICE = "com_dfl_api_vehicle_meter_meterdisplay_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_BCM_WIPER_WIPER_SERVICE = "com_dfl_api_vehicle_bcm_wiper_wiper_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_POWER_DRIVER_DRIVER_SERVICE = "com_dfl_api_vehicle_power_driver_driver_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_BCM_LAMP_LAMP_SERVICE = "com_dfl_api_vehicle_bcm_lamp_lamp_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_ADAS_ADAS_SERVICE = "com_dfl_api_vehicle_adas_adas_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_BCM_SMARTVEHICLE_SMARTVEHICLE_SERVICE = "com_dfl_api_vehicle_bcm_smartvehicle_smartvehicle_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_TIRE_TIRE_SERVICE = "com_dfl_api_vehicle_tire_tire_service";
    @VehicleType_ALL
    public static final String DFL_VEHICLE_CARINFO_CARINFO_SERVICE = "com_dfl_api_vehicle_carinfo_carinfo_service";
    @VehicleType_CCS5
    public static final String DFL_VEHICLE_HUD_HUDSETTING_SERVICE = "com_dfl_api_vehicle_hud_hudsetting_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_ECU_ECUSETTING_SERVICE = "com_dfl_api_vehicle_ecu_ecusetting_service";
    @VehicleType_623
    public static final String DFL_VEHICLE_BCM_WEATHER_SENSORWEATHER_SERVICE = "com_dfl_api_vehicle_bcm_weather_sensorweather_service";
    @VehicleType_623
    public static final String DFL_DA_HARDKEY_CUSTOMKEY_SERVICE = "com_dfl_api_da_hardkey_customkey_service";
    @VehicleType_CCS5
    public static final String DFL_DA_OS_SYSTEM_SERVICE = "com_dfl_api_da_os_system_service";
    @VehicleType_ALL
    public static final String DFL_DA_VERSION_VERSION_SERVICE = "com_dfl_api_da_version_version_service";
    @VehicleType_CCS5
    public static final String DFL_DA_SETTING_SETTING_SERVICE = "com_dfl_api_da_setting_setting_service";
    @VehicleType_CCS5
    public static final String DFL_DA_SETTING_IVC_SERVICE = "com_dfl_api_da_setting_ivc_service";
    @VehicleType_CCS5
    public static final String DFL_DA_SETTING_SETTINGSOUNDEFFECT_SERVICE = "com_dfl_api_da_setting_settingsoundeffect_service";
    @VehicleType_623
    public static final String DFL_DA_SETTING_SETTINGCARSOUND_SERVICE = "com_dfl_api_da_setting_settingcarsound_service";
    @VehicleType_623
    public static final String DFL_DA_SETTING_SETTINGSOUNDTHEME_SERVICE = "com_dfl_api_da_setting_settingsoundtheme_service";
    @VehicleType_ALL
    public static final String DFL_DA_SETTING_SETTINGPHONECHARGE_SERVICE = "com_dfl_api_da_setting_settingphonecharge_service";
    @VehicleType_623
    public static final String DFL_DA_SETTING_SETTINGVEHICLESOUND_SERVICE = "com_dfl_api_da_setting_settingvehiclesound_service";
    @VehicleType_623
    public static final String DFL_DA_SETTING_SETTINGCUSTOMBUTTON_SERVICE = "com_dfl_api_da_setting_settingcustombutton_service";
    @VehicleType_CCS5
    public static final String DFL_DA_CAMERA_CAMERA_SERVICE = "com_dfl_api_da_camera_camera_service";
    @VehicleType_CCS5
    public static final String DFL_DA_OTA_OTA_SERVICE = "com_dfl_api_da_ota_ota_service";
    @VehicleType_ALL
    public static final String DFL_DA_ECALL_ECALL_SERVICE = "com_dfl_api_da_ecall_ecall_service";
    @VehicleType_CCS5
    public static final String DFL_DA_WIDEMODE_WIDEMODE_SERVICE = "com_dfl_api_da_widemode_widemode_service";
    @VehicleType_CCS5
    public static final String DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE = "com_dfl_api_da_systemview_systemview_service";
    @VehicleType_623
    public static final String DFL_DA_SYSTEMVIEW_DAWINDOW_SERVICE = "com_dfl_api_da_systemview_dawindow_service";
    @VehicleType_CCS5
    public static final String DFL_DA_POPUP_POPUP_SERVICE = "com_dfl_api_da_popup_popup_service";
    @VehicleType_CCS5
    public static final String DFL_DA_FACE_FACE_SERVICE = "com_dfl_api_da_face_face_service";
    @VehicleType_CCS5
    public static final String DFL_DA_DVR_DVR_SERVICE = "com_dfl_api_da_dvr_dvr_service";
    @VehicleType_ALL
    public static final String DFL_DA_SOUNDEFFECT_SOUND_SERVICE = "com_dfl_api_da_soundeffect_sound_service";
    @VehicleType_CCS5
    public static final String DFL_DA_BT_PBAPCLIENTSYNC_SERVICE = "com_dfl_api_da_bt_pbapclientsync_service";
    @VehicleType_CCS5
    public static final String DFL_DA_BT_PHONENUM_SERVICE = "com_dfl_api_da_bt_phonenum_service";
    @VehicleType_CCS5
    public static final String DFL_DA_MASK_MASK_SERVICE = "com_dfl_api_da_mask_mask_service";
    @VehicleType_ALL
    public static final String DFL_DA_LOG_LOG_SERVICE = "com_dfl_api_da_log_log_service";
    @VehicleType_CCS5
    public static final String DFL_DA_APN_APN_SERVICE = "com_dfl_api_da_apn_apn_service";
    @VehicleType_CCS5
    public static final String DFL_DA_VIRTUALKEY_VIRTUALINPUTKEY_SERVICE = "com_dfl_api_da_virtualkey_virtualinputkey_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_SYS_USERCENTER_SERVICE = "com_dfl_api_app_usercenter_sys_usercenter_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_CP_ACCOUNTINTERFLOW_SERVICE = "com_dfl_api_app_usercenter_cp_accountinterflow_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_SETTING_USERCENTERSETTING_SERVICE = "com_dfl_api_app_usercenter_setting_usercentersetting_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_SCHEDULE_SCHEDULE_SERVICE = "com_dfl_api_app_usercenter_schedule_schedule_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_WEATHER_WEATHER_SERVICE = "com_dfl_api_app_usercenter_weather_weather_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_DETECTION_VEHICLEDETECTION_SERVICE = "com_dfl_api_app_usercenter_detection_vehicledetection_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_COMMON_COMMON_SERVICE = "com_dfl_api_app_usercenter_common_common_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_MINIPROGRAM_PROGRAM_SERVICE = "com_dfl_api_app_usercenter_miniprogram_program_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_VOICE_VOICE_SERVICE = "com_dfl_api_app_usercenter_voice_voice_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_PROTOCOL_CCSVUSERCENTERPROTOCOL_SERVICE = "com_dfl_api_app_usercenter_protocol_ccsvusercenterprotocol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_USERCENTER_PROTOCOL_USERCENTERPROTOCOL_SERVICE = "com_dfl_api_app_usercenter_protocol_usercenterprotocol_service";
    @VehicleType_ALL
    public static final String DFL_APP_EVENTTRACK_EVENTTRACK_SERVICE = "com_dfl_api_app_eventtrack_eventtrack_service";
    @VehicleType_CCS5
    public static final String DFL_APP_MESSAGEBOX_MESSAGEBOX_SERVICE = "com_dfl_api_app_messagebox_messagebox_service";
    @VehicleType_CCS5
    public static final String DFL_APP_MESSAGECENTER_PROTOCOL_MESSAGECENTERPROTOCOL_SERVICE = "com_dfl_api_app_messagecenter_protocol_messagecenterprotocol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_OTA_OTA_SERVICE = "com_dfl_api_app_ota_ota_service";
    @VehicleType_CCS5
    public static final String DFL_APP_GUISTORE_SKIN_SERVICE = "com_dfl_api_app_guistore_skin_service";
    @VehicleType_CCS5
    public static final String DFL_APP_GUISTORE_VPASKIN_SERVICE = "com_dfl_api_app_guistore_vpaskin_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_NAVI_NAVI_SERVICE = "com_dfl_api_app_navi_navi_navi_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_ARNAVI_ARNAVI_SERVICE = "com_dfl_api_app_navi_arnavi_arnavi_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_CRUISE_CRUISE_SERVICE = "com_dfl_api_app_navi_cruise_cruise_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_SETTING_SETTING_SERVICE = "com_dfl_api_app_navi_setting_setting_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_SEARCH_SEARCH_SERVICE = "com_dfl_api_app_navi_search_search_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_ACCOUNT_ACCOUNT_SERVICE = "com_dfl_api_app_navi_account_account_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_EHP_EHP_SERVICE = "com_dfl_api_app_navi_ehp_ehp_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_ROUTE_ROUTE_SERVICE = "com_dfl_api_app_navi_route_route_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_AOS_AOS_SERVICE = "com_dfl_api_app_navi_aos_aos_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_LOCATION_LOCATION_SERVICE = "com_dfl_api_app_navi_location_location_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_LAYER_LAYER_SERVICE = "com_dfl_api_app_navi_layer_layer_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_APPLETLAYER_APPLETLAYER_SERVICE = "com_dfl_api_app_navi_appletlayer_appletlayer_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_NAVI_NAVISCENE_SERVICE = "com_dfl_api_app_navi_navi_naviscene_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NAVI_NAVI_NAVIPROTOCOL_SERVICE = "com_dfl_api_app_navi_navi_naviprotocol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_MINIAPP_MINIAPP_SERVICE = "com_dfl_api_app_miniapp_miniapp_service";
    @VehicleType_CCS5
    public static final String DFL_APP_ACCOUNT_CONNECTOR_ACCOUNTAIDLINTERFACE_SERVICE = "com_dfl_api_app_account_connector_accountaidlinterface_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_VPA_VPA_SERVICE = "com_dfl_api_app_vr_vpa_vpa_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_VOICESETTING_VOICESETTING_SERVICE = "com_dfl_api_app_vr_voicesetting_voicesetting_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_UPLOADHOTWORDS_UPLOADHOTWORDS_SERVICE = "com_dfl_api_app_vr_uploadhotwords_uploadhotwords_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_TTS_TTSAIDL_SERVICE = "com_dfl_api_app_vr_tts_ttsaidl_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_VPR_VPR_SERVICE = "com_dfl_api_app_vr_vpr_vpr_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_SONG_SONGRECOGNIZE_SERVICE = "com_dfl_api_app_vr_song_songrecognize_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_CONTACT_UPLOADCONTACTS_SERVICE = "com_dfl_api_app_vr_contact_uploadcontacts_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VR_CONTROL_SPEECHCONTROL_SERVICE = "com_dfl_api_app_vr_control_speechcontrol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_IME_KEYBOARDSETTING_SERVICE = "com_dfl_api_app_ime_keyboardsetting_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VRFC_VRCONTROL_SERVICE = "com_dfl_api_app_vrfc_vrcontrol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_VRFC_VRSTATECONTROL_SERVICE = "com_dfl_api_app_vrfc_vrstatecontrol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AIOT_CHILDSEAT_SERVICE = "com_dfl_api_app_aiot_childseat_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AIOT_AIOTSCENE_SERVICE = "com_dfl_api_app_aiot_aiotscene_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AIOT_AISMARTHOME_SERVICE = "com_dfl_api_app_aiot_aismarthome_service";
    @VehicleType_CCS5
    public static final String DFL_APP_PAY_BANMAPAY_SERVICE = "com_dfl_api_app_pay_banmapay_service";
    @VehicleType_CCS5
    public static final String DFL_APP_LAUNCHER_VIRTUALDISPLAY_VIRTUALDISPLAY_SERVICE = "com_dfl_api_app_launcher_virtualdisplay_virtualdisplay_service";
    @VehicleType_CCS5
    public static final String DFL_APP_HOME_LIGHTAPPLIST_SERVICE = "com_dfl_api_app_home_lightapplist_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AILOCAL_INFORM_AILOCALINFORM_SERVICE = "com_dfl_api_app_ailocal_inform_ailocalinform_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AILOCAL_AILOCAL_SERVICE = "com_dfl_api_app_ailocal_ailocal_service";
    @VehicleType_CCS5
    public static final String DFL_APP_AILOCALHMI_PROTOCOL_AILOCALHMIPROTOCOL_SERVICE = "com_dfl_api_app_ailocalhmi_protocol_ailocalhmiprotocol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_APPSTORE_APPLIST_SERVICE = "com_dfl_api_app_appstore_applist_service";
    @VehicleType_CCS5
    public static final String DFL_APP_SCENEPATTERN_SCENEPATTERN_SERVICE = "com_dfl_api_app_scenepattern_scenepattern_service";
    @VehicleType_CCS5
    public static final String DFL_APP_DINGDING_VIDEOCALL_SERVICE = "com_dfl_api_app_dingding_videocall_service";
    @VehicleType_CCS5
    public static final String DFL_APP_SYSTEMUI_STATUSBAR_SERVICE = "com_dfl_api_app_systemui_statusbar_service";
    @VehicleType_CCS5
    public static final String DFL_APP_SYSTEMUI_SCREENSAVER_SERVICE = "com_dfl_api_app_systemui_screensaver_service";
    @VehicleType_CCS5
    public static final String DFL_APP_DVR_DVRAPP_SERVICE = "com_dfl_api_app_dvr_dvrapp_service";
    @VehicleType_623
    public static final String DFL_APP_MEDIA_PROTOCOL_MEDIAPROTOCOL_SERVICE = "com_dfl_api_app_media_protocol_mediaprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_SPEECH_PROTOCOL_SPEECHPROTOCOL_SERVICE = "com_dfl_api_app_speech_protocol_speechprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_VPA_PROTOCOL_VPAPROTOCOL_SERVICE = "com_dfl_api_app_vpa_protocol_vpaprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_CARCONTROL_PROTOCOL_CARCONTROLPROTOCOL_SERVICE = "com_dfl_api_app_carcontrol_protocol_carcontrolprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_ENERGYCENTER_PROTOCOL_ENERGYCENTERPROTOCOL_SERVICE = "com_dfl_api_app_energycenter_protocol_energycenterprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_SMARTSCENE_PROTOCOL_SMARTSCENEPROTOCOL_SERVICE = "com_dfl_api_app_smartscene_protocol_smartsceneprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_COMFORT_PROTOCOL_COMFORTPROTOCOL_SERVICE = "com_dfl_api_app_comfort_protocol_comfortprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_SYSTEMSETTINGS_PROTOCOL_SYSTEMSETTINGSPROTOCOL_SERVICE = "com_dfl_api_app_systemsettings_protocol_systemsettingsprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_OTA_PROTOCOL_OTAPROTOCOL_SERVICE = "com_dfl_api_app_ota_protocol_otaprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_WEATHER_PROTOCOL_WEATHERPROTOCOL_SERVICE = "com_dfl_api_app_weather_protocol_weatherprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_SOUNDTHEME_PROTOCOL_SOUNDTHEMEPROTOCOL_SERVICE = "com_dfl_api_app_soundtheme_protocol_soundthemeprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_SPORTSCARSOUND_PROTOCOL_SPORTSCARSOUNDPROTOCOL_SERVICE = "com_dfl_api_app_sportscarsound_protocol_sportscarsoundprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_BTPHONE_PROTOCOL_BTPHONEPROTOCOL_SERVICE = "com_dfl_api_app_btphone_protocol_btphoneprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_AVM_PROTOCOL_AVMPROTOCOL_SERVICE = "com_dfl_api_app_avm_protocol_avmprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_VIDEO_PROTOCOL_VIDEOPROTOCOL_SERVICE = "com_dfl_api_app_video_protocol_videoprotocol_service";
    @VehicleType_623
    public static final String DFL_APP_OWNERSGUIDE_PROTOCOL_OWNERSGUIDEPROTOCOL_SERVICE = "com_dfl_api_app_ownersguide_protocol_ownersguideprotocol_service";
    @VehicleType_CCS5
    public static final String DFL_APP_NEWSCENEPATTERN_NEWSCENEPATTERN_SERVICE = "com_dfl_api_app_newscenepattern_newscenepattern_service";
    @VehicleType_ALL
    public static final String DFL_APP_CARCONNECT_CARCONNECT_SERVICE = "com_dfl_api_app_carconnect_carconnect_service";
    @VehicleType_ALL
    public static final String DFL_APP_LAUNCHER_WALLPAPERMANAGEMENT_WALLPAPERMANAGEMENT_SERVICE = "com_dfl_api_app_launcher_wallpapermanagement_wallpapermanagement_service";
    @VehicleType_ALL
    public static final String DFL_APP_LAUNCHER_DESKTYPE_DESKTYPE_SERVICE = "com_dfl_api_app_launcher_desktype_desktype_service";

    public Constants() {
    }
}
