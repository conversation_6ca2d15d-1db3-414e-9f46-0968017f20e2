package com.dfl.smartscene.ccs.model.manager;

import android.text.TextUtils;

import com.tencent.mmkv.MMKV;

/**
 * <AUTHOR>
 * @date ：2023/6/19 10:21
 * @description ：场景角标需求：用户点击操作进入查看或者收藏，角标消失，角标数据超过三个月后自动消失
 */
public class SceneCornerManager {
    private static final String BADGE_PREFS_KEY = "badge_prefs";

    // 检查图片是否显示角标
    public static boolean shouldShowBadge(String sceneId , String badgeId) {
        if(TextUtils.isEmpty(badgeId)){
            return false;
        }

        // Step 1: 获取上次点击时间和第一次记录时间
        long lastClickTime = getLastClickTime(sceneId, badgeId);
        long firstRecordTime = getFirstRecordTime(sceneId, badgeId);

        // Step 2: 如果点击过
        if (lastClickTime > 0) {
            return false;
        }
        // 条件 4 和 条件 5（90天间隔）判断
        long currentTime = System.currentTimeMillis();
        if (firstRecordTime > 0 && currentTime - firstRecordTime > 90 * 24 * 60 * 60 * 1000) {
            return false;  // 不显示角标
        }

        // 其他情况，返回显示角标
        recordFirstRecordTime(sceneId, badgeId);
        return true;
    }

    // 记录图片的点击时间
    public static void recordClickTime(String sceneId , String badgeId) {
        if(TextUtils.isEmpty(badgeId)){
            return;
        }
        long clickTime = System.currentTimeMillis();
        MMKV mmkv = MMKV.defaultMMKV();
        mmkv.encode(getClickTimePrefsKey(sceneId,badgeId), clickTime);
    }

    // 记录图片的第一次获取角标的时间
    public static void recordFirstRecordTime(String sceneId , String badgeId) {
        if (getFirstRecordTime(sceneId, badgeId) == 0) {
            long firstRecordTime = System.currentTimeMillis();
            MMKV mmkv = MMKV.defaultMMKV();
            mmkv.encode(getFirstRecordTimePrefsKey(sceneId, badgeId), firstRecordTime);
        }
    }

    // 获取图片的点击时间
    private static long getLastClickTime(String sceneId , String badgeId) {
        MMKV mmkv = MMKV.defaultMMKV();
        return mmkv.decodeLong(getClickTimePrefsKey(sceneId, badgeId));
    }

    // 获取图片的第一次获取角标的时间
    private static long getFirstRecordTime(String sceneId , String badgeId) {
        MMKV mmkv = MMKV.defaultMMKV();
        return mmkv.decodeLong(getFirstRecordTimePrefsKey(sceneId, badgeId));
    }

    // 生成图片点击时间的存储键值
    private static String getClickTimePrefsKey(String sceneId , String badgeId) {
        return BADGE_PREFS_KEY + "_" + sceneId +badgeId + "_click_time";
    }

    // 生成图片第一次记录时间的存储键值
    private static String getFirstRecordTimePrefsKey(String sceneId , String badgeId) {
        return BADGE_PREFS_KEY + "_" + sceneId +badgeId + "_first_record_time";
    }
}
