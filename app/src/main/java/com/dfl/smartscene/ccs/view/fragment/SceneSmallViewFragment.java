package com.dfl.smartscene.ccs.view.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.navigation.Navigation;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.manager.NetworkManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.base.SimpleBaseFragment;
import com.dfl.smartscene.ccs.view.commonview.VideoServiceView;
import com.iauto.uibase.utils.MLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/11/14 17:05
 * @description ：舒压模式服务态的Fragment
 */
public class SceneSmallViewFragment extends SimpleBaseFragment {

    private static final String TAG = SceneSmallViewFragment.class.getSimpleName();


    private VideoServiceView mSmallView;

    private List<String> mArgs;
    //语音唤醒需要立即执行舒压模式
    private boolean executeNow;

    private int mOriginVolume = 0;

    @Override
    protected void init() {
        if (getArguments() != null) {
            mArgs = getArguments().getStringArrayList(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN);
            executeNow = getArguments().getBoolean(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN_NOW);
        }
        LogUtil.e(TAG,"executeNow ----- " + executeNow);
        mOriginVolume = DAModel.getInstance().getMediaVolume();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {


        mSmallView = new VideoServiceView(inflater.getContext());

        if (executeNow){
            mSmallView.buttonStart.performClick();
        }
        return mSmallView;
    }

    @Override
    protected void initView(View view) {

    }

    @Override
    protected void initData() {
        mSmallView.setData(mArgs , 0);
    }


    @Override
    protected void initObserver() {
        mSmallView.setIImageOrVideoViewListener(new VideoServiceView.IImageOrVideoViewListener() {
            @Override
            public void onCloseScene() {
                closeScene();
            }
        });
    }

    public void closeScene() {
        View view = getView();
        if (view != null) {
            Navigation.findNavController(view).navigateUp();
        } else {
            MLog.e(TAG, "view is null, cant navigation");
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        LogUtil.d(TAG, "onStop");
        SceneControlManager.getInstance().pauseScene(true);
    }

    @Override
    public void onResume() {
        super.onResume();
        //判断网络异常时，提示音乐无法播放的Toast
        if(!NetworkManager.getInstance().hasNetwork()){
            ToastManager.showToast(getString(R.string.string_need_network_play_music));
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 退出视频模式后，将音量重置为进入模式前的音量
        DAModel.getInstance().setMediaVolume(mOriginVolume);
    }


    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }

    @Override
    protected void onPermissionChanged(boolean permissionStatus) {

    }

}
