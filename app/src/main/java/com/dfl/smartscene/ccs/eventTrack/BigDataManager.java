package com.dfl.smartscene.ccs.eventTrack;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.RemoteException;

import com.dfl.api.app.eventtrack.CommonInfo;
import com.dfl.api.app.eventtrack.IEventTrack;
import com.dfl.api.app.eventtrack.IEventTrackCallback;
import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.Constants;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.google.gson.Gson;

import java.util.Map;

import io.reactivex.schedulers.Schedulers;

/**
 * 数据埋点的处理,负责与埋点Api进行交互，
 */
public class BigDataManager {
    private static final String TAG = "BigDataManager";
    @SuppressLint("StaticFieldLeak")
    private static BigDataManager mBigDataManager = null;
    private IEventTrack mIEventTrack;
    private final Context mContext;
    private final byte[] mApiLock = new byte[0];
    private int handle;
    private static final String environment_Type = "gray";//develop（开发）test (测试环境)、gray(灰度环境)、business(商用)
    private Gson mGson;

    public static BigDataManager getInstance() {
        return mBigDataManager;
    }

    private BigDataManager(Context context, int clientId) {
        mContext = context;
        registerCustomApiCallBack(clientId);
    }

    public static void create(Context context, int clientId) {
        if (mBigDataManager == null) {
            mBigDataManager = new BigDataManager(context, clientId);
        }
    }

    /**
     * 注册回调
     *
     * @param clientId
     */
    public void registerCustomApiCallBack(final int clientId) {
        BaseManager.create(mContext.getApplicationContext(), Constants.DFL_APP_EVENTTRACK_EVENTTRACK_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiState, String s, IBaseAPI iBaseAPI) {
                if (Constants.DFL_APP_EVENTTRACK_EVENTTRACK_SERVICE.equals(s)) {
                    synchronized (mApiLock) {
                        if (apiState == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof IEventTrack) {
                            mIEventTrack = (IEventTrack) iBaseAPI;

                            try {
                                mIEventTrack.registerEventTrace(clientId, new IEventTrackCallback() {
                                    @Override
                                    public void onGetEventHandle(int i) {
                                        LogUtil.d(TAG, "onGetEventHandle handle = " + i);
                                        handle = i;
                                    }

                                    @Override
                                    public void onGetEventIsWrite(boolean b) {
                                        LogUtil.d(TAG, "onGetEventIsWrite b = " + b);
                                    }

                                    @Override
                                    public void onGetAllAppSwitchState(Map map) {

                                    }
                                });
                            } catch (NullServiceException e) {
                                LogUtil.e(TAG, "catch NullServiceException");
                                mIEventTrack = null; //不成功或者连接断开等情况设置成null
                            }
                        } else {
                            LogUtil.d(TAG, "mIEventTrack is created failed");
                            mIEventTrack = null; //不成功或者连接断开等情况设置成null
                        }
                    }
                }

            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiState, String s) {

            }
        });
    }

    /**
     * 写入埋点数据
     *
     * @param eventId
     * @param eventName
     * @param eventType
     * @param eventDetailJson
     * @return
     */
    private boolean writeToEventTrack(String eventId, String eventName, String eventType, EventDetailTrackBean eventDetailJson) {
        if (mIEventTrack == null) {
            LogUtil.d(TAG, "IeventTrack regist CustomApi Failed !!");
            return false;
        }
        CommonInfo commonInfo = new CommonInfo();
        //app version
        commonInfo.setApp_version(getVersionCode(mContext));
        //timestamp
        long now = System.currentTimeMillis();
        commonInfo.setClientTimeStamp(String.valueOf(now));
        //userid
        commonInfo.setUserId("");
        //appname
        commonInfo.setApp_name(mContext.getString(R.string.app_name));
        //environmentType
        commonInfo.setEnvironment_type(environment_Type);
        //eventId
        commonInfo.setEventId(eventId);
        //eventName
        commonInfo.setEventName(eventName);
        //eventType
        commonInfo.setEventType(eventType);

        boolean result = false;

        if (mGson == null) {
            mGson = new Gson();
        }

        try {
             mIEventTrack.writeEventTrack(handle, commonInfo, mGson.toJson(eventDetailJson), true);
        } catch (NullServiceException e) {
            LogUtil.e(TAG, "NullServiceException");
            mIEventTrack = null; // 不成功或者连接断开等情况设置成null
        } catch (RemoteException e) {
            LogUtil.e(TAG, "RemoteException");
        }

        return result;
    }

    /**
     * 获取当前本地apk的版本
     *
     * @param mContext
     * @return
     */
    private static String getVersionCode(Context mContext) {
        int versionCode = 0;
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionCode = mContext.getPackageManager().
                    getPackageInfo(mContext.getPackageName(), 0).versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return String.valueOf(versionCode);
    }

    /**
     * 浏览智慧场景首页时触发
     *
     * @param smartSceneModel 模式 ，例如：舒压模式等
     * @param modelValue      模式内的配置，例如：主驾记忆，空调 3挡等
     */
    public void writeEventMod2(String smartSceneModel, String modelValue) {
        EventDetailTrackBean eventDetailTrackBean = new EventDetailTrackBean();
        eventDetailTrackBean.setPage_title(mContext.getString(R.string.scene_pv));
        eventDetailTrackBean.setSmartscene_model(smartSceneModel);
        eventDetailTrackBean.setModel_value(modelValue);
        Schedulers.computation().scheduleDirect(()-> {
            writeToEventTrack(
                    EventTrackDef.MOD_EVENT_ID_02,
                    EventTrackDef.MOD_Event_NAME_02,
                    EventTrackDef.MOD_EVENT_TYPE_PV,
                    eventDetailTrackBean);
            LogUtil.d(TAG,"writeEventMod2:"+modelValue);
        });
    }

    /**
     * 智慧场景首页点击时触发
     *
     * @param bntTitle 点击对象名称
     */
    public void writeEventMod3(String bntTitle) {
        EventDetailTrackBean eventDetailTrackBean = new EventDetailTrackBean();
        eventDetailTrackBean.setPage_title(mContext.getString(R.string.scene_pv));
        eventDetailTrackBean.setBnt_title(bntTitle);
        writeToEventTrack(
                EventTrackDef.MOD_EVENT_ID_03,
                EventTrackDef.MOD_Event_NAME_03,
                EventTrackDef.MOD_EVENT_TYPE_CLICK,
                eventDetailTrackBean);
        LogUtil.d(TAG,"-----------------------------------------------");
        LogUtil.d(TAG,"bntTitle:"+bntTitle);
        LogUtil.d(TAG,"writeEventMod3:"+eventDetailTrackBean.toString());
        LogUtil.d(TAG,"-----------------------------------------------");
    }

    /**
     * 浏览智慧场景弹窗时触发
     *
     * @param windowTitle 弹窗标题
     * @param pageTitle 页面标题
     */
    public void writeEventMod4(String windowTitle,String pageTitle) {
        EventDetailTrackBean eventDetailTrackBean = new EventDetailTrackBean();
        eventDetailTrackBean.setPage_title(pageTitle);
        eventDetailTrackBean.setWindow_title(windowTitle);
        writeToEventTrack(
                EventTrackDef.MOD_EVENT_ID_04,
                EventTrackDef.MOD_Event_NAME_04,
                EventTrackDef.MOD_EVENT_TYPE_POPUP,
                eventDetailTrackBean);
        LogUtil.d(TAG,"-----------------------------------------------");
        LogUtil.d(TAG,"windowTitle:"+windowTitle);
        LogUtil.d(TAG,"pageTitle:"+pageTitle);
        LogUtil.d(TAG,"writeEventMod4:"+eventDetailTrackBean.toString());
        LogUtil.d(TAG,"-----------------------------------------------");
    }

    /**
     * 浏览智慧场景弹窗点击时触发
     * @param windowTitle 弹窗标题
     * @param btnTitle 点击对象名称，例如：开关，P挡等
     * @param btnStatus 点击对象状态 例如：3挡 关闭 打开等
     */
    public void writeEventMod5(String windowTitle,String btnStatus,String btnTitle) {
        EventDetailTrackBean eventDetailTrackBean = new EventDetailTrackBean();
        eventDetailTrackBean.setWindow_title(windowTitle);
        eventDetailTrackBean.setBnt_title(btnTitle);
        eventDetailTrackBean.setBnt_status(btnStatus);
        writeToEventTrack(
                EventTrackDef.MOD_EVENT_ID_05,
                EventTrackDef.MOD_Event_NAME_05,
                EventTrackDef.MOD_EVENT_TYPE_CLICK,
                eventDetailTrackBean);
        LogUtil.d(TAG,"-----------------------------------------------");
        LogUtil.d(TAG,"windowTitle:"+windowTitle);
        LogUtil.d(TAG,"btnTitle:"+btnTitle);
        LogUtil.d(TAG,"btnStatus:"+btnStatus);
        LogUtil.d(TAG,"writeEventMod5:"+eventDetailTrackBean.toString());
        LogUtil.d(TAG,"-----------------------------------------------");
    }

}

