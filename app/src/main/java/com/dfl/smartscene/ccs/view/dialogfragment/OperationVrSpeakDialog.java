package com.dfl.smartscene.ccs.view.dialogfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.InputCheckResult;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.viewmodel.InputCheckViewModel;

/**
 * <AUTHOR>
 * @date ：2022/12/31 8:47
 * @description ：具备敏感词检查功能的操作容器，用于用户自定义播报
 */
public class OperationVrSpeakDialog extends OperationBaseDialogFragment{
    private InputCheckViewModel mInputCheckViewModel;
    private ConstraintLayout mLoadingLayout;      // 页面加载布局
    private View content;

    public OperationVrSpeakDialog(int operationType, SingleOperation singleOperation) {
        super(operationType, singleOperation);
    }

    public OperationVrSpeakDialog(int operationType, int pos, SettingOperation settingOperation, SingleOperation singleOperation) {
        super(operationType, pos, settingOperation, singleOperation);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_fragment_operation_base_vr,container,false);
    }

    @Override
    protected void init() {
        super.init();
        mInputCheckViewModel = new ViewModelProvider(this).get(InputCheckViewModel.class);
    }

    @Override
    protected void initView(View view) {
        super.initView(view);
        content = view.findViewById(R.id.layout_operation_dialog_content);
        mLoadingLayout = (ConstraintLayout) (getLayoutInflater().inflate(R.layout.layout_input_check_loading,(ViewGroup)view,false));
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        mLoadingLayout.setLayoutParams(layoutParams);
        ((ViewGroup) content).addView(mLoadingLayout);
        mLoadingLayout.setVisibility(View.GONE);


    }

    @Override
    protected void initObserver() {
        super.initObserver();
        mInputCheckViewModel.getInputCheckResultMutableLiveData().observe(getViewLifecycleOwner(), new Observer<InputCheckResult>() {
            @Override
            public void onChanged(InputCheckResult inputCheckResult) {
                if(inputCheckResult.getCheckString().equals(mOperationBaseView.extractArgs().get(0))){
                    switch (inputCheckResult.getCheckResult()){
                        case InputCheckResult.RESULT_PASS:
                            OperationVrSpeakDialog.super.onPositiveButtonClick();
                            break;
                        case InputCheckResult.RESULT_UNPASS:
                            ToastManager.showToast("保存失败，输入内容包含敏感词汇");
                            showContent();
                            break;
                        case InputCheckResult.RESULT_NONETWORK:
                            ToastManager.showToast("网络异常，请检查网络后重试");
                            showContent();
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    @Override
    public void onPositiveButtonClick() {
        showLoading();
        mInputCheckViewModel.checkInput(mOperationBaseView.extractArgs().get(0));

    }

    public void showLoading(){
        for(int i = 0 ; i < ((ViewGroup)content).getChildCount() ; i++){
            ((ViewGroup)content).getChildAt(i).setVisibility(View.GONE);
        }
        mLoadingLayout.setVisibility(View.VISIBLE);
        mViewConfirm.setEnabled(false);

    }

    public void showContent(){
        for(int i = 0 ; i < ((ViewGroup)content).getChildCount() ; i++){
            ((ViewGroup)content).getChildAt(i).setVisibility(View.VISIBLE);
        }
        mLoadingLayout.setVisibility(View.GONE);
        mViewConfirm.setEnabled(true);

    }
}
