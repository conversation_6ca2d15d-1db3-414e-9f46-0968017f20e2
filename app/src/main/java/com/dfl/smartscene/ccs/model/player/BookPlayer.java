package com.dfl.smartscene.ccs.model.player;

import android.content.ComponentName;
import android.os.Bundle;
import android.support.v4.media.session.MediaControllerCompat;

import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;

/**
 * <AUTHOR>
 * @date ：2023/5/30 14:33
 * @description ：
 */
public class BookPlayer extends CommonPlayer{

    public BookPlayer(ComponentName componentName) {
        super(componentName);
    }

    @Override
    public void play(String arg) {
        String[] split = arg.split("@");

        MediaControllerCompat controller = getController();
        if (controller == null) {
            return;
        }
        if (METHOD_SPECIAL.equals(split[0])) {
            if (PARAM_RESUME.equals(split[1])) {
                controller.getTransportControls().play();
            }
        } else if (METHOD_SCENE.equals(split[0])) {
            Bundle bundle = new Bundle();
            bundle.putInt("MediaSessionExtraIndex", DataTypeFormatUtil.string2Int(split[1]));
            controller.getTransportControls().sendCustomAction("MediaSessionExtraPlayCategory", bundle);//场景id
        }

    }
}
