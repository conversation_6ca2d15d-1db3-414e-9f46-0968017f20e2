package com.dfl.smartscene.ccs.model;

import com.alibaba.fastjson.JSON;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.http.UploadAddBean;
import com.dfl.smartscene.ccs.http.UploadDelBean;
import com.tencent.mmkv.MMKV;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2023/2/27 11:22
 * @description ：用于记录由于网络问题导致的，增/删/改未和云端同步的信息
 */
public class SceneSyncHelper {
    private SceneList unUpdateAddScene;
    private SceneList unUpdateDeleteScene;
    private SceneList unUpdateReplaceScene;
    private boolean unUpdateSort = false;
    private static final String MMKV_KEY_ADD  = "MMKV_KEY_ADD";
    private static final String MMKV_KEY_DELETE  = "MMKV_KEY_DELETE";
    private static final String MMKV_KEY_REPLACE  = "MMKV_KEY_REPLACE";
    private static final String MMKV_KEY_SORT  = "MMKV_KEY_SORT";

    private MMKV mMMKV = MMKV.defaultMMKV();
    public SceneSyncHelper(){
        unUpdateAddScene = JSON.parseObject(mMMKV.decodeString(MMKV_KEY_ADD),SceneList.class);
        if(unUpdateAddScene == null){
            unUpdateAddScene = new SceneList();
        }
        unUpdateDeleteScene = JSON.parseObject(mMMKV.decodeString(MMKV_KEY_DELETE),SceneList.class);
        if(unUpdateDeleteScene == null){
            unUpdateDeleteScene = new SceneList();
        }
        unUpdateReplaceScene = JSON.parseObject(mMMKV.decodeString(MMKV_KEY_REPLACE),SceneList.class);
        if(unUpdateReplaceScene == null){
            unUpdateReplaceScene = new SceneList();
        }
        if(mMMKV.containsKey(MMKV_KEY_SORT)){
            unUpdateSort = JSON.parseObject(mMMKV.decodeString(MMKV_KEY_SORT),boolean.class);
        }


    }

    public void onSortScene(){
        unUpdateSort = true;
        saveLocalSort();
    }

    /**
     * 发起添加场景请求时
     * @param sceneBean
     */
    public void onAddScene(SceneBean sceneBean){
        unUpdateAddScene.add(sceneBean);
        saveLocalAdd();
    }

    /**
     * 发起删除场景请求时
     * @param sceneBean
     */
    public void onDeleteScene(SceneBean sceneBean){
        if(!unUpdateAddScene.remove(sceneBean)){
            unUpdateDeleteScene.add(sceneBean);
            saveLocalDelete();
        }else {
            saveLocalAdd();
        }
        if(unUpdateReplaceScene.remove(sceneBean)){
            saveLocalReplace();
        }
    }


    /**
     * 发起替换场景请求时
     * @param sceneBean
     */
    public void onReplaceScene(SceneBean sceneBean){
        int addPos = unUpdateAddScene.indexOf(sceneBean);
        if(addPos > -1){
            unUpdateAddScene.set(addPos , sceneBean);
            saveLocalAdd();
        }else {
            unUpdateReplaceScene.add(sceneBean);
            saveLocalReplace();
        }
    }

    public void onSortSceneSuccess(){
        unUpdateSort = false;
        saveLocalSort();
    }

    /**
     * 添加请求返回成功时
     * @param sceneBean
     */
    public void onAddSuccess(SceneBean sceneBean){
        unUpdateAddScene.remove(sceneBean);
        saveLocalAdd();
    }

    /**
     * 替换请求返回成功时
     * @param sceneBean
     */
    public void onReplaceSuccess(SceneBean sceneBean){
        unUpdateReplaceScene.remove(sceneBean);
        saveLocalReplace();
    }

    /**
     * 删除请求返回成功时
     * @param sceneBean
     */
    public void onDeleteSuccess(SceneBean sceneBean){
        unUpdateDeleteScene.remove(sceneBean);
        saveLocalDelete();
    }

    /**
     * 是否有云端未同步成功的请求
     * @return
     */
    public boolean hasUnUpdatedScene(){
        return !(unUpdateAddScene.size() == 0 && unUpdateReplaceScene.size() == 0 && unUpdateDeleteScene.size() == 0 && !unUpdateSort);
    }

    /**
     * 把未同步成功的添加请求保存到本地
     */
    private void saveLocalAdd(){
        mMMKV.encode(MMKV_KEY_ADD,JSON.toJSONString(unUpdateAddScene));
    }
    /**
     * 把未同步成功的替换请求保存到本地
     */
    private void saveLocalReplace(){
        mMMKV.encode(MMKV_KEY_REPLACE,JSON.toJSONString(unUpdateReplaceScene));
    }
    /**
     * 把未同步成功的删除请求保存到本地
     */
    private void saveLocalDelete(){
        mMMKV.encode(MMKV_KEY_DELETE,JSON.toJSONString(unUpdateDeleteScene));
    }

    private void saveLocalSort(){
        mMMKV.encode(MMKV_KEY_SORT,JSON.toJSONString(unUpdateSort));
    }


    /**
     * 当场景上传请求返回成功时
     */
    public void onAllUpdateSuccess(){
        unUpdateAddScene = new SceneList();
        unUpdateDeleteScene = new SceneList();
        unUpdateReplaceScene = new SceneList();
        unUpdateSort = false;
        saveLocalAdd();
        saveLocalDelete();
        saveLocalReplace();
    }

    public List<UploadAddBean> getAddBeans(){
        Map<String , UploadAddBean> datas= new HashMap<>();
        for(SceneBean sceneBean : unUpdateAddScene){
            if(datas.containsKey(sceneBean.getSceneCategoryId())){
                datas.get(sceneBean.getSceneCategoryId()).add(sceneBean);
            }else {
                UploadAddBean uploadAddBean = new UploadAddBean();
                uploadAddBean.setCategoryId(sceneBean.getSceneCategoryId());
                uploadAddBean.add(sceneBean);
                datas.put(sceneBean.getSceneCategoryId(),uploadAddBean);
            }
        }
        return new ArrayList<>(datas.values());
    }

    public List<UploadDelBean> getDelBean(){
        List<UploadDelBean> result = new ArrayList<>();
        for(SceneBean sceneBean : unUpdateDeleteScene){
            result.add(new UploadDelBean(sceneBean.getSceneCategoryId(),sceneBean.getSceneId()));
        }
        return result;
    }

    /**
     * 该list的remove和indexof方法根据场景id来进行区分
     */
    public static class SceneList extends ArrayList<SceneBean>{

        public boolean remove(SceneBean sceneBean){
            for(int i = 0 ; i < size() ; i++){
                if(sceneBean.getSceneId().equals(get(i).getSceneId())){
                    remove(i);
                    return true;
                }
            }
            return false;
        }

        public int indexOf(SceneBean sceneBean) {
            for(int i = 0 ; i < size() ; i++){
                if(sceneBean.getSceneId().equals(get(i).getSceneId())){
                    return i;
                }
            }
            return -1;
        }
    }


}
