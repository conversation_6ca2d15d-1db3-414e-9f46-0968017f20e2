package com.dfl.smartscene.ccs.model.manager;

import java.util.HashMap;
import java.util.Map;

/**
 * 静态模型注册器
 * 用于替代XML配置方式，通过静态注册管理模型映射关系
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public class ModelRegistry {

    /**
     * 存储模型映射关系的Map
     * key: 通用模型名称（如"LibraryDriveModel"）
     * value: 具体实现类的Class对象
     */
    private static final Map<String, Class<?>> MODEL_MAP = new HashMap<>();

    /**
     * 静态初始化块，在类加载时自动执行
     * 在这里注册所有的模型映射关系
     */
    static {
        // 注册驱动模型映射
        registerModel("LibraryDriveModel", "com.dfl.scenelibrary.model.CCSDriveModel");

        // 注册自定义管理器映射
        registerModel("LibraryCustomManager", "com.dfl.scenelibrary.model.CCSCustomApiManager");

        // 如果有更多模型，在这里继续添加
        // registerModel("其他模型名称", "对应的实现类全名");
    }

    /**
     * 注册模型映射关系
     *
     * @param commonName 通用模型名称
     * @param implementationClassName 具体实现类的全类名
     */
    private static void registerModel(String commonName, String implementationClassName) {
        try {
            // 通过反射获取类的Class对象
            Class<?> implementationClass = Class.forName(implementationClassName);
            MODEL_MAP.put(commonName, implementationClass);
        } catch (ClassNotFoundException e) {
            // 如果找不到类，打印错误信息
            System.err.println("无法找到实现类: " + implementationClassName);
            e.printStackTrace();
        }
    }

    /**
     * 根据通用模型名称获取对应的实现类Class对象
     *
     * @param commonName 通用模型名称
     * @return 对应的实现类Class对象，如果找不到返回null
     */
    public static Class<?> getModelClass(String commonName) {
        return MODEL_MAP.get(commonName);
    }

    /**
     * 根据通用模型名称获取对应的实现类全类名
     * 这个方法保持与原LibraryModelParser相同的接口
     *
     * @param commonName 通用模型名称
     * @return 对应的实现类全类名，如果找不到返回null
     */
    public static String getModelName(String commonName) {
        Class<?> clazz = MODEL_MAP.get(commonName);
        return clazz != null ? clazz.getName() : null;
    }

    /**
     * 检查是否包含指定的模型名称
     * 这个方法保持与原LibraryModelParser相同的接口
     *
     * @param commonName 通用模型名称
     * @return 如果包含返回true，否则返回false
     */
    public static boolean containModelName(String commonName) {
        return MODEL_MAP.containsKey(commonName);
    }

    /**
     * 创建模型实例
     * 根据通用模型名称创建对应的实现类实例
     *
     * @param commonName 通用模型名称
     * @param <T> 返回类型的泛型
     * @return 创建的实例对象，如果创建失败返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> T createModel(String commonName) {
        Class<?> clazz = getModelClass(commonName);
        if (clazz != null) {
            try {
                // 使用默认构造函数创建实例
                return (T) clazz.newInstance();
            } catch (InstantiationException e) {
                System.err.println("无法实例化类: " + clazz.getName() + " - 可能缺少默认构造函数");
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                System.err.println("无法访问类: " + clazz.getName() + " - 构造函数不可访问");
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 获取所有已注册的模型名称
     *
     * @return 包含所有模型名称的数组
     */
    public static String[] getAllModelNames() {
        return MODEL_MAP.keySet().toArray(new String[0]);
    }

    /**
     * 获取已注册模型的数量
     *
     * @return 模型数量
     */
    public static int getModelCount() {
        return MODEL_MAP.size();
    }

    /**
     * 清空所有注册的模型（主要用于测试）
     */
    public static void clearAllModels() {
        MODEL_MAP.clear();
    }

    /**
     * 动态添加模型映射（运行时添加）
     *
     * @param commonName 通用模型名称
     * @param implementationClass 具体实现类
     */
    public static void addModel(String commonName, Class<?> implementationClass) {
        MODEL_MAP.put(commonName, implementationClass);
    }
}
