package com.dfl.smartscene.ccs.base;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;


import com.dfl.smartscene.R;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date ：2022/8/29 15:37
 * @description ：
 */
public abstract class BaseDialogFragment extends DialogFragment {

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCommonStyle();
        init();
    }

    protected  void setCommonStyle(){
        setStyle(STYLE_NO_FRAME, R.style.Dialog_CommonDialog);

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {

        super.onViewCreated(view, savedInstanceState);

        initView(view);
        //监听数据须在view初始化之后，防止空指针异常
        initObserver();

        initData();

        fixLeak();
    }

    protected abstract void init();

    protected abstract void initView(View view);

    protected abstract void initObserver();

    protected abstract void initData();

    private void fixLeak(){
        try {
            Field dismissField = getClass().getDeclaredField("mOnDismissListener");
            Field cancelField = getClass().getDeclaredField("mOnCancelListener");

            dismissField.setAccessible(true);
            cancelField.setAccessible(true);

            dismissField.set(this,new DialogDismissListener(this));
            cancelField.set(this,new DialogCancleListener(this));
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }


    public static class DialogDismissListener implements DialogInterface.OnDismissListener {
        private WeakReference<DialogFragment> leakDialogFragmentWeakReference;

        public DialogDismissListener(DialogFragment leakDialogFragment) {
            this.leakDialogFragmentWeakReference = new WeakReference<>(leakDialogFragment);
        }

        @Override
        public void onDismiss(DialogInterface dialog) {
            DialogFragment leakDialogFragment = leakDialogFragmentWeakReference.get();
            if(leakDialogFragment!=null){
                leakDialogFragment.onDismiss(dialog);
            }
        }
    }

    public static class DialogCancleListener implements DialogInterface.OnCancelListener {
        private WeakReference<DialogFragment> leakDialogFragmentWeakReference;

        public DialogCancleListener(DialogFragment leakDialogFragment) {
            this.leakDialogFragmentWeakReference = new WeakReference<>(leakDialogFragment);
        }

        @Override
        public void onCancel(DialogInterface dialog) {
            DialogFragment leakDialogFragment = leakDialogFragmentWeakReference.get();
            if(leakDialogFragment!=null){
                leakDialogFragment.onCancel(dialog);
            }
        }
    }
}
