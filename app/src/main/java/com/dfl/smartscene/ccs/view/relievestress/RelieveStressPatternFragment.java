package com.dfl.smartscene.ccs.view.relievestress;

import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.graphics.Outline;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.Toast;

import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.PagerAdapter;

import com.dfl.dflcommonlibs.toast.ToastUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.util.ScenePatternViewManager;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ScenePatternBaseFragment;
import com.dfl.smartscene.ccs.view.ScenePatternMainActivity;
import com.dfl.smartscene.ccs.viewmodel.RelieveStressPatternVM;
import com.dfl.smartscene.databinding.FragmentRelieveStressPatternBinding;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ANumberPicker;
import com.iauto.uicontrol.ButtonBase;
import com.iauto.uicontrol.ButtonView;
import com.iauto.uicontrol.GestureBoard;
import com.iauto.uicontrol.GifPlayer;
import com.iauto.uicontrol.PageIndicator;
import com.iauto.uicontrol.ProgressBar;
import com.iauto.uicontrol.ViewPagerList;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/*
 * RelieveStressPatternFragment class
 * 舒压模式fragment
 * <AUTHOR>
 * @date 2022/4/24
 */
public class RelieveStressPatternFragment extends ScenePatternBaseFragment {
    private static final String     TAG      = "RelieveStressPatternFragment";

    private  int  mPageList[] ={R.layout.relieve_stress_view_pager_list_page0,
                                R.layout.relieve_stress_view_pager_list_page1,
                                R.layout.relieve_stress_view_pager_list_page2};

    private  int  mGifArrayList[] ={R.array.relieve_stress_narrow_pattern1,
                                    R.array.relieve_stress_narrow_pattern2,
                                    R.array.relieve_stress_narrow_pattern1};
    //定义一个View的数组
    private List<View> mViews =new ArrayList<>();
    private ViewPagerList  mViewpager = null;
    private PageIndicator  mPageIndicator = null;
    private FragmentRelieveStressPatternBinding binding = null;
    private RelieveStressPatternVM              mViewModel = null;

    private GestureBoard mGestureBoard = null;
    private GestureBoard.GestureBoardListener mGestureBoardListener = null;

    PatternAdapter mPatternAdapter = null;

    onTouchListenerImpl mOnTouchListener = new onTouchListenerImpl();

    private ANumberPicker mTimerPicker;

    Timer timer;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("WrongThread")
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        MLog.d(TAG, "onCreateView");
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_relieve_stress_pattern, container, false);

        mViewModel = new ViewModelProvider(requireActivity()).get(RelieveStressPatternVM.class);
        binding.setVm(mViewModel);
        binding.setLifecycleOwner(this);
        initCCEventReceiver((ViewGroup) binding.getRoot());

        // 倒计时选择控件设定以及值变更回调
        mTimerPicker = (ANumberPicker)binding.getRoot().findViewById(R.id.timer_picker);
        mTimerPicker.setDisplayValues(getResources().getString(R.string.string_relieve_stress_timer_list));
        mTimerPicker.setOnValueChangedListener(new ANumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(ANumberPicker aNumberPicker, int i, int i1) {
                mViewModel.setRelieveStressTimerIndex(i1);
            }
        });

        //场景pager控件设定以及回调函数
        mViewpager = (ViewPagerList) binding.getRoot().findViewById(R.id.background_view_paper);
        mPageIndicator = (PageIndicator) binding.getRoot().findViewById(R.id.staticPageIndicator);
        mViewpager.setPageIndicator(mPageIndicator);
        mViewpager.addOnPageChangeListener(new ViewPagerList.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                MLog.d(TAG, "onPageScrolled");
                MLog.d(TAG, "滑动");
            }

            @Override
            public void onPageSelected(int position) {
                MLog.d(TAG, "onPageSelected");
                MLog.d(TAG, "当新的页面被选中时调用");
                if (mViewModel.getTransferOrigin().getValue() != RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Narrow) {
                    // 此处调用停止当前播放，切换新的播放。重新计时
                    mViewModel.setRelieveStressPattern(position);
                    if (mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                            || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                        setCtrlViewVisibleTimer();
                        mViewModel.setRelieveStressPlayNext();
                        mViewModel.setCtrlViewVisible(true);
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                MLog.d(TAG, "onPageScrollStateChanged");
                MLog.d(TAG, "onPageScrolled-state:" + state);
                MLog.d(TAG, "滑动状态改变时");
            }
        });

        //设定場景view前需要清空view
        mViews.clear();
        //将images数组中的图片放入ImageView
        for (int i = 0; i < mPageList.length; i++) {
            View page = View.inflate(getActivity(), mPageList[i],null);
            GifPlayer gifPlayer = page.findViewById(R.id.gif_player);
            gifPlayer.setGifFrames(mGifArrayList[i]);
            gifPlayer.setGifState(3);
            mViews.add(page);
        }
        //为ViewPager设置适配器
        mPatternAdapter = new PatternAdapter();
        mViewpager.setAdapter(mPatternAdapter);

        mViewpager.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                Rect rect = new Rect();
                view.getGlobalVisibleRect(rect);
                int leftMargin = 0;
                int topMargin = 0;
                Rect selfRect = new Rect(leftMargin, topMargin,
                        rect.right - rect.left - leftMargin,
                        rect.bottom - rect.top - topMargin);
                outline.setRoundRect(selfRect, 20);
            }
        });
        mViewpager.setClipToOutline(true);

        //手势控件设定
        mGestureBoard = (GestureBoard) binding.getRoot().findViewById(R.id.gesture_board_relieve_stress_start);
        mGestureBoardListener = new GestureBoard.GestureBoardListener() {
            @Override
            public void onSingleTapUp(MotionEvent e) {
                int action = e.getActionMasked();
                MLog.d(TAG, "onSingleTapUp() action:" + action);
                switch (action) {
                    case MotionEvent.ACTION_UP:
                    default:
                        break;
                }
            }
            @Override
            public void onDoubleTap(MotionEvent e) {

            }
            @Override
            public void onVerticalScrollLeft(float percent) {
                MLog.d(TAG, "onVerticalScrollLeft() percent:" + percent);
            }

            /**
             * 右側手勢上下滑动表示volume dialog
             *
             * @param percent
             */
            @Override
            public void onVerticalScrollRight(float percent){
                MLog.d(TAG, "onVerticalScrollRight() percent:" + percent);
                mViewModel.reqVolumeValue(percent);
            }
            @Override
            public void onHorizontalScroll(float percent) {
                MLog.d(TAG, "onHorizontalScroll() percent:" + percent);
            }

        };
        mGestureBoard.setGestureBoardListener(mGestureBoardListener);

        MLog.d(TAG, "onCreateView end");
        return binding.getRoot();
    }

    /**
     * 蓝牙电话变更观察者
     */
    protected RelieveStressPhoneObserver relieveStressPhoneObserver = new RelieveStressPhoneObserver();
    public class RelieveStressPhoneObserver implements Observer<Integer> {
        public void onChanged(Integer phoneStatus) {
            MLog.d(TAG, "RelieveStressPhoneObserver onChanged");
            if (RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_True == phoneStatus) {
                if (timer != null) {
                    timer.cancel();
                }
//                mViewModel.setRelieveStressViewCtrlVisible();
            }
        }
    }

    /**
     * 全屏关闭观察者
     */
    protected FullDSPStatusObserver fullDSPStatusObserver = new FullDSPStatusObserver();
    public class FullDSPStatusObserver implements Observer<Boolean> {
        public void onChanged(Boolean status) {
            MLog.d(TAG, "FullDSPStatusObserver onChanged");
            if (!status) {
                mViewpager.setCurrentItem(mViewModel.getRelieveStressPattern().getValue(),false);
                if ( mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                        || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                    setCtrlViewVisibleTimer();
                    mViewModel.setCtrlViewVisible(true);
                }
            }
        }
    }

    /**
     * 动画状态变更观察者
     */
    protected RelieveStressAnimateStatusChange mRelieveStressAnimateStatusChange = new RelieveStressAnimateStatusChange();
    public class RelieveStressAnimateStatusChange implements Observer<Integer> {
        public void onChanged(Integer Status) {
            doAnimated(Status);
        }
    }

    /**
     * 添加观察者
     */
    private void addObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:addObserver ");
        mViewModel.getPhoneInterruptStatus().observeForever(relieveStressPhoneObserver);
        mViewModel.getAnimateStatus().observeForever(mRelieveStressAnimateStatusChange);
        mViewModel.getFullDSPStatus().observeForever(fullDSPStatusObserver);
    }

    /**
     * 移除观察者
     */
    private void removeObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:removeObserver ");
        mViewModel.getPhoneInterruptStatus().removeObserver(relieveStressPhoneObserver);
        mViewModel.getAnimateStatus().removeObserver(mRelieveStressAnimateStatusChange);
        mViewModel.getFullDSPStatus().removeObserver(fullDSPStatusObserver);
    }

    /**
     * 初始化子控件event receiver
     *
     * @param view
     */
    private void initCCEventReceiver(ViewGroup view) {
        for (int i = 0; i < view.getChildCount(); i++) {
            View childView = view.getChildAt(i);
            if (childView instanceof ButtonBase) {
                ButtonBase child = (ButtonBase) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ButtonView) {
                ButtonView child = (ButtonView) childView;
                child.setEventReceiver(this);
            }
            if (childView instanceof ViewGroup) {
                initCCEventReceiver((ViewGroup) childView);
            }
        }
    }


    @Override
    public void onStop() {
        super.onStop();
        MLog.d(TAG, "onStop");
        mViewModel.setRelieveStressStartStatus(ScenePatternFuncDef.Relieve_Stress_Status_Close);
    }

    /**
     * 隐藏表示处理
     *
     * @param hidden
     */
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        MLog.d(TAG, " onHiddenChanged onHiddenChanged " + hidden);
        if (!hidden) {
            mViewModel.initLiveData();
            onResume();
        } else {
            mViewModel.removeObserver();
            ScenePatternViewManager.getInstance().getScenePatternActivity().unregisterMyOnTouchListener(mOnTouchListener);
        }
    }

    /**
     * 恢复表示处理
     *
     */
    @Override
    public void onResume() {
        super.onResume();
        mViewModel.setRelieveStressStartStatus(ScenePatternFuncDef.Relieve_Stress_Status_Open);
        addObserver();
        MLog.d(TAG, "RelieveStressPatternFragment onResume");
        ScenePatternViewManager.getInstance().getScenePatternActivity().registerMyOnTouchListener(mOnTouchListener);
        mViewModel.setCtrlViewVisible(true);
        int relieveStressStatus = mViewModel.getRelieveStressStatus().getValue();
        setCtrlViewVisibleTimer();
        if (null != mViewpager) {
            mViewpager.setCurrentItem(mViewModel.getRelieveStressPattern().getValue(), false);
        }
        doAnimated(relieveStressStatus);
        if (null != getActivity()) {
            if (RelieveStressFuncDef.Relieve_Stress_Status_Stop == relieveStressStatus && PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(getActivity(), "android.permission.VEHICLE_DATA")) {
                new ToastUtil.ToastUtilBuilder(getResources().getString(R.string.string_relieve_stress_play_tips))
                        .setDuration(Toast.LENGTH_SHORT)
                        .setClickable(true)
                        .setGravity(Gravity.BOTTOM|Gravity.CENTER_HORIZONTAL).build().showToast(binding.getRoot());
            }
        }
    }

    /**
     * 画面停止处理
     *
     */
    @Override
    public void onPause() {
        super.onPause();
        removeObserver();

        if (timer != null) {
            timer.cancel();
        }
        MLog.d(TAG, "onPause");
        ScenePatternViewManager.getInstance().getScenePatternActivity().unregisterMyOnTouchListener(mOnTouchListener);
    }

    /**
     * Relieve Pattern Status relate button's event method
     * @param v Relieve stress pattern fragment view
     */
    public void onCCMethod_RelievePatternStatusChangeClick(View v) {
        MLog.d(TAG, "onCCMethod_RelievePatternStatusChangeClick: ");
        int id = v.getId();
        if (id == R.id.button_relieve_stress_start) {
            onRelievePatternStartClick();
        } else if (id == R.id.button_relieve_stress_play) {
            onRelievePatternPlayClick();
        } else if (id == R.id.button_relieve_stress_pause) {
            onRelievePatternPauseClick();
        } else if (id == R.id.button_relieve_stress_stop || id == R.id.button_relieve_stress_phone_stop) {
            onRelievePatternStopClick();
        } else if (id == R.id.button_relieve_stress_timer) {
            onRelievePatternTimerSelClick();
        } else if (id == R.id.button_relieve_stress_expand) {
            onRelieveStressExpand();
        }
    }

    /**
     * 舒压模式开始处理
     *
     */
    private void onRelievePatternStartClick() {
        MLog.d(TAG, "onRelievePatternStartClick: ");

        if (ScenePatternFuncDef.CAR_GEAR_TYPE_P == mViewModel.getGearType().getValue()) {
            mViewModel.setTransferOrigin(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Start);
            mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);
            SysViewControl.getInstance().reqOpenRelieveStressPatternFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_RELIEVE);
        }
        else {
            ToastManager.getInstance().showToast(getContext().getString(R.string.string_relieve_stress_toast_gear_type_p),
                    Toast.LENGTH_SHORT,
                    false);
        }
    }

    /**
     * 舒压模式停止处理
     *
     */
    public void onRelievePatternStopClick() {
        MLog.d(TAG, "onRelievePatternStopClick: ");
        mViewModel.makeBigDataStopReason(ScenePatternFuncDef.SCENE_PATTERN_STOP_REASON_MANUAL);
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
    }

    /**
     * 舒压模式暂停处理
     *
     */
    private void onRelievePatternPauseClick() {
        MLog.d(TAG, "onRelievePatternPauseClick: ");
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Pause);
    }

    /**
     * 舒压模式播放处理
     *
     */
    private void onRelievePatternPlayClick() {
        MLog.d(TAG, "onRelievePatternPlayClick: ");
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);
    }

    /**
     * 舒压模式倒计时选择处理
     *
     */
    private void onRelievePatternTimerSelClick() {
        MLog.d(TAG, "onRelievePatternTimerSelClick: ");
        mViewModel.setRelieveStressTimerListVisible(View.VISIBLE);
    }

    /**
     * 舒压模式全屏展开处理
     *
     */
    private void onRelieveStressExpand() {
        MLog.d(TAG, "onRelievePatternTimerSelClick: ");
        mViewModel.setTransferOrigin(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Expand);
        SysViewControl.getInstance().reqOpenRelieveStressPatternFullView(ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_RELIEVE);
    }

    /*
     * 音量progressbar监听处理
     * <AUTHOR>
     * @date 2022/4/24
     */
    public class VolumeProgressBarListener implements ProgressBar.OnTouchChangedListener {
        @Override
        public void onCcPress(int value) {
        }
        @Override
        public void onCcRelease(int value) {
            mViewModel.reqVolumeValue(value);
        }
        @Override
        public void onCcMove(int value) {
        }
        @Override
        public void onCcCancel(int value) {
            mViewModel.reqVolumeValue(value);
        }
        @Override
        public void onCcMoveOut(int value) {
        }
    }

    /*
     * 画面压下监听处理
     * <AUTHOR>
     * @date 2022/4/24
     */
    public class onTouchListenerImpl implements ScenePatternMainActivity.MyOnTouchListener {
        int mLastDownX = 0;
        int mLastDownY = 0;
        int mTouchSlop =10;
        @Override
        public boolean onTouch(MotionEvent ev) {
            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    MLog.d(TAG, "onTouch:ACTION_DOWN");
                    mViewModel.setTransferOrigin(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_None);
                    mLastDownX = (int) ev.getRawX();   // 触摸点与屏幕左边的距离
                    mLastDownY = (int) ev.getRawY();   // 触摸点与屏幕上边的距离
                    if (mViewModel.getRelieveStressGestureEnable().getValue()) {
                        mGestureBoard.onTouchEvent(ev);
                    }

                    break;
                case MotionEvent.ACTION_MOVE:
                    float downX = ev.getRawX();
                    float downY = ev.getRawY();
                    float dx = Math.abs(downX - mLastDownX);
                    float dy = Math.abs(downY - mLastDownY);
                    if (Math.sqrt(dx * dx + dy * dy) > mTouchSlop && dy > dx) {
                        if (mViewModel.getRelieveStressGestureEnable().getValue()) {
                            mGestureBoard.onTouchEvent(ev);
                        }
                        return true;
                    } else {
                        return true;
                    }
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    // 音量dialog在压下抬起时消失
                    if ( mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                            || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                        if (mViewModel.getIsVolumeSlipping().getValue()) {
                            mViewModel.reqVolumeSlipStop();
                            return true;
                        }

                        if( Math.abs((int) ev.getRawX() - mLastDownX) > 0 || Math.abs((int) ev.getRawY() - mLastDownY) > 0 ) {
                            return true;
                        }

                        // 压下抬起时进行是否表示控件处理
                        mViewModel.ChangeCtrlViewVisible();
                        setCtrlViewVisibleTimer();
                    }
                    break;
                default:
                    //do nothing
                    break;
            }
            return false;
        }
    }

    /*
     * pagerAdapter处理
     * <AUTHOR>
     * @date 2022/4/24
     */
    class PatternAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return mViews.size();
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view==object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View v=mViews.get(position);
            container.addView(v);

            return v;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            View v=mViews.get(position);
            //前一张图片划过后删除该View
            container.removeView(v);
        }
    }

    /**
     * 播放时，控件5秒后消失处理
     *
     */
    private void setCtrlViewVisibleTimer() {
        MLog.d(TAG, "setCtrlViewVisibleTimer: ");
        if (mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
            if (timer != null) {
                timer.cancel();
            }
            timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    mViewModel.setCtrlViewVisible(false);
                    timer.cancel();
                }
            }, 5000);
        }
    }

    /**
     * 动画播放处理
     *
     * @param status 播放状态
     */
    private void doAnimated(int status) {
        MLog.d(TAG, "doAnimated");
        int pattern = mViewModel.getRelieveStressPattern().getValue();

        View page =  mViews.get(pattern);
        GifPlayer gifPlayer = page.findViewById(R.id.gif_player);
        if (RelieveStressFuncDef.Relieve_Stress_Status_Play == status) {
            MLog.d(TAG, "doAnimated play");
            gifPlayer.setGifState(1);
        } else if (RelieveStressFuncDef.Relieve_Stress_Status_Pause == status) {
            MLog.d(TAG, "doAnimated pause");
            gifPlayer.setGifState(2);
        } else {
            MLog.d(TAG, "doAnimated stop");
            gifPlayer.setGifState(3);
        }
    }
}
