package com.dfl.smartscene.ccs.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Point;
import android.hardware.display.DisplayManager;
import android.util.DisplayMetrics;
import android.view.Display;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.library.app.LibraryApp;

/**
 * <AUTHOR>
 * @date ：2023/3/10 9:03
 * @description ：
 */
public class DisplayUtil {
    private static int appSizeWeight = 0;
    private static int appSizeHeight = 0;
    private static int screenSizeWeight = 0;
    private static int screenSizeHeight = 0;


    public static int getAppSizeWeight() {
        return appSizeWeight;
    }

    public static int getAppSizeHeight() {
        return appSizeHeight;
    }

    public static int getScreenSizeWeight() {
        return screenSizeWeight;
    }

    public static int getScreenSizeHeight() {
        return screenSizeHeight;
    }

    public static void initDisplaySize(Activity activity){
        Display display = activity.getWindowManager().getDefaultDisplay();

        Point point = new Point();
        display.getSize(point);
        appSizeWeight = display.getWidth();
        appSizeHeight = display.getHeight();
        LogUtil.e("DisplayUtil", "awidth: "+appSizeWeight+",aheight:"+appSizeHeight );
    }

    public static void initFullScreenSize(){
        DisplayManager displayManager = (DisplayManager) LibraryApp.getApp().getSystemService(Context.DISPLAY_SERVICE);
        Display[] displays = displayManager.getDisplays();
        for(Display display : displays){
            DisplayMetrics displayMetrics = new DisplayMetrics();
            display.getRealMetrics(displayMetrics);
            if(displayMetrics.heightPixels > screenSizeHeight){
                screenSizeHeight = displayMetrics.heightPixels;
            }
            if(displayMetrics.widthPixels > screenSizeWeight){
                screenSizeWeight = displayMetrics.widthPixels;
            }
        }
        LogUtil.e("DisplayUtil", "swidth: "+screenSizeWeight+",sheight:"+screenSizeHeight );

    }
}
