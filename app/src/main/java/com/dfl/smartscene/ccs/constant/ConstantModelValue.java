package com.dfl.smartscene.ccs.constant;

import androidx.annotation.IntDef;


import com.dfl.smartscene.ccs.util.DisplayUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @date ：2022/7/20 14:38
 * @description ：
 */
public class ConstantModelValue {

    //ccu/pz1g_normal/all
    public static String DEBUG_CAR_TYPE_CCU = "ccu";
    public static String DEBUG_CAR_TYPE_CCS5 = "ccs5";
    public static String DEBUG_CAR_TYPE_PHONE = "ccs5";
//    public static String DEBUG_REQUEST_TYPE = "local";
    public static String DEBUG_REQUEST_TYPE = "cloud";
    public static String DEBUG_REQUEST_TYPE_LOCAL = "local";//获取本地数据库数据
    public static String DEBUG_REQUEST_TYPE_CLOUD = "cloud";//获取后台数据

    /**
     * common
     */
    public static final int ERROR_FORMAT_INT_FLOAT_DOUBLE = -100;

    /**
     * action/condition/state
     */
    public static final String OPERATION_TYPE_ACTION = "OPERATION_TYPE_ACTION";//执行
    public static final String OPERATION_TYPE_CONDITION = "OPERATION_TYPE_CONDITION";//触发
    public static final String OPERATION_TYPE_STATE = "OPERATION_TYPE_STATE";//状态

    /**
     * deviceId
     */
    public static final String DEVICE_ID_EVENT = "DID_EVENT";//事件
    public static final String DEVICE_ID_HVAC = "DID_HVAC";//空调
    public static final String DEVICE_ID_SEAT = "DID_SEAT";//座椅
    public static final String DEVICE_ID_WINDOW = "DID_WINDOW";//车窗
    public static final String DEVICE_ID_LIGHT = "DID_LIGHT";//灯光
    public static final String DEVICE_ID_GLASS = "DID_GLASS";//调光玻璃
    public static final String DEVICE_ID_ENV = "DID_ENV";
    public static final String DEVICE_ID_DRIVE = "DID_DRIVE";//行驶相关
    public static final String DEVICE_ID_APP = "DID_APP";//应用
    public static final String DEVICE_ID_AIOT = "DID_AIOT";//aiot设别
    public static final String DEVICE_ID_MORE = "DID_MORE";//更多分类
    public static final String DEVICE_ID_DOOR = "DID_DOOR";//车门分类

    public static final String DEVICE_ID_TIME = "DID_TIME";//时间分类
    //----------------------------------------action operation id-----------------------------------
    /**
     * time operation
     */
    public static final String OPERATION_ID_ACTION_MORE_TIME_DELAY = "OID_ACT_MORE_TIME_DELAY";

    /**
     * hvac operation
     */
    public static final String OPERATION_ID_ACTION_HVAC_SWITCH = "OID_ACT_HVAC_SWITCH";
    public static final String OPERATION_ID_ACTION_HVAC_TEMP = "OID_ACT_HVAC_TEMP";
    public static final String OPERATION_ID_ACTION_HVAC_CYCLE = "OID_ACT_HVAC_CYCLE";
    public static final String OPERATION_ID_ACTION_HVAC_BLOW_LEVEL = "OID_ACT_HVAC_BLOW_LEVEL";
    public static final String OPERATION_ID_ACTION_HVAC_BLOW_MODE = "OID_ACT_HVAC_BLOW_MODE";
    public static final String OPERATION_ID_ACTION_HVAC_DEFOG = "OID_ACT_HVAC_REAR_DEFOG";
    public static final String OPERATION_ID_ACTION_HVAC_AC = "OID_ACT_HVAC_AC";
    public static final String OID_ACT_SEAT_DRIVER_HEAT_VENTILATE = "OID_ACT_SEAT_DRIVER_HEAT_VENTILATE";
    public static final String OID_ACT_SEAT_PASSAGER_HEAT_VENTILATE = "OID_ACT_SEAT_PASSAGER_HEAT_VENTILATE";


    /**
     * 主副驾座椅调节
     */
    public static final String OID_ACT_SEAT_DRIVER_MEMORY = "OID_ACT_SEAT_DRIVER_MEMORY";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST = "OID_ACT_SEAT_DRIVER_ADJUST";
    public static final String OID_ACT_SEAT_DRIVER_MASSAGE = "OID_ACT_SEAT_DRIVER_MASSAGE";
    public static final String OID_ACT_SEAT_PASSAGER_MEMORY = "OID_ACT_SEAT_PASSAGER_MEMORY";
    public static final String OID_ACT_SEAT_PASSAGER_ADJUST = "OID_ACT_SEAT_PASSAGER_ADJUST";
    public static final String OID_ACT_SEAT_PASSAGER_MASSAGE = "OID_ACT_SEAT_PASSAGER_MASSAGE";

    /**
     * 主副驾座椅调节子操作
     */
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR = "OID_ACT_SEAT_DRIVER_ADJUST_FRONT_REAR";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN = "OID_ACT_SEAT_DRIVER_ADJUST_UP_DOWN";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK = "OID_ACT_SEAT_DRIVER_ADJUST_LAZYBACK";
    public static final String OID_ACT_SEAT_PASSAGER_ADJUST_FRONT_REAR = "OID_ACT_SEAT_PASSAGER_ADJUST_FRONT_REAR";
    public static final String OID_ACT_SEAT_PASSAGER_ADJUST_LAZYBACK = "OID_ACT_SEAT_PASSAGER_ADJUST_LAZYBACK";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_CUSHION = "OID_ACT_SEAT_DRIVER_ADJUST_CUSHION";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_LEFT_RIGHT = "OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_LEFT_RIGHT";
    public static final String OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_FRONT_REAR = "OID_ACT_SEAT_DRIVER_ADJUST_LUMBAR_FRONT_REAR";


    /**
     * 座椅记忆睡眠/休闲位置
     */
    public static final String OID_ACT_SEAT_DRIVER_SLEEP_MEMORY = "OID_ACT_SEAT_DRIVER_SLEEP_MEMORY";
    public static final String OID_ACT_SEAT_DRIVER_RELAX_MEMORY = "OID_ACT_SEAT_DRIVER_RELAX_MEMORY";
    public static final String OID_ACT_SEAT_PASSAGER_SLEEP_MEMORY = "OID_ACT_SEAT_PASSAGER_SLEEP_MEMORY";
    public static final String OID_ACT_SEAT_PASSAGER_RELAX_MEMORY = "OID_ACT_SEAT_PASSAGER_RELAX_MEMORY";
    //氛围灯开关
    public static final String OID_ACT_LIGHT_ATM_SWITCH = "OID_ACT_LIGHT_ATM_SWITCH";
    //音乐律动
    public static final String OID_ACT_LIGHT_ATM_RHYTHM = "public static final String ";
    //开始导航
    public static final String OID_ACT_APP_NAVI_START = "OID_ACT_APP_NAVI_START";
    public static final String OID_ACT_APP_NAVI_START_TRAFFIC = "OID_ACT_APP_NAVI_START_TRAFFIC";
    public static final String OID_ACT_APP_NAVI_START_DESTINATION = "OID_ACT_APP_NAVI_START_DESTINATION";
    //导航音量
    public static final String OID_ACT_APP_NAVI_VOLUME = "OID_ACT_APP_NAVI_VOLUME";
    //听电台
    public static final String OID_ACT_APP_MEDIA_RADIO = "OID_ACT_APP_MEDIA_RADIO";
    //听音乐
    public static final String OID_ACT_APP_MEDIA_MUSIC = "OID_ACT_APP_MEDIA_MUSIC";
    //听书
    public static final String OID_ACT_APP_MEDIA_BOOK = "OID_ACT_APP_MEDIA_BOOK";

    //停止播放
    public static final String OID_ACT_APP_MEDIA_END = "OID_ACT_APP_MEDIA_END";
    //多媒体音量
    public static final String OID_ACT_APP_MEDIA_VOLUME = "OID_ACT_APP_MEDIA_VOLUME";
    //音效
    public static final String OID_ACT_APP_MEDIA_EFFECT = "OID_ACT_APP_MEDIA_EFFECT";
    //天气播报
    public static final String OID_ACT_APP_VR_WEATHER = "OID_ACT_APP_VR_WEATHER";
    //自定义播报
    public static final String OID_ACT_APP_VR_USER = "OID_ACT_APP_VR_USER";
    //语音音量
    public static final String OID_ACT_APP_VR_VOLUME = "OID_ACT_APP_VR_VOLUME";
    //屏幕亮度
    public static final String OID_ACT_MORE_SCREEN_LIGHT = "OID_ACT_MORE_SCREEN_LIGHT";
    //全屏视频
    public static final String OID_ACT_MORE_FULL_SCREEN_VIDEO = "OID_ACT_MORE_FULL_SCREEN_VIDEO";
    //全屏视图
    public static final String OID_ACT_MORE_FULL_SCREEN_IMAGE = "OID_ACT_MORE_FULL_SCREEN_IMAGE";
    //全屏定时
    public static final String OID_ACT_MORE_FULL_SCREEN_TIME = "OID_ACT_MORE_FULL_SCREEN_TIME";

    //全屏音乐
    public static final String OID_ACT_MORE_FULL_SCREEN_MUSIC = "OID_ACT_MORE_FULL_SCREEN_MUSIC";

    public static final String OPERATION_ID_TITLE = "OID_TITLE";
    //用户购买设备的操作，非云端预设
    public static final String OID_ACT_DEVICE_AIOT_USER = "OID_ACT_DEVICE_AIOT_USER";
    //-------------------------------------condition operation id------------------------------------

    public static final int SYS_VIEW_CTRL_PATTERN_COMMON = 3;


    /**
     * vr condition
     */
    public static final String OPERATION_ID_CONDITION_VR = "OID_CDT_EVENT_VR";
    //离开某地触发
    public static final String OID_CDT_EVENT_LEAVE = "OID_CDT_EVENT_LEAVE";
    //到达某地触发
    public static final String OID_CDT_EVENT_ARRIVE = "OID_CDT_EVENT_ARRIVE";
    //时间触发
    public static final String OID_CDT_EVENT_TIME = "OID_CDT_EVENT_TIME";

    //续航里程（油车）触发
    public static final String OID_CDT_DRIVE_RANGE_FUEL = "OID_CDT_DRIVE_RANGE_FUEL";
    //续航里程（电车）触发
    public static final String OID_CDT_DRIVE_RANGE_POWER = "OID_CDT_DRIVE_RANGE_POWER";
    //电池电量触发
    public static final String OID_CDT_DRIVE_ELECTRICITY = "OID_CDT_DRIVE_ELECTRICITY";
    /**
     * 安全带触发
     */
    public static final String OID_CDT_SEAT_FRONT_LEFT_BELT = "OID_CDT_SEAT_FRONT_LEFT_BELT";

    public static final String OID_CDT_SEAT_FRONT_RIGHT_BELT = "OID_CDT_SEAT_FRONT_RIGHT_BELT";

    public static final String OID_CDT_SEAT_REAR_LEFT_BELT = "OID_CDT_SEAT_REAR_LEFT_BELT";

    public static final String OID_CDT_SEAT_REAR_MIDDLE_BELT = "OID_CDT_SEAT_REAR_MIDDLE_BELT";

    public static final String OID_CDT_SEAT_REAR_RIGHT_BELT = "OID_CDT_SEAT_REAR_RIGHT_BELT";

    /**
     * 车门触发
     */
    public static final String OID_CDT_DOOR_FRONT_LEFT = "OID_CDT_DOOR_FRONT_LEFT";

    public static final String OID_CDT_DOOR_FRONT_RIGHT = "OID_CDT_DOOR_FRONT_RIGHT";

    public static final String OID_CDT_DOOR_REAR_LEFT = "OID_CDT_DOOR_REAR_LEFT";

    public static final String OID_CDT_DOOR_REAR_RIGHT = "OID_CDT_DOOR_REAR_RIGHT";

    public static final String OID_CDT_DOOR_BACK = "OID_CDT_DOOR_BACK";

    //-------------------------------------state operation id----------------------------------------
    /**
     * time state
     */
    public static final String OPERATION_ID_STATE_TIME_CLOCK = "OID_STA_TIME_CLOCK";
    public static final String OPERATION_ID_STATE_TIME_DAY = "OID_STA_TIME_DAY";
    //挡位状态
    public static final String OPERATION_ID_STATE_DRIVE_GEAR = "OID_STA_DRIVE_GEAR";
    //导航状态
    public static final String OID_STA_NAVI_DESTINATION = "OID_STA_NAVI_DESTINATION";
    //导航当前路况
    public static final String OID_STA_NAVI_TRAFFIC = "OID_STA_NAVI_TRAFFIC";
    //天气温度
    public static final String OID_STA_ENV_OUT_TEMP = "OID_STA_ENV_OUT_TEMP";
    //车内空气质量
    public static final String OID_STA_ENV_IN_AIR_QUALITY = "OID_STA_ENV_IN_AIR_QUALITY";
    //车外空气质量
    public static final String OID_STA_ENV_OUT_AIR_QUALITY = "OID_STA_ENV_OUT_AIR_QUALITY";
    //天气
    public static final String OID_STA_ENV_WEATHER = "OID_STA_ENV_WEATHER";
    //续航里程油车
    public static final String OID_STA_DRIVE_RANGE_FUEL = "OID_STA_DRIVE_RANGE_FUEL";
    //续航里程电车
    public static final String OID_STA_DRIVE_RANGE_POWER = "OID_STA_DRIVE_RANGE_POWER";

    //电量
    public static final String OID_STA_DRIVE_ELECTRICITY = "OID_STA_DRIVE_ELECTRICITY";
    //车速
    public static final String OID_STA_DRIVE_SPEED = "OID_STA_DRIVE_SPEED";
    /**
     * 车门状态
     */
    public static final String OID_STA_DOOR_FRONT_LEFT = "OID_STA_DOOR_FRONT_LEFT";
    public static final String OID_STA_DOOR_FRONT_RIGHT = "OID_STA_DOOR_FRONT_RIGHT";
    public static final String OID_STA_DOOR_REAR_LEFT = "OID_STA_DOOR_REAR_LEFT";
    public static final String OID_STA_DOOR_REAR_RIGHT = "OID_STA_DOOR_REAR_RIGHT";
    public static final String OID_STA_DOOR_BACK = "OID_STA_DOOR_BACK";
    /**
     * 车窗状态
     */
    public static final String OID_STA_WINDOW_FRONT_LEFT = "OID_STA_WINDOW_FRONT_LEFT";
    public static final String OID_STA_WINDOW_FRONT_RIGHT = "OID_STA_WINDOW_FRONT_RIGHT";
    public static final String OID_STA_WINDOW_REAR_LEFT = "OID_STA_WINDOW_REAR_LEFT";
    public static final String OID_STA_WINDOW_REAR_RIGHT = "OID_STA_WINDOW_REAR_RIGHT";

    public static final String OPERATION_ID_ADD = "OID_ADD";

    /**
     * singleOperation key:
     */
    //value:String
    public static final String VIEW_DESC_TEXT = "VIEW_DESC_TEXT";//描述文言,如textview的hint值
    public static final String VIEW_DESC_TEXT_END = "VIEW_DESC_TEXT_END";//通常为单位
    public static final String VIEW_DESC_TEXT_BUTTOM = "VIEW_DESC_TEXT_BUTTOM";//弹窗的底部提示
    public static final String  VIEW_DESC_PIC_START = "VIEW_DESC_PIC_START";//滑动条的首部图片
    public static final String VIEW_DESC_PIC_END = "VIEW_DESC_PIC_END";//滑动条的尾部图片
    public static final String OUTPUT_DESC_TEXT = "OUTPUT_DESC_TEXT";//场景简介时的简要描述
    public static final String VIEW_DESC_PIC = "VIEW_DESC_PIC";//操作的icon，场景简介时展示
    public static final String VIEW_DEFAULT_POS = "VIEW_DEFAULT_POS";//用户添加动作时的默认选中值
    public static final String GEAR_P_LIMIT = "GEAR_P_LIMIT";//是否和p挡强相关
    public static final String VIEW_DESC_RANGE_LOW = "VIEW_DESC_RANGE_LOW";//范围视图最低值
    public static final String VIEW_DESC_RANGE_HIGH = "VIEW_DESC_RANGE_HIGH";//范围视图最高值
    public static final String VIEW_DESC_RANGE_STEP = "VIEW_DESC_RANGE_STEP";//范围视图步进值
    public static final String VIEW_DESC_RANGE_TYPE = "VIEW_DESC_RANGE_TYPE";//范围视图取值类型
    public static final String RELATED_DATA_CULCULATE = "RELATED_DATA_CULCULATE";//取值计算方式
    //value:List<String>
    public static final String DATA_DESC_LIST = "DATA_DESC_LIST";//复数按钮取出参数时的具体数值
    public static final String ACTION_DATA_POS_LIST = "ACTION_DATA_POS_LIST";//操作参数位于操作方法中的位置
    public static final String ACTION_METHOD_NAME_LIST = "ACTION_METHOD_NAME_LIST";//操作方法列表
    public static final String API_NAME_LIST = "API_NAME_LIST";//api名字列表
    public static final String VIEW_DESC_TEXT_LIST = "VIEW_DESC_TEXT_LIST";//复数按钮视图展示时的文言
    public static final String VIEW_DESC_PIC_LIST = "VIEW_DESC_PIC_LIST";//复数按钮视图展示时的icon
    public static final String VIEW_CONFLICT_ID_LIST = "VIEW_CONFLICT_ID_LIST";//具有冲突的id列表
    public static final String VIEW_CONFLICT_ARG_LIST = "VIEW_CONFLICT_ARG_LIST";//冲突操作的冲突参数

    //value:List<List<String>>
    public static final String DATA_TYPE_DYADIC_LIST = "DATA_TYPE_DYADIC_LIST";//方法参数类型列表
    public static final String DATA_DEFAULT_DYADIC_LIST = "DATA_DEFAULT_DYADIC_LIST";//方法默认参数列表


    /**
     * VIEW TYPE
     */
    public static final String VIEW_TYPE_NAVI_COLLECT_POINT = "VIEW_TYPE_NAVI_COLLECT_POINT";//导航收藏点选择
    public static final String VIEW_TYPE_TIME_HOUR_MINUTE = "VIEW_TYPE_TIME_HOUR_MINUTE";//时间选择器
    public static final String VIEW_TYPE_TIME_MINUTE_SECOND = "VIEW_TYPE_TIME_MINUTE_SECOND";//时间选择器
    public static final String VIEW_TYPE_MINUTE_SECOND = "VIEW_TYPE_MINUTE_SECOND"; // 延时操作

    public static final String VIEW_TYPE_TEXT = "VIEW_TYPE_TEXT";//文本输入框
    public static final String VIEW_TYPE_NUM_PICKER = "VIEW_TYPE_NUM_PICKER";//数字选择器
    public static final String VIEW_TYPE_SINGLE_CHOOSE = "VIEW_TYPE_SINGLE_CHOOSE";//多选择项单选类型
    public static final String VIEW_TYPE_NUM_COMPARE = "VIEW_TYPE_NUM_COMPARE";//大于小于+数字选择器
    public static final String VIEW_TYPE_ADJUST_STEP = "VIEW_TYPE_ADJUST_STEP";//步进调节
    public static final String VIEW_TYPE_LAYOUT_VERTICAL = "VIEW_TYPE_LAYOUT_VERTICAL";//视图纵向堆叠
    public static final String VIEW_TYPE_RADIO_TEXT_GROUP = "VIEW_TYPE_RADIO_TEXT_GROUP";//radio group，按钮是文本类型
    public static final String VIEW_TYPE_RADIO_PIC_GROUP = "VIEW_TYPE_RADIO_PIC_GROUP";//radio group，按钮是图片类型
    public static final String VIEW_TYPE_TITLE = "VIEW_TYPE_TITLE";//不是操作，只是标题
    public static final String VIEW_TYPE_SEAT_MEMORY = "VIEW_TYPE_SEAT_MEMORY";//座椅记忆特殊
    public static final String VIEW_TYPE_SEAT_ADJUST = "VIEW_TYPE_SEAT_ADJUST";//座椅调节特殊
    public static final String VIEW_TYPE_LAYOUT_VERTICAL_RELATED = "VIEW_TYPE_LAYOUT_VERTICAL_RELATED";//视图纵向堆叠+组合输出参数
    public static final String VIEW_TYPE_ATM_THEME = "VIEW_TYPE_ATM_THEME";//氛围灯主题特殊
    public static final String VIEW_TYPE_ADJUST_SLIDE = "VIEW_TYPE_ADJUST_SLIDE";//无极滑动调节
    public static final String VIEW_TYPE_STATE_TIME_DAY = "VIEW_TYPE_STATE_TIME_DAY";//状态条件的每日，工作日，节假日，自定义
    public static final String VIEW_TYPE_STATE_TIME_CLOCK = "VIEW_TYPE_STATE_TIME_CLOCK";//状态条件的时间点
    public static final String VIEW_TYPE_AIOT_SALE_DEVICE = "VIEW_TYPE_AIOT_SALE_DEVICE";

    public static final String VIEW_TYPE_RADIO_COLOR_GROUP = "VIEW_TYPE_RADIO_COLOR_GROUP";
    public static final String VIEW_TYPE_COLOR_BAR = "VIEW_TYPE_COLOR_BAR";
    public static final String VIEW_TYPE_SEAT_ADJUST_ITEM = "VIEW_TYPE_SEAT_ADJUST_ITEM";
    public static final String VIEW_TYPE_TEXT_PICKER = "VIEW_TYPE_TEXT_PICKER";//文本选择器



    //---------------------------------------Car Config reset------------------------------------------------------
    /**
     * reset resut
     */
    public static final int CONFIG_RESET_RESULT_RESET = 1;//根据车型差异重新设定
    public static final int CONFIG_RESET_RESULT_NOCHANGE = 0;//该操作无车型差异
    public static final int CONFIG_RESET_RESULT_NOCONFIG = -1;//该车型无此操作
    public static final int CONFIG_RESET_RESULT_NORELATIVE = -2;//未处理该操作
    /**
     * havc flow mode type
     */
    public static final int CONFIG_HVAC_BLOW_MODE0 = -1;//无吹风模式
    public static final int CONFIG_HVAC_BLOW_MODE1 = 1;//脸，脚，窗，脸脚，脚窗
    public static final int CONFIG_HVAC_BLOW_MODE2 = 2;//脸，脚，窗，脸窗，脸脚，脚窗，脸脚窗
    public static final int CONFIG_HVAC_BLOW_MODE3 = 3;//脚，脸，脸脚，雾脚，雾脸，雾脸脚

    /**
     * seat memory type
     */
    public static final int CONFIG_SEAR_MEMORY_MODE0 = -1;//无吹风模式
    public static final int CONFIG_SEAR_MEMORY_MODE1 = 1;//四种记忆+睡眠+休闲
    public static final int CONFIG_SEAR_MEMORY_MODE2 = 2;//三种记忆+睡眠+休闲
    /**
     * seat adjust type
     */
    public static final int CONFIG_SEAR_ADJUST_MODE2 = 2;//支持绝对调节

    /**
     * seat massage type
     */
    public static final int CONFIG_SEAR_MASSAGE_MODE0 = -1;//无座椅按摩
    public static final int CONFIG_SEAR_MASSAGE_MODE1 = 1;//自由，波浪，舒腰
    public static final int CONFIG_SEAR_MASSAGE_MODE2 = 2;//波浪，蛇形，猫步，敲击，舒腰，肩部
    public static final int CONFIG_SEAR_MASSAGE_MODE3 = 3;//海浪，猫步，轻柔

    //--------------------------------------special data--------------------------------------------
    public static final String HVAC_SWITCH_DATA_OPEN_ALL = "HVAC_SWITCH_DATA_OPEN_ALL";
    public static final String HVAC_SWITCH_DATA_CLOSE_ALL = "HVAC_SWITCH_DATA_CLOSE_ALL";
    public static final String HVAC_SWITCH_DATA_OPEN_DRIVER = "HVAC_SWITCH_DATA_OPEN_DRIVER";

    /**
     * special category id
     */
    public static final String CATEGORY_ID_CUSTOM = "category_self";//我的自定义栏目
    public static final String CATEGORY_ID_COLLECT = "category_collect";//我的收藏栏目
    /**
     * special scene id
     */
    public static final String SCENE_ID_ADD = "SPECIAL_SCENE_ADD";//添加自定义场景id

    /**
     * day operation
     */
    public static final String VALUE_STATE_DAY_UNKNOW = "-1";
    public static final String VALUE_STATE_DAY_EVERYDAY = "384";//OB 110000000
    public static final String VALUE_STATE_DAY_WORKDAY = "256";//OB 100000000
    public static final String VALUE_STATE_DAY_HOLIDAY = "128";//OB 010000000
    public static final String VALUE_STATE_DAY_USERDAY = "0";//OB 000000000

    /**
     * state > <
     */
    public static final String SYMBOL_GREATER = ">";//大于号，用于比较类型的状态条件
    public static final String SYMBOL_LESS = "<";//小于号，用于比较类型的状态条件

    /**
     * library card
     */
    public static final String SCENE_CATEGORY_ID_LIBRARY_CARD = "category_card";//首页卡片类型的轻松一刻分类id
    public static final String SCENE_CATEGORY_ID_LIBRARY_SERVICE = "category_service";//首页卡片类型的快捷场景分类id
    public static final String SCENE_ID_OLD_RELAX = "scene_card_relax_old";//首页卡片类型的舒压模式
    public static final String SCENE_ID_OLD_SLEEP = "scene_card_sleep";//首页卡片类型的睡眠模式
    public static final String SCENE_ID_OLD_EGG = "scene_card_surprise";//首页卡片类型的彩蛋模式
    public static final String SCENE_ID_OLD_WORK = "scene_card_work";//首页卡片类型的上班模式
    public static final String SCENE_ID_OLD_WAKE = "scene_card_wake";//首页卡片类型的醒神模式
    public static final String SCENE_ID_OLD_HOME = "scene_card_home";//首页卡片类型的回家模式
    public static final String SCENE_ID_OLD_CHILD = "scene_parent_child";//首页卡片类型的亲子模式

    public static final String KEY_SERVICE_INTENT_CUSTOMAPI_NAME = "KEY_SERVICE_INTENT_CUSTOMAPI_NAME";//
    public static final String ACTION_INTENT_CUSTOMAPI_CONDITION_INIT = "ACTION_INTENT_CUSTOMAPI_CONDITION_INIT";//

    public static final int SCENE_EDIT_TYPE_NORMAL = 0;//最普通的场景
    public static final int SCENE_EDIT_TYPE_EDIT_CARD = 1;//卡片型可编辑场景
    public static final int SCENE_EDIT_TYPE_UNEDIT_CARD = 2;//卡片型不可编辑场景
    public static final int SCENE_EDIT_TYPE_SPECIAL = 3;//特殊功能场景，如彩蛋模式

    public static final int SCENE_OPERATION_SHOW_TYPE_NORMAL = 0;//普通的用户可见且可给用户编辑的场景能力
    public static final int SCENE_OPERATION_SHOW_TYPE_UNEDIT = 1;//用户不可见且不可编辑的场景能力

    public static final int CAR_OPERATION_SHOW_TYPE_NORMAL = 0;//普通的用户可见且可给用户添加的车辆能力
    public static final int CAR_OPERATION_SHOW_TYPE_UNEDIT = 1;//用户不可见且不可添加的车辆能力
    public static final int CAR_OPERATION_SHOW_TYPE_AIOT_OFFLINE = 2;//AIOT操作中断线设备

//    public static final String RESOURCE_VIDEO_URL = "/video";



//    public static final String RESOURCE_VIDEO_URL = ScenePatternApp.getInstance().getExternalFilesDir("").getAbsolutePath() + "/video";;
//public static final String RESOURCE_VIDEO_URL = "/sdcard/Android/media/com.dfl.newscenepattern/sceneres/video";

    public static final String RESOURCE_VIDEO_URL = "/sceneres" + "/video";
    //27寸全屏视频存储路径
    public static final String RESOURCE_VIDEO_BIG_URL = "/sdcard/Android/data/com.dfl.newscenepattern/sceneres" + "/video-1920x720";
    //27存服务态视频存储路径
    public static String RESOURCE_VIDEO_SMALL_URL = "/sdcard/Android/data/com.dfl.newscenepattern/sceneres" + "/video-" + DisplayUtil.getAppSizeWeight() + "x" + DisplayUtil.getAppSizeHeight();

    public static final String RESOURCE_IMAGE_SMALL_URL = "/sceneres" + "/image-small";
    public static final String RESOURCE_IMAGE_BIG_URL = "/sceneres" + "/image-big";

//    public static final String RESOURCE_VIDEO_URL = ScenePatternApp.getInstance().getExternalMediaDirs()[0].getAbsolutePath() + "/video";
//    public static final String RESOURCE_MUSIC_URL = ScenePatternApp.getInstance().getExternalFilesDir("").getAbsolutePath() + "/music";;
//    public static final String RESOURCE_IMAGE_URL = ScenePatternApp.getInstance().getExternalFilesDir("").getAbsolutePath() + "/image";

//    public static final String RESOURCE_VIDEO_URL = "file:///android_asset" + "/video";
    public static final String RESOURCE_MUSIC_URL = "file:///android_asset" + "/music";
//    public static final String RESOURCE_IMAGE_SMALL_URL = "file:///android_asset" + "/image-small";
//    public static final String RESOURCE_IMAGE_BIG_URL = "file:///android_asset" + "/image-big";

    public static final String TEST_LANYOU_APP_ID = "app6RCsolG0qW5M43aETaij1qWAziAm1BUP";//联友SDK初始化参数
    public static final String TEST_LANYOU_APP_KEY = "450nJU5QDyViCuAMV9IOE21FgOI73bcxuxsFtWbM4A4KKg3DbVj4SCG1kcK20Avw56upHgMeeOwudNPsimy1Y4XQ1ZIaCSJ6RWBvhAMBZtraOCTDzLFUPaVOLemKSJet";//联友SDK初始化参数
    public static final String TEST_LIANYOU_APP_CLIENT_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgTBYdAVTiANiJXWXFQuhrk9vi5R52wqDK3byiY4SceHSgCgYIKoEcz1UBgi2hRANCAASim3lLRdOmfX3c9t6Ub5j+z132tRnG+pbPpZeQd5Kf2/667IUIJSHh1pXDrn3dK9s5vHvEWVnEltlwkbgw9kbE";//联友SDK初始化参数-客户端私钥
    public static final String TEST_LIANYOU_APP_SERVER_PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEBss2fx+AvQZhX2aT30mtqw9FhrPZGHfIL/b+ETCHdOHjpZrkui2p9ivGQy2XMdtiw3gYzrNFfCnzZaI5wK25dw==";//联友SDK初始化参数-服务端公钥

    public static final String LANYOU_APP_ID = "appUuNkI4E69r0xP4Qk9OF9bWmNUOmFktAY";//联友SDK初始化参数
    public static final String LANYOU_APP_KEY = "2oxb9VgpQxQxMqJRNpKloraYhiVpXP8iCVRXK044NOl6CekmUKO7IW60zBxuVfjSHeKGRv6Q98wWI1qgELm7e63n6TMlBnJTr4eQTMnFm4DAkxP26FJFLy1JHlffLNE0";//联友SDK初始化参数
    public static final String LIANYOU_APP_CLIENT_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgsfXh8utbnWVVHi9ZAuMhHton/x+4jtK/1noUpu8gXYmgCgYIKoEcz1UBgi2hRANCAARpGo/OeKDdHQnBX+0iXIcuukgkBmX/HwYNMM33HDn0Zo+7Q2iodrWVLJ6cJvaXezRo6MO8ipJ4c6Yq0yIpQEu2";//联友SDK初始化参数-客户端私钥
    public static final String LIANYOU_APP_SERVER_PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEa4eaPr8nL2kDThL/4n8PfGOM64gcrQygeJZbiQiyU54PGe1Jbsg1Ov0d8rmoFNfuRJNK4g31+JGZdyAB8wTj7A==";//联友SDK初始化参数-服务端公钥
    public static final String LANYOU_APP_CODE_NISSAN = "nissan";//联友SDK初始化参数
    public static final String LANYOU_PROJECT_TYPE_NISSAN = "nissan";//联友SDK初始化参数

    //每个操作之间的调用间隔
    public static final int DELAY_CUSOMAPI_USE = 100;
    //每个操作的子操作之间的调用间隔
    public static final int DELAY_CUSOMAPI_SUB_OPE = 130;
    //每个操作有多个customapi调用的间隔
    public static final int DELAY_CUSOMAPI_SUB_OPE_METHOD = 170;

    public static final String CAR_AIR_QUELITY_GOOD = "1";
    public static final String CAR_AIR_QUELITY_BAD = "0";

    @IntDef ({CAR_GEAR_P})
    @Retention (RetentionPolicy.SOURCE)
    public @interface CarGear{}
    public static final int CAR_GEAR_P = 0;

}
