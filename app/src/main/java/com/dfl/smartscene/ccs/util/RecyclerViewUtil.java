package com.dfl.smartscene.ccs.util;

import androidx.annotation.DimenRes;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

/**
 * <AUTHOR>
 * @date ：2022/8/30 9:31
 * @description ：
 */
public class RecyclerViewUtil {
    //list类型的recycleview进行初始化，类似首页上的展示数量固定不可滑动的情况
    public static void setLinearRecycleView(RecyclerView recyclerView, @RecyclerView.Orientation int orientation, @DimenRes int itemSpace, boolean fixed) {
        if (fixed){
            recyclerView.setLayoutManager(new LinearLayoutManager(recyclerView.getContext(), orientation, false){
                @Override
                public boolean canScrollHorizontally() {
                    return false;
                }

                @Override
                public boolean canScrollVertically() {
                    return false;
                }
            });
        }else {
            recyclerView.setLayoutManager(new LinearLayoutManager(recyclerView.getContext(), orientation, false));
        }
        recyclerView.setHasFixedSize(true);
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        recyclerView.setRecycledViewPool(recycledViewPool);
        int decoCount = recyclerView.getItemDecorationCount();
        if(decoCount != 0){
            for(int i = 0; i < decoCount ; i++ ){
                recyclerView.removeItemDecorationAt(i);
            }
        }
        if (orientation == RecyclerView.HORIZONTAL) {
            recyclerView.addItemDecoration(new HorizontalItemDecorationUtil(recyclerView.getContext().getResources().getDimensionPixelOffset(itemSpace)));
        } else if (orientation == RecyclerView.VERTICAL) {
            recyclerView.addItemDecoration(new VerticalItemDecorationUtil(recyclerView.getContext().getResources().getDimensionPixelOffset(itemSpace)));
        }
        ((SimpleItemAnimator) recyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
    }

    //默认可滑动
    public static void setLinearRecycleView(RecyclerView recyclerView, @RecyclerView.Orientation int orientation, @DimenRes int itemSpace){
        setLinearRecycleView(recyclerView,orientation,itemSpace,false);
    }


    /**
     * grid类型的recycleview进行初始化
     * @param recyclerView
     * @param orientation 方向
     * @param spanNumber The number of columns or rows in the grid
     * @param columnSpace
     * @param lineSpace
     * @param fixed
     */
    public static GridLayoutManager setGridRecycleView(RecyclerView recyclerView, @RecyclerView.Orientation int orientation, int spanNumber, @DimenRes int columnSpace, @DimenRes int lineSpace,boolean fixed) {
        GridLayoutManager layoutManager;
        if(fixed){
            layoutManager = new GridLayoutManager(recyclerView.getContext(), spanNumber,orientation, false){
                @Override
                public boolean canScrollVertically() {
                    return false;
                }

                @Override
                public boolean canScrollHorizontally() {
                    return false;
                }
            };
        }else {
            layoutManager = new GridLayoutManager(recyclerView.getContext(), spanNumber,orientation, false);
        }
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setHasFixedSize(true);
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        recyclerView.setRecycledViewPool(recycledViewPool);
        int decoCount = recyclerView.getItemDecorationCount();
        if(decoCount != 0){
            for(int i = 0; i < decoCount ; i++ ){
                recyclerView.removeItemDecorationAt(i);
            }
        }

        if (orientation == RecyclerView.HORIZONTAL) {
            recyclerView.addItemDecoration(new HorizontalGridItemDecorationUtil(recyclerView.getContext().getResources().getDimensionPixelOffset(lineSpace), recyclerView.getContext().getResources().getDimensionPixelOffset(columnSpace),spanNumber));
            recyclerView.setPadding(recyclerView.getPaddingLeft(),recyclerView.getPaddingTop(),-recyclerView.getContext().getResources().getDimensionPixelOffset(columnSpace),recyclerView.getPaddingBottom());
        } else if (orientation == RecyclerView.VERTICAL) {
            recyclerView.addItemDecoration(new VerticalGridItemDecorationUtil(recyclerView.getContext().getResources().getDimensionPixelOffset(lineSpace), recyclerView.getContext().getResources().getDimensionPixelOffset(columnSpace),spanNumber));
            recyclerView.setPadding(recyclerView.getPaddingLeft(),recyclerView.getPaddingTop(),recyclerView.getPaddingRight(),-recyclerView.getContext().getResources().getDimensionPixelOffset(lineSpace)+recyclerView.getPaddingBottom());
        }
        ((SimpleItemAnimator) recyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
        return layoutManager;
    }
    public static GridLayoutManager setGridRecycleView(RecyclerView recyclerView, @RecyclerView.Orientation int orientation, int fixNumber, @DimenRes int columnSpace, @DimenRes int lineSpace){
        return setGridRecycleView(recyclerView,orientation,fixNumber,columnSpace,lineSpace,false);
    }

}
