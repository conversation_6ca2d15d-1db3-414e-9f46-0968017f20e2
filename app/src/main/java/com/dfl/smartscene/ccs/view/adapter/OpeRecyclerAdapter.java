package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseHolder;


/**
 * <AUTHOR>
 * @date ：2022/9/2 14:02
 * @description ：单选操作的选项卡片
 */
public class OpeRecyclerAdapter extends SelectBaseAdapter<OpeRecyclerAdapter.DataBean> {

    @Override
    protected BaseHolder<DataBean> getViewHolder(View view, int viewType) {
        return new OpeRecyclerHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_ope_recycler;
    }

    class OpeRecyclerHolder extends SelectBaseHolder<DataBean>{
        TextView mTextViewName;
        ImageView shade;
        ImageView icon;

        public OpeRecyclerHolder(View itemView) {
            super(itemView);
            mTextViewName = itemView.findViewById(R.id.textview_item_ope_recycler);
            shade = itemView.findViewById(R.id.imageview_item_ope_recycler_shade);
            icon = itemView.findViewById(R.id.imageview_item_ope_recycler_icon);
        }

        @Override
        public void setupData(DataBean dataBean, int position) {
            super.setupData(dataBean, position);

            mTextViewName.setText(dataBean.desc);
        }

        @Override
        protected void setupSelectedData(DataBean dataBean, int position) {
            mTextViewName.setTextColor(mTextViewName.getResources().getColor(R.color.color_text_selected));
            itemView.setBackgroundResource(R.color.transparent);
            shade.setVisibility(View.VISIBLE);
            icon.setImageResource(R.drawable.bg_single_choose_icon_selected);
        }

        @Override
        protected void setupUnSelectedData(DataBean dataBean, int position) {
            mTextViewName.setTextColor(mTextViewName.getResources().getColor(R.color.color_text_unselected));
            itemView.setBackgroundResource(R.drawable.bg_single_choose_unselect);
            shade.setVisibility(View.GONE);
            icon.setImageResource(R.drawable.bg_single_choose_icon_unselected);
        }
    }

    public static class DataBean{
        private String desc;
        private String value;

        public DataBean(String desc, String value) {
            this.desc = desc;
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
