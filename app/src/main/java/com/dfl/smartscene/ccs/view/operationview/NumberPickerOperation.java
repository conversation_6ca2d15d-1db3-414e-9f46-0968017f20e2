package com.dfl.smartscene.ccs.view.operationview;

import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.NumberPickerView;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

public class NumberPickerOperation implements OperationBaseView {


    private static final String TAG = NumberPickerOperation.class.getSimpleName();

    private final SingleOperation mSingleOperation;
    private final NumberPickerView mNumberPickerView;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> arrayList = new ArrayList<>(1);
        arrayList.add(mNumberPickerView.getValue());
        return arrayList;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        return desc.replaceFirst("&",mNumberPickerView.getValue());
    }

    @Override
    public void onConfigurationChanged() {

    }


    public NumberPickerOperation(SingleOperation singleOperation, ViewGroup parent) {
        mSingleOperation = singleOperation;

        String unitStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT_END);
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        String minStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_LOW);
        String maxStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_HIGH);
        String stepStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_STEP);
        String valueTypeStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_TYPE);

        mNumberPickerView = new NumberPickerView(parent.getContext());
        MLog.d(TAG, "NumberPickerOperation: "+defaultStr);

        if ("26".equals(defaultStr) && singleOperation.getOperationId().equals("OID_ACT_HVAC_TEMP")){
            defaultStr = "26.0";
        }
        if ("OID_CDT_DRIVE_RANGE_POWER".equals(singleOperation.getOperationId()) && "DID_DRIVE".equals(singleOperation.getDeviceId())){
            if("20".equals(defaultStr)){
                defaultStr = "300";
            }
        }
        if ("OID_CDT_DRIVE_ELECTRICITY".equals(singleOperation.getOperationId()) && "DID_DRIVE".equals(singleOperation.getDeviceId())){
            defaultStr = "50";
        }
        if ("int".equals(valueTypeStr)) {
            mNumberPickerView.setValue(valueTypeStr, DataTypeFormatUtil.string2Int(minStr, 0), DataTypeFormatUtil.string2Int(maxStr, 0),
                    DataTypeFormatUtil.string2Int(stepStr, 0), defaultStr,unitStr);
        } else {
            mNumberPickerView.setValue(valueTypeStr, DataTypeFormatUtil.string2Float(minStr, 0), DataTypeFormatUtil.string2Float(maxStr, 0),
                    DataTypeFormatUtil.string2Float(stepStr, 0), defaultStr,unitStr);
        }

        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        parent.addView(mNumberPickerView, layoutParams);

    }



}
