package com.dfl.smartscene.ccs.viewmodel;

import android.app.Application;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;


/*
 * SurpriseEggListItemVM class
 * 彩蛋list item view model
 * <AUTHOR>
 * @date 2022/5/26
 */
public class SurpriseEggListItemVM extends ViewModel {
    private static final String TAG = "SurpriseEggListItemVM";

    private       int                     mItemViewType = 0;                            // itemType 0:title 1:item
    private       int                    mID           = -1;                         // 执行时检索用ID
    private       String                  mTTSText      = "";                           // TTS文言
    private       String                  mURL          = "";                           // H5播放链接
    private       String                  mImageURL     = "";
    private       int                     mVoiceType     = 1;
    private       String                  mVoiceFile     = "";
    private final MutableLiveData<String> mTime         = new MutableLiveData<>();      // 做成时间
    private final MutableLiveData<String> mName         = new MutableLiveData<>();      // 彩蛋名称
    private final MutableLiveData<Boolean> mIsSel         = new MutableLiveData<>();   // 选中状态
    private final MutableLiveData<Boolean> mIsEdit         = new MutableLiveData<>();   // 编辑状态
    private final MutableLiveData<Integer> mEditVisibility = new MutableLiveData<>();   // 编辑状态表示状态



    public SurpriseEggListItemVM(Application application) {
        initLiveData();
    }

    /**
     * 数据初始化
     */
    public void initLiveData() {
        mTime.setValue("");
        mName.setValue("");
        mID = -1;
        mIsSel.setValue(false);
        mIsEdit.setValue(false);
        mEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        mIsEdit.setValue(false);
    }


    public int getItemViewType() {
        return mItemViewType;
    }

    public int getID() {
        return mID;
    }

    public String getURL() {
        return mURL;
    }

    public MutableLiveData<String> getTime() {
        if (mTime.getValue() == null) {
            mTime.setValue("");
        }
        return mTime;
    }

    public MutableLiveData<String> getName() {
        if (mName.getValue() == null) {
            mName.setValue("");
        }
        return mName;
    }

    public String getTTSText() {
        return mTTSText;
    }
    public String getImageURL() {
        return mImageURL;
    }

    public int getVoiceType() {
        return mVoiceType;
    }

    public String getVoiceFile() {
        return mVoiceFile;
    }

    public void setItemData(SurpriseEggListVM.SurpriseEggItemInfo info) {
        mURL = info.mURL;
        mName.setValue(info.mName);
        mTime.setValue(info.mTime);
        mID = info.mID;
        mItemViewType = info.itemType;
        mIsEdit.setValue(info.mIsEdit);
        mIsSel.setValue(info.mIsSel);
        if (mIsEdit.getValue()) {
            mEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
        } else {
            mIsSel.setValue(false);
            mEditVisibility.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        }

        mTTSText = info.TTSText;
        mImageURL = info.mImageURL;
        mVoiceFile = info.voiceFile;
        mVoiceType = info.voiceType;
    }

    public MutableLiveData<Integer> getEditVisibility() {
        return mEditVisibility;
    }

    public MutableLiveData<Boolean> getIsSel() {
        return mIsSel;
    }

    public MutableLiveData<Boolean> getIsEdit() {
        return mIsEdit;
    }

}
