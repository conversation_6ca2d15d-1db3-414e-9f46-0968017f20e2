package com.dfl.smartscene.ccs.viewmodel;

import android.app.Application;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.model.RelieveStressPatternModel;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.dfl.smartscene.ccs.util.TimerProcessor;
import com.iauto.uibase.lifecycle.MViewModelBase;
import com.iauto.uibase.utils.MLog;

import java.util.Timer;
import java.util.TimerTask;

/**
 * The view model class of seat massage and seat adjustment
 * uses the model data for UI's display
 * <AUTHOR>
 * @date 2022/01/10
 */
public class RelieveStressPatternVM extends MViewModelBase {

    private static final String TAG = "RelieveStressPatternVM";
    private RelieveStressPatternModel mRelieveStressPatternModel;

    Timer timer;

    protected VolumeValueObserver volumevalueobserver = new VolumeValueObserver();

    public MutableLiveData<Integer> getRelieveStressNarrowVisible() {
        return mRelieveStressNarrowVisible;
    }

    /**
     * 音量值变更观察者
     */
    public class VolumeValueObserver implements Observer<Integer> {
        public void onChanged(Integer volumeValue) {
            MLog.d(TAG,"RelieveStressPatternVM:VolumeValueObserver " + volumeValue);
            getRelieveStressVolumeValue().setValue(volumeValue);
        }
    }

    /**
     * 播放状态变化
     */
    protected StatusChangeObserver statusChangeObserver = new StatusChangeObserver();
    public class StatusChangeObserver implements Observer<Integer> {
        public void onChanged(Integer status) {
            MLog.d(TAG,"StatusChangeObserver:" + status);
            if (RelieveStressFuncDef.Relieve_Stress_Status_Play == status ||
                    RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop == status) {
                getRelieveStressTimerListVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressGestureEnable().setValue(true);
                if (getRelieveStressTimerIndex().getValue() != 0) {
                    getRelieveStressTimerVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                } else {
                    getRelieveStressTimerVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                }

                if(RelieveStressFuncDef.Relieve_Stress_Status_Play == status) {
                    getRelieveStressCtrlViewVisibleInPlay().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                    getRelieveStressCtrlViewVisibleInPhoneStop().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                    getRelieveStressExpandVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                    getRelieveStressNarrowVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                } else {
                    getRelieveStressCtrlViewVisibleInPlay().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                    getRelieveStressCtrlViewVisibleInPhoneStop().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                    getRelieveStressExpandVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                    getRelieveStressNarrowVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                }

                getRelieveStressCtrlViewVisibleInStop().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInPause().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }
            else if (RelieveStressFuncDef.Relieve_Stress_Status_Pause == status) {
                getRelieveStressTimerListVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressGestureEnable().setValue(true);
                if (getRelieveStressTimerIndex().getValue() != 0) {
                    getRelieveStressTimerVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                } else {
                    getRelieveStressTimerVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                }
                getRelieveStressExpandVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                getRelieveStressNarrowVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                getRelieveStressCtrlViewVisibleInPlay().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInPhoneStop().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInStop().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInPause().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            }
            else {
                getRelieveStressGestureEnable().setValue(false);
                getRelieveStressTimerListVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressTimerVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressExpandVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressNarrowVisible().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInPlay().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInPhoneStop().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressCtrlViewVisibleInStop().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                getRelieveStressCtrlViewVisibleInPause().setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }
        }
    }

    /**
     * 倒计时index变更观察者
     */
    protected RelieveStressTimerObserver relieveStressTimerObserver = new RelieveStressTimerObserver();
    public class RelieveStressTimerObserver implements Observer<Integer> {
        public void onChanged(Integer timerValue) {
            MLog.d(TAG,"RelieveStressPatternVM:relieveStressTimerObserver " + timerValue);
            getRelieveStressTimerText().setValue(getTimerText(timerValue));
        }
    }

    /**
     * SeatSettingVM object constructor
     * @param application object
     */
    public RelieveStressPatternVM(Application application) {
        super(application);
        mRelieveStressPatternModel = RelieveStressPatternModel.getInstance();
        initLiveData();
    }

    private final MutableLiveData<Boolean> mIsVolumeSlipping = new MutableLiveData<>();                     // 音量滑动中
    private final MutableLiveData<Integer> mSettingVolumeViewVisible = new MutableLiveData<>();             // 音量表示
    private final MutableLiveData<Integer> mVolumeBeginPosition = new MutableLiveData<>();                  // 音量初始位置
    private final MutableLiveData<Integer> mVolumeValueMoveValue = new MutableLiveData<>();                 // 音量移动值
    private final MutableLiveData<Integer> mRelieveStressVolumeValue = new MutableLiveData<>();             // 音量值

    private final MutableLiveData<Integer> mGestureBeginPosition = new MutableLiveData<>();                 // 手势初始位置
    private final MutableLiveData<String>  mRelieveStressTimerText = new MutableLiveData<>();               // 倒计时时间text
    private final MutableLiveData<Integer> mRelieveStressCtrlViewVisibleInPlay = new MutableLiveData<>();   // play状态控件表示状态
    private final MutableLiveData<Integer> mRelieveStressCtrlViewVisibleInPhoneStop = new MutableLiveData<>();  // 电话中断状态控件表示状态
    private final MutableLiveData<Integer> mRelieveStressCtrlViewVisibleInPause = new MutableLiveData<>();  // 暂停状态控件表示状态
    private final MutableLiveData<Integer> mRelieveStressCtrlViewVisibleInStop = new MutableLiveData<>();   // 停止状态控件表示状态
    private final MutableLiveData<Boolean> mRelieveStressGestureEnable = new MutableLiveData<>();           // 手势可否状态
    private final MutableLiveData<Integer> mRelieveStressTimerVisible = new MutableLiveData<>();            // 倒计时表示状态
    private final MutableLiveData<Integer> mRelieveStressExpandVisible = new MutableLiveData<>();           // 展开按钮表示状态
    private final MutableLiveData<Integer> mRelieveStressNarrowVisible = new MutableLiveData<>();           // 缩小按钮表示状态
    private final MutableLiveData<Integer> mRelieveStressTimerListVisible = new MutableLiveData<>();        // 倒计时list表示状态

    private final MutableLiveData<Integer> mCtrlViewVisible = new MutableLiveData<>();                      // star状态控件表示状态

    /**
     * init seat live data
     */
    public void initLiveData() {
        MLog.d(TAG,"initLiveData ");
        mCtrlViewVisible.setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
        mSettingVolumeViewVisible.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        mGestureBeginPosition.setValue(0);
        mIsVolumeSlipping.setValue(false);
        mVolumeBeginPosition.setValue(0);
        mVolumeValueMoveValue.setValue(RelieveStressFuncDef.Relieve_Stress_none);
        mRelieveStressTimerListVisible.setValue(ScenePatternFuncDef.Scene_Pattern_inVisible);

        addObserver();
    }

    /**
     * 添加观察者
     */
    public void addObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:addObserver ");
        getVolumeValue().observeForever(volumevalueobserver);
        getRelieveStressTimerIndex().observeForever(relieveStressTimerObserver);
        getRelieveStressStatus().observeForever(statusChangeObserver);
    }

    /**
     * 移除观察者
     */
    public void removeObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:removeObserver ");
        getVolumeValue().removeObserver(volumevalueobserver);
        getRelieveStressTimerIndex().removeObserver(relieveStressTimerObserver);
        getRelieveStressStatus().removeObserver(statusChangeObserver);
    }

    public MutableLiveData<Boolean> getIsVolumeSlipping() {
        return mIsVolumeSlipping;
    }

    public MutableLiveData<Integer> getSettingVolumeViewVisible() {
        return mSettingVolumeViewVisible;
    }

    public MutableLiveData<Integer> getVolumeBeginPosition() {
        return mVolumeBeginPosition;
    }

    private MutableLiveData<Integer> getVolumeValueMoveValue() {
        return mVolumeValueMoveValue;
    }

    public MutableLiveData<Integer> getVolumeValue() {
        return mRelieveStressPatternModel.getVolumeValue();
    }

    public MutableLiveData<Integer> getRelieveStressTimerIndex() {
        return mRelieveStressPatternModel.getRelieveStressTimerIndex();
    }

    public MutableLiveData<Integer> getRelieveStressStatus() {
        return mRelieveStressPatternModel.getRelieveStressStatus();
    }

    public MutableLiveData<Integer> getRelieveStressPattern() {
        return mRelieveStressPatternModel.getRelieveStressPattern();
    }

    public MutableLiveData<Integer> getRelieveStressStartStatus() {
        return mRelieveStressPatternModel.getRelieveStressStartStatus();
    }

    public MutableLiveData<Integer> getGearType() {
        return ScenePatternInterrupt.getInstance().getGearType();
    }

    public MutableLiveData<Integer> getRelieveStressVolumeValue() {
        return mRelieveStressVolumeValue;
    }

    public MutableLiveData<Long> getRelieveStressTimer() {
        return mRelieveStressPatternModel.getRelieveStressTimer();
    }

    public void resetRelieveStressTimer() {
        mRelieveStressPatternModel.resetRelieveStressTimer();
    }

    public MutableLiveData<Integer> getPhoneInterruptStatus() {
        return mRelieveStressPatternModel.getPhoneInterruptStatus();
    }

    public MutableLiveData<Integer> getAnimateStatus() {
        return mRelieveStressPatternModel.getAnimateStatus();
    }

    public MutableLiveData<Integer> getRelieveStressCtrlViewVisibleInPlay() {
        return mRelieveStressCtrlViewVisibleInPlay;
    }

    public MutableLiveData<Integer> getRelieveStressCtrlViewVisibleInPhoneStop() {
        return mRelieveStressCtrlViewVisibleInPhoneStop;
    }

    public MutableLiveData<Integer> getRelieveStressCtrlViewVisibleInPause() {
        return mRelieveStressCtrlViewVisibleInPause;
    }

    public MutableLiveData<Integer> getRelieveStressCtrlViewVisibleInStop() {
        return mRelieveStressCtrlViewVisibleInStop;
    }

    public MutableLiveData<Boolean> getRelieveStressGestureEnable() {
        return mRelieveStressGestureEnable;
    }

    public MutableLiveData<Integer> getRelieveStressTimerVisible() {
        return mRelieveStressTimerVisible;
    }

    public MutableLiveData<Integer> getRelieveStressTimerListVisible() {
        return mRelieveStressTimerListVisible;
    }

    public MutableLiveData<Integer> getRelieveStressExpandVisible() {
        return mRelieveStressExpandVisible;
    }

    public MutableLiveData<Integer> getTransferOrigin() {
        return mRelieveStressPatternModel.getTransferOrigin();
    }

    public void setTransferOrigin(int transferOrigin) {
        mRelieveStressPatternModel.setTransferOrigin(transferOrigin);
    }

    public void setRelieveStressTimerListVisible(int visible) {
        mRelieveStressTimerListVisible.setValue(visible);
    }

    public void setRelieveStressTimerIndex(int timerIndex) {
        mRelieveStressPatternModel.setRelieveStressTimerIndex(timerIndex);
    }

    public void setRelieveStressStatus(int status) {
        mRelieveStressPatternModel.setRelieveStressStatus(status);
    }

    public void setRelieveStressPlayNext() {
        mRelieveStressPatternModel.setRelieveStressPlayNext();
    }

    public MutableLiveData<Boolean> getFullDSPStatus() {
        return mRelieveStressPatternModel.getFullDSPStatus();
    }

    public void setFullDSPStatus(boolean status) {
        mRelieveStressPatternModel.setFullDSPStatus(status);
    }

    public void setRelieveStressPattern(int patternIndex) {
        mRelieveStressPatternModel.setRelieveStressPattern(patternIndex);
    }

    public void setRelieveStressStartStatus(int startStatus) {
        mRelieveStressPatternModel.setRelieveStressStartStatus(startStatus);
    }

    public void setRelieveStressTimer(long value) {
        mRelieveStressPatternModel.setRelieveStressTimer(value);
    }

    public MutableLiveData<String> getRelieveStressTimerText() {
        return mRelieveStressTimerText;
    }

    public MutableLiveData<Integer> getGestureBeginPosition() {
        return mGestureBeginPosition;
    }

    public MutableLiveData<String> getRelieveStressDspTimer() {
        return mRelieveStressPatternModel.getRelieveStressDspTimer();
    }

    /**
     * 请求应两调整
     *
     * @param value 想要调整的音量
     */
    public void reqVolumeValue(float value){
        MLog.d(TAG,"reqVolumeValue" + value);
        if (!getIsVolumeSlipping().getValue()) {
            getIsVolumeSlipping().setValue(true);
            getSettingVolumeViewVisible().setValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            getVolumeBeginPosition().setValue(getVolumeValue().getValue());
            TimerProcessor.getInstance().startTimer(RelieveStressFuncDef.Relieve_Stress_VolumeSeekSlip, 200, true,
                    () -> {
                        MLog.d(TAG, "RelieveStressPatternVM:reqVolumeValue time out");
                        mRelieveStressPatternModel.reqVolumeto(getVolumeValueMoveValue().getValue());
                    });
        }

        int seekPosition = volumeSeekPosition(value);
        getVolumeValueMoveValue().setValue(seekPosition);
    }

    /**
     * 音量滑动结束处理
     *
     */
    public void reqVolumeSlipStop() {
        MLog.d(TAG,"reqVolumeSlipStop");
        getIsVolumeSlipping().setValue(false);
        getGestureBeginPosition().setValue(0);
        mRelieveStressPatternModel.reqVolumeto(getVolumeValueMoveValue().getValue());
        TimerProcessor.getInstance().stopTimer(RelieveStressFuncDef.Relieve_Stress_VolumeSeekSlip);
        if (null != timer) {
            timer.cancel();
        }
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                getSettingVolumeViewVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                if (null != timer) {
                    timer.cancel();
                }
            }
        }, 0, 5000);
        getVolumeValueMoveValue().setValue(RelieveStressFuncDef.Relieve_Stress_none);
    }

    /**
     * 滑动距离转换成音量值处理
     *
     * @param distance 滑动距离
     */
    public int volumeSeekPosition(float distance) {
        int seekPosition = 0;
        int duration = RelieveStressFuncDef.Relieve_Stress_MaxVolume;
        int currentPosition = getVolumeBeginPosition().getValue();
        int timeChange = (int) (duration * distance * -1);
        MLog.d(TAG,"RelieveStressPatternVM:volumeSeekPosition valueChange = " + timeChange);
        if (0 <= (currentPosition + timeChange) && (currentPosition + timeChange) <= duration) {
            seekPosition = currentPosition + timeChange;
        } else if (currentPosition + timeChange> duration) {
            seekPosition = duration;
        } else {
            seekPosition = 0;
        }
        return seekPosition;
    }

    /**
     * 控件表示反转处理
     *
     */
    public void ChangeCtrlViewVisible() {
        MLog.d(TAG, "ChangeCtrlViewVisible");
        if (mCtrlViewVisible.getValue() == ScenePatternFuncDef.Scene_Pattern_inVisible) {
            mCtrlViewVisible.postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play) {
                getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressTimerIndex().getValue() != 0 &&
                    ((getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play) ||
                            (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause))) {
                getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressStatus().getValue() != RelieveStressFuncDef.Relieve_Stress_Status_Stop &&
                    getRelieveStressStatus().getValue() != RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop) {
                getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            }else {
                getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }
        } else {
            mCtrlViewVisible.postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        }
    }

    /**
     * 控件表示设定处理
     *
     */
    public void setCtrlViewVisible(Boolean ctrlViewVisible) {
        MLog.d(TAG, "setCtrlViewVisible");
        getRelieveStressTimerListVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        if (ctrlViewVisible) {
            mCtrlViewVisible.postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play) {
                getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressTimerIndex().getValue() != 0 &&
                    ((getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play) ||
                            (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause))) {
                getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            } else {
                getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

            if (getRelieveStressStatus().getValue() != RelieveStressFuncDef.Relieve_Stress_Status_Stop &&
                    getRelieveStressStatus().getValue() != RelieveStressFuncDef.Relieve_Stress_Status_Phone_Stop) {
                getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
                getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_Visible);
            }else {
                getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
                getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            }

        } else {
            mCtrlViewVisible.postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressTimerVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressCtrlViewVisibleInPlay().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressCtrlViewVisibleInPause().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressExpandVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
            getRelieveStressNarrowVisible().postValue(ScenePatternFuncDef.Scene_Pattern_inVisible);
        }
    }

    public void makeBigDataStopReason(int type) {
        mRelieveStressPatternModel.makeBigDataStopReason(type);
    }

    private String getTimerText(int index) {
        String timerText = "";
        String []timerList = getApplication().getResources().getStringArray(R.array.relieve_timer_list_text);
        if(index >= 0 && index < timerList.length) {
            timerText = timerList[index];
        }
        return timerText;
    }
}
