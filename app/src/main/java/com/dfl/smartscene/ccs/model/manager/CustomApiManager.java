package com.dfl.smartscene.ccs.model.manager;

import com.dfl.api.app.aiot.IAiotScene;
import com.dfl.api.app.navi.account.IAccount;
import com.dfl.api.app.navi.location.ILocation;
import com.dfl.api.app.navi.navi.INaviScene;
import com.dfl.api.app.navi.route.IRoute;
import com.dfl.api.app.usercenter.common.ICommon;
import com.dfl.api.app.usercenter.cp.IAccountInterflow;
import com.dfl.api.app.usercenter.weather.IWeather;
import com.dfl.api.app.vr.control.ISpeechControl;
import com.dfl.api.app.vr.tts.ITtsAidl;
import com.dfl.api.app.vr.uploadhotwords.IUploadHotwords;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.appcommon.IAppCommon;
import com.dfl.api.base.appcommon.media.IMedia;
import com.dfl.api.da.popup.IPopup;
import com.dfl.api.da.setting.ISetting;
import com.dfl.api.da.setting.ISettingSoundEffect;
import com.dfl.api.da.systemview.ISystemView;
import com.dfl.api.da.widemode.IWideMode;
import com.dfl.api.vehicle.backdoor.IBackDoor;
import com.dfl.api.vehicle.bcm.atmospherelamp.IAtmosphereLamp;
import com.dfl.api.vehicle.bcm.window.IWindow;
import com.dfl.api.vehicle.doorandtrunk.IDoorAndTrunk;
import com.dfl.api.vehicle.hvac.IHvac;
import com.dfl.api.vehicle.seat.ISeat;
import com.dfl.smartscene.ccs.constant.Constants;

import io.reactivex.schedulers.Schedulers;

/**
 * 用于生成customapi相关的类
 * <AUTHOR> Zezheng
 * @date ：2022/7/13 18:07
 * @description ：
 */
public class CustomApiManager extends CommonCustomManager {
    private static final String TAG = "CustomApiManager";
    private static volatile CustomApiManager sInstance;

    public static CustomApiManager getInstance(){
        if (null == sInstance){
            synchronized (CustomApiManager.class){
                if (null == sInstance){
                    sInstance = new CustomApiManager();
                }
            }
        }
        return sInstance;
    }

    private CustomApiManager(){
        LibraryManager.getInstance().getCommonLibraryCustomManager().registerCommonManager(this);
    }

    @Override
    public IHvac getIHvac(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_HVAC_HVAC_SERVICE) instanceof IHvac){
            return (IHvac) otherApiMap.get(Constants.DFL_VEHICLE_HVAC_HVAC_SERVICE);
        }else {
            return null;
        }
    }



    public ISetting getISetting(){
        if(otherApiMap.get(Constants.DFL_DA_SETTING_SETTING_SERVICE) instanceof ISetting){
            return (ISetting) otherApiMap.get(Constants.DFL_DA_SETTING_SETTING_SERVICE);
        }else {
            return null;
        }
    }

    public com.dfl.api.app.navi.setting.ISetting getNaviSetting(){
        if(otherApiMap.get(Constants.DFL_APP_NAVI_SETTING_SETTING_SERVICE) instanceof com.dfl.api.app.navi.setting.ISetting){
            return (com.dfl.api.app.navi.setting.ISetting) otherApiMap.get(Constants.DFL_APP_NAVI_SETTING_SETTING_SERVICE);
        }else {
            return null;
        }
    }

    @Override
    public IAppCommon getIAppCommon(){
        if(otherApiMap.get(Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE) instanceof IAppCommon){
            return (IAppCommon) otherApiMap.get(Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE);
        }else {
            return null;
        }
    }

    @Override
    public ISeat getISeat(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_SEAT_SEAT_SERVICE) instanceof ISeat){
            return (ISeat) otherApiMap.get(Constants.DFL_VEHICLE_SEAT_SEAT_SERVICE);
        }else {
            return null;
        }
    }

    public IBackDoor getIBackDoor(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_BACKDOOR_BACKDOOR_SERVICE) instanceof IBackDoor){
            return (IBackDoor) otherApiMap.get(Constants.DFL_VEHICLE_BACKDOOR_BACKDOOR_SERVICE);
        }else {
            return null;
        }
    }

    public IDoorAndTrunk getIDoor(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_DOORANDTRUNK_DOORANDTRUNK_SERVICE) instanceof IDoorAndTrunk){
            return (IDoorAndTrunk) otherApiMap.get(Constants.DFL_VEHICLE_DOORANDTRUNK_DOORANDTRUNK_SERVICE);
        }else {
            return null;
        }

    }

    public IWindow getIWindow(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_BCM_WINDOW_WINDOW_SERVICE) instanceof IWindow){
            return (IWindow) otherApiMap.get(Constants.DFL_VEHICLE_BCM_WINDOW_WINDOW_SERVICE);
        }else {
            return null;
        }
    }

    public IAtmosphereLamp getIAtmosphereLamp(){
        if(otherApiMap.get(Constants.DFL_VEHICLE_BCM_ATMOSPHERELAMP_ATMOSPHERELAMP_SERVICE) instanceof IAtmosphereLamp){
            return (IAtmosphereLamp) otherApiMap.get(Constants.DFL_VEHICLE_BCM_ATMOSPHERELAMP_ATMOSPHERELAMP_SERVICE);
        }else {
            return null;
        }
    }

    public ISystemView getISystemView(){
        if(otherApiMap.get(Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE) instanceof ISystemView){
            return (ISystemView) otherApiMap.get(Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE);
        }else {
            return null;
        }
    }

    public IMedia getIMedia(){
        if(otherApiMap.get(Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE) instanceof IMedia){
            return (IMedia) otherApiMap.get(Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE);
        }else {
            return null;
        }
    }
    @Override
    public IBaseAPI getIBaseApi(String name){
        IBaseAPI iBaseAPI = otherApiMap.get(name);
        if(iBaseAPI == null){
            return LibraryManager.getInstance().getCommonLibraryCustomManager().getIBaseApi(name);
        }else {
            return otherApiMap.get(name);
        }

    }


    public ILocation getILocation(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_NAVI_LOCATION_LOCATION_SERVICE);
        if(iBaseAPI instanceof ILocation){
            return (ILocation) iBaseAPI;
        }else {
            return null;
        }
    }

    public IAccount getIAccount(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_NAVI_ACCOUNT_ACCOUNT_SERVICE);
        if(iBaseAPI instanceof IAccount){
            return (IAccount) iBaseAPI;
        }else {
            return null;
        }
    }

    public IRoute getIRoute(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_NAVI_ROUTE_ROUTE_SERVICE);
        if(iBaseAPI instanceof IRoute){
            return (IRoute) iBaseAPI;
        }else {
            return null;
        }
    }

    public IWeather getIWeather(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_USERCENTER_WEATHER_WEATHER_SERVICE);
        if(iBaseAPI instanceof IWeather){
            return (IWeather) iBaseAPI;
        }else {
            return null;
        }
    }


    public ITtsAidl getITtsAidl(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_VR_TTS_TTSAIDL_SERVICE);
        if(iBaseAPI instanceof ITtsAidl){
            return (ITtsAidl) iBaseAPI;
        }else {
            return null;
        }
    }

    public IUploadHotwords getIUploadHotwords(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_VR_UPLOADHOTWORDS_UPLOADHOTWORDS_SERVICE);
        if(iBaseAPI instanceof IUploadHotwords){
            return (IUploadHotwords) iBaseAPI;
        }else {
            return null;
        }
    }

    public ISpeechControl getISpeechControl(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_VR_CONTROL_SPEECHCONTROL_SERVICE);
        if(iBaseAPI instanceof ISpeechControl){
            return (ISpeechControl) iBaseAPI;
        }else {
            return null;
        }
    }

    @Override
    public ISettingSoundEffect getISettingSoundEffect(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_DA_SETTING_SETTINGSOUNDEFFECT_SERVICE);
        if(iBaseAPI instanceof ISettingSoundEffect){
            return (ISettingSoundEffect) iBaseAPI;
        }else {
            return null;
        }
    }

    public IWideMode getIWideMode(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_DA_WIDEMODE_WIDEMODE_SERVICE);
        if(iBaseAPI instanceof IWideMode){
            return (IWideMode) iBaseAPI;
        }else {
            return null;
        }
    }

    public IAiotScene getIAiotScene() {
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_AIOT_AIOTSCENE_SERVICE);
        if (iBaseAPI instanceof IAiotScene) {
            return (IAiotScene) iBaseAPI;
        } else {
            return null;
        }

    }

    public IPopup getIPopUp() {
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_DA_POPUP_POPUP_SERVICE);
        if (iBaseAPI instanceof IPopup) {
            return (IPopup) iBaseAPI;
        } else {
            return null;
        }

    }

    public IAccountInterflow getIAccountInterflow() {
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_USERCENTER_CP_ACCOUNTINTERFLOW_SERVICE);
        if (iBaseAPI instanceof IAccountInterflow) {
            return (IAccountInterflow) iBaseAPI;
        } else {
            return null;
        }

    }

    public INaviScene getINaviScene() {
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_NAVI_NAVI_NAVISCENE_SERVICE);
        if (iBaseAPI instanceof INaviScene) {
            return (INaviScene) iBaseAPI;
        } else {
            return null;
        }

    }

    public ICommon getICommon(){
        IBaseAPI iBaseAPI = otherApiMap.get(Constants.DFL_APP_USERCENTER_COMMON_COMMON_SERVICE);
        if(iBaseAPI instanceof ICommon){
            return (ICommon)iBaseAPI;
        }else{
            return null;
        }
    }




    /**
     * 初始化customapi的具体对象类
     */
    @Override
    public void registerCustomApi(){
        Schedulers.computation().scheduleDirect(() -> {
            //初始化系统登录管理类
            initUnknownApi(Constants.DFL_APP_USERCENTER_COMMON_COMMON_SERVICE);
            //初始化da控制类
            initUnknownApi(Constants.DFL_DA_SETTING_SETTING_SERVICE);
            //初始化路线偏好设置
            initUnknownApi(Constants.DFL_APP_NAVI_SETTING_SETTING_SERVICE);
            //初始化车辆控制类
            initUnknownApi(Constants.DFL_BASE_APPCOMMON_APPCOMMON_SERVICE);
            //初始化车门控制类
            initUnknownApi(Constants.DFL_VEHICLE_DOORANDTRUNK_DOORANDTRUNK_SERVICE);
            //初始化后背门控制类
            initUnknownApi(Constants.DFL_VEHICLE_BACKDOOR_BACKDOOR_SERVICE);
            //初始化座椅控制类
            initUnknownApi(Constants.DFL_VEHICLE_SEAT_SEAT_SERVICE);
        });

        Schedulers.computation().scheduleDirect(() -> {
            //初始化空调控制类
            initUnknownApi(Constants.DFL_VEHICLE_HVAC_HVAC_SERVICE);
//        //初始化系统窗口类
            initUnknownApi(Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE);

            //初始化系统声音相关，如声音焦点监听
            initUnknownApi(Constants.DFL_BASE_APPCOMMON_MEDIA_MEDIA_SERVICE);

            // 初始化导航账号服务
            initUnknownApi(Constants.DFL_APP_NAVI_ACCOUNT_ACCOUNT_SERVICE);
            // 初始化导航算路服务
            initUnknownApi(Constants.DFL_APP_NAVI_ROUTE_ROUTE_SERVICE);
            // 初始化导航位置服务
            initUnknownApi(Constants.DFL_APP_NAVI_LOCATION_LOCATION_SERVICE);

            // 初始化用户中心的天气服务
            initUnknownApi(Constants.DFL_APP_USERCENTER_WEATHER_WEATHER_SERVICE);

            //初始化语音交互状态服务
            initUnknownApi(Constants.DFL_APP_VR_CONTROL_SPEECHCONTROL_SERVICE);

            //初始化语音TTS服务
            initUnknownApi(Constants.DFL_APP_VR_TTS_TTSAIDL_SERVICE);

            //初始化语音可见即可说服务
            initUnknownApi(Constants.DFL_APP_VR_UPLOADHOTWORDS_UPLOADHOTWORDS_SERVICE);
        });

        Schedulers.computation().scheduleDirect(() -> {
            //初始化车窗服务
            initUnknownApi(Constants.DFL_VEHICLE_BCM_WINDOW_WINDOW_SERVICE);

            //初始化氛围灯服务
            initUnknownApi(Constants.DFL_VEHICLE_BCM_ATMOSPHERELAMP_ATMOSPHERELAMP_SERVICE);

            //初始化音效服务
            initUnknownApi(Constants.DFL_DA_SETTING_SETTINGSOUNDEFFECT_SERVICE);

            //初始化全屏模式服务
            initUnknownApi(Constants.DFL_DA_WIDEMODE_WIDEMODE_SERVICE);
            //初始化AIOT设备服务
            initUnknownApi(Constants.DFL_APP_AIOT_AIOTSCENE_SERVICE);

            initUnknownApi(Constants.DFL_DA_POPUP_POPUP_SERVICE);

            initUnknownApi(Constants.DFL_APP_USERCENTER_CP_ACCOUNTINTERFLOW_SERVICE);

            initUnknownApi(Constants.DFL_APP_NAVI_NAVI_NAVISCENE_SERVICE);

            //不同车型子模块初始化服务
            LibraryManager.getInstance().getCommonLibraryCustomManager().registerCustomApi();
        });
    }



}
