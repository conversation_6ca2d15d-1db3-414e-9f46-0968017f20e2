package com.dfl.smartscene.ccs;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.RequiresApi;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.dfl.api.app.navi.navi.INaviSceneCallback;
import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.BuildConfig;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.DebugValue;
import com.dfl.smartscene.ccs.model.DoorModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.util.DisplayUtil;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.HotFixManager;
import com.dfl.smartscene.ccs.util.PermissionManager;
import com.dfl.smartscene.ccs.util.TestUtil;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.viewmodel.MainFragmentViewModel;
import com.iauto.uibase.utils.MLog;
import com.tencent.mmkv.MMKV;


public class MainActivity extends FragmentActivity {
    private static final String TAG = "MainActivity";
    private NavController mainController;
    private MainFragmentViewModel mMainFragmentViewModel;

    @RequiresApi(api = Build.VERSION_CODES.R)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mMainFragmentViewModel = new ViewModelProvider(this).get(MainFragmentViewModel.class);
        setWindowParams();

        setContentView(R.layout.activity_main);

        PermissionManager.requestPermission(this);
//        initDebugButton();
        mainController = Navigation.findNavController(this, R.id.fragment_main_nav_host);

        //todo 需优化
        if (getIntent() != null) {
            HandlerUtil.getMainHandler().postDelayed(() -> onNewIntent(getIntent()), 500);
        }

        DisplayUtil.initDisplaySize(this);
        DisplayUtil.initFullScreenSize();

//        ((TextView)findViewById(R.id.textview_package_message)).setText("测试版本v" + BuildConfig.VERSION_NAME + "-"+BuildConfig.buldtime);
        HotFixManager.checkAndApplyPatch(this);
        mMainFragmentViewModel.initToastManager(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        //检查个人中心是否已登录
        mMainFragmentViewModel.init();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        MLog.d(TAG, "onNewIntent");
        super.onNewIntent(intent);
        String type = intent.getStringExtra("ViewType");

        if (type == null) {
            type = intent.getStringExtra("car_show");
        }

        if (type == null) {
            return;
        }
        MLog.d(TAG, "onNewIntentv:" + type);

        switch (type) {
            case "Main":
                returnToMainPage();
                break;
            case "Scene":
                String sceneId = intent.getStringExtra("INTENT_KEY_SCENE_ID");
                if (sceneId == null) {
                    sceneId = intent.getStringExtra("car_action");
                }
                MLog.d(TAG, "SceneId:" + sceneId);
                //scene_card_relax
                startScene(sceneId);
                break;
            case "User":
                returnToUserPage();
                break;
            case "Relieve":
                mainController.navigate(R.id.action_mainFragment_to_relieveStressPatternFragment);
                break;
            default:
                break;
        }
    }

    /**
     * 接受从intent传入的打开某一场景的请求
     *
     * @param sceneId
     */
    private void startScene(String sceneId) {
        MLog.d(TAG, "startScene");

        SceneBean sceneBean;
        sceneBean = SceneDataModel.getInstance().findLibrarySceneBeanById(sceneId);
        if(sceneBean != null){
            ViewControlManager.executeCardScene(sceneBean,true);
            return;
        }
        sceneBean = SceneDataModel.getInstance().findUserSceneBeanById(sceneId);
        if (sceneBean == null) {
            return;
        }
        ViewControlManager.executeScene(sceneBean , "即将执行 ");
//        if (sceneBean.getEditType() == ConstantModelValue.SCENE_EDIT_TYPE_EDIT_CARD || sceneBean.getEditType() == ConstantModelValue.SCENE_EDIT_TYPE_UNEDIT_CARD) {
//            SettingOperation videoOp = sceneBean.findActionOperationById(ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO);
//            if (videoOp != null && mainController.getCurrentDestination().getId() != R.id.sceneSmallViewFragment) {
//                Bundle bundle = new Bundle();
//                bundle.putStringArrayList(ConstantViewValue.FRAGMENT_KEY_SCENE_VIDEO_SCENE_BEAN, new ArrayList<>(videoOp.getListArgs()));
//                mainController.navigate(R.id.action_global_sceneSmallViewFragment, bundle);
//            }
//        }
//        SceneControlManager.getInstance().saveCurrentAndStartScene(sceneBean);
    }


    private void initDebugButton() {
        findViewById(R.id.button_debug).setOnLongClickListener(v -> {
            new AlertDialog.Builder(MainActivity.this).setItems(new String[]{
                    "模拟车门关闭", "清除缓存", "模拟P挡", "导出json数据","gc","通用测试5","通用测试6"
            }, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    switch (which) {
                        case 0:
                            DoorModel.getInstance().testDoor();
                            break;
                        case 1:
                            MMKV mmkv = MMKV.defaultMMKV();
                            mmkv.clearAll();
                            break;
                        case 2:
                            DebugValue.DEBUG_P_LIMIT = true;
                            break;
                        case 3:
                            TestUtil.exportLocalJson();
                            break;
                        case 4:
                            System.gc();
                            break;
                        case 5:
                            try {
                                CustomApiManager.getInstance().getINaviScene().registerNaviSceneCallback(new INaviSceneCallback() {
                                    @Override
                                    public void onUpdateNaviCollectedPlaces(String s) {
                                        LogUtil.d(TAG,s);
                                    }

                                    @Override
                                    public void onSceneConditionMeet(String s) {

                                    }
                                });
                            } catch (NullServiceException e) {
                                e.printStackTrace();
                            }
                            break;
                        case 6:
                        default:
                            break;
                    }
                }
            }).show();
            return false;
        });
    }


    /**
     * 回到首页
     */
    private void returnToMainPage() {
        if (mainController.getCurrentDestination().getId() == R.id.sceneSmallViewFragment) {
            SceneControlManager.getInstance().exitScene();
        } else if (mainController.getCurrentDestination().getId() == R.id.mainFragment) {
            mMainFragmentViewModel.setMainPagePos(0);
        } else {
            mainController.popBackStack(R.id.mainFragment, false);
            mMainFragmentViewModel.setMainPagePos(0);
        }
    }

    /**
     * 进入自定义页面
     */
    private void returnToUserPage() {
        if (mainController.getCurrentDestination().getId() == R.id.sceneSmallViewFragment) {
        } else if (mainController.getCurrentDestination().getId() == R.id.mainFragment) {
            mMainFragmentViewModel.setMainPagePos(1);
            mMainFragmentViewModel.setMyPageCategoryId(ConstantModelValue.CATEGORY_ID_CUSTOM);
        } else {
            mainController.popBackStack(R.id.mainFragment, false);
            mMainFragmentViewModel.setMainPagePos(1);
            mMainFragmentViewModel.setMyPageCategoryId(ConstantModelValue.CATEGORY_ID_CUSTOM);
        }
    }

    private void setWindowParams() {
//        WindowManager.LayoutParams params1 = getWindow().getAttributes();
//        params1.width = (int) getResources().getDimension(R.dimen.x_px_project_width);
//        params1.height = (int) getResources().getDimension(R.dimen.y_px_project_height);
////        params.gravity = Gravity.TOP | Gravity.LEFT;
//        getWindow().setAttributes(params1);

        if (BuildConfig.DEBUG && CarConfigUtil.isPhone()) {
            WindowManager.LayoutParams params = getWindow().getAttributes();
            params.width = (int) getResources().getDimension(R.dimen.x_px_project_width);
            params.height = (int) getResources().getDimension(R.dimen.y_px_project_height);
//        params.gravity = Gravity.TOP | Gravity.LEFT;
            getWindow().setAttributes(params);
        }
    }

}