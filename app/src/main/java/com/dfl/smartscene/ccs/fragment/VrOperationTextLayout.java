package com.dfl.smartscene.ccs.fragment;


import static com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment.EDIT_TYPE_ADD;
import static com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment.EDIT_TYPE_MODIFY;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.model.VRModel;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/12/7 11:35
 * @description ：
 */
public class VrOperationTextLayout extends EditTextLayout{
    private static final String TAG = "VrOperationTextLayout";
    /**
     * 由EditFragment定义，详见该类
     */
    private int operationType = SceneEditorFragment.OPERATION_TYPE_VR;
    /**
     * 0：修改操作
     * 1：新增操作
     */
    private int editType = 0;
    /**
     * 修改位置
     */
    private int pos = 0;
    private final List<String> mVRHotWords = new ArrayList<>();//保存的所有热词
    private  List<String> mOriginalWords = new ArrayList<>();//当前编辑的场景初始状态下的所有指令
    private  List<String> mCurrentWords = new ArrayList<>();//编辑状态下当前所有指令（不包含输入框内的结果）
    public VrOperationTextLayout(@NonNull Context context) {
        super(context);
    }

    public VrOperationTextLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public VrOperationTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public VrOperationTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void showList(List<String> mList){
        this.editType = EDIT_TYPE_ADD;
        if (null != mList){
            mCurrentWords.clear();
            mCurrentWords.addAll(mList);
        }
        show(null);
    }

    private void getVrHotWords() {
        mVRHotWords.clear();
        mVRHotWords.addAll(VRModel.getInstance().getVRHotWords());
    }

    public void show(){
        this.editType = EDIT_TYPE_ADD;
        mCurrentWords.clear();
        show(null);
    }

    public void show(int pos, SettingOperation settingOperation){
        this.editType = EDIT_TYPE_MODIFY;
        this.pos = pos;
        mCurrentWords.clear();
        mSettingOperation = settingOperation;
        show(settingOperation.getListArgs().get(0));
    }

    public void show(int pos, SettingOperation settingOperation,List<String> mList){
        this.editType = EDIT_TYPE_MODIFY;
        this.pos = pos;
        mSettingOperation = settingOperation;
        if (null != mList){
            mCurrentWords.clear();
            mCurrentWords.addAll(mList);
        }
        show(settingOperation.getListArgs().get(0));
    }

    @Override
    public void show(String text) {
        LogUtil.d(TAG, "show: "+text);
        super.show(text);
    }

    /**
     * 设置初始的值
     * @param list
     */
    public void setOriginalWords(List<String> list){
        mOriginalWords.clear();
        mOriginalWords.addAll(list);
    }

    @Override
    protected void onclickConfirmButton() {
        if (checkSameVrOperation()){
            ToastManager.showToast("指令重复了，换一个吧");
            return;
        }
        if(editType == EDIT_TYPE_ADD){
            mOperationChangeListerner.onOperationAdd(SceneEditorFragment.OPERATION_TYPE_VR,creatOperation());
        }else {
            mOperationChangeListerner.onOperationModify(SceneEditorFragment.OPERATION_TYPE_VR,pos,creatOperation());
        }
        super.onclickConfirmButton();

    }

    /**
     * 检查是否有相同语音指令
     * @return
     */
    private boolean checkSameVrOperation(){
        //获取保存的最新的VR热词
        getVrHotWords();
        //输入框文本内容
        String updateWord = mEditTextContent.getText().toString().trim();
        LogUtil.e(TAG, "checkSameData: updateWord " + updateWord + " mOriginalWords: " + mOriginalWords + " mCurrentWords: " + mCurrentWords + " mVRHotWords: " + mVRHotWords);
        LogUtil.e(TAG, "checkSameData: pos" + pos);
        //先判断内部3个是否重合
        if (editType == EDIT_TYPE_ADD) {
            //添加状态下判断是否已在当前场景中
            if (mCurrentWords.contains(updateWord)) {
                return true;
            }
        } else {
            //编辑状态下判断是否已在当前场景中且编辑位置不同
            if (!ListUtil.isEmpty(mCurrentWords)) {
                for (int i = 0; i < mCurrentWords.size(); i++) {
                    if (mCurrentWords.get(i).equals(updateWord) && i != pos) {
                        return true;
                    }
                }
            }
        }
        //外部重合判断，使用移除原始数据的所有热词作过滤
        List<String> otherWordList = new ArrayList<>(mVRHotWords);
        otherWordList.removeAll(mOriginalWords);
        return otherWordList.contains(updateWord);
    }

    public SettingOperation creatOperation(){
        SettingOperation settingOperation = new SettingOperation();
        settingOperation.setOperationId(ConstantModelValue.OPERATION_ID_CONDITION_VR);
        settingOperation.setDesc(mEditTextContent.getText().toString().trim());
        settingOperation.setListArgs(Arrays.asList(mEditTextContent.getText().toString()));
        return settingOperation;
    }

    @Override
    public void hide() {
        super.hide();
        mOperationChangeListerner = null;
    }

    private SettingOperation mSettingOperation;
    private OperationBaseDialogFragment.OperationChangeListerner mOperationChangeListerner;

    public void setOperationChangeListerner(OperationBaseDialogFragment.OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
    }

}
