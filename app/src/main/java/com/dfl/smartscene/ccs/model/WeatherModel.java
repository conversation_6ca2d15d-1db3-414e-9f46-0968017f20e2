package com.dfl.smartscene.ccs.model;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.dfl.api.app.navi.location.ILocation;
import com.dfl.api.app.usercenter.weather.CityBean;
import com.dfl.api.app.usercenter.weather.IWeather;
import com.dfl.api.app.usercenter.weather.IWeatherCallback;
import com.dfl.api.app.usercenter.weather.WeatherItems;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.navi.CarLocationInfo;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.WeatherValue;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.util.GsonUtil;
import com.google.gson.JsonSyntaxException;
import com.iauto.uibase.utils.MLog;

import java.util.List;

public class WeatherModel implements StateBaseModel {

    private static final String TAG = "WeatherModel";

    private static final long SECONDS_OF_WAIT_TIME = 5L;// 等待时间5s

    private static volatile WeatherModel sInstance;

    private static volatile WeatherItems mWeatherItems;

    private static final int TIME_SYNC_HAS_DATA = 30 * 60 * 1000;
    private static final int TIME_SYNC_HAS_NO_DATA = 60 * 1000;

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public static WeatherModel getInstance() {
        if (null == sInstance) {
            synchronized (WeatherModel.class) {
                if (null == sInstance) {
                    sInstance = new WeatherModel();
                }
            }
        }
        return sInstance;
    }

    private WeatherModel() {

    }

    public void init() {
        MLog.d(TAG, "WeatherModel init()");
        mHandler.post(this::syncWeatherData);
    }

    @Override
    public boolean checkStateOperationMeet(SettingOperation settingOperation) {

        MLog.d(TAG,"checkStateOperationMeet settingOperation = " + settingOperation);

        if (null == settingOperation) {
            return false;
        }

        // 天气
        String operationId = settingOperation.getOperationId();
        if (ConstantModelValue.OID_STA_ENV_WEATHER.equals(operationId)) {
            return checkWeather2(settingOperation);
        }

        // 车外温度
        if(ConstantModelValue.OID_STA_ENV_OUT_TEMP.equals(operationId)) {
            return checkOutTemp(settingOperation);
        }

        // 车外空气质量
        if(ConstantModelValue.OID_STA_ENV_OUT_AIR_QUALITY.equals(operationId)) {
            return checkOutAirQuality(settingOperation);
        }

        return false;
    }

    private boolean checkWeather2(SettingOperation settingOperation) {
        if (mWeatherItems == null) {
            LogUtil.d(TAG, "checkWeather: mWeatherItems is null ");
            return false;
        }
        String conditionNum = mWeatherItems.getConditionNum();
        int condition = Integer.parseInt(conditionNum);
        String weatherCondition = WeatherValue.parseWeatherCondition(condition);
        if (TextUtils.isEmpty(weatherCondition)) {
            return false;
        } else {
            List<String> listArgs = settingOperation.getListArgs();
            if (null != listArgs && !listArgs.isEmpty()) {
                MLog.d(TAG, "checkWeather weather condition = " + weatherCondition);
                return simpleStateCheck(listArgs,weatherCondition);
            } else {
                return false;
            }
        }
    }


    // 检测车外温度
    private boolean checkOutTemp(SettingOperation settingOperation) {

        if (mWeatherItems == null) {
            LogUtil.d(TAG, "checkOutTemp: mWeatherItems is null ");
            return false;
        }
        String temp = mWeatherItems.getTemp();
        LogUtil.d(TAG, "checkOutTemp: "+settingOperation.getListArgs()+" ,,"+temp);
        boolean result = simpleRangeStateCheck(settingOperation.getListArgs(),temp);
        LogUtil.d(TAG, "checkOutTemp: result "+result);
        return result;
    }

    // 检测车外空气质量
    private boolean checkOutAirQuality(SettingOperation settingOperation) {
        if (mWeatherItems == null) {
            return false;
        }

        String aqiValue = mWeatherItems.getAqiValue();

        return simpleStateCheck(settingOperation.getListArgs(),aqiValue);
    }

    private void syncWeatherRunnable() {
        mHandler.removeCallbacksAndMessages(null);
        int time = null == mWeatherItems ? TIME_SYNC_HAS_NO_DATA : TIME_SYNC_HAS_DATA;
        mHandler.postDelayed(this::syncWeatherData, time);
        MLog.d(TAG, "syncWeatherRunnable after " + time + "ms");
    }

    IWeatherCallback iWeatherCallback = new IWeatherCallback() {
        @Override
        public void onReqWeather(int i, WeatherItems weatherItems) {
            if (i == 1) {// 获取数据成功
                mWeatherItems = weatherItems;
                MLog.d(TAG, "onReqWeather = " + JSON.toJSONString(weatherItems));
                MLog.d(TAG, "onReqWeather = " + WeatherValue.packageWeatherInfo(weatherItems));
            } else {// 获取数据失败
                MLog.d(TAG, "checkWeather iWeatherCallback data acquisition failure");
            }
            syncWeatherRunnable();
        }

        @Override
        public void replayCityList(int i, List<CityBean> list) {
            syncWeatherRunnable();
        }
    };

    private void syncWeatherData() {
        try {
            IWeather iWeather = CustomApiManager.getInstance().getIWeather();
            ILocation iLocation = CustomApiManager.getInstance().getILocation();

            // 天气服务未连接
            if (iWeather == null || iLocation == null) {
                MLog.d(TAG, "checkWeather iWeather = " + iWeather + ", iLocation = " + iLocation);
                syncWeatherRunnable();
                return;
            }

            CarLocationInfo carLocationInfo;
            String locationResult = iLocation.getCarLocationInfo();
            MLog.d(TAG, "checkWeather locationResult "+locationResult);
            try {
                carLocationInfo = GsonUtil.strToBean(locationResult, CarLocationInfo.class);
            } catch (JsonSyntaxException e) {
                MLog.d(TAG, "checkWeather car location info = null");
                return;
            }

            MLog.i(TAG, "checkWeather car location info, carLocationInfo = " + carLocationInfo.toString());
            boolean checkResult = checkLocationInfoValid(carLocationInfo);
            if(!checkResult){
                locationResult = iLocation.getCarLocationInfo();
                MLog.d(TAG, "checkWeather2 locationResult "+locationResult);
                try {
                    carLocationInfo = GsonUtil.strToBean(locationResult, CarLocationInfo.class);
                } catch (JsonSyntaxException e) {
                    MLog.d(TAG, "checkWeather2 car location info = null");
                    return;
                }
                MLog.i(TAG, "checkWeather2 car location info, carLocationInfo = " + carLocationInfo.toString());
            }

            iWeather.registerCallback(iWeatherCallback);
            iWeather.reqWeather(carLocationInfo.getProvince(), carLocationInfo.getCityName(), carLocationInfo.getDistrictName());
        } catch (Exception e) {
            e.printStackTrace();
            syncWeatherRunnable();
        }
    }
    private boolean checkLocationInfoValid(CarLocationInfo carLocationInfo){
        boolean result = true;
        if (carLocationInfo == null) {
            return false;
        }
        if (carLocationInfo.getProvince() == null && carLocationInfo.getCityName() == null && carLocationInfo.getDistrictName() == null) {
            return false;
        }
        String province = "";
        String city = "";
        String district = "";
        if (carLocationInfo.getProvince() != null) {
            province = carLocationInfo.getProvince();
        }
        if (carLocationInfo.getCityName() != null) {
            city = carLocationInfo.getCityName();
        }
        if (carLocationInfo.getDistrictName() != null) {
            district = carLocationInfo.getDistrictName();
        }
        if (province.isEmpty() && city.isEmpty() && district.isEmpty()) {
            return false;
        }
        return result;
    }


    public static WeatherItems getmWeatherItems() {
        return mWeatherItems;
    }
}
