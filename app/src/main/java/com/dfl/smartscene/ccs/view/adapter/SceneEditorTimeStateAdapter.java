package com.dfl.smartscene.ccs.view.adapter;

import android.view.View;

import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SettingOperation;


/**
 * <AUTHOR>
 * @date ：2022/11/11 15:58
 * @description ：编辑页面时间的相关的操作adapter，特点：隐藏icon
 */
public class SceneEditorTimeStateAdapter extends SceneEditorAdapter{

    @Override
    protected BaseHolder<SettingOperation> getViewHolder(View view, int viewType) {
        return new SceneEditorTimeStateHolder(view);
    }

    class SceneEditorTimeStateHolder extends SceneEditorHolder{

        public SceneEditorTimeStateHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            super.setupData(settingOperation, position);
            mImageViewIcon.setVisibility(View.GONE);
        }

    }
}
