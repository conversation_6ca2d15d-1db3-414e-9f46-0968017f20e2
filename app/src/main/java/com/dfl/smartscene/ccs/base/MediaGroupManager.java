package com.dfl.smartscene.ccs.base;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.media.session.PlaybackState;
import android.os.IBinder;
import android.os.RemoteException;
import android.support.v4.media.session.MediaControllerCompat.TransportControls;

import com.dfl.multimedia.IMediaGroupInfo;
import com.dfl.multimedia.ISourceInfoCallback;
import com.dfl.multimedia.base.SActiveSourceInfo;
import com.dfl.multimedia.base.SSourceInfo;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * showLocalView
 * 音源管理Service
 */
public class MediaGroupManager {
    private final String TAG = MediaGroupManager.class.getSimpleName();
    @SuppressLint("StaticFieldLeak")
    private static MediaGroupManager sMediaGroupManager = null;
    private Context mContext = null;
    private IMediaGroupInfo mAidl;
    private SourceInfoCallback mSourceInfoCallback;

    // for retry connect
    private static final int ReconnectDelay = 1000;
    private static final int MaxRecCnt = 30;
    private int mReConnectCnt = 0;

    private static final String MM_SERVICE = "com.dfl.multimedia.service.CMediaGroupService";
    private static final String MM_PACKAGE = "com.dfl.multimedia";

    // MediaSessionControl对象集合
    private Map<String, MediaSessionControl> mMediaSessionControlMap = new HashMap<>();
    private List<SSourceInfo> mMediaAppList = new ArrayList<>();
    private static final String QQ_PACKAGE =  "com.dfl.qqmusic";
    private static final String QQ_SERVICE =  "com.dfl.module_player.service.media_session.SongMediaService";

    private MediaGroupManager(Context context) {
        mContext = context;
        bind();
    }

    /**
     * create RelieveStressPatternModel's instance
     * @param context
     */
    public static synchronized void create(Context context) {
        if(sMediaGroupManager == null){
            sMediaGroupManager = new MediaGroupManager(context);
        }
    }

    public static MediaGroupManager getInstance(){
        return sMediaGroupManager;
    }


    /**
     * bind
     * 绑定音源管理Service
     */
    public void bind() {
        MLog.d(TAG, "bind");
        connectService();
    }

    /**
     * unbind
     * 解绑音源管理Service
     */
    public void unbind() {
        MLog.d(TAG, "unbind");
        if(mAidl != null) {
            try {
                mAidl.unregisterSourceInfoCallback(mSourceInfoCallback);
            } catch (Exception e) {
                MLog.e(TAG, "unbind");
            }
            mContext.unbindService(mServiceConnection);
            mContext = null;
            mAidl = null;
        }
    }

    /**
     * reconnect method
     */
    private void scheduleServiceReconnection() {
        MLog.d(TAG, "scheduleServiceReconnection");

        if (mAidl == null) {
            HandlerThreadProcessor.getInstance().postDelayedToMainThread(new Runnable() {
                @Override
                public void run() {
                    if (mAidl == null) {
                        connectService();
                    }
                }
            }, ReconnectDelay);
        } else {
            MLog.e(TAG, "scheduleServiceReconnection handler is null");
        }

    }

    /**
     * 绑定音源管理Service
     */
    private void connectService() {
        MLog.d(TAG, "connectService");
        Intent intent = new Intent();
        intent.setClassName(MM_PACKAGE, MM_SERVICE);
        if (!mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE)) {
            if (mReConnectCnt > MaxRecCnt) {
                MLog.d(TAG, "connectService:bind failed");
                return;
            }
            mReConnectCnt++;
            scheduleServiceReconnection();
        } else {
            MLog.d(TAG, "connectService:bind Success");
            mReConnectCnt = 0;
        }
    }

    /**
     * 连接所有的 audio source
     */
    public void connectAllMediaSource() {
        MLog.d(TAG, "connectAllMediaSource");
        for (SSourceInfo info : mMediaAppList) {
            String pkgName = info.m_PackageName;
            String name = info.m_ServiceName;

            MLog.d(TAG, "list packageName == " + pkgName
                    + " name == " + name);
            MediaSessionControl mediaSession = mMediaSessionControlMap.get(pkgName);
            if (null != mediaSession) {
                continue;
            }
            //connect all source with mediaSession
            MediaSessionControl mediaSessionControl = new MediaSessionControl(mContext, pkgName,
                    name);
            mMediaSessionControlMap.put(pkgName, mediaSessionControl);
        }
        MediaSessionControl qqMediaSession = mMediaSessionControlMap.get(QQ_PACKAGE);
        if (null == qqMediaSession) {
            //connect all source with mediaSession
            MediaSessionControl mediaSessionControl = new MediaSessionControl(mContext, QQ_PACKAGE,
                    QQ_SERVICE);
            mMediaSessionControlMap.put(QQ_PACKAGE, mediaSessionControl);
        }
    }

    public TransportControls getSessionTransportControls(String pkgName) {
        MediaSessionControl mediaSession = mMediaSessionControlMap.get(pkgName);
        if (null == mediaSession) {
            return null;
        }
        else {
            return mediaSession.getTransportControls();
        }
    }

    public int getSessionPlayState(String pkgName) {
        MediaSessionControl mediaSession = mMediaSessionControlMap.get(pkgName);
        if (null == mediaSession) {
            MLog.d(TAG, "getSessionPlayState null " +pkgName);
            return PlaybackState.STATE_NONE;
        }
        else {
            MLog.d(TAG, "getSessionPlayState pkgName " +pkgName+" state "+mediaSession.getPlayState());
            return mediaSession.getPlayState();
        }
    }
    
    /**
     * Service连接Callback
     */
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            MLog.d(TAG, "onServiceConnected");
            mAidl = IMediaGroupInfo.Stub.asInterface(iBinder);
            if (mAidl != null) {
                try {
                    mSourceInfoCallback = new SourceInfoCallback();
                    mAidl.registerSourceInfoCallback(mSourceInfoCallback);

                    //listen to icar binder's death
                    iBinder.linkToDeath(new MultimediaBindDeathRecipient(), 0);
                } catch (RemoteException remoteException) {
                    MLog.e(TAG, "onServiceConnected:exception");
                }
            } else {
                MLog.d(TAG, "onServiceConnected");
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
            MLog.d(TAG, "onServiceDisconnected");
            mAidl = null;
        }

        @Override
        public void onBindingDied(ComponentName name) {
            MLog.d(TAG, "onBindingDied");
        }
    };

    /**
     * 死亡监听Callback
     */
    private class MultimediaBindDeathRecipient implements IBinder.DeathRecipient {
        public void binderDied() {
            MLog.d(TAG, "binderDied");
            mAidl = null;
            scheduleServiceReconnection();
        }
    }
    
    /**
     * SourceInfoCallback
     * 音源管理Callback
     */
    public class SourceInfoCallback extends ISourceInfoCallback.Stub {
        @Override
        public void onActiveSourceInfoChange(SActiveSourceInfo info) {
            MLog.d(TAG, "onActiveSourceInfoChange");

        }

        @Override
        public void onSourceListInfoChange(List<SSourceInfo> info) throws RemoteException {
            MLog.d(TAG, "onSourceListInfoChange");

        }

        @Override
        public void onRecommendWordsChange(String words) {
            MLog.d(TAG, "onRecommendWordsChange:"+words);
        }

        public void onMediaAppListInfoChange(List<SSourceInfo> info) throws RemoteException {
            MLog.d(TAG, "onMediaAppListInfoChange");
            MLog.d(TAG, "onMediaAppListInfoChange:info size"+info.size());
            mMediaAppList = info;
            MLog.d(TAG, "onMediaAppListInfoChange:mMediaAppList size"+mMediaAppList.size());
            if (!mMediaAppList.isEmpty()) {
                connectAllMediaSource();
            }
        }
    }
}
