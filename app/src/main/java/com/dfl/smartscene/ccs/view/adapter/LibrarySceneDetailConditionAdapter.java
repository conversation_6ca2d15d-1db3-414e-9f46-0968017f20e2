package com.dfl.smartscene.ccs.view.adapter;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.util.ImageUtil;


/**
 * <AUTHOR>
 * @date ：2022/10/12 11:23
 * @description ：用户广场场景详情里的触发操作描述item
 */
public class LibrarySceneDetailConditionAdapter extends BaseAdapter<LibrarySceneDetailConditionAdapter.DataBean> {


    @Override
    protected BaseHolder<DataBean> getViewHolder(View view, int viewType) {
        return new Holder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_library_scene_detail_condition_state;
    }

    public class Holder extends BaseHolder<DataBean> {

        private TextView mTextViewTitle;
        private TextView mTextViewDesc;
        private ImageView mImageViewIcon;
        private View mDivide;

        public Holder(View itemView) {
            super(itemView);
            mTextViewTitle = itemView.findViewById(R.id.textview_library_scene_detail_condition_item_title);
            mTextViewDesc = itemView.findViewById(R.id.textview_library_scene_detail_condition_item_desc);
            mImageViewIcon = itemView.findViewById(R.id.imageview_library_scene_detail_condition_item_icon);
            mDivide = itemView.findViewById(R.id.divide_item_library_scene_detail_condition);
        }

        @Override
        public void setupData(DataBean dataBean, int position) {
            setTitle(dataBean.getTitle());
            setDesc(dataBean.getDesc());
            if("gone".equals(dataBean.getIcon())){
                mImageViewIcon.setVisibility(View.GONE);
            }else {
                mImageViewIcon.setVisibility(View.VISIBLE);
                ImageUtil.loadIcon(dataBean.getIcon(), mImageViewIcon);
            }
            mDivide.setVisibility(dataBean.isDivide() ? View.VISIBLE : View.GONE);
        }

        protected void setTitle(String value){
            mTextViewTitle.setText(value);
        }

        protected void setDesc(String value){
            if(value.contains(" ")){
                SpannableString spannableString = new SpannableString(value);
                int index = value.indexOf(" ");
                spannableString.setSpan(new ForegroundColorSpan(mTextViewDesc.getResources().getColor(R.color.color_text_selected)),index,value.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                mTextViewDesc.setText(spannableString);
            }else {
                mTextViewDesc.setText(value);
            }
        }
    }

    public static class DataBean {
        private String title;
        private String desc;
        private String icon;
        private boolean divide;

        public DataBean(String title, String desc, String icon) {
            this.title = title;
            this.desc = desc;
            this.icon = icon;
        }

        public DataBean(String title, String desc, String icon, boolean divide) {
            this.title = title;
            this.desc = desc;
            this.icon = icon;
            this.divide = divide;
        }

        public DataBean(String title, String desc) {
            this.title = title;
            this.desc = desc;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public boolean isDivide() {
            return divide;
        }

        public void setDivide(boolean divide) {
            this.divide = divide;
        }
    }
}
