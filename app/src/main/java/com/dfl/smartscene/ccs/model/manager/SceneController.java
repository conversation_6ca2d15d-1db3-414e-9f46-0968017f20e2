package com.dfl.smartscene.ccs.model.manager;


import android.os.RemoteException;

import com.dfl.api.base.NullServiceException;
import com.dfl.dflcommonlibs.rxbus.RxBus;

import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.busevent.VideoStateEvent;
import com.dfl.smartscene.ccs.busevent.VideoTimeEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.SceneStateListener;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.MediaModel;
import com.dfl.smartscene.ccs.util.CountDownTimer;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.viewmodel.SoundPoolManager;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.LogUtil;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> Zezheng
 * @date ：2022/11/14 14:05
 * @description ：用于控制首页卡片类型场景的进入与退出
 */
public class SceneController implements SceneStateListener {

    private static final String TAG = "SceneController";
    private SoundPoolManager soundPoolManager;
    public static final int STATE_IDLE = 0;
    public static final int STATE_START = 1;
    public static final int STATE_PAUSE = 2;
    public static final int STATE_STOP = 3;
    public static final int STATE_DESTROY = 4;
    private SceneBean mSceneBean;
    //音量需恢复
    private Integer mediaVolume = null;
    //多媒体需停止
    private boolean stopMedia = false;
    //前排氛围灯开关
    private Integer atmLightFront = null;
    //后排氛围灯开关
    private Integer atmLightRear = null;
    //音乐律动
    private Boolean musicRhythm = null;
    //主驾睡眠恢复
    private Boolean driverSeatSleep = null;
    //主驾休闲恢复
    private Boolean driverSeatRelax = null;
    //副驾睡眠恢复
    private Boolean passagerSeatSleep = null;
    //副驾休闲恢复
    private Boolean passagerSeatRelax = null;
    //主驾按摩恢复
    private Integer driverMassageMode = null;

    //副驾按摩恢复
    private Integer passagerMassageMode = null;
    //主驾前后位置
    private Integer driverSeatFR = null;
    //主驾上下位置
    private Integer driverSeatTB = null;
    //主驾靠背角度
    private Integer driverSeatBD = null;

    //主驾前后位置
    private Integer passagerSeatFR = null;
    //主驾上下位置
    private Integer passagerSeatTB = null;
    //主驾靠背角度
    private Integer passagerSeatBD = null;

    private int sState = STATE_IDLE;

    private CountDownTimer mTimer;

    private SceneBean stopScene;

    public SceneController(SceneBean sceneBean) {
        mSceneBean = sceneBean;
    }

    public SceneController() {
    }


    public int getsState() {
        return sState;
    }

    public SceneBean getSceneBean() {
        return mSceneBean;
    }

    public String getSceneId(){
        return mSceneBean.getSceneId();
    }
    private SettingOperation findActionOperationById(String id) {
        for (SettingOperation settingOperation : mSceneBean.getActionOperations()) {
            if (settingOperation.getOperationId().equals(id)) {
                return settingOperation;
            }
        }
        return null;
    }

    /**
     * 做一些准备工作，比如记忆一下当前的车控状态
     */
    private void restoreCarState() {
        if (mediaVolume != null) {
            DAModel.getInstance().setMediaVolume(mediaVolume);
        }
        if (driverSeatSleep != null && driverSeatSleep) {

        }
        if (driverSeatRelax != null && driverSeatRelax) {

        }
        if (passagerSeatSleep != null && passagerSeatSleep) {

        }
        if (passagerSeatRelax != null && passagerSeatRelax) {

        }
//        if (driverSeatFR != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(1, 1, driverSeatFR);
//        }
//        if (driverSeatTB != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(1, 2, driverSeatTB);
//        }
//        if (driverSeatBD != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(1, 3, driverSeatBD);
//        }
//
//        if (passagerSeatFR != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(2, 1, passagerSeatFR);
//        }
//        if (passagerSeatTB != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(2, 2, passagerSeatTB);
//        }
//        if (passagerSeatBD != null) {
//            LibraryManager.getInstance().getLibrarySeatModel().setSeatMovementPos(2, 3, passagerSeatBD);
//        }
//
//        if (atmLightFront != null) {
//            try {
//                CustomApiManager.getInstance().getIAtmosphereLamp().setAtmosphereLampColorSwitch(0, atmLightFront);
//            } catch (NullServiceException | NullPointerException e) {
//                e.printStackTrace();
//            }
//        }
//        if (atmLightRear != null) {
//            try {
//                CustomApiManager.getInstance().getIAtmosphereLamp().setAtmosphereLampColorSwitch(1, atmLightFront);
//            } catch (NullServiceException | NullPointerException e) {
//                e.printStackTrace();
//            }
//        }
        if (musicRhythm != null) {

        }
        if (stopMedia) {
//            MediaModel.getInstance().pauseCurrentMedia();
        }
    }


    @Override
    public void onScenePrepare() {
        List<SettingOperation> settingOperations = new ArrayList<>();
        settingOperations.add(new SettingOperation(null,ConstantModelValue.OID_ACT_APP_MEDIA_VOLUME,null));
        for (SettingOperation settingOperation : settingOperations) {
            switch (settingOperation.getOperationId()) {
                //主驾睡眠位置
                case ConstantModelValue.OID_ACT_SEAT_DRIVER_SLEEP_MEMORY:
                    driverSeatSleep = true;
                    break;
                //主驾休闲位置
                case ConstantModelValue.OID_ACT_SEAT_DRIVER_RELAX_MEMORY:
                    driverSeatRelax = true;
                    break;
                //副驾睡眠位置
                case ConstantModelValue.OID_ACT_SEAT_PASSAGER_SLEEP_MEMORY:
                    passagerSeatSleep = true;
                    break;
                //副驾休闲位置
                case ConstantModelValue.OID_ACT_SEAT_PASSAGER_RELAX_MEMORY:
                    passagerSeatRelax = true;
                    break;
                //氛围灯开关
                case ConstantModelValue.OID_ACT_LIGHT_ATM_SWITCH:
                    try {
                        atmLightFront = CustomApiManager.getInstance().getIAtmosphereLamp().getAtmosphereLampColorSwitch(0);
                        atmLightRear = CustomApiManager.getInstance().getIAtmosphereLamp().getAtmosphereLampColorSwitch(1);
                    } catch (NullServiceException | RemoteException | NullPointerException e) {
                        e.printStackTrace();
                    }
                    break;
                //主驾按摩
//                case ConstantModelValue.OID_ACT_SEAT_DRIVER_MASSAGE:
//                    try {
//                        driverMassageMode = CustomApiManager.getInstance().getISeat().getSeatMassage(1);
//                    } catch (NullServiceException | RemoteException | NullPointerException e) {
//                        e.printStackTrace();
//                    }
//                    break;
//                //副驾按摩
//                case ConstantModelValue.OID_ACT_SEAT_PASSAGER_MASSAGE:
//                    try {
//                        passagerMassageMode = CustomApiManager.getInstance().getISeat().getSeatMassage(2);
//                    } catch (NullServiceException | RemoteException | NullPointerException e) {
//                        e.printStackTrace();
//                    }
//                    break;

                //主驾位置
                case ConstantModelValue.OID_ACT_SEAT_DRIVER_ADJUST:
                    try {
                        driverSeatFR = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(1, 1);
                        driverSeatTB = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(1, 2);
                        driverSeatBD = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(1, 3);
                    } catch (NullServiceException | RemoteException | NullPointerException e) {
                        e.printStackTrace();
                    }
                    break;
                //副驾位置
                case ConstantModelValue.OID_ACT_SEAT_PASSAGER_ADJUST:
                    try {
                        passagerSeatFR = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(2, 1);
                        passagerSeatTB = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(2, 2);
                        passagerSeatBD = CustomApiManager.getInstance().getISeat().getSeatMovementPosition(2, 3);

                    } catch (NullServiceException | RemoteException | NullPointerException e) {
                        e.printStackTrace();
                    }
                    break;
                //音量
                case ConstantModelValue.OID_ACT_APP_MEDIA_VOLUME:
                    mediaVolume = DAModel.getInstance().getMediaVolume();
                    break;
                //音乐律动
                case ConstantModelValue.OID_ACT_LIGHT_ATM_RHYTHM:
//                    try {
//                        passagerMassageMode = CustomApiManager.getInstance().getISeat().getSeatMassage(2);
//                    } catch (NullServiceException | RemoteException | NullPointerException e) {
//                        e.printStackTrace();
//                    }
                    break;
                //多媒体停止
                case ConstantModelValue.OID_ACT_APP_MEDIA_MUSIC:
                case ConstantModelValue.OID_ACT_APP_MEDIA_BOOK:
                case ConstantModelValue.OID_ACT_APP_MEDIA_RADIO:
                    stopMedia = true;
                    break;
                default:
                    break;

            }
        }
    }

    private int totalTimeSecond = -1;

    public void setTotalTimeSecond(int time){
        totalTimeSecond = time;
    }

    @Override
    public void onSceneStart() {
        sState = STATE_START;
        MLog.d(TAG,"onSceneStart sState = " + sState);
        soundPoolManager = SoundPoolManager.getInstance();
        if (totalTimeSecond != -1) {
            //参数的第3个是执行时间，单位s
            mTimer = new CountDownTimer(totalTimeSecond * 1000L, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    RxBus.getDefault().post(new VideoTimeEvent(millisUntilFinished));
                }

                @Override
                public void onFinish() {
                    LogUtil.d(TAG, "onFinish: ");
                    RxBus.getDefault().post(new VideoTimeEvent(0));
                    HandlerUtil.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            ToastManager.showToast("倒计时已结束");
                        }
                    });
                    SceneControlManager.getInstance().stopScene();
                    //播报提示音
                    soundPoolManager.playSceneFinishNotification();
                }
            };
            mTimer.start();
        }else {
            //执行不计时
            RxBus.getDefault().post(new VideoTimeEvent(-1));
        }
        RxBus.getDefault().post(new VideoStateEvent(STATE_START));

    }

    @Override
    public void onSceneStop() {
        LogUtil.d(TAG," onSceneStop sState = " + sState);
        if(sState < STATE_STOP){
            ToastManager.showToast("舒压模式已结束");
            LogUtil.d(TAG, " onSceneStop: 舒压模式已结束");
        }
        sState = STATE_STOP;
        if(mTimer != null){
            mTimer.cancel();
        }
        RxBus.getDefault().post(new VideoStateEvent(STATE_STOP));
        restoreCarState();
    }

    @Override
    public void onScenePause() {
        sState = STATE_PAUSE;
        MLog.d(TAG,"onScenePause sState = " + sState);
        if(mTimer != null){
            mTimer.pause();

        }
        RxBus.getDefault().post(new VideoStateEvent(STATE_PAUSE));
        if(MediaModel.getInstance().isCurrentFullMusic()){
            MediaModel.getInstance().pauseCurrentMedia();
        }
    }

    @Override
    public void onSceneResume() {
        sState = STATE_START;
        MLog.d(TAG,"onSceneResume sState = " + sState);
        if(mTimer != null){
            mTimer.resume();
        }
        RxBus.getDefault().post(new VideoStateEvent(STATE_START));
        if(MediaModel.getInstance().isCurrentFullMusic()){
            MediaModel.getInstance().resumeCurrentMedia();
        }
    }

    @Override
    public void onSceneDestroy() {
        sState = STATE_DESTROY;
        MLog.d(TAG,"onSceneDestroy sState = " + sState);
        mTimer = null;
        RxBus.getDefault().post(new VideoStateEvent(STATE_DESTROY));
        if(MediaModel.getInstance().isCurrentFullMusic()){
            MediaModel.getInstance().disconnectPlayer();
        }

    }
}
