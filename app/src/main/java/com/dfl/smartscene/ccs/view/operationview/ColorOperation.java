package com.dfl.smartscene.ccs.view.operationview;

import android.graphics.Color;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.ColorSlideBar;
import com.dfl.smartscene.ccs.view.weight.LongPressButton;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

public class ColorOperation implements OperationBaseView, ConflictViewClient{

    private static final String TAG = ColorOperation.class.getSimpleName();

    private final SingleOperation mSingleOperation;

    private ColorSlideBar colorSlideBar;
    private int mColorSize;

    private List<String> datas;

    @Override
    public List<String> extractArgs() {
        if(null != colorSlideBar) {
            int curColorIndex = colorSlideBar.getCurColorIndex();
            MLog.d(TAG,"curColorIndex = " + curColorIndex);
            ArrayList<String> values = new ArrayList<>(1);
            values.add(datas.get(curColorIndex));
            return values;
        }

        return new ArrayList<>(0);
    }

    @Override
    public String extractDesc() {
        return "";
    }

    public ColorOperation(SingleOperation singleOperation , ViewGroup parent) {
        mSingleOperation = singleOperation;

        View contentView = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_operation_color_bar_layout, parent, false);
        LinearLayout linearLayout = new LinearLayout(parent.getContext());
        linearLayout.setGravity(LinearLayout.VERTICAL);
        linearLayout.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(494, 56);
        params.gravity = Gravity.CENTER;
        parent.addView(linearLayout, params);
        linearLayout.addView(contentView, new LinearLayout.LayoutParams(494, 56));

        colorSlideBar = parent.findViewById(R.id.longpressbutton_item_color_bar_seekbar);

        LongPressButton btnReduce = parent.findViewById(R.id.longpressbutton_item_color_bar_reduce);
        btnReduce.setILongPressListener(() -> {
            colorSlideBar.incrementProgressBy(-1);
        });
        btnReduce.setOnClickListener(v -> {
            colorSlideBar.incrementProgressBy(-1);
        });

        LongPressButton btnAdd = parent.findViewById(R.id.longpressbutton_item_color_bar_add);
        btnAdd.setILongPressListener(() -> {
            colorSlideBar.incrementProgressBy(1);
        });
        btnAdd.setOnClickListener(v -> {
            colorSlideBar.incrementProgressBy(1);
        });


        List<String> view_desc_pic_list = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_PIC_LIST);
        datas = singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);

        StringBuilder stringBuilder = new StringBuilder();
        for (String s : view_desc_pic_list) {
            stringBuilder.append(s).append(",");

        }

        MLog.d("ABCX","colors = " + stringBuilder.toString());

        if(null != view_desc_pic_list && !view_desc_pic_list.isEmpty()) {
            mColorSize = view_desc_pic_list.size() - 1;
            int[] colors = new int[view_desc_pic_list.size()];
            for (int i = 0; i < view_desc_pic_list.size(); i++) {
                colors[i] = Color.parseColor(view_desc_pic_list.get(i));
            }
            colorSlideBar.setColors(colors);
        } else {
            mColorSize = 0;
        }


    }

    @Override
    public void setViewUnabled(boolean status) {
        unableStatus = status;
    }

    boolean unableStatus = false;
    @Override
    public boolean isViewUnable() {
        return unableStatus;
    }

    @Override
    public void onConfigurationChanged() {

    }

}
