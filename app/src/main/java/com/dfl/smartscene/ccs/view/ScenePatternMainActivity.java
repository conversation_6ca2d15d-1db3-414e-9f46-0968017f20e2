package com.dfl.smartscene.ccs.view;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.RelieveStressFuncDef;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.model.RelieveStressPatternModel;
import com.dfl.smartscene.ccs.model.ScenePatternInterrupt;
import com.dfl.smartscene.ccs.model.SurpriseEggListModel;
import com.dfl.smartscene.ccs.util.ScenePatternViewManager;
import com.dfl.smartscene.ccs.view.relievestress.RelieveStressPatternFragment;
import com.iauto.uibase.utils.MLog;
import com.iauto.uicontrol.ButtonView;

import java.util.ArrayList;

public class ScenePatternMainActivity extends FragmentActivity {
    private static final String TAG = "ScenePatternMainActivity";

    private static final int RESET = -1;
    private int mStartupMode;
    private ArrayList<MyOnTouchListener> onTouchListeners = new ArrayList<MyOnTouchListener>(10);

    private ButtonView mScenePatternCloseButton;
    private ButtonView mBackButton;
    private boolean mPermissions = true;
    private boolean mIsFinish = false;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        MLog.d(TAG, "onCreate" );

        setContentView(R.layout.activity_scenepattern_main);
        mStartupMode = getIntent().getIntExtra(ScenePatternFuncDef.STARTUP_PATTERN_SCENE, ScenePatternFuncDef.START_MENU);
        ScenePatternViewManager.getInstance().init(this);


        mScenePatternCloseButton = (ButtonView) findViewById(R.id.button_base_close);
        mScenePatternCloseButton.setOnBtnClickListener(new ButtonView.OnBtnClickListener() {
            @Override
            public void onBtnClick(View view) {
                finishScenePattern();
            }
        });

        mBackButton = (ButtonView) findViewById(R.id.button_surprise_egg_list_back);
        mBackButton.setOnBtnClickListener(new BackBtnClickListener());
    }

    private static class BackBtnClickListener implements ButtonView.OnBtnClickListener {
        @Override
        public void onBtnClick(View view) {
            ScenePatternViewManager.getInstance().backWard();
        }
    }

    public static Intent newIntent(Context packageContext, int startupPattern) {
        Intent intent = new Intent(packageContext, ScenePatternMainActivity.class);
        intent.putExtra(ScenePatternFuncDef.STARTUP_PATTERN_SCENE, startupPattern);
        return intent;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        for (MyOnTouchListener listener : onTouchListeners) {
            listener.onTouch(ev);
        }
        return super.dispatchTouchEvent(ev);
    }

    public void registerMyOnTouchListener(MyOnTouchListener myOnTouchListener) {
        onTouchListeners.add(myOnTouchListener);
    }

    public void unregisterMyOnTouchListener(MyOnTouchListener myOnTouchListener) {
        onTouchListeners.remove(myOnTouchListener);
    }

    public interface MyOnTouchListener {
        public boolean onTouch(MotionEvent ev);
    }

    @Override
    protected void onStart() {
        super.onStart();
        if(mIsFinish) {
            ScenePatternViewManager.getInstance().resetFragmentManager();
            mIsFinish = false;
        }
        MLog.d(TAG, "onStart: mStartupMode:"+mStartupMode );
    }

    @Override
    protected void onResume() {
        super.onResume();
        MLog.d(TAG, "onResume: mStartupMode:"+mStartupMode );
        if (mPermissions) {
            if (PackageManager.PERMISSION_GRANTED != ContextCompat.checkSelfPermission(this, "android.permission.VEHICLE_DATA")) {
                MLog.d(TAG, "request permissions in onResume");
                ActivityCompat.requestPermissions(this, new String[]{"android.permission.VEHICLE_DATA","android.permission.INTERNET_CONTROL"}, ScenePatternFuncDef.PERMISSIONS_REQUEST_VEHICLE_CODE);
            }
            transFragment();
        } else {
            mPermissions = true;
            finishScenePattern();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        MLog.d(TAG, "onStop");
        if(mIsFinish) {
            ScenePatternViewManager.getInstance().clearFragmentList();
            finish();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        MLog.d(TAG, "requestCode:"+requestCode );
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case ScenePatternFuncDef.PERMISSIONS_REQUEST_VEHICLE_CODE:
                MLog.d(TAG, "ScenePatternFuncDef.PERMISSIONS_REQUEST_VEHICLE_CODE");
                if (grantResults.length > 0 && PackageManager.PERMISSION_GRANTED == grantResults[0]) {
                    MLog.d(TAG, "grantResults:"+requestCode );
                    ScenePatternInterrupt.getInstance().setAppCommonNotify();
                    ScenePatternInterrupt.getInstance().setSourceChangeNotify();
                    SurpriseEggListModel.getInstance().initAppCommon();
                } else {
                    if (grantResults.length <= 0) {
                        mPermissions = true;
                    } else {
                        mPermissions = false;
                    }
                    MLog.d(TAG, "unGranted:"+requestCode );
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mStartupMode = intent.getIntExtra(ScenePatternFuncDef.STARTUP_PATTERN_SCENE, ScenePatternFuncDef.START_MENU);
        if(mIsFinish) {
            ScenePatternViewManager.getInstance().resetFragmentManager();
            mIsFinish = false;
        }
        MLog.d(TAG, "onNewIntent: mStartupMode:"+mStartupMode );
    }

    public void setCloseButtonVisible(boolean visible) {
        if (null != mScenePatternCloseButton) {
            if (visible) {
                mScenePatternCloseButton.setVisibility(View.VISIBLE);
            } else {
                mScenePatternCloseButton.setVisibility(View.INVISIBLE);
            }
        }
    }

    public void setBackButtonVisible (boolean visible) {
        if (null != mBackButton) {
            if (visible) {
                mBackButton.setVisibility(View.VISIBLE);
            } else {
                mBackButton.setVisibility(View.INVISIBLE);
            }
        }
    }

     public void finishScenePattern() {
         MLog.d(TAG, "finishScenePattern");
         stopRelieveStressPattern();
         Intent intent = new Intent();
         intent.setPackage(ScenePatternFuncDef.FINISH_LAUNCHER);
         intent.setAction(ScenePatternFuncDef.FINISH_ACTION);
         getApplicationContext().sendBroadcast(intent);
         mIsFinish = true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        MLog.d(TAG, "onKeyDown");
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            ScenePatternViewManager.getInstance().backWard();
            return true;
        }
        return false;
    }

    public void stopRelieveStressPattern() {
        MLog.d(TAG, "stopRelieveStressPattern");
        Fragment fragment = ScenePatternViewManager.getInstance().getCurFragment();
        if(fragment instanceof RelieveStressPatternFragment) {
            RelieveStressPatternModel.getInstance().setRelieveStressStartStatus(ScenePatternFuncDef.Relieve_Stress_Status_Close);
            int relieveStressStatus = RelieveStressPatternModel.getInstance().getRelieveStressStatus().getValue();
            if(RelieveStressFuncDef.Relieve_Stress_Status_Stop != relieveStressStatus) {
                RelieveStressPatternModel.getInstance().setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
            }
        }
    }

    private void transFragment() {
        MLog.d(TAG, "transFragment start");
        if (mStartupMode == ScenePatternFuncDef.START_MENU) {
            MLog.d(TAG, "transFragment start START_MENU");
            ScenePatternViewManager.getInstance().start_reset_ScenePatternActivityFragment(ScenePatternFuncDef.ScenePatternMenuFragment_TAG);
            mStartupMode = RESET;
        } else if(mStartupMode == ScenePatternFuncDef.START_RELIEVE_STRESS) {
            MLog.d(TAG, "transFragment start START_RELIEVE_STRESS");
            MLog.d(TAG, "isForeground false");
            ScenePatternViewManager.getInstance().transFragment(ScenePatternFuncDef.RelieveStressPatternFragment_TAG, ScenePatternFuncDef.TransType.ScenePattern_Normal, true);
            mStartupMode = RESET;
        } else if(mStartupMode == ScenePatternFuncDef.START_RELIEVE_STRESS_FOREGROUND) {
            MLog.d(TAG, "transFragment start START_RELIEVE_STRESS_FOREGROUND");
            MLog.d(TAG, "isForeground true");
            ScenePatternViewManager.getInstance().transFragment(ScenePatternFuncDef.RelieveStressPatternFragment_TAG, ScenePatternFuncDef.TransType.ScenePattern_Normal, false);
            mStartupMode = RESET;
        } else {
            ScenePatternViewManager.getInstance().start_reset_ScenePatternActivityFragment(ScenePatternFuncDef.ScenePatternMenuFragment_TAG);
        }
    }
}
