package com.dfl.smartscene.ccs.util;

import com.alibaba.fastjson.JSON;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.db.CarConfigUtils;

import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @date ：2023/3/10 9:12
 * @description ：
 */
public class TestUtil {

    public static void exportLocalJson(){
//        CarConfigUtils.querySceneByCarType(ConstantModelValue.DEBUG_CAR_TYPE_CCS5).observeOn(Schedulers.io()).subscribe(new Consumer<List<SceneCategory>>() {
//            @Override
//            public void accept(List<SceneCategory> sceneCategories) throws Exception {
//                FileUtils.saveAsFileWriter("/sdcard/Android/data/com.dfl.newscenepattern" + "/ccsScene.txt", JSON.toJSONString(sceneCategories));
//
////                FileUtils.saveAsFileWriter(Environment.getExternalStorageDirectory().getAbsolutePath() + "/pz1gScene.txt", JSON.toJSONString(sceneCategories));
//            }
//        });
//        CarConfigUtils.querySceneByCarType(ConstantModelValue.DEBUG_CAR_TYPE_CCU).observeOn(Schedulers.io()).subscribe(new Consumer<List<SceneCategory>>() {
//            @Override
//            public void accept(List<SceneCategory> sceneCategories) throws Exception {
//                FileUtils.saveAsFileWriter(Environment.getExternalStorageDirectory().getAbsolutePath() + "/ccuScene.txt", JSON.toJSONString(sceneCategories));
//            }
//        });
        CarConfigUtils.queryCarConfig(ConstantModelValue.DEBUG_CAR_TYPE_CCS5).observeOn(Schedulers.io()).subscribe(new Consumer<CarConfig>() {
            @Override
            public void accept(CarConfig carConfig) throws Exception {
                FileUtils.saveAsFileWriter("/sdcard/Android/data/com.dfl.newscenepattern" + "/ccsConfig.txt", JSON.toJSONString(carConfig));
            }
        });
//        CarConfigUtils.queryCarConfig(ConstantModelValue.DEBUG_CAR_TYPE_CCU).observeOn(Schedulers.io()).subscribe(new Consumer<CarConfig>() {
//            @Override
//            public void accept(CarConfig carConfig) throws Exception {
//                FileUtils.saveAsFileWriter(Environment.getExternalStorageDirectory().getAbsolutePath() + "/ccuConfig.txt", JSON.toJSONString(carConfig));
//            }
//        });

    }
}
