package com.dfl.smartscene.ccs.view.weight;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.dfl.smartscene.R;
import com.iauto.uibase.utils.MLog;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：自定义等级滑动条
 */
public class CustomRatingBar extends androidx.appcompat.widget.AppCompatRatingBar {

    private int mItemWidth;
    private int mItemHeight;

    private int mViewSize = 0;

    private int divideSpace = 3;


    public CustomRatingBar(@NonNull Context context) {
        this(context, null);
    }

    public CustomRatingBar(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomRatingBar(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mViewSize = 40;

        if (attrs != null) {
            TypedArray array = getContext().getResources().obtainAttributes(attrs, R.styleable.customRatingBarStyle);

            mItemWidth = array.getInt(R.styleable.customRatingBarStyle_itemWidth, 149);
            mItemHeight = array.getInt(R.styleable.customRatingBarStyle_itemHeight, 8);

            Drawable bgDrawable_s = array.getDrawable(R.styleable.customRatingBarStyle_background_bg_start);
            Drawable bgDrawable_m = array.getDrawable(R.styleable.customRatingBarStyle_background_bg_mid);
            Drawable bgDrawable_e = array.getDrawable(R.styleable.customRatingBarStyle_background_bg_end);

            Drawable prDrawable_s = array.getDrawable(R.styleable.customRatingBarStyle_progress_bg_start);
            Drawable prDrawable_m = array.getDrawable(R.styleable.customRatingBarStyle_progress_bg_mid);
            Drawable prDrawable_e = array.getDrawable(R.styleable.customRatingBarStyle_progress_bg_end);

            array.recycle();

            setProgressDrawable(bgDrawable_s, bgDrawable_m, bgDrawable_e, prDrawable_s, prDrawable_m, prDrawable_e);
        }

        setThumb(ContextCompat.getDrawable(getContext(), R.drawable.ic_volume_seekbar_thumb));

    }

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int numStars = getNumStars();
        final int width = mItemWidth * numStars + divideSpace * (numStars - 1);
//        int measuredHeight = getMeasuredHeight();
//        int height = Math.max(mItemHeight, measuredHeight);
        setMeasuredDimension(View.resolveSizeAndState(width, widthMeasureSpec, 0), mViewSize);
    }

    public void setItemSize(int w, int h) {
        mItemWidth = w;
        mItemHeight = h;
    }

    public void setProgressDrawable(@DrawableRes int bg_drawable_l, @DrawableRes int bg_drawable_m, @DrawableRes int bg_drawable_r,
                                    @DrawableRes int pr_drawable_l, @DrawableRes int pr_drawable_m, @DrawableRes int pr_drawable_r) {
        setProgressDrawable(
                ContextCompat.getDrawable(getContext(), bg_drawable_l),
                ContextCompat.getDrawable(getContext(), bg_drawable_m),
                ContextCompat.getDrawable(getContext(), bg_drawable_r),
                ContextCompat.getDrawable(getContext(), pr_drawable_l),
                ContextCompat.getDrawable(getContext(), pr_drawable_m),
                ContextCompat.getDrawable(getContext(), pr_drawable_r));

    }

    public void setProgressDrawable(Drawable bg_drawable_l, Drawable bg_drawable_m, Drawable bg_drawable_r,
                                    Drawable pr_drawable_l, Drawable pr_drawable_m, Drawable pr_drawable_r) {
        Drawable[] layers = new Drawable[2];
        layers[0] = mergeDrawable(bg_drawable_l, bg_drawable_m, bg_drawable_r, false);
//        layers[1] = mergeDrawable(pr_drawable_l, pr_drawable_m, pr_drawable_r, true);
        layers[1] = getColorProgressDrawable();

        LayerDrawable layerDrawable = new LayerDrawable(layers);
        layerDrawable.setId(0, android.R.id.background);
        layerDrawable.setId(1, android.R.id.progress);

        setProgressDrawable(layerDrawable);
    }

    Drawable mergeDrawable(Drawable drawable_l, Drawable drawable_m, Drawable drawable_r, boolean clip) {

        int mNumStars = getNumStars();
        Bitmap result = Bitmap.createBitmap(mItemWidth * mNumStars + divideSpace * (mNumStars - 1), mViewSize, Bitmap.Config.ARGB_4444);
        BitmapDrawable bitmapDrawable = new BitmapDrawable(getResources(), result);
        Canvas canvas = new Canvas(result);

        int diffHeight = mViewSize - mItemHeight;

        Drawable drawable;
        for (int i = 1; i <= mNumStars; i++) {
            if (i == 1 && drawable_l != null) {
                drawable = drawable_l;
            } else if (i == mNumStars && drawable_r != null) {
                drawable = drawable_r;
            } else {
                drawable = drawable_m;
            }
            if (drawable != null) {
                if (clip && drawable instanceof GradientDrawable) {
                    int colorId;
                    if (i == 1) {
                        colorId = getResources().getColor(R.color.color_adjust_step_check_0, null);
                    } else if (i == 2) {
                        colorId = getResources().getColor(R.color.color_adjust_step_check_1, null);
                    } else if (i == 3) {
                        colorId = getResources().getColor(R.color.color_adjust_step_check_2, null);
                    } else {
                        colorId = getResources().getColor(R.color.color_adjust_step_check_3, null);
                    }
                    ((GradientDrawable) drawable).setColor(colorId);
                }
                drawable.setBounds(mItemWidth * (i - 1) + (i - 1) * divideSpace, diffHeight / 2, mItemWidth * i + (i - 1) * divideSpace, diffHeight / 2 + mItemHeight);
                drawable.draw(canvas);
            }

        }

        return clip ? new ClipDrawable(bitmapDrawable, Gravity.START, ClipDrawable.HORIZONTAL) : bitmapDrawable;
    }

    private Drawable getColorProgressDrawable() {

        int mNumStars = getNumStars();
        int viewWidth = mItemWidth * mNumStars + divideSpace * (mNumStars - 1);
        int diffHeight = mViewSize - mItemHeight;
        int topOffset = diffHeight / 2;
        int radius = mItemHeight / 2;


        int[] colors = new int[2];

        colors[0] = ContextCompat.getColor(getContext(), R.color.color_adjust_step_start);
        colors[1] = ContextCompat.getColor(getContext(), R.color.color_adjust_step_end);

        LinearGradient linearGradient = new LinearGradient(0, 0, viewWidth, 0, colors, getColorPosition(colors), Shader.TileMode.CLAMP);
        Paint paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
        paint.setShader(linearGradient);

        Bitmap result = Bitmap.createBitmap(viewWidth, mViewSize, Bitmap.Config.ARGB_4444);
        BitmapDrawable bitmapDrawable = new BitmapDrawable(getResources(), result);
        Canvas canvas = new Canvas(result);

        MLog.d("CustomRatingBar", "getThumb:" + getThumb().getIntrinsicWidth());
        canvas.drawRoundRect(0, topOffset, viewWidth, topOffset + mItemHeight, radius, radius, paint);

        Paint paintInterval = new Paint();
        paintInterval.setStyle(Paint.Style.FILL);
        paintInterval.setColor(ContextCompat.getColor(getContext(), R.color.color_adjust_step_interval));

        for (int i = 0; i < mNumStars - 1; i++) {
            int l = mItemWidth * (i + 1) + i * divideSpace;
            canvas.drawRect(l, topOffset, l + divideSpace, topOffset + mItemHeight, paintInterval);
        }

        return new ClipDrawable(bitmapDrawable, Gravity.START, ClipDrawable.HORIZONTAL);

    }

    private float[] getColorPosition(int[] colors) {
        float[] mColorPositions;
        float intervalPosition = 1f / (colors.length - 1f);
        mColorPositions = new float[colors.length];
        float result = 0;
        for (int i = 0; i < mColorPositions.length; i++) {
            mColorPositions[i] = result;
            result += intervalPosition;
        }
        return mColorPositions;
    }


}
