package com.dfl.smartscene.ccs.view.adapter;


import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;

/**
 * <AUTHOR>
 * @date ：2022/12/6 16:42
 * @description ：编辑页面语音操作的adapter，特点添加卡片的描述不同
 */
public class SceneEditorVrAdapter extends SceneEditorStateAdapter {
    @Override
    protected SettingOperation creatAddOperation() {
        return SpecialBeanFactory.productAddVrOperation();
    }
}
