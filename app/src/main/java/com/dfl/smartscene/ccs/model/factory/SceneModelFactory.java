package com.dfl.smartscene.ccs.model.factory;

import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.core.ConditionBaseModel;
import com.dfl.smartscene.ccs.core.DeviceBaseModel;
import com.dfl.smartscene.ccs.model.AiotModel;
import com.dfl.smartscene.ccs.model.CCSDriveModel;
import com.dfl.smartscene.ccs.model.CarConfigResetModel;
import com.dfl.smartscene.ccs.model.CommonActionModel;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.DoorModel;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.HVACModel;
import com.dfl.smartscene.ccs.model.MediaModel;
import com.dfl.smartscene.ccs.model.NaviModel;
import com.dfl.smartscene.ccs.model.SaveStateModel;
import com.dfl.smartscene.ccs.model.SeatModel;
import com.dfl.smartscene.ccs.model.StateBaseModel;
import com.dfl.smartscene.ccs.model.TimeModel;
import com.dfl.smartscene.ccs.model.VRModel;
import com.dfl.smartscene.ccs.model.WeatherModel;
import com.dfl.smartscene.ccs.model.WindowModel;
import com.iauto.uibase.utils.MLog;

/**
 * 场景作动与场景触发的通用工厂类
 *
 * <AUTHOR> Zezheng
 * @date ：2022/7/12 16:25
 * @description ：
 */
public class SceneModelFactory {
    private static final String TAG = "SceneModelFactory";
    /**
     * 生产设备相关的类，包括场景作动，状态获取
     *
     * @param deviceId
     * @return
     */
    public static DeviceBaseModel productDeviceModel(String deviceId, String operationId) {
        MLog.d(TAG,"productDeviceModel deviceId = " + deviceId + ", operationId = " + operationId);
        switch (operationId) {
            case ConstantModelValue.OID_ACT_APP_VR_VOLUME:
            case ConstantModelValue.OID_ACT_APP_MEDIA_VOLUME:
            case ConstantModelValue.OID_ACT_APP_NAVI_VOLUME:
            case ConstantModelValue.OID_ACT_MORE_SCREEN_LIGHT:
            case ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO:
                return DAModel.getInstance();
            case ConstantModelValue.OID_ACT_APP_NAVI_START:
                return NaviModel.getInstance();
            case ConstantModelValue.OID_ACT_APP_MEDIA_RADIO:
            case ConstantModelValue.OID_ACT_APP_MEDIA_MUSIC:
            case ConstantModelValue.OID_ACT_APP_MEDIA_BOOK:
            case ConstantModelValue.OID_ACT_APP_MEDIA_END:
            case ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_MUSIC:
                return MediaModel.getInstance();
            case ConstantModelValue.OID_ACT_APP_VR_WEATHER:
            case ConstantModelValue.OID_ACT_APP_VR_USER:
                return VRModel.getInstance();

            default:
                break;
        }
        switch (deviceId) {
            case ConstantModelValue.DEVICE_ID_HVAC:
                return HVACModel.getInstance();
            case ConstantModelValue.DEVICE_ID_TIME:
                return TimeModel.getInstance();
            case ConstantModelValue.DEVICE_ID_AIOT:
                return AiotModel.getInstance();
            case ConstantModelValue.DEVICE_ID_SEAT:
                return SeatModel.getInstance();
            default:
                return CommonActionModel.getInstance();
        }
    }
    /**
     * 生产触发条件的设备相关类
     *
     * @param deviceId
     * @return
     */
    public static ConditionBaseModel productConditionBaseModel(String deviceId, String operationId) {
        MLog.d(TAG,"productConditionBaseModel deviceId = " + deviceId + ", operationId = " + operationId);
        switch (operationId) {
            case ConstantModelValue.OID_CDT_EVENT_TIME:
                return TimeModel.getInstance();
            case ConstantModelValue.OID_CDT_EVENT_LEAVE:
            case ConstantModelValue.OID_CDT_EVENT_ARRIVE:
                return NaviModel.getInstance();
            case ConstantModelValue.OPERATION_ID_CONDITION_VR:
                return VRModel.getInstance();
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_FUEL:
            case ConstantModelValue.OID_CDT_DRIVE_ELECTRICITY:
            case ConstantModelValue.OID_CDT_DRIVE_RANGE_POWER:
                return CCSDriveModel.getInstance();
            default:
                break;
        }
        switch (deviceId) {
            case ConstantModelValue.DEVICE_ID_SEAT:
                return SeatModel.getInstance();
            case ConstantModelValue.DEVICE_ID_DOOR:
                return DoorModel.getInstance();
            default:
                break;
        }
        return null;
    }

    /**
     * 生产用于状态判断的model类，用于状态条件确认
     * @param deviceId
     * @param operationId
     * @return
     */
    public static StateBaseModel productStateBaseModel(String deviceId, String operationId) {
        MLog.d(TAG,"productStateBaseModel deviceId = " + deviceId + ", operationId = " + operationId);
        switch (operationId) {
            case ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK:
            case ConstantModelValue.OPERATION_ID_STATE_TIME_DAY:
                return TimeModel.getInstance();
            case ConstantModelValue.OID_STA_ENV_IN_AIR_QUALITY:
                return AiotModel.getInstance();
            case ConstantModelValue.OID_STA_NAVI_DESTINATION:
            case ConstantModelValue.OID_STA_NAVI_TRAFFIC:
                return NaviModel.getInstance();
            case ConstantModelValue.OID_STA_ENV_WEATHER:
            case ConstantModelValue.OID_STA_ENV_OUT_TEMP:
            case ConstantModelValue.OID_STA_ENV_OUT_AIR_QUALITY:
                return WeatherModel.getInstance();
        }
        switch (deviceId){
            case ConstantModelValue.DEVICE_ID_SEAT:
                return SeatModel.getInstance();
            case ConstantModelValue.DEVICE_ID_DOOR:
                return DoorModel.getInstance();
            case ConstantModelValue.DEVICE_ID_DRIVE:
                return DriveModel.getInstance();
            case ConstantModelValue.DEVICE_ID_WINDOW:
                return WindowModel.getInstance();
        }
        return null;
    }
    /**
     * 生产用于重新定义车辆配置的model
     *
     * @param deviceId
     * @return
     */
    public static CarConfigResetModel productCarConfigModel(String deviceId , String operationId) {
        switch (deviceId) {
            case ConstantModelValue.DEVICE_ID_HVAC:
                return HVACModel.getInstance();
            case ConstantModelValue.DEVICE_ID_SEAT:
                return SeatModel.getInstance();
            default:
                MLog.e(TAG, "productCarConfigModel INVAILD ID!!! , deviceid = " + deviceId);
                return null;
        }
    }
    public static SaveStateModel productSaveStateModel(String deviceId, String operationId) {
        return null;
    }}
