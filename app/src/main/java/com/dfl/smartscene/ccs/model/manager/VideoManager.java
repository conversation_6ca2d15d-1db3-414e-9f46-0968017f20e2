package com.dfl.smartscene.ccs.model.manager;

import android.graphics.Bitmap;
import android.net.Uri;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.util.ThreadExecutor;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DisplayUtil;
import com.dfl.smartscene.ccs.util.MediaUtil;
import com.iauto.uibase.utils.MLog;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2023/4/13 17:34
 * @description ：由于加载视频及图片缓慢，在子线程提前进行解析
 * 请求首页资源时：
 * 1.获取到首页场景时，解析获取对应视频的地址
 * 2.获取第一个视频的第一帧图片
 */
public class VideoManager {
    private Map<String , Uri> fullVideoUri = new HashMap<>();
    private Map<String , Uri> appVideoUri = new HashMap<>();
    private Map<String , Bitmap> firstVideoFirstFrame = new HashMap<>();

    public static final int TYPE_VIDEO_APP = 0;
    public static final int TYPE_VIDEO_FULL = 1;

    private static final String TAG = "VideoManager";
    private static volatile VideoManager sInstance;

    public static VideoManager getInstance() {
        if (null == sInstance) {
            synchronized (VideoManager.class) {
                if (null == sInstance) {
                    sInstance = new VideoManager();
                }
            }
        }
        return sInstance;
    }

    public void parseScene(List<SceneCategory> sceneCategories){
        ThreadExecutor.getInstance().execute(() -> parseScenesRun(sceneCategories));
    }
    public void parseScenesRun(List<SceneCategory> sceneCategories){
        for(SceneCategory sceneCategory : sceneCategories){
            for(SceneBean sceneBean : sceneCategory.getSceneBeanList()){
                for(SettingOperation settingOperation : sceneBean.getActionOperations()){
                    if(settingOperation.getOperationId().equals(ConstantModelValue.OID_ACT_MORE_FULL_SCREEN_VIDEO)){
                        for(String name : settingOperation.getListArgs()){
                            if(appVideoUri.containsKey(name)){
                                continue;
                            }
                            String temp = name.contains(".") ? name : name + ".mp4";
                            Uri fullUri = MediaUtil.getVideoUri(temp , DisplayUtil.getScreenSizeWeight(),DisplayUtil.getScreenSizeHeight());
                            Uri appUri = MediaUtil.getVideoUri(temp , DisplayUtil.getAppSizeWeight(),DisplayUtil.getAppSizeHeight());
                            fullVideoUri.put(name,fullUri);
                            appVideoUri.put(name,appUri);
                            if(settingOperation.getListArgs().indexOf(name) == 0 && appUri != null){
                                firstVideoFirstFrame.put(appUri.toString() , MediaUtil.getVideoFirstFrame(appUri));
                            }
                        }
                        break;
                    }
                }
            }
        }
    }

    public Uri getUriByVideoName(String name , int type){
        Uri uri = null;
        String temp = name.contains(".") ? name : name + ".mp4";

        if(type == TYPE_VIDEO_APP){
            uri = appVideoUri.get(name);
            if (uri == null) {
                uri = MediaUtil.getVideoUri(temp, DisplayUtil.getAppSizeWeight(), DisplayUtil.getAppSizeHeight());
                appVideoUri.put(name, uri);
            }
        }else if(type == TYPE_VIDEO_FULL){
            uri = fullVideoUri.get(name);
            if (uri == null) {
                uri = MediaUtil.getVideoUri(temp, DisplayUtil.getScreenSizeWeight(), DisplayUtil.getScreenSizeHeight());
                fullVideoUri.put(name, uri);
            }
        }
        MLog.d(TAG,"getUriByVideoName : name : " + name + " , result = " + uri);
        return uri;
    }


    public Bitmap getBitMapByUri(Uri uri){
        if(uri == null){
            return null;
        }
        Bitmap bitmap = null;
        bitmap = firstVideoFirstFrame.get(uri.toString());
        if(bitmap != null){
            LogUtil.d(TAG,"use temp 1");

            return bitmap;
        }
        bitmap = sceneBitMap.get(uri.toString());
        if(bitmap != null){
            LogUtil.d(TAG,"use temp");
            return bitmap;
        }
        LogUtil.d(TAG , "getBitMapByUri" + uri);
        bitmap = MediaUtil.getVideoFirstFrame(uri);
        return bitmap;
    }

    private Map<String,Bitmap> sceneBitMap = new HashMap<>();

    public void initVideoBitmap(List<String> videoNames){
        ThreadExecutor.getInstance().execute(() -> {
            for(String name : videoNames){
                Uri appUri = getUriByVideoName(name,TYPE_VIDEO_APP);
                if(appUri != null && !firstVideoFirstFrame.containsKey(appUri.toString())){
                        LogUtil.d(TAG,"put bitmap uri : " + appUri.toString());
                        sceneBitMap.put(appUri.toString(),MediaUtil.getVideoFirstFrame(appUri));
                }
                Uri fullUri = getUriByVideoName(name,TYPE_VIDEO_FULL);

                if(fullUri != null){
                    sceneBitMap.put(fullUri.toString(),MediaUtil.getVideoFirstFrame(fullUri));
                    LogUtil.d(TAG,"put bitmap furi : " + fullUri.toString());
                }
            }
        });
    }

    public void clearVideoBitmap(){
        sceneBitMap.clear();
    }


}
