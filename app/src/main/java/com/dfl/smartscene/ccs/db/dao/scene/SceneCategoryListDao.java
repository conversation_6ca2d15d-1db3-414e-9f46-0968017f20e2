package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneCategoryListEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneCategoryListDao {

    @Insert
    void insertStudent(SceneCategoryListEntity item);

    @Delete
    void deleteStudent(SceneCategoryListEntity item);

    @Update
    void updateStudent(SceneCategoryListEntity item);

    @Query("SELECT * FROM scene_category_list")
    Observable<List<SceneCategoryListEntity>> queryAll();

    @Query("SELECT * FROM scene_category_list WHERE :mSceneCategoryId='' or mSceneCategoryId= :mSceneCategoryId")
    Observable<List<SceneCategoryListEntity>> queryBySceneCategoryId(String mSceneCategoryId);


    @Query("SELECT * FROM scene_category_list WHERE mSceneCategoryId in (:mSceneCategoryIds)")
    Observable<List<SceneCategoryListEntity>> queryBySceneCategoryIds(String[] mSceneCategoryIds);

}
