package com.dfl.smartscene.ccs.util;

/**
 * <AUTHOR>
 * @date ：2022/9/19 13:45
 * @description ：
 */
public class CulculateUtil {

    /**
     * 判断表达式是不是只有一个数字
     *
     * @param str 原值
     * @return 数字非法性校验结果，失败返回false;
     */
    public static boolean isNumber(String str) {

        if(str == null || str.length() == 0){
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i)) && str.charAt(i) != '.' && str.charAt(i) != ' ') {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析算式，并计算算式结果；
     *
     * @param str 算式的字符串
     * @return double类型的算式结果
     */
    public static Double getResult(String str) {

        // 递归头
        if (str.isEmpty() || isNumber(str)) {
            return str.isEmpty() ? 0 : Double.parseDouble(str);
        }

        //递归体
        if (str.contains(")")) {
            // 最后一个左括号
            int lIndex = str.lastIndexOf("(");
            // 对于的右括号
            int rIndex = str.indexOf(")", lIndex);
            return getResult(str.substring(0, lIndex) + getResult(str.substring(lIndex + 1, rIndex)) + str.substring(rIndex + 1));
        }
        if (str.contains("+")) {
            int index = str.lastIndexOf("+");
            return getResult(str.substring(0, index)) + getResult(str.substring(index + 1));
        }
        if (str.contains("-")) {
            int index = str.lastIndexOf("-");
            return getResult(str.substring(0, index)) - getResult(str.substring(index + 1));
        }
        if (str.contains("*")) {
            int index = str.lastIndexOf("*");
            return getResult(str.substring(0, index)) * getResult(str.substring(index + 1));
        }
        if (str.contains("/")) {
            int index = str.lastIndexOf("/");
            return getResult(str.substring(0, index)) / getResult(str.substring(index + 1));
        }

        // 出错
        return null;
    }

    /**
     * 解析算式，并计算算式结果；
     *
     * @param str 算式的字符串
     * @return double类型的算式结果
     */
    public static Integer getIntResult(String str) {

        // 递归头
        if (str.isEmpty() || isNumber(str)) {
            return str.isEmpty() ? 0 : Integer.parseInt(str);
        }

        //递归体
        if (str.contains(")")) {
            // 最后一个左括号
            int lIndex = str.lastIndexOf("(");
            // 对于的右括号
            int rIndex = str.indexOf(")", lIndex);
            return getIntResult(str.substring(0, lIndex) + getIntResult(str.substring(lIndex + 1, rIndex)) + str.substring(rIndex + 1));
        }
        if (str.contains("+")) {
            int index = str.lastIndexOf("+");
            return getIntResult(str.substring(0, index)) + getIntResult(str.substring(index + 1));
        }
        if (str.contains("-")) {
            int index = str.lastIndexOf("-");
            return getIntResult(str.substring(0, index)) - getIntResult(str.substring(index + 1));
        }
        if (str.contains("*")) {
            int index = str.lastIndexOf("*");
            return getIntResult(str.substring(0, index)) * getIntResult(str.substring(index + 1));
        }
        if (str.contains("/")) {
            int index = str.lastIndexOf("/");
            return getIntResult(str.substring(0, index)) / getIntResult(str.substring(index + 1));
        }

        // 出错
        return null;
    }
}
