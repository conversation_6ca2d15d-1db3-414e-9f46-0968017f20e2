package com.dfl.smartscene.ccs.view.operationview;

import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.CustomTimePicker;

import java.util.ArrayList;
import java.util.List;

public class TimePickerOperation implements OperationBaseView {

    private final SingleOperation mSingleOperation;
    private final CustomTimePicker customTimePicker;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);
        values.add(frontCompWithZero(customTimePicker.getValue1(), 2) + ":" + frontCompWithZero(customTimePicker.getValue2(), 2));
        return values;
    }

    @Override
    public String extractDesc() {
        return frontCompWithZero(customTimePicker.getValue1(), 2) +
                customTimePicker.getText1() +
                frontCompWithZero(customTimePicker.getValue2(), 2) +
                customTimePicker.getText2();
    }

    public TimePickerOperation(SingleOperation singleOperation, ViewGroup parent, boolean isHour) {
        this.mSingleOperation = singleOperation;
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        customTimePicker = new CustomTimePicker(parent.getContext());
        LogUtil.e("默认值","------" + defaultStr);
        if (isHour) {
            customTimePicker.setValue1(0);
            customTimePicker.setMinValue1(0);
            customTimePicker.setMaxValue1(23);
            customTimePicker.setDisplayArray1(getDisplayArray(24));

            customTimePicker.setValue2(0);
            customTimePicker.setMinValue2(0);
            customTimePicker.setMaxValue2(59);
            customTimePicker.setDisplayArray2(getDisplayArray(60));
            if (null != defaultStr && defaultStr.contains(":")){
                String[] split = defaultStr.split(":");
                customTimePicker.setValue1(Integer.parseInt(split[0]));
                customTimePicker.setValue2(Integer.parseInt(split[1]));
            }
            customTimePicker.setText1("时");
            customTimePicker.setText2("分");

        } else {
            customTimePicker.setValue1(0);
            customTimePicker.setMinValue1(0);
            customTimePicker.setMaxValue1(59);
            customTimePicker.setDisplayArray1(getDisplayArray(60));

            customTimePicker.setValue2(0);
            customTimePicker.setMinValue2(0);
            customTimePicker.setMaxValue2(59);
            customTimePicker.setDisplayArray2(getDisplayArray(60));

            customTimePicker.setText1("分");
            customTimePicker.setText2("秒");

        }
        customTimePicker.refreshView();

        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        parent.addView(customTimePicker, layoutParams);


    }

    private String[] getDisplayArray(int maxValue) {
        String[] displayStr = new String[maxValue];
        for (int i = 0; i < maxValue; i++) {
            if (i < 10) {
                displayStr[i] = "0" + i;
            } else {
                displayStr[i] = String.valueOf(i);
            }
        }
        return displayStr;
    }

    public static String frontCompWithZero(int sourceDate, int formatLength) {
        return String.format("%0" + formatLength + "d", sourceDate);
    }

    @Override
    public void onConfigurationChanged() {

    }

}
