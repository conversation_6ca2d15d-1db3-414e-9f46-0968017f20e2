package com.dfl.smartscene.ccs.view.operationview;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.adapter.OpeRecyclerAdapter;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/2 13:49
 * @description ：
 */
public class RecyclerButtonOperation implements OperationBaseView, BaseAdapter.OnItemClickListener<OpeRecyclerAdapter.DataBean>{
    private static final String TAG = "RecyclerButtonOperation";
    private RecyclerView mRecyclerView;
    protected OpeRecyclerAdapter mOpeRecyclerAdapter;
    protected SingleOperation mSingleOperation;

    int COLUMN_SPACE = R.dimen.x_px_24;//item之间的间距
    int LINE_SPACE = R.dimen.y_px_24;//item之间的间距

    protected ViewGroup mContentView;

    @Override
    public List<String> extractArgs() {
        return Arrays.asList(mOpeRecyclerAdapter.getItemData(mOpeRecyclerAdapter.getSelectPos()).getValue());
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        LogUtil.d("RecyclerButtonOperation","extractDesc-----:" + desc);
        return desc.replaceFirst("&",mOpeRecyclerAdapter.getItemData(mOpeRecyclerAdapter.getSelectPos()).getDesc());
    }

    public RecyclerButtonOperation(SingleOperation singleOperation , ViewGroup parent){
        mSingleOperation = singleOperation;
        mContentView = parent;
        mRecyclerView = new RecyclerView(parent.getContext());
        mRecyclerView.setClipChildren(false);
        mRecyclerView.setClipToPadding(false);
        FrameLayout.LayoutParams layoutParams;
        layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT,Gravity.CENTER);
        setRecyclerViewLayout(mRecyclerView);
        List<String> descs = mSingleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        int num = descs == null ? 0 : descs.size();
        if(num > 3) {
            int paddingLeft = mRecyclerView.getPaddingLeft();
            int paddingRight = mRecyclerView.getPaddingRight();
            int paddingTop = mRecyclerView.getPaddingTop();
            int paddingBottom = mRecyclerView.getPaddingBottom();
            mRecyclerView.setPadding(paddingLeft+18,paddingTop,paddingRight,paddingBottom);
        }

        mOpeRecyclerAdapter = creatAdapter();
        mRecyclerView.setAdapter(mOpeRecyclerAdapter);
        parent.addView(mRecyclerView,layoutParams);
        mOpeRecyclerAdapter.setOnItemClickListener(this);
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setNestedScrollingEnabled(false);
        initData(singleOperation);
    }

    public OpeRecyclerAdapter creatAdapter(){
        return new OpeRecyclerAdapter();
    }

    public void setRecyclerViewLayout(RecyclerView recyclerView){
        List<String> descs = mSingleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        int num = descs == null ? 0 : descs.size();
        //UI上3个以下竖向排列，3个以上网格排列
        if(num <= 3){
            RecyclerViewUtil.setLinearRecycleView(recyclerView, RecyclerView.VERTICAL,LINE_SPACE);
        }else {
            RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL,2,COLUMN_SPACE,LINE_SPACE);
        }

    }

    public void initData(SingleOperation singleOperation){
        List<OpeRecyclerAdapter.DataBean> datas = new ArrayList<>();
        List<String> descs = singleOperation.getListArg(ConstantModelValue.VIEW_DESC_TEXT_LIST);
        List<String> values = singleOperation.getListArg(ConstantModelValue.DATA_DESC_LIST);
        String defaultPos = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        LogUtil.d("RecyclerButtonOperation","defaultPos-----:"+defaultPos);
        int pos = DataTypeFormatUtil.string2Int(defaultPos,0);
        if(null != descs && null != values) {
            for(int i = 0 ; i < descs.size() ; i++){
                LogUtil.d("RecyclerButtonOperation","values.get(i):"+values.get(i));
                LogUtil.d("RecyclerButtonOperation","descs.get(i):"+descs.get(i));
                datas.add(new OpeRecyclerAdapter.DataBean(descs.get(i), values.get(i)));
                if (defaultPos != null && defaultPos.equals(values.get(i))){
                    pos = i;
                }
            }
            if (ConstantModelValue.VIEW_TYPE_STATE_TIME_DAY.equals(singleOperation.getViewType()) && ConstantModelValue.OPERATION_ID_STATE_TIME_DAY.equals(singleOperation.getOperationId())){
                //生效时间，重复，自定义会出现未选择
                if (!values.contains(defaultPos)){
                    pos = 3;//选择自定义
                }
            }
            mOpeRecyclerAdapter.setSelesctedPos(pos);
            mOpeRecyclerAdapter.setDataList(datas,pos);
        }

    }

    private int mPosition = 0;

    @Override
    public void onItemClick(View view, int viewType, OpeRecyclerAdapter.DataBean dataBean, int position) {
        mOpeRecyclerAdapter.setSelesctedPos(position);
        mPosition = position;
    }

    @Override
    public void onConfigurationChanged() {
        if (mRecyclerView != null) {
            mRecyclerView.removeAllViews();
            mRecyclerView.removeAllViewsInLayout();
            mRecyclerView.setAdapter(mOpeRecyclerAdapter);
            mRecyclerView.getRecycledViewPool().clear();
            mOpeRecyclerAdapter.setSelesctedPos(mPosition);
        }
    }


}
