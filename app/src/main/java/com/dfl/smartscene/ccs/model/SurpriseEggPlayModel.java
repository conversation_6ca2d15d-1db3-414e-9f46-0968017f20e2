package com.dfl.smartscene.ccs.model;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.session.PlaybackState;
import android.os.RemoteException;
import android.support.v4.media.session.MediaControllerCompat;

import androidx.lifecycle.MutableLiveData;

import com.dfl.api.app.vr.tts.ITtsAgentAidlCallback;
import com.dfl.api.app.vr.tts.ITtsAidl;
import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.setting.StreamType;
import com.dfl.smartscene.ccs.base.MediaGroupManager;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.constant.SurpriseEggFuncDef;
import com.dfl.smartscene.ccs.util.AudioPlayer;
import com.dfl.smartscene.ccs.util.HandlerThreadProcessor;
import com.dfl.smartscene.ccs.util.SysViewControl;
import com.iauto.uibase.utils.MLog;

/*
 * 彩蛋播放 model类
 * <AUTHOR>
 * @date 2022/7/1
 */
public class SurpriseEggPlayModel {
    private static final String TAG = "SurpriseEggPlayModel";
    @SuppressLint("StaticFieldLeak")
    private static SurpriseEggPlayModel sSurpriseEggPlayModel = null;
    private        Context mContext = null;
    private        byte[] mApiLock = new byte[0];
    private        ITtsAidl mITtsAidl = null;
    private HandlerThreadProcessor handlerThreadProcessor = HandlerThreadProcessor.getInstance();
    InterruptListenerForSurpriseEggImpl mInterruptListener = new InterruptListenerForSurpriseEggImpl();

    private final MutableLiveData<String> mSurpriseEggURL = new MutableLiveData<>();         // 表示状态变化live data
    private final MutableLiveData<String> mSurpriseEggTTSText = new MutableLiveData<>();       // 彩蛋list变化live data
    private final MutableLiveData<Integer> mSurpriseEggPlaybackStatus     = new MutableLiveData<>();   // 播放状态
    private final MutableLiveData<Integer> mInterrupt     = new MutableLiveData<>();        // 中断类型
    private final MutableLiveData<Integer> mTTSPlayStatus     = new MutableLiveData<>();    // TTS播放状态

    private final MutableLiveData<String> mSurpriseEggVoiceFile = new MutableLiveData<>();       // 彩蛋录音文件

    private final MutableLiveData<Integer> mSurpriseEggVoiceType = new MutableLiveData<>();       // 彩蛋音频类型
    private final MutableLiveData<Integer> mVoiceFilePlayStatus = new MutableLiveData<>();       // 彩蛋音频播放状态

    private String   mOriginPkgName   = "";
    private int      mOriginPlayState = PlaybackState.STATE_NONE;

    /**
     * get SurpriseEggPlayModel's instance
     */
    public static SurpriseEggPlayModel getInstance() {
        return sSurpriseEggPlayModel;
    }

    /**
     * SurpriseEggListModel object constructor
     *
     * @param context
     */
    private SurpriseEggPlayModel(Context context) {
        mContext = context;

        ScenePatternInterrupt.getInstance().registerScenePatternInterruptListener(mInterruptListener);
        // init livedata
        initLiveData();
    }

    /**
     * create SurpriseEggListModel's instance
     *
     * @param context
     */
    public static synchronized void create(Context context) {
        MLog.d(TAG,"create");
        if (sSurpriseEggPlayModel == null) {
            sSurpriseEggPlayModel = new SurpriseEggPlayModel(context);
        }
    }

    public MutableLiveData<String> getSurpriseEggURL() {
        return mSurpriseEggURL;
    }

    public MutableLiveData<String> getSurpriseEggTTSText() {
        return mSurpriseEggTTSText;
    }

    public MutableLiveData<Integer> getSurpriseEggPlaybackStatus() {
        return mSurpriseEggPlaybackStatus;
    }

    public MutableLiveData<Integer> getTTSPlayStatus() {
        return mTTSPlayStatus;
    }

    public void resetTTSPlayStatus() {
        mTTSPlayStatus.setValue(SurpriseEggFuncDef.TTS_Playback_Status_None);
    }

    public MutableLiveData<String> getSurpriseEggVoiceFile() {
        return mSurpriseEggVoiceFile;
    }

    public MutableLiveData<Integer> getSurpriseEggVoiceType() {
        return mSurpriseEggVoiceType;
    }

    public MutableLiveData<Integer> getVoiceFilePlayStatus() {
        return mVoiceFilePlayStatus;
    }

    public void resetVoiceFilePlayStatus() {
        mVoiceFilePlayStatus.setValue(SurpriseEggFuncDef.VOICE_FILE_Playback_Status_None);
    }

    public void setSurpriseEggURL(String url) {
        mSurpriseEggURL.setValue(url);
    }
    public void postSurpriseEggURL(String url) {
        mSurpriseEggURL.postValue(url);
    }

    public void setSurpriseEggTTSText(String TTSText) {
        mSurpriseEggTTSText.setValue(TTSText);
    }
    public void postSurpriseEggTTSText(String TTSText) {
        mSurpriseEggTTSText.postValue(TTSText);
    }

    public void setSurpriseEggVoiceFile(String file) {
        mSurpriseEggVoiceFile.setValue(file);
    }
    public void postSurpriseEggVoiceFile(String url) {
        mSurpriseEggVoiceFile.postValue(url);
    }

    public void setSurpriseEggVoiceTyp(int type) {
        mSurpriseEggVoiceType.setValue(type);
    }
    public void postSurpriseEggVoiceTyp(int type) {
        mSurpriseEggVoiceType.postValue(type);
    }

    /**
     * 设定H5的播放状态
     *
     * @param playStatus
     */
    public void setSurpriseEggPlaybackStatus(int playStatus) {
        MLog.d(TAG, "setSurpriseEggPlaybackStatus playStatus:"+playStatus);
        mSurpriseEggPlaybackStatus.setValue(playStatus);
        if (SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Start == playStatus) {
            String activeSource = ScenePatternInterrupt.getInstance().getActiveSource();
            if (!activeSource.isEmpty()) {
                mOriginPkgName = activeSource;
                mOriginPlayState = MediaGroupManager.getInstance().getSessionPlayState(mOriginPkgName);
                MLog.d(TAG, "mOriginPkgName:"+mOriginPkgName);
                MLog.d(TAG, "mOriginPlayState:"+mOriginPlayState);
            }
            requestSourceOn();
            silentSwitch(true);
        } else if (SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Play == playStatus) {
            MLog.d(TAG, "setSurpriseEggPlaybackStatus play:");
            if(mSurpriseEggVoiceType.getValue() == SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT){
                doTTSPlay();
            }else {
                doVoiceFilePlay();
            }
        } else if(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Stop == playStatus) {
            MLog.d(TAG, "setSurpriseEggPlaybackStatus stop:");
            if(mSurpriseEggVoiceType.getValue() == SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT){
                doTTSStop();
            }else {
                doVoiceFileStop();
            }
            silentSwitch(false);
            if (ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE == mInterrupt.getValue()) {
                String pkgName = ScenePatternInterrupt.getInstance().getActiveSource();
                MLog.d(TAG, "pkgName:"+pkgName);
                if ((!mOriginPkgName.equals(pkgName)) && (mOriginPlayState == PlaybackState.STATE_PLAYING)) {
                    MediaControllerCompat.TransportControls originTransportControls = MediaGroupManager.getInstance().getSessionTransportControls(mOriginPkgName);
                    originTransportControls.play();
                }
                else {
                    requestSourceOn();
                }
            }

        }else {
            // do nothing
        }
    }

    /**
     * init live data
     */
    private void initLiveData() {
        mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
        mSurpriseEggURL.setValue("");
        mSurpriseEggTTSText.setValue("");
        mSurpriseEggVoiceFile.setValue("");
        mSurpriseEggVoiceType.setValue(SurpriseEggFuncDef.JSON_ITEM_VOICE_TYPE_TEXT);
        mSurpriseEggPlaybackStatus.setValue(SurpriseEggFuncDef.Surprise_Egg_Playback_Status_None);
        mTTSPlayStatus.setValue(SurpriseEggFuncDef.TTS_Playback_Status_None);
        mVoiceFilePlayStatus.setValue(SurpriseEggFuncDef.VOICE_FILE_Playback_Status_None);
        initTTS();
    }

    /**
     * 初始化TTS语音播报(为了播放菜单中的文本)
     */
    private void initTTS() {
        BaseManager.create(mContext, Constants.DFL_APP_VR_TTS_TTSAIDL_SERVICE, new APICreateCallback() {
            @Override
            public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                if (Constants.DFL_APP_VR_TTS_TTSAIDL_SERVICE.equals(s)) {
                    synchronized(mApiLock) {
                        if (apiStateType == APIStateType.APIState.SUCCESS && iBaseAPI != null && iBaseAPI instanceof ITtsAidl) {
                            mITtsAidl = (ITtsAidl) iBaseAPI;
                            try {
                                MLog.d(TAG, "mITtsAidl.registerClientCallback begin");
                                boolean result = mITtsAidl.registerClientCallback(new ITtsAgentAidlCallback() {
                                    @Override
                                    public void onPlayBegin() {
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                MLog.d(TAG,"TTS Play callback:onPlayBegin");
                                                mTTSPlayStatus.setValue(SurpriseEggFuncDef.TTS_Playback_Status_Play);
                                            }
                                        });
                                    }

                                    @Override
                                    public void onPlayCompleted() {
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                MLog.d(TAG,"TTS Play callback:onPlayCompleted");
                                                mTTSPlayStatus.setValue(SurpriseEggFuncDef.TTS_Playback_Status_Stop);
                                            }
                                        });
                                    }

                                    @Override
                                    public void onPlayInterrupted() {
                                        handlerThreadProcessor.postToMainThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                MLog.d(TAG,"TTS Play callback:onPlayInterrupted");
                                                mTTSPlayStatus.setValue(SurpriseEggFuncDef.TTS_Playback_Status_Interrupt);
                                            }
                                        });
                                    }

                                    @Override
                                    public void onProgressReturn(int i, int i1) {

                                    }

                                    @Override
                                    public void onTtsInited(boolean b, int i) {

                                    }

                                    @Override
                                    public void onPlayPause() {

                                    }

                                    @Override
                                    public String getClientId() {
                                        return ScenePatternFuncDef.PACKAGE_NAME;
                                    }

                                    @Override
                                    public void onPlayResume() {

                                    }
                                }, AudioAttributes.USAGE_ASSISTANT, false);
                                MLog.d(TAG, "mITtsAidl.registerClientCallback result:"+result);
                            } catch (NullServiceException | RemoteException e) {
                                mITtsAidl = null;
                                MLog.e(TAG, "->registerClientCallback() Exception!!!");

                            }
                        } else {
                            mITtsAidl = null;
                            MLog.d(TAG, "mAppCommonServiceManager is created failed");
                        }
                    }
                }
            }

            @Override
            public void onMethodStateCallback(APIStateType.APIState apiStateType, String s) {

            }
        });
    }

    /**
     * 播放指定的TTS文本
     */
    private void doTTSPlay() {
        MLog.d(TAG,"doTTSPlay");
        synchronized(mApiLock) {
            if ((!mSurpriseEggTTSText.getValue().isEmpty()) && (null != mITtsAidl)) {
                try {
                    MLog.d(TAG,"startSpeak");
                    mITtsAidl.startSpeak(ScenePatternFuncDef.PACKAGE_NAME, mSurpriseEggTTSText.getValue(), 1);
                } catch (NullServiceException | RemoteException e) {
                    mITtsAidl = null;
                    MLog.d(TAG,e.getMessage());
                    MLog.e(TAG, "->startSpeak() Exception!!!");
                }
            }
        }
    }

    /**
     * 停止播放指定的TTS文本
     */
    private void doTTSStop() {
        MLog.d(TAG,"doTTSStop");
        synchronized(mApiLock) {
            if ((!mSurpriseEggTTSText.getValue().isEmpty()) && (null != mITtsAidl)) {
                try {
                    mITtsAidl.stopSpeak(ScenePatternFuncDef.PACKAGE_NAME);
                } catch (NullServiceException | RemoteException e) {
                    mITtsAidl = null;
                    MLog.d(TAG,e.getMessage());
                    MLog.e(TAG, "->stopSpeak() Exception!!!");
                }
            }
        }
    }

    /**
     * 播放录音文件
     */
    private void doVoiceFilePlay(){
        if(!mSurpriseEggVoiceFile.getValue().isEmpty()){
            try{
                AudioPlayer.getInstance().start(mSurpriseEggVoiceFile.getValue());
            }catch (Exception e){
                MLog.d(TAG,e.getMessage());
            }
        }
    }

    /**
     * 停止播放录音文件
     */
    private void doVoiceFileStop(){
        AudioPlayer.getInstance().stop();
    }

    /*
     * 中断通知接口类
     * <AUTHOR>
     * @date 2022/5/15
     */
    public class InterruptListenerForSurpriseEggImpl implements ScenePatternInterrupt.ScenePatternInterruptListener {
        @Override
        public void onInterrupt(int interruptType) {
            if(mSurpriseEggPlaybackStatus.getValue() != SurpriseEggFuncDef.Surprise_Egg_Playback_Status_Play){
                MLog.d(TAG, "not play");
                mInterrupt.setValue(ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_NONE);
                return;
            }
            switch (interruptType) {
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_GEAR_TYPE_NOT_P:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_VR:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_ECALL:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_INSTRUMENT_WARNING:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_DISCONNECT:
                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_BLUETOOTH_CALL:
                    mInterrupt.setValue(interruptType);
                    SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
                    break;
//                case ScenePatternFuncDef.SCENE_PATTERN_INTERRUPT_SOURCE_CHANGE:
//                    MLog.d(TAG, "onInterrupt:source change");
//                    String originalSource = ScenePatternInterrupt.getInstance().getOriginalSource().getValue();
//                    MLog.d(TAG, "onInterrupt:originalSource " + originalSource);
//                    if(originalSource.equals(ScenePatternFuncDef.PACKAGE_NAME)) {
//                        mInterrupt.setValue(interruptType);
//                        SysViewControl.getInstance().reqCloseSurpriseEggPlayFullView();
//                    }
//                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 获取音源焦点
     */
    private void requestSourceOn() {
        MLog.d(TAG, "requestSourceOn reqSource: ");
        MLog.d(TAG, "requestSourceOn usage: " + AudioAttributes.USAGE_MEDIA + ",type: " + AudioAttributes.CONTENT_TYPE_MUSIC);

        AudioFocusListener listener = new AudioFocusListener();

        AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);

        AudioAttributes audioAtt = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build();
        AudioFocusRequest audioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAtt)
                .setAcceptsDelayedFocusGain(true)
                .setWillPauseWhenDucked(false)
                .setOnAudioFocusChangeListener(listener)
                .build();
        int rtn = audioManager.requestAudioFocus(audioFocusRequest);

        switch (rtn) {
            case AudioManager.AUDIOFOCUS_REQUEST_GRANTED:
                MLog.d(TAG, "requestSourceOn: rtn AUDIOFOCUS_REQUEST_GRANTED: " + rtn);
                // source on call tune
                break;
            case AudioManager.AUDIOFOCUS_REQUEST_FAILED:
                MLog.d(TAG, "requestSourceOn: rtn AUDIOFOCUS_REQUEST_FAILED: " + rtn);
                // request failed
                break;
            case AudioManager.AUDIOFOCUS_REQUEST_DELAYED:
                MLog.d(TAG, "requestSourceOn: rtn AUDIOFOCUS_REQUEST_DELAYED: " + rtn);
                // request delay
                break;
            default:
                break;
        }
    }

    /**
     * 音源焦点变更回调listener
     */
    private class AudioFocusListener implements AudioManager.OnAudioFocusChangeListener {

        @Override
        public void onAudioFocusChange(int changState) {
            switch (changState) {
                case AudioManager.AUDIOFOCUS_GAIN:
                    // Source On
                    MLog.d(TAG, "onAudioFocusChange: AUDIOFOCUS_GAIN: " + changState);
                    break;
                case AudioManager.AUDIOFOCUS_LOSS:
                    // Source Off
                    MLog.d(TAG, "onAudioFocusChange: AUDIOFOCUS_LOSS: " + changState);
                    break;
                case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                    // loss suspend
                    MLog.d(TAG, "onAudioFocusChange: AUDIOFOCUS_LOSS_TRANSIENT: " + changState);
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * mute掉navi的TTS播报
     * @param isMute true：mute;false:unmute
     */
    private void silentSwitch(boolean isMute) {
        MLog.d(TAG, "silentSwitch: " + isMute);
        try {
            boolean muteFlag = false;
            AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
            //获取当前音乐多媒体是否静音
            muteFlag = audioManager.isStreamMute(StreamType.STREAM_TTS);
            MLog.d(TAG, "muteFlag: " + muteFlag);
            if (isMute) {
                if (!muteFlag) {
                    MLog.d(TAG, "mute set:");
                    audioManager.adjustStreamVolume(StreamType.STREAM_TTS,
                            AudioManager.ADJUST_MUTE, 0);//设为静音
                }
            } else {
                if (muteFlag) {
                    MLog.d(TAG, "unmute set:");
                    audioManager.adjustStreamVolume(StreamType.STREAM_TTS,
                            AudioManager.ADJUST_UNMUTE, 0);//取消静音

                }
            }

        } catch (Exception e) {
            MLog.d(TAG, "silentSwitch: " + e);
        }
    }
}
