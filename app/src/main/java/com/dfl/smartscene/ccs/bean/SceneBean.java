package com.dfl.smartscene.ccs.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/7/12 11:39
 * @description ：场景类，包括广场场景与用户场景
 */
public class SceneBean implements Serializable {

    /**
     * 场景id
     */
    private String sceneId = "";
    /**
     * 场景名
     */
    private String sceneName = "";
    /**
     * 场景图片
     */
    private String scenePic = "";
    /**
     * 场景描述
     */
    private String sceneDesc = "";
    /**
     * 从属分类id
     */
    private String sceneCategoryId = "";

    /**
     * 所属场景，自定义场景 + 我的收藏场景
     */
    private String sceneCategoryName = "";

    /**
     * 场景icon
     */
    private String sceneIcon = "";

    /**
     * 排序参数
     */
    private int sort = 0;

    /**
     * 执行动作列表
     */
    List<SettingOperation> actionOperations = new ArrayList<>();
    /**
     * 触发条件列表
     */
    List<SettingOperation> conditionOperations = new ArrayList<>();
    /**
     * 状态条件列表
     */
    List<SettingOperation> stateOperations = new ArrayList<>();

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getScenePic() {
        return scenePic;
    }

    public void setScenePic(String scenePic) {
        this.scenePic = scenePic;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    public void setSceneDesc(String sceneDesc) {
        this.sceneDesc = sceneDesc;
    }

    public String getSceneCategoryId() {
        return sceneCategoryId;
    }

    public void setSceneCategoryId(String sceneCategoryId) {
        this.sceneCategoryId = sceneCategoryId;
    }

    public void setSceneCategoryName(String sceneCategoryName) {
        this.sceneCategoryName = sceneCategoryName;
    }

    public String getSceneCategoryName() {
        return sceneCategoryName;
    }

    public List<SettingOperation> getActionOperations() {
        return actionOperations;
    }

    public void setActionOperations(List<SettingOperation> actionOperations) {
        this.actionOperations = actionOperations;
    }

    public List<SettingOperation> getConditionOperations() {
        return conditionOperations;
    }

    public void setConditionOperations(List<SettingOperation> conditionOperations) {
        this.conditionOperations = conditionOperations;
    }

    public List<SettingOperation> getStateOperations() {
        return stateOperations;
    }

    public void setStateOperations(List<SettingOperation> stateOperations) {
        this.stateOperations = stateOperations;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    /**
     * 是否是新增场景
     */
    private boolean isNew = false;

    /**
     * 收藏状态
     */
    private boolean collectState;
    /**
     * 可编辑状态
     */
    private int mEditType;

    public boolean isCollectState() {
        return collectState;
    }

    public void setCollectState(boolean collectState) {
        this.collectState = collectState;
    }
    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    /**
     * 修改时间戳
     */
    private long timestamp;
    /**
     * 是否自动触发
     */
    private boolean autoEnable = false;

    /**
     * 触发后是否提醒
     */
    private boolean autoNotify = true;

    /**
     * 角标描述
     */
    private String sceneCorner;

    /**
     * 是否处于执行状态
     */
    private boolean executeStatus;

    public String getSceneCorner() {
        return sceneCorner;
    }

    public void setSceneCorner(String sceneCorner) {
        this.sceneCorner = sceneCorner;
    }

    public boolean isAutoEnable() {
        return autoEnable;
    }

    public void setAutoEnable(boolean autoEnable) {
        this.autoEnable = autoEnable;
    }
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public boolean isAutoNotify() {
        return autoNotify;
    }

    public void setAutoNotify(boolean autoNotify) {
        this.autoNotify = autoNotify;
    }

    public String getSceneIcon() {
        return sceneIcon;
    }

    public void setSceneIcon(String sceneIcon) {
        this.sceneIcon = sceneIcon;
    }

    public int getEditType() {
        return mEditType;
    }

    public void setEditType(int editType) {
        mEditType = editType;
    }

    public boolean isExecuteStatus() {
        return executeStatus;
    }

    public void setExecuteStatus(boolean executeStatus) {
        this.executeStatus = executeStatus;
    }

    public SettingOperation findActionOperationById(String operationId){
        for (SettingOperation settingOperation : actionOperations){
            if(operationId.equals(settingOperation.getOperationId())){
                return settingOperation;
            }
        }
        return null;
    }

    public boolean containActionOperation(String operationId){
        return findActionOperationById(operationId) != null;
    }

    public String getActionString() {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < actionOperations.size(); i++) {
            SettingOperation settingOperation = actionOperations.get(i);
            builder.append(settingOperation);
            builder.append(",");
        }
        return builder.toString();
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append(", actionOperations====");
        for (int i = 0; i < actionOperations.size(); i++) {
            SettingOperation settingOperation = actionOperations.get(i);
            builder.append(settingOperation);
            builder.append(",");
        }
        builder.append("conditionOperations====");
        for (int i = 0; i < conditionOperations.size(); i++) {
            SettingOperation settingOperation = conditionOperations.get(i);
            builder.append(settingOperation);
            builder.append(",");
        }
        builder.append("stateOperations====");
        for (int i = 0; i < stateOperations.size(); i++) {
            SettingOperation settingOperation = stateOperations.get(i);
            builder.append(settingOperation);
            builder.append(",");
        }
        return "SceneBean{" +
                "sceneId='" + sceneId + '\'' +
                ", sceneName='" + sceneName + '\'' +
                ", scenePic='" + scenePic + '\'' +
                ", sceneDesc='" + sceneDesc + '\'' +
                ", sceneCategoryId='" + sceneCategoryId + '\'' +
                ", sceneIcon='" + sceneIcon + '\'' +
                ", sort=" + sort +
                builder +
//                ", actionOperations=" + actionOperations +
//                ", conditionOperations=" + conditionOperations +
//                ", stateOperations=" + stateOperations +
                "isNew=" + isNew +
                ", collectState=" + collectState +
                ", mEditType=" + mEditType +
                ", mTimestamp=" + timestamp +
                ", autoEnable=" + autoEnable +
                ", autoNotify=" + autoNotify +
                '}';
    }
}
