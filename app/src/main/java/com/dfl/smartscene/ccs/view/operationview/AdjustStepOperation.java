package com.dfl.smartscene.ccs.view.operationview;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.Glide;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.library.app.ContextUtil;
import com.dfl.smartscene.ccs.util.DataTypeFormatUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.weight.CustomRatingBar;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2022/9/19
 * @description ：带等级滑动条页面
 */
public class AdjustStepOperation implements OperationBaseView {

    private CustomRatingBar mCustomRatingBar;
    private ImageView mImageViewAdd;
    private ImageView mImageViewReduce;

    private final SingleOperation mSingleOperation;

    private final float mRangeMin;
    private final float mRangMax;
    private final float mStepValue;

    @Override
    public List<String> extractArgs() {
        ArrayList<String> values = new ArrayList<>(1);
        float rating = mCustomRatingBar.getRating();
        int value = (int) (rating * mStepValue);
//        values.add(String.valueOf(rating));
        values.add(String.valueOf(value));
        return values;
    }

    @Override
    public String extractDesc() {
        String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
        if (mCustomRatingBar != null) {
            int rating = (int) mCustomRatingBar.getRating();
//            int value = (int) (mRangeMin + rating * mStepValue);
            desc = desc.replaceFirst("&", String.valueOf(rating));
        }
        return desc;
    }

    public AdjustStepOperation(SingleOperation singleOperation, ViewGroup parent) {

        mSingleOperation = singleOperation;
        String unitStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT_END);
        String defaultStr = singleOperation.getArg(ConstantModelValue.VIEW_DEFAULT_POS);
        String minStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_LOW);
        String maxStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_HIGH);
        String stepStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_STEP);
        String valueTypeStr = singleOperation.getArg(ConstantModelValue.VIEW_DESC_RANGE_TYPE);

//        String startPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_START);
//        String endPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_END);
//        Log.d("ABCX", "startPicStr = " + startPicStr + ", endPicStr = " + endPicStr);

        float defaultValue = Float.parseFloat(defaultStr);

        mRangeMin = DataTypeFormatUtil.string2Int(minStr);
        mRangMax = DataTypeFormatUtil.string2Int(maxStr);

        mStepValue = DataTypeFormatUtil.string2Int(stepStr);

        LinearLayout linearLayout = new LinearLayout(parent.getContext());
        linearLayout.setGravity(Gravity.CENTER);
//        linearLayout.setBackgroundResource(R.drawable.ic_adjust_step_bg);

        mCustomRatingBar = new CustomRatingBar(parent.getContext());
        mCustomRatingBar.setNumStars(DataTypeFormatUtil.string2Int(maxStr));
        mCustomRatingBar.setStepSize(DataTypeFormatUtil.string2Int(stepStr));
        mCustomRatingBar.setItemSize(58, 8);
        mCustomRatingBar.setProgressDrawable(R.drawable.bg_shape_l, R.drawable.bg_shape, R.drawable.bg_shape_r, R.drawable.bg_shape_check_l, R.drawable.bg_shape_check, R.drawable.bg_shape_check_r);
        mCustomRatingBar.setOnRatingBarChangeListener((ratingBar, rating, fromUser) -> {
            if(rating < mRangeMin){
                mCustomRatingBar.setRating(mRangeMin);
            }
        });

        // 设置默认值
        mCustomRatingBar.setRating(defaultValue);

        mImageViewReduce = new ImageView(parent.getContext());
        mImageViewReduce.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
//        if (!TextUtils.isEmpty(startPicStr)) {
//            if (startPicStr.startsWith("http")) {
//                Glide.with(mImageViewReduce).load(startPicStr).into(mImageViewReduce);
//            } else {
//                Drawable drawable = ContextUtil.getDrawableByName(startPicStr);
//                if (null != drawable) {
//                    mImageViewReduce.setImageDrawable(drawable);
//                }
//            }
//        } else {
//            mImageViewReduce.setBackgroundResource(R.drawable.ic48_system_reduce_n);
//        }
        mImageViewReduce.setOnClickListener(v -> {
            float value = Math.max(mCustomRatingBar.getRating() - mStepValue, mRangeMin);
            mCustomRatingBar.setRating(value);
        });

        mImageViewAdd = new ImageView(parent.getContext());
        mImageViewAdd.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
//        if (!TextUtils.isEmpty(endPicStr)) {
//            if (endPicStr.startsWith("http")) {
//                Glide.with(mImageViewAdd).load(endPicStr).into(mImageViewAdd);
//            } else {
//                Drawable drawable = ContextUtil.getDrawableByName(endPicStr);
//                if (null != drawable) {
//                    mImageViewAdd.setImageDrawable(drawable);
//                }
//            }
//        } else {
//            mImageViewAdd.setBackgroundResource(R.drawable.ic48_system_plus_n);
//        }
        setImage();
        mImageViewAdd.setOnClickListener(v -> {
            float value = Math.min(mCustomRatingBar.getRating() + mStepValue, mRangMax);
            mCustomRatingBar.setRating(value);
        });

        ViewGroup.MarginLayoutParams paramsReduce = new ViewGroup.MarginLayoutParams(30, 30);
        paramsReduce.setMargins(0, 0, 14, 0);
        linearLayout.addView(mImageViewReduce, paramsReduce);

        linearLayout.addView(mCustomRatingBar);

        ViewGroup.MarginLayoutParams paramsAdd = new ViewGroup.MarginLayoutParams(30, 30);
        paramsAdd.setMargins(14, 0, 0, 0);
        linearLayout.addView(mImageViewAdd, paramsAdd);
        linearLayout.setClipChildren(false);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, 56);
//        layoutParams.setLayoutDirection(FrameLayout.HORIZONTAL);
        layoutParams.gravity = Gravity.CENTER;
        parent.addView(linearLayout, layoutParams);
    }


    @Override
    public void onConfigurationChanged() {
        setImage();
    }

    private void setImage() {
        if (mImageViewReduce == null || mImageViewAdd == null) {
            return;
        }
        String startPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_START);
        String endPicStr = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_PIC_END);

        if (!TextUtils.isEmpty(startPicStr)) {
            if (startPicStr.startsWith("http")) {
                Glide.with(mImageViewReduce).load(startPicStr).into(mImageViewReduce);
            } else {
                Drawable drawable = ContextUtil.getDrawableByName(startPicStr);
                if (null != drawable) {
                    mImageViewReduce.setImageDrawable(drawable);
                }
            }
        } else {
            mImageViewReduce.setBackgroundResource(R.drawable.ic48_system_reduce_n);
        }

        if (!TextUtils.isEmpty(endPicStr)) {
            if (endPicStr.startsWith("http")) {
                Glide.with(mImageViewAdd).load(endPicStr).into(mImageViewAdd);
            } else {
                Drawable drawable = ContextUtil.getDrawableByName(endPicStr);
                if (null != drawable) {
                    mImageViewAdd.setImageDrawable(drawable);
                }
            }
        } else {
            mImageViewAdd.setBackgroundResource(R.drawable.ic48_system_plus_n);
        }
        mCustomRatingBar.setProgressDrawable(R.drawable.bg_shape_l, R.drawable.bg_shape, R.drawable.bg_shape_r, R.drawable.bg_shape_check_l, R.drawable.bg_shape_check, R.drawable.bg_shape_check_r);
        mCustomRatingBar.invalidate();
    }


}
