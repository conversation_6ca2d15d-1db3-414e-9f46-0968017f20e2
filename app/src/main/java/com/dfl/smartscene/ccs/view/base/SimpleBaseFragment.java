package com.dfl.smartscene.ccs.view.base;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.viewmodel.base.MainViewModel;

/**
 * @author: huangzezheng
 * @date: 2022/2/17
 */
public abstract class SimpleBaseFragment extends Fragment {
    private boolean mStoped;
    protected MainViewModel mMainViewModel;
    private static final String TAG = "SimpleBaseFragment";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mMainViewModel = new ViewModelProvider(requireActivity()).get(MainViewModel.class);
        init();

    }

    @Override
    public void onResume() {
        super.onResume();
        if(mStoped){
            mStoped = false;
            onRestart();
        }
    }

    public void onRestart(){

    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {

        super.onViewCreated(view, savedInstanceState);
        LogUtil.d(TAG, "onViewCreated");
        initView(view);
        //监听数据须在view初始化之后，防止空指针异常
        initObserver();

        initBaseObserver();

        initData();

    }

    protected void refreshData(){
        initData();
    }
    protected abstract void init();

    protected abstract void initView(View view);

    protected abstract void initObserver();

    protected abstract void initData();

    //监听网络状态，播放状态，登陆状态，会员状态，vr状态
    protected void initBaseObserver(){
        mMainViewModel.getNetWorkStatus().observe(getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                onNetWorkChanged(aBoolean);
            }
        });

        mMainViewModel.getLoginStatus().observe(getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                onLoginStatusChanged(aBoolean);
            }
        });
    }

    //子类重写应对网络状态变化方法
    abstract protected void onNetWorkChanged(Boolean netWorkStatus);

    //子类应对登录状态变化方法
    abstract protected void onLoginStatusChanged(boolean loginStatus);

    //子类应对VR唤醒状态变化方法
    abstract protected void onVrWakeUpChanged(boolean vrStatus);

    abstract protected void onPermissionChanged(boolean permissionStatus);

    @Override
    public void onStop() {
        super.onStop();
        mStoped = true;
    }

}
