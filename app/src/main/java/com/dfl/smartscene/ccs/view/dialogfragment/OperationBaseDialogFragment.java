package com.dfl.smartscene.ccs.view.dialogfragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseDialogFragment;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.eventTrack.BigDataManager;
import com.dfl.smartscene.ccs.factory.OperationBaseViewFactory;
import com.iauto.uibase.utils.MLog;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：2022/8/29 15:34
 * @description ：操作的容器类，在内部添加具体的操作
 */
public class OperationBaseDialogFragment extends BaseDialogFragment {
    private static final String TAG = "OperationBaseDialogFragment";
    /**
     * 由EditFragment定义，详见该类
     */
    private int operationType = 0;
    /**
     * 0：修改操作
     * 1：新增操作
     */
    private int editType = 0;
    /**
     * 修改位置
     */
    private int pos = 0;
    public static final int EDIT_TYPE_MODIFY = 0;
    public static final int EDIT_TYPE_ADD = 1;
    protected SettingOperation mSettingOperation;//传入的场景操作
    protected SingleOperation mSingleOperation;//根据场景操作查询到对应的车辆配置

    protected OperationBaseView mOperationBaseView;//操作视图
    private ViewGroup mContentView;
    private OperationChangeListerner mOperationChangeListerner;
    private TextView mTextViewTitle;
    private View mImageViewBack;
    protected View mViewConfirm;

    public OperationBaseDialogFragment(int operationType, SingleOperation singleOperation) {
        this.operationType = operationType;
        this.editType = EDIT_TYPE_ADD;
        mSingleOperation = singleOperation;
    }

    public OperationBaseDialogFragment(int operationType, int pos, SettingOperation settingOperation, SingleOperation singleOperation) {
        this.operationType = operationType;
        this.editType = EDIT_TYPE_MODIFY;
        this.pos = pos;
        mSettingOperation = settingOperation;
        mSingleOperation = singleOperation;
        copyDefaultValue();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        LogUtil.d("OperationBaseDialogFragment", "onCreate");
        return inflater.inflate(R.layout.dialog_fragment_operation_base_wide, container, false);
    }

    @Override
    protected void init() {

    }

    @Override
    protected void initView(View view) {
        //根据操作类型添加对应的操作视图
        mContentView = view.findViewById(R.id.layout_operation_dialog_content);
        mViewConfirm = view.findViewById(R.id.button_operation_base_dialog_positive);
        mImageViewBack = view.findViewById(R.id.imageview_operation_base_dialog_back);
        mOperationBaseView = OperationBaseViewFactory.addOperationBaseView(mSingleOperation, mContentView);
        mImageViewBack.setOnClickListener(v -> dismiss());
        mViewConfirm.setOnClickListener(v -> onPositiveButtonClick());
        // 初始化title
        mTextViewTitle = view.findViewById(R.id.button_operation_base_dialog_title);
        if (null != mTextViewTitle) {
            if (null != mSingleOperation) {
                String operationName = mSingleOperation.getOperationName();
                if (!TextUtils.isEmpty(operationName)) {
                    mTextViewTitle.setText(operationName);
                }
            }
        }
        if (null != mSingleOperation) {
            String bottomDesc = mSingleOperation.getArg(ConstantModelValue.VIEW_DESC_TEXT_BUTTOM);
            if (bottomDesc != null) {
                TextView textViewBottomDesc = view.findViewById(R.id.textview_dialog_operation_base_bottom_desc);
                textViewBottomDesc.setText(bottomDesc);
            }
        }

        //埋点：弹窗出现时触发
        LogUtil.d(TAG, "textViewTitle.getText().toString()");
        BigDataManager.getInstance().writeEventMod4(mTextViewTitle.getText().toString(), "OperationBaseDialogFragment");

    }

    @Override
    protected void initObserver() {

    }

    @Override
    protected void initData() {

    }

    public void setOperationChangeListerner(OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
    }

    /**
     * 点击确认按钮
     */
    public void onPositiveButtonClick() {
        if (editType == EDIT_TYPE_ADD) {
            mOperationChangeListerner.onOperationAdd(operationType, extractOperation());
        } else if (editType == EDIT_TYPE_MODIFY) {
            mOperationChangeListerner.onOperationModify(operationType, pos, extractOperation());
        }
        dismiss();
    }

    public void onNegativeButtonClick() {

    }

    @Override
    public void dismiss() {
        super.dismiss();

        mOperationChangeListerner = null;
    }

    /**
     * 提取对应的场景操作
     *
     * @return
     */
    private SettingOperation extractOperation() {
        SettingOperation settingOperation = new SettingOperation();
        settingOperation.setOperationId(mSingleOperation.getOperationId());
        settingOperation.setDeviceId(mSingleOperation.getDeviceId());
        if (null == mOperationBaseView) {
            mOperationBaseView = OperationBaseViewFactory.addOperationBaseView(mSingleOperation, mContentView);
        }
        settingOperation.setListArgs(mOperationBaseView.extractArgs());
        settingOperation.setDesc(mOperationBaseView.extractDesc());
        LogUtil.d(TAG, JSON.toJSONString(settingOperation));

        String btnStatus = settingOperation.getListArgs().toString();
        btnStatus = btnStatus.replace("[","").replace("]","");
        //埋点：弹窗点击时触发
        BigDataManager.getInstance().writeEventMod5(mTextViewTitle.getText().toString(), btnStatus, settingOperation.getDesc());

        return settingOperation;
    }

    /**
     * 用于通知场景编辑类执行结果
     */
    public interface OperationChangeListerner {
        /**
         * 改变场景编辑类的操作
         *
         * @param operationType
         * @param pos
         * @param settingOperation
         */
        void onOperationModify(int operationType, int pos, SettingOperation settingOperation);

        /**
         * 添加场景编辑类的操作
         *
         * @param operationType
         * @param settingOperation
         */
        void onOperationAdd(int operationType, SettingOperation settingOperation);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mOperationBaseView != null) {
            MLog.d("OperationBaseDialogFragment", "onConfigurationChanged");
            mOperationBaseView.onConfigurationChanged();
        }
    }

    /**从设置参数中获取参数设定值，赋值给配置参数的默认值*/
    private void copyDefaultValue(){
        List<String> listArgs = mSettingOperation.getListArgs();
        //单一操作的场景
        if(null != listArgs && listArgs.size() == 1){
            if (ConstantModelValue.OPERATION_ID_STATE_TIME_DAY.equals(mSettingOperation.getOperationId())
                    && !Objects.equals(listArgs.get(0), String.valueOf(384))
                    && !Objects.equals(listArgs.get(0), String.valueOf(256))
                    && !Objects.equals(listArgs.get(0), String.valueOf(128))) {
                //生效时间的重复内容，如果是自定义，会没有内容，需要手动添加
                String desc = mSingleOperation.getArg(ConstantModelValue.OUTPUT_DESC_TEXT);
                if(TextUtils.isEmpty(desc)|| !desc.contains("&")){
                    mSingleOperation.setMapArgs(ConstantModelValue.OUTPUT_DESC_TEXT,"重复: &");
                }
            }
            mSingleOperation.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, listArgs.get(0));
            return;
        }else if(null != listArgs && listArgs.size() == 2){
            mSingleOperation.setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, listArgs.get(0)+"@"+listArgs.get(1));
        }
        //存在子任务的case
        boolean hasSubOperation = null != listArgs && listArgs.size() > 1 && null != mSingleOperation.getSubOperations() && mSingleOperation.getSubOperations().size() > 1;
        if(hasSubOperation){
            for(int i = 0; i < mSingleOperation.getSubOperations().size(); i++){
                mSingleOperation.getSubOperations().get(i).setMapArgs(ConstantModelValue.VIEW_DEFAULT_POS, listArgs.get(i));
            }
        }
    }
}
