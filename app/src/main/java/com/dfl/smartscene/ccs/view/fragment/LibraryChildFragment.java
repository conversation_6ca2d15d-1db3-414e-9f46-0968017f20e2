package com.dfl.smartscene.ccs.view.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.busevent.CollectEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.util.DialogManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.LibrarySceneAdapter;
import com.dfl.smartscene.ccs.viewmodel.CollectViewModel;


/**
 * <AUTHOR>
 * @date ：2022/8/30 8:58
 * @description ：广场子页面
 */
public class LibraryChildFragment extends LibraryChildBaseFragment<LibrarySceneAdapter> {
    private CollectViewModel mCollectViewModel;

    @Override
    protected void init() {
        super.init();
        mCollectViewModel = new ViewModelProvider(this).get(CollectViewModel.class);

    }

    @Override
    protected void initAdapterItemClickEvent(LibrarySceneAdapter adapter) {
        adapter.setOnButtonClickListener(new BaseAdapter.OnButtonClickListener<SceneBean>() {
            @Override
            public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                if(view.getId() == R.id.imagebutton_item_library_scene_collect){
                    if(sceneBean.isCollectState()){
                        ViewControlManager.uncollectScene(mSceneCategory.getCategoryId(),sceneBean);
                    }else {
                        ViewControlManager.collectScene(mSceneCategory.getCategoryId(), sceneBean);
                    }
                }else if(view.getId() == R.id.image_view_item_library_scene_excute){
                    DialogManager.showExcuteSceneDialog(sceneBean);
                }
            }
        });

        adapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SceneBean>() {
            @Override
            public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                Bundle bundle = new Bundle();
                bundle.putSerializable(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_SCENE_BEAN,sceneBean);
                bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_CATEGORY_ID, ConstantModelValue.CATEGORY_ID_COLLECT);
                bundle.putString(ConstantViewValue.FRAGMENT_KEY_SCENE_EDITOR_TYPE,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW);
                Navigation.findNavController(view).navigate(R.id.action_libraryChildFragment_to_sceneDetailFragment,bundle);
            }
        });
    }

    @Override
    protected LibrarySceneAdapter createAdapter() {
        return new LibrarySceneAdapter();
    }

    @Override
    protected void initObserver() {
        //收藏返回监听
        mCollectViewModel.getCollectEventMutableLiveData().observe(getViewLifecycleOwner(), new Observer<CollectEvent>() {
            @Override
            public void onChanged(CollectEvent collectEvent) {
                if(null != collectEvent){
                    if(collectEvent.getCategoryId().equals(mSceneCategory.getCategoryId())){
                        getAdapter().onCollectChange(collectEvent.getSceneId(), collectEvent.isStatus());
                    }
                }
            }
        });

    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }
}
