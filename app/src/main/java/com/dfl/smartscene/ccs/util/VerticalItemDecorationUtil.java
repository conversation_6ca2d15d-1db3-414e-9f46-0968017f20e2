package com.dfl.smartscene.ccs.util;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class VerticalItemDecorationUtil extends RecyclerView.ItemDecoration {
    private int space;

    public VerticalItemDecorationUtil(int space) {
        this.space = space;
    }


    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        outRect.bottom = 0;
        if(parent.getChildLayoutPosition(view) == 0){
            outRect.top = 0;
        }else {
            outRect.top = space;
        }
    }
}

