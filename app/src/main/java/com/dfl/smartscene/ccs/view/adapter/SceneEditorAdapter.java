package com.dfl.smartscene.ccs.view.adapter;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;


import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.BaseHolder;
import com.dfl.smartscene.ccs.bean.SettingOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.util.ImageUtil;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：2022/9/1 10:49
 * @description ：场景编辑页面的操作卡片(基类）
 */
public class SceneEditorAdapter extends BaseAdapter<SettingOperation> {

    private static String mSceneType = ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_ADD;

    public void setSceneType(String sceneType) {
        mSceneType = sceneType;
    }

    @Override
    protected BaseHolder<SettingOperation> getViewHolder(View view, int viewType) {
        return new SceneEditorHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_scene_editor_operation;
    }

    public List<SettingOperation> getValidData(){
        return getDataList();
    }

    /**
     * 状态，执行卡片的holder
     */
    class SceneEditorHolder extends SceneEditorBaseHolder{

        public SceneEditorHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void onClickDeleteButton(int position) {
            if(mOnButtonClickListener != null){
                mOnButtonClickListener.onItemClick(mImageButtonDelete,getItemViewType(),getItemData(position),position);
            }
        }
    }

    /**
     * 对于状态、执行动作，触发条件卡片的删除逻辑不同
     */
    public static class SceneEditorBaseHolder extends BaseHolder<SettingOperation> {
        private TextView mTextViewDesc;
        protected ImageView mImageButtonDelete;
        protected ImageView mImageViewIcon;
        protected ConstraintLayout mRootLayout;
        public SceneEditorBaseHolder(View itemView) {
            super(itemView);
            mRootLayout = itemView.findViewById(R.id.constraint_layout_item);
            mTextViewDesc = itemView.findViewById(R.id.textview_item_scene_editor_desc);
            mImageButtonDelete = itemView.findViewById(R.id.imagebutton_item_scene_editor_delete);
            mImageViewIcon = itemView.findViewById(R.id.imageview_scene_editor_item_icon);
            mImageButtonDelete.setOnClickListener(v -> {
                onClickDeleteButton(getAdapterPosition());
            });
        }

        @Override
        public void setupData(SettingOperation settingOperation, int position) {
            setTitle(settingOperation.getDesc());
            if (Objects.equals(mSceneType,ConstantViewValue.SCENE_EDITOR_TYPE_SCENE_SHOW)){
                mImageButtonDelete.setVisibility(View.GONE);
            }else {
                if (settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_ADD)
                        || settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_CLOCK)
                        || settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_STATE_TIME_DAY)) {
                    mImageButtonDelete.setVisibility(View.GONE);
                } else {
                    mImageButtonDelete.setVisibility(View.VISIBLE);
                }
            }
            if(settingOperation.getOperationId().equals(ConstantModelValue.OPERATION_ID_CONDITION_VR)){
                setTitle(settingOperation.getListArgs().get(0));

                mImageViewIcon.setImageResource(R.drawable.icon_microphone);
            }else {
                setTitle(settingOperation.getDesc());
            }
            if (ConstantModelValue.OID_CDT_DOOR_BACK.equals(settingOperation.getOperationId()) | ConstantModelValue.OID_STA_DOOR_BACK.equals(settingOperation.getOperationId())){
                //后背门图标不一样
                if (settingOperation.getDesc().contains("关上")){
                    mImageViewIcon.setImageResource(R.drawable.icon_trunk_close);
                }else {
                    mImageViewIcon.setImageResource(R.drawable.icon_trunk);
                }
            }else {
                ImageUtil.loadIcon(CarConfigManager.getInstance().findOperationIconById(settingOperation.getOperationId()), mImageViewIcon);
            }

        }

        protected void setTitle(String value){
            if(value.contains(" ")){
                SpannableString spannableString = new SpannableString(value);
                int index = value.indexOf(" ");
                spannableString.setSpan(new ForegroundColorSpan(mTextViewDesc.getResources().getColor(R.color.color_text_selected)),index,value.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                mTextViewDesc.setText(spannableString);
            }else {
                mTextViewDesc.setText(value);
            }

        }

        public void refreshBackground(){
            if(mRootLayout != null){
                mRootLayout.setBackground(null);
                mRootLayout.setBackground(ScenePatternApp.getInstance().getResources().getDrawable(R.drawable.drawable_bg_scene_editor_operation));
            }
        }

        protected void onClickDeleteButton(int position){
            //P挡相关的触发条件和状态条件需要同步删除
        }
    }
}
