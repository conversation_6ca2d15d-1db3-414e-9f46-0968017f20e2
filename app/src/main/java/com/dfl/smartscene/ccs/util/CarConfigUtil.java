package com.dfl.smartscene.ccs.util;

import com.dfl.smartscene.BuildConfig;
/**
 * description:
 * author:huangzezheng
 * Data:2023/2/23
 **/
public class CarConfigUtil {
    public static boolean isCCU(){
        return BuildConfig.platform.equals(BuildConfig.platform_ccu);
    }
    public static boolean isCCS(){
        return BuildConfig.platform.equals(BuildConfig.platform_ccs5);
    }
    public static boolean isPhone(){
        return BuildConfig.platform.equals(BuildConfig.platform_phone);
    }
}
