package com.dfl.smartscene.ccs.view.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.View;
import android.widget.Button;

/**
 * 长按时，连续触发动作的按钮
 */
@SuppressLint("AppCompatCustomView")
public class LongPressButton extends Button {

    private static final int HANDLER_ACTION_TIME = 100; // ms

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public LongPressButton(Context context) {
        this(context, null);
    }

    public LongPressButton(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LongPressButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }


    private static class LongClickRunnable implements Runnable {

        private View view;

        public LongClickRunnable(View view) {
            this.view = view;
        }

        public boolean isRunState() {
            boolean flat = null != view && view.isPressed();
            if (!flat) {
                release();
            }
            return flat;
        }

        public void release() {
            this.view = null;
        }

        public View getView() {
            return this.view;
        }

        @Override
        public void run() {

        }
    }

    private void initView(Context context) {

        setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {

                final LongClickRunnable runnable = new LongClickRunnable(v) {
                    @Override
                    public void run() {
                        if (isRunState()) {
                            if (null != mILongPressListener) {
                                mILongPressListener.onLongPress();
                            }
                            mHandler.postDelayed(this, HANDLER_ACTION_TIME);
                        } else {
                            mHandler.removeCallbacksAndMessages(null);
                        }
                    }
                };
                mHandler.postDelayed(runnable, HANDLER_ACTION_TIME);

                return true;
            }
        });
    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (null != mHandler) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
    }


   public interface ILongPressListener {
        void onLongPress();
    }

    private ILongPressListener mILongPressListener;

    public void setILongPressListener(ILongPressListener iLongPressListener) {
        mILongPressListener = iLongPressListener;
    }
}
