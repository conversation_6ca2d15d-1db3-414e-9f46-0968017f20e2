package com.dfl.smartscene.ccs.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/*
 * 彩蛋list类
 * <AUTHOR>
 * @date 2022/5/15
 */
public class SurpriseEggList {

    private List<SurpriseEgg> mSurpriseEggs;
    private static final String TAG = "SurpriseEggList";

    /**
     * ScenePatternInterrupt object constructor
     */
    public SurpriseEggList() {
        mSurpriseEggs = new ArrayList<>();
    }

    /**
     * 取得彩蛋list
     * @return List<SurpriseEgg> 彩蛋list
     */
    public List<SurpriseEgg> getSurpriseEggs() {
        return mSurpriseEggs;
    }

    /**
     * 取得指定管理id的彩蛋item
     * @param id 彩蛋管理id
     * @return SurpriseEgg 彩蛋item
     */
    public SurpriseEgg getSurpriseEggFromEggID(Integer id) {
        for(SurpriseEgg surpriseEgg:mSurpriseEggs) {
            if(surpriseEgg.getEggID() == id) {
                return surpriseEgg;
            }
        }

        return null;
    }

    /**
     * 取得指定index的彩蛋item
     * @param index 彩蛋index
     * @return SurpriseEgg 彩蛋item
     */
    public SurpriseEgg getSurpriseEggFromIndex(int index) {
        if (index >= mSurpriseEggs.size()) {
            return null;
        }
        return mSurpriseEggs.get(index);
    }

    /**
     * 取得彩蛋list size
     * @return int 彩蛋list size
     */
    public int getSurpriseEggListSize() {
        return mSurpriseEggs.size();
    }

    /**
     * 清空彩蛋list
     */
    public void clearSurpriseEggList() {
        mSurpriseEggs.clear();
    }

    /**
     * 追加彩蛋item到list中
     * @param egg 彩蛋item
     */
    public void addSurpriseEgg(SurpriseEgg egg) {
        mSurpriseEggs.add(egg);
    }

    private static final class SurpriseEggComparator implements Comparator<SurpriseEgg> {
        @Override
        public int compare(SurpriseEgg t1, SurpriseEgg t2) {
            return t2.getTime().compareTo(t1.getTime());
        }
    }
    /**
     * 彩蛋list排序
     */
    public void sortSurpriseEgg() {
        Collections.sort(mSurpriseEggs, new SurpriseEggComparator());
    }

    /**
     * 删除指定管理id的彩蛋item
     * @param id 指定管理id的彩蛋item
     */
    public void deleteSurpriseEgg(int id) {
        for(int i = 0; i < mSurpriseEggs.size(); i++) {
            if(mSurpriseEggs.get(i).getEggID() == id) {
                mSurpriseEggs.remove(i);
                break;
            }
        }
    }
}
