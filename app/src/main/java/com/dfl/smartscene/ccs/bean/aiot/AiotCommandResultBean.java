package com.dfl.smartscene.ccs.bean.aiot;

/**
 * <AUTHOR>
 * @date ：2022/9/21 14:02
 * @description ：操作结果返回
 */
public class AiotCommandResultBean {
    //指令执行结果：1：成功
    private int result;
    //指令类型
    private int cmd;
    private String pid;
    private String did;

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public int getCmd() {
        return cmd;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }
}
