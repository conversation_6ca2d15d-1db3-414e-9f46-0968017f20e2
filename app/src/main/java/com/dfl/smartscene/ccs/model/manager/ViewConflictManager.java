package com.dfl.smartscene.ccs.model.manager;

import android.view.View;
import android.view.ViewGroup;

import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseView;
import com.dfl.smartscene.ccs.view.operationview.ConflictViewClient;
import com.dfl.smartscene.ccs.view.operationview.ConflictViewService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2022/9/15 14:02
 * @description ：视图冲突管理类，用于同一弹窗里有两个操作会有冲突时其中一个要置灰
 * 分为服务端和客户端，服务端出现数据变化时，若和客户端有冲突，则告知客户端进行置灰处理
 */
public class ViewConflictManager {
    private boolean needGone = false;

    private Map<String , OperationBaseView> viewMap = new HashMap<>();//操作视图，客户端或者服务端
    private Map<String , SingleOperation> operationMap = new HashMap<>();//存储操作
    private Map<String , ViewGroup> containerMap = new HashMap<>();//存储父视图
    private static final String TAG = "ViewConflictManager";

    public ViewConflictManager(boolean needGone) {
        this.needGone = needGone;
    }

    /**
     * 注册操作
     * @param input
     * @param inputView
     */
    public void registerOperation(SingleOperation input , OperationBaseView inputView, ViewGroup container){
        viewMap.put(input.getOperationId(),inputView);
        containerMap.put(input.getOperationId(),container);

        for(Map.Entry<String , SingleOperation> entry : operationMap.entrySet()){
            makeConflictRelation(input, entry.getValue());
        }
        operationMap.put(input.getOperationId(),input);

    }

    /**
     * 建立客户端和服务端之间的联系
     * @param o1
     * @param o2
     */
    private void makeConflictRelation(SingleOperation o1 , SingleOperation o2){
        makeOneWayRelation(o1,o2);
        makeOneWayRelation(o2,o1);
    }

    /**
     * 建立单向的冲突关系
     * @param client 获取对应的冲突id列表，和service进行判断
     * @param service
     */
    private void makeOneWayRelation(SingleOperation client,SingleOperation service){
        List<String> inputConflictId = client.getListArg(ConstantModelValue.VIEW_CONFLICT_ID_LIST);
        List<String> inputConflictArgs = client.getListArg(ConstantModelValue.VIEW_CONFLICT_ARG_LIST);

        //如果客户端的冲突id和传入的服务端id相对应
        if(!ListUtil.isEmpty(inputConflictId) && inputConflictId.contains(service.getOperationId())){
            OperationBaseView clientView  = viewMap.get(client.getOperationId());
            OperationBaseView serviceView = viewMap.get(service.getOperationId());

            //并且两个视图都实现了相应的接口
            if(serviceView instanceof ConflictViewService && clientView instanceof ConflictViewClient){
                LogUtil.d(TAG, "add arg change listener, arg = "+service.getOperationId());
                //则注册服务端监听，当数据变化时改变客户端的置灰状态
                ((ConflictViewService) serviceView).addArgChangeLintener(arg -> {
                    for(int i = 0 ; i < inputConflictId.size() ; i++){
                        if(service.getOperationId().equals(inputConflictId.get(i)) && arg.equals(inputConflictArgs.get(i))){
                            ((ConflictViewClient) clientView).setViewUnabled(true);
                            containerMap.get(client.getOperationId()).setAlpha(0.5f);
                            if(needGone){
                                containerMap.get(client.getOperationId()).setVisibility(View.GONE);
                            }
                            return;
                        }
                    }
                    ((ConflictViewClient) clientView).setViewUnabled(false);
                    containerMap.get(client.getOperationId()).setAlpha(1f);

                    if(needGone){
                        containerMap.get(client.getOperationId()).setVisibility(View.VISIBLE);
                    }

//                    if(inputConflictArgs.get(inputConflictId.indexOf(service.getOperationId())).equals(arg)){
//                        ((ConflictViewClient) clientView).setViewUnabled(true);
//                    }else {
//                        ((ConflictViewClient) clientView).setViewUnabled(false);
//                    }
                });
            }
        }

    }
}
