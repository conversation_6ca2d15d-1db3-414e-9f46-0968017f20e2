package com.dfl.smartscene.ccs.view.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DAModel;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.LibraryCardAdapter;
import com.iauto.uibase.utils.MLog;

/**
 * <AUTHOR>
 * @date ：2022/12/5 9:50
 * @description ：
 */
public class LibraryCardChildFragment extends LibraryChildBaseFragment<LibraryCardAdapter>{
    private static final String TAG = "LibraryCardChildFragment";
    @Override
    protected LibraryCardAdapter createAdapter() {
        return new LibraryCardAdapter();
    }

    @Override
    protected void initAdapterItemClickEvent(LibraryCardAdapter adapter) {
        //点击执行场景，或者跳转菜单页面
        adapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<SceneBean>() {
            @Override
            public void onItemClick(View view, int viewType, SceneBean sceneBean, int position) {
                MLog.d(TAG, "on item click position"+position);
                if (sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_EGG)) {
                    if(!DAModel.getInstance().isUserCenterLogin()){
                        Intent intent   = new Intent();
                        intent.setClassName(ScenePatternFuncDef.CENTER_PACKAGE_NAME,
                                ScenePatternFuncDef.CENTER_ACTIVITY_NAME);
                        //设置拉起登录页的标识，便于返回
                        intent.putExtra("cpType", 24);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                        ScenePatternApp.getInstance().startActivity(intent);
                    }else {
                        NavController navController = Navigation.findNavController(view);
                        Bundle bundle = new Bundle();
                        bundle.putBoolean("PGear", DriveModel.getInstance().isPGear());
                        navController.navigate(R.id.action_libraryCardChildFragment_to_surpriseEggListFragment);
                    }

                } else if (sceneBean.getSceneId().equals(ConstantModelValue.SCENE_ID_OLD_RELAX)) {
                    NavController navController = Navigation.findNavController(view);
                    navController.navigate(R.id.action_libraryCardChildFragment_to_relieveStressPatternFragment);
                    MLog.d(TAG, "on old relax click "+sceneBean.getSceneName());
                } else {
                    MLog.d(TAG, "on item click "+sceneBean.getSceneName());
                    ViewControlManager.executeCardScene(sceneBean,false);

//                    DialogUtil.showDefaultDoubleDialog(new DefaultDoubleDialogListener() {
//                        @Override
//                        public View.OnClickListener getPositiveButtonListener(LibDialog libDialog) {
//                            return v -> {
//                                ViewControlManager.excuteScene(sceneBean,"即将体验 ");
//                                libDialog.dismiss();
//                            };
//                        }
//
//                        @Override
//                        public String getDialogMessage() {
//                            return "开始试用 " + sceneBean.getSceneName() + "? 试用中请确保车辆及人员处于安全状态";
//                        }
//
//                        @Override
//                        public int getDialogLayout() {
//                            return R.layout.dialog_double_title_layout_2;
//                        }
//                    });
                }
            }
        });

    }

}
