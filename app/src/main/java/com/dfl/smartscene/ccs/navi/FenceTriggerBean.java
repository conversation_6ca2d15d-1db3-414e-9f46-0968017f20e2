package com.dfl.smartscene.ccs.navi;

import java.io.Serializable;

/**
 * 情景模式中地理围栏条件的数据实体<P/>
 * 该数据实体序列化之后转为String，在情景应用与Navi之间进行信息传递<P/>
 * <AUTHOR>
 * @date 2022-9-24
 */
public class FenceTriggerBean implements Serializable {
    /**
     * 情景模式id（需要由情景模式保证其唯一性和持久性）
     */
    private String sceneId;
    /**
     * 情景模式的名称（例如：回家模式）
     */
    private String sceneName;
    /**
     * 触发条件，与场景条件定义相同
     */
    private ConditionBean conditionBean;

    /**
     * 状态条件
     */
    private ConditionBean conditionData;

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public ConditionBean getConditionBean() {
        return conditionBean;
    }

    public void setConditionBean(ConditionBean conditionBean) {
        this.conditionBean = conditionBean;
    }

    public ConditionBean getConditionData() {
        return conditionData;
    }

    public void setConditionData(ConditionBean conditionData) {
        this.conditionData = conditionData;
    }

    public void copyValue(FenceTriggerBean bean) {
        this.sceneId = bean.sceneId;
        this.sceneName = bean.sceneName;
        conditionBean = bean.conditionBean;
    }
}