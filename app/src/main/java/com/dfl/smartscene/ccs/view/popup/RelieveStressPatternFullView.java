package com.dfl.smartscene.ccs.view.popup;

import android.content.Context;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.PagerAdapter;

import com.dfl.scenepattern.R;
import com.dfl.scenepattern.constant.RelieveStressFuncDef;
import com.dfl.scenepattern.constant.ScenePatternFuncDef;
import com.dfl.scenepattern.databinding.ViewRelieveStressPatternFullBinding;
import com.dfl.scenepattern.util.SysViewControl;
import com.dfl.scenepattern.viewmodel.RelieveStressPatternVM;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MViewBase;
import com.iauto.uicontrol.ButtonBase;
import com.iauto.uicontrol.ButtonView;
import com.iauto.uicontrol.GestureBoard;
import com.iauto.uicontrol.GifPlayer;
import com.iauto.uicontrol.ProgressBar;
import com.iauto.uicontrol.ViewPagerList;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class RelieveStressPatternFullView extends MViewBase {
    private static final String     TAG = "RelieveStressPatternFullView";

    private  int  mPageList[] ={R.layout.relieve_stress_view_pager_list_full_page0,
                                R.layout.relieve_stress_view_pager_list_full_page1,
                                R.layout.relieve_stress_view_pager_list_full_page2};

    private  int  mGifArrayList[] ={R.array.relieve_stress_expand_pattern1,
                                    R.array.relieve_stress_expand_pattern2,
                                    R.array.relieve_stress_expand_pattern1};
    //定义一个View的数组
    private List<View> mViews =new ArrayList<>();
    private ViewPagerList mViewpager = null;
    private RelieveStressPatternVM mViewModel = null;
    private GestureBoard mGestureBoard = null;
    private GestureBoard.GestureBoardListener mGestureBoardListener = null;
    private Context mContext = null;
    PatternAdapter mPatternAdapter = null;
    Timer timer;

    public RelieveStressPatternFullView(Context context) {
        super(context);
        mContext = context;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        MLog.d(TAG, "onCreateView");
        ViewRelieveStressPatternFullBinding binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.view_relieve_stress_pattern_full, this, true);
        mViewModel = new ViewModelProvider(this).get(RelieveStressPatternVM.class);
        mViewModel.initLiveData();
        binding.setVm(mViewModel);
        binding.setLifecycleOwner(this);
        initCCEventReceiver((ViewGroup) binding.getRoot());

        mViewpager = (ViewPagerList) binding.getRoot().findViewById(R.id.background_view_full_paper);
        mViewpager.addOnPageChangeListener(new ViewPagerList.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                MLog.d(TAG, "onPageScrolled");
                MLog.d(TAG, "onPageScrolled-position:" + position);
                MLog.d(TAG, "onPageScrolled-positionOffset:" + positionOffset);
                MLog.d(TAG, "onPageScrolled-positionOffsetPixels:" + positionOffsetPixels);
                MLog.d(TAG, "滑动");
            }

            @Override
            public void onPageSelected(int position) {
                MLog.d(TAG, "onPageSelected");
                MLog.d(TAG, "onPageScrolled-position:" + position);
                MLog.d(TAG, "当新的页面被选中时调用");
                mViewModel.setRelieveStressPattern(position);
                if (mViewModel.getTransferOrigin().getValue() != RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Expand &&
                        mViewModel.getTransferOrigin().getValue() != RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Start) {
                    // 此处调用停止当前播放，切换新的播放。重新计时

                    if (mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                            || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                        setCtrlViewVisibleTimer();
                        mViewModel.setRelieveStressPlayNext();
                        mViewModel.setCtrlViewVisible(true);
                    }
                }

            }

            @Override
            public void onPageScrollStateChanged(int state) {
                MLog.d(TAG, "onPageScrollStateChanged");
                MLog.d(TAG, "onPageScrolled-state:" + state);
                MLog.d(TAG, "滑动状态改变时");
            }
        });

        //将images数组中的图片放入ImageView
        mViews.clear();
        for (int i = 0; i < mPageList.length; i++) {
            View page = View.inflate(mContext, mPageList[i],null);
            GifPlayer gifPlayer = page.findViewById(R.id.gif_player);
            gifPlayer.setGifFrames(mGifArrayList[i]);
            mViews.add(page);
        }
        //为ViewPager设置适配器
        mPatternAdapter = new PatternAdapter();
        mViewpager.setAdapter(mPatternAdapter);

        mGestureBoard = (GestureBoard) binding.getRoot().findViewById(R.id.gesture_board_relieve_stress_full_start);
        mGestureBoardListener = new GestureBoard.GestureBoardListener() {
            @Override
            public void onSingleTapUp(MotionEvent e) {
                int action = e.getActionMasked();
                MLog.d(TAG, "onSingleTapUp() action:" + action);
                switch (action) {

                    case MotionEvent.ACTION_UP:
                    default:
                        break;
                }
            }
            @Override
            public void onDoubleTap(MotionEvent e) {

            }
            @Override
            public void onVerticalScrollLeft(float percent) {
                MLog.d(TAG, "onVerticalScrollLeft() percent:" + percent);
            }
            @Override
            public void onVerticalScrollRight(float percent){
                MLog.d(TAG, "onVerticalScrollRight() percent:" + percent);
                mViewModel.reqVolumeValue(percent);
            }
            @Override
            public void onHorizontalScroll(float percent) {
                MLog.d(TAG, "onHorizontalScroll() percent:" + percent);
            }

        };
        mGestureBoard.setGestureBoardListener(mGestureBoardListener);

        MLog.d(TAG, "onCreate end");
    }

    /**
     * 蓝牙电话变更观察者
     */
    protected RelieveStressPhoneObserver relieveStressPhoneObserver = new RelieveStressPhoneObserver();
    public class RelieveStressPhoneObserver implements Observer<Integer> {
        public void onChanged(Integer phoneStatus) {
            if (RelieveStressFuncDef.Relieve_Stress_Phone_Interrupt_True == phoneStatus) {
                if (timer != null) {
                    timer.cancel();
                }
            }
        }
    }

    /**
     * 动画状态变更观察者
     */
    protected RelieveStressAnimateStatusChange mRelieveStressAnimateStatusChange = new RelieveStressAnimateStatusChange();
    public class RelieveStressAnimateStatusChange implements Observer<Integer> {
        public void onChanged(Integer Status) {
            doAnimated(Status);
        }
    }

    /**
     * 添加观察者
     */
    private void addObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:addObserver ");
        mViewModel.getPhoneInterruptStatus().observeForever(relieveStressPhoneObserver);
        mViewModel.getAnimateStatus().observeForever(mRelieveStressAnimateStatusChange);
    }

    /**
     * 移除观察者
     */
    private void removeObserver() {
        MLog.d(TAG,"RelieveStressPatternVM:removeObserver ");
        mViewModel.getPhoneInterruptStatus().removeObserver(relieveStressPhoneObserver);
        mViewModel.getAnimateStatus().removeObserver(mRelieveStressAnimateStatusChange);
    }

    /**
     * 初始化子控件event receiver
     *
     * @param view
     */
    private void initCCEventReceiver(ViewGroup view) {
        for (int i = 0; i < view.getChildCount(); i++) {
            View childView = view.getChildAt(i);
            if (childView instanceof ButtonBase) {
                ButtonBase child = (ButtonBase) childView;
                child.setEventReceiver(this);
            }

            if (childView instanceof ButtonView) {
                ButtonView child = (ButtonView) childView;
                child.setEventReceiver(this);
            }

            if (childView instanceof ViewGroup) {
                initCCEventReceiver((ViewGroup) childView);
            }
        }
    }

    @Override
    public void onStop() {
        super.onStop();
    }



    /**
     * 恢复表示处理
     *
     */
    @Override
    public void onResume() {
        super.onResume();
        MLog.d(TAG, "RelieveStressPatternFragment onResume");
        addObserver();
        mViewModel.setCtrlViewVisible(true);
        setCtrlViewVisibleTimer();
        if (null != mViewpager) {
            mViewpager.setCurrentItem(mViewModel.getRelieveStressPattern().getValue(), false);
        }
        doAnimated(mViewModel.getRelieveStressStatus().getValue());

    }

    /**
     * 画面停止处理
     *
     */
    @Override
    public void onPause() {
        super.onPause();
        removeObserver();

        if (timer != null) {
            timer.cancel();
        }
        MLog.d(TAG, "onPause");
    }

    /**
     * Relieve Pattern Status relate button's event method
     * @param v Relieve stress pattern fragment view
     */
    public void onCCMethod_RelievePatternStatusChangeClick(View v) {
        MLog.d(TAG, "onCCMethod_RelievePatternStatusChangeClick: ");

        int id = v.getId();
        if (id == R.id.button_relieve_stress_full_play) {
            onRelievePatternPlayClick();
        } else if (id == R.id.button_relieve_stress_full_pause) {
            onRelievePatternPauseClick();
        } else if (id == R.id.button_relieve_stress_full_stop) {
            onRelievePatternStopClick();
        } else if (id == R.id.button_relieve_stress_full_narrow) {
            onRelievePatternNarrowClick();
        }
    }

    /**
     * 舒压模式停止处理
     *
     */
    public void onRelievePatternStopClick() {
        MLog.d(TAG, "onRelievePatternStopClick: ");
        mViewModel.makeBigDataStopReason(ScenePatternFuncDef.SCENE_PATTERN_STOP_REASON_MANUAL);
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
        SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
        doAnimated(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
    }

    /**
     * 舒压模式暂停处理
     *
     */
    private void onRelievePatternPauseClick() {
        MLog.d(TAG, "onRelievePatternPauseClick: ");
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Pause);
    }

    /**
     * 舒压模式播放处理
     *
     */
    private void onRelievePatternPlayClick() {
        MLog.d(TAG, "onRelievePatternPlayClick: ");
        mViewModel.setRelieveStressStatus(RelieveStressFuncDef.Relieve_Stress_Status_Play);
    }

    /**
     * 舒压模式窄屏变更处理
     *
     */
    private void onRelievePatternNarrowClick() {
        MLog.d(TAG, "onRelievePatternNarrowClick: ");
        mViewModel.setTransferOrigin(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_Narrow);
        doAnimated(RelieveStressFuncDef.Relieve_Stress_Status_Stop);
        SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
    }


    /*
     * pagerAdapter处理
     * <AUTHOR>
     * @date 2022/4/24
     */
    class PatternAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return mViews.size();
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view==object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View v=mViews.get(position);
            container.addView(v);

            return v;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            View v=mViews.get(position);
            //前一张图片划过后删除该View
            container.removeView(v);
        }
    }


    /**
     * 播放时，控件5秒后消失处理
     *
     */
    public class VolumeProgressBarListener implements ProgressBar.OnTouchChangedListener {
        @Override
        public void onCcPress(int value) {
        }
        @Override
        public void onCcRelease(int value) {
            mViewModel.reqVolumeValue(value);
        }
        @Override
        public void onCcMove(int value) {
        }
        @Override
        public void onCcCancel(int value) {
            mViewModel.reqVolumeValue(value);
        }
        @Override
        public void onCcMoveOut(int value) {
        }
    }

    private void setCtrlViewVisibleTimer() {
        MLog.d(TAG, "setCtrlViewVisibleTimer: ");
        if (mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
            if (timer != null) {
                timer.cancel();
            }
            timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    mViewModel.setCtrlViewVisible(false);
                    MLog.d(TAG, "5s timeout, display is out");
                    timer.cancel();
                }
            }, 5000);
        }
    }

    private void doAnimated(int status) {
        MLog.d(TAG, "doAnimated");
        int pattern = mViewModel.getRelieveStressPattern().getValue();

        View page =  mViews.get(pattern);
        GifPlayer gifPlayer = page.findViewById(R.id.gif_player);
        if (RelieveStressFuncDef.Relieve_Stress_Status_Play == status) {
            MLog.d(TAG, "doAnimated play");
            gifPlayer.setGifState(1);
        } else if (RelieveStressFuncDef.Relieve_Stress_Status_Pause == status) {
            MLog.d(TAG, "doAnimated pause");
            gifPlayer.setGifState(2);
        } else {
            MLog.d(TAG, "doAnimated stop");
            gifPlayer.setGifState(3);
        }
    }
    int mLastDownX = 0;
    int mLastDownY = 0;
    int mTouchSlop =10;
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        MLog.d(TAG, "onTouchEvent");
        super.onTouchEvent(ev);
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                MLog.d(TAG, "onTouch:ACTION_DOWN");
                mViewModel.setTransferOrigin(RelieveStressFuncDef.Relieve_Stress_Transfer_Origin_None);
                mLastDownX = (int) ev.getRawX();   // 触摸点与屏幕左边的距离
                mLastDownY = (int) ev.getRawY();   // 触摸点与屏幕上边的距离
                if (mViewModel.getRelieveStressGestureEnable().getValue()) {
                    mGestureBoard.onTouchEvent(ev);
                }

                break;
            case MotionEvent.ACTION_MOVE:
                float downX = ev.getRawX();
                float downY = ev.getRawY();
                float dx = Math.abs(downX - mLastDownX);
                float dy = Math.abs(downY - mLastDownY);
                if (Math.sqrt(dx * dx + dy * dy) > mTouchSlop && dy > dx) {
                    if (mViewModel.getRelieveStressGestureEnable().getValue()) {
                        mGestureBoard.onTouchEvent(ev);
                    }
                    return true;
                } else {
                    return true;
                }
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                // 音量dialog在压下抬起时消失
                if ( mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Play
                        || mViewModel.getRelieveStressStatus().getValue() == RelieveStressFuncDef.Relieve_Stress_Status_Pause) {
                    if (mViewModel.getIsVolumeSlipping().getValue()) {
                        mViewModel.reqVolumeSlipStop();
                        return true;
                    }

                    if( Math.abs((int) ev.getRawX() - mLastDownX) > 0 || Math.abs((int) ev.getRawY() - mLastDownY) > 0 ) {
                        return true;
                    }
                    // 压下抬起时进行是否表示控件处理
                    mViewModel.ChangeCtrlViewVisible();
                    setCtrlViewVisibleTimer();
                }
                break;
            default:
                break;
        }
        return false;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev){
        MLog.d(TAG, "dispatchTouchEvent");
        onTouchEvent(ev);

        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        MLog.d(TAG, "dispatchKeyEvent");
        if(event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            SysViewControl.getInstance().reqCloseRelieveStressPatternFullView();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }
}
