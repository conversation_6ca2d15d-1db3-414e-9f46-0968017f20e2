package com.dfl.smartscene.ccs.view.operationview;

import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.view.adapter.NaviRouteChooseAdapter;
import com.dfl.smartscene.ccs.view.adapter.OpeRecyclerAdapter;


/**
 * <AUTHOR>
 * @date ：2023/4/8 11:03
 * @description ：
 */
public class RecyclerButtonRouteOperation extends RecyclerButtonOperation{

    public RecyclerButtonRouteOperation(SingleOperation singleOperation, ViewGroup parent) {
        super(singleOperation, parent);
    }

    @Override
    public OpeRecyclerAdapter creatAdapter() {
        return new NaviRouteChooseAdapter();
    }

    @Override
    public void setRecyclerViewLayout(RecyclerView recyclerView) {
        RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL,4,COLUMN_SPACE,LINE_SPACE);

    }
}
