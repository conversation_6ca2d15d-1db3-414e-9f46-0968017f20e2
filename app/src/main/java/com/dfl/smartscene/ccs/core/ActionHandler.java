package com.dfl.smartscene.ccs.core;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date ：2023/3/23 13:54
 * @description ：
 */
public class ActionHandler extends Handler {
    private Object token = new Object();
    private static final int oneSedWhat = -1;
    private String mSceneId;

    public ActionHandler(@NonNull Looper looper) {
        super(looper);
    }

    public void startScene(String sceneId) {
        mSceneId = sceneId;
        Message message = obtainMessage(oneSedWhat,token);
        sendMessageDelayed(message,1000);
    }

    public void excuteDelay(Runnable runnable , long delay){
        Message message = Message.obtain(this,runnable);
        message.what = hash(mSceneId);
        message.obj = token;
        sendMessageDelayed(message,delay);
    }

    /**
     * 判断该场景是否还有操作没执行的
     * @param sceneId
     * @return
     */
    public boolean hasMessage(String sceneId){
        return hasMessages(hash(sceneId));
    }



    /**
     * 如果距离上一个场景还没有1s，返回true
     */
    public boolean isBusy(){
        return hasMessages(oneSedWhat);
    }

    public void removeAllMessage(){
        removeCallbacksAndMessages(token);
    }

    private int hash(String sceneId){
        int h;
        return (sceneId == null) ? 0 : (h = sceneId.hashCode()) ^ (h >>> 16);
    }
}
