package com.dfl.smartscene.ccs.viewmodel;

import android.app.Application;

import com.iauto.uibase.lifecycle.MViewModelBase;
import com.iauto.uibase.utils.MLog;

public class ScenePatternMenuVM extends MViewModelBase {
    private static final String TAG = "ScenePatternMenuVM";

    /**
     * SeatSettingVM object constructor
     * @param application object
     */
    public ScenePatternMenuVM(Application application) {
        super(application);
        initLiveData();
    }

    /**
     * init seat live data
     */
    public void initLiveData() {
        MLog.d(TAG,"initLiveData ");

    }
}
