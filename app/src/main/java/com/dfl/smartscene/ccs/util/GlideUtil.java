package com.dfl.smartscene.ccs.util;

import static com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil.TAG_GLIDE_ERROR;
import static com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil.TAG_GLIDE_FALLBACK;
import static com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil.TAG_GLIDE_LOADING;
import static com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil.TAG_ROUND_CORNER;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dfl.dflcommonlibs.uimodeutil.GlideUiRes;
import com.dfl.dflcommonlibs.uimodeutil.ResourceTagLoadUtil;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;

/**
 * @author: huangzezheng
 * @date: 2021/12/3
 */
public class GlideUtil {

    private static final String GIF = "gif";

    /**
     * 使用glide请求url数据,发现页的大图
     * @param imageView
     * @param url
     */
    public static void loadImageUrl(ImageView imageView, String url){
        GlideUiRes glideUiRes=new GlideUiRes.Builder()
                .setHeight((int) imageView.getResources().getDimension(R.dimen.x_px_286))
                .setWide((int) imageView.getResources().getDimension(R.dimen.y_px_663))
                .setShape(TAG_ROUND_CORNER)
                .setCorner(16).build();
        if(url == null || "".equals(url)){
            Glide.with(imageView)
                    .load(R.drawable.drawable_card_default_bg)
                    .into(imageView);
            ResourceTagLoadUtil.addImgPropertyTag(glideUiRes,imageView);
            return;
        }
        RequestOptions options = RequestOptions
                .bitmapTransform(new MultiTransformation<>(new CenterCrop(), new RoundedCorners(10)))
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.ALL).override((int) imageView.getResources().getDimension(R.dimen.x_px_160), (int) imageView.getResources().getDimension(R.dimen.y_px_160))
                .placeholder(R.drawable.drawable_card_default_bg);
        glideUiRes.setError(R.drawable.drawable_card_default_bg);
        glideUiRes.setPlaceHolder(R.drawable.drawable_card_default_bg);
        ResourceTagLoadUtil.addImgPropertyTag(glideUiRes,imageView);

        if (url.endsWith(GIF)) {
            options.format(DecodeFormat.PREFER_ARGB_8888);
        } else {
            options.format(DecodeFormat.PREFER_RGB_565);
        }

        Glide.with(imageView)
                .load(url)
                .apply(options)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(new SimpleTarget<Drawable>() {
                    @Override
                    public void onResourceReady (@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        imageView.setImageDrawable(resource);
                        ResourceTagLoadUtil.modifyViewTagOnlyInGlideState(TAG_GLIDE_FALLBACK,imageView);
                    }

                    @Override
                    public void onLoadStarted (@Nullable Drawable placeholder) {
                        super.onLoadStarted(placeholder);
                        imageView.setImageDrawable(placeholder);
                        ResourceTagLoadUtil.modifyViewTagOnlyInGlideState(TAG_GLIDE_LOADING,imageView);

                    }

                    @Override
                    public void onLoadFailed (@Nullable Drawable errorDrawable) {
                        super.onLoadFailed(errorDrawable);
                        imageView.setImageDrawable(errorDrawable);
                        ResourceTagLoadUtil.modifyViewTagOnlyInGlideState(TAG_GLIDE_ERROR,imageView);
                    }
                });

    }

    public static void initGlide() {
        GlideBuilder builder = new GlideBuilder();
        long memoryCacheSize = 1024 * 1024 * 5; //10M
        // 设置内存缓存大小
        builder.setMemoryCache(new LruResourceCache(memoryCacheSize));
        long diskCacheSize = 1024 * 1024 * 100; //100M
        // 设置硬盘缓存大小
        builder.setDiskCache(new InternalCacheDiskCacheFactory(ScenePatternApp.getInstance().getApplicationContext(), diskCacheSize));
        Glide.init(ScenePatternApp.getInstance().getApplicationContext(), builder);
    }

}
