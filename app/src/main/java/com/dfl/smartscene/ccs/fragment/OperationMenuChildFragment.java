package com.dfl.smartscene.ccs.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseAdapter;
import com.dfl.smartscene.ccs.base.MyClickableSpan;
import com.dfl.smartscene.ccs.bean.SingleOperation;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.factory.OperationBaseViewFactory;
import com.dfl.smartscene.ccs.util.RecyclerViewUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.OperationMenuChildAdapter;
import com.dfl.smartscene.ccs.view.base.BaseFragment;
import com.dfl.smartscene.ccs.view.dialogfragment.OperationBaseDialogFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/9/5 13:55
 * @description ：操作菜单弹窗的子页面
 */
public class OperationMenuChildFragment extends BaseFragment implements BaseAdapter.OnItemClickListener<SingleOperation> {
    private static final String TAG = "OperationMenuChildFragment";
    private List<SingleOperation> mSingleOperations;
    protected OperationMenuChildAdapter mOperationMenuChildAdapter;
    private OperationBaseDialogFragment.OperationChangeListerner mOperationChangeListerner;//操作反馈给场景编辑页的回调监听

    private static final @DimenRes
    int COLUMN_SPACE = R.dimen.x_px_24;//item之间的间距
    int LINE_SPACE = R.dimen.y_px_24;//item之间的间距
    private int mOperationType;//操作类型，触发/状态/执行
    private static final int COLUMN_NUM = 2;
    RecyclerView recyclerView;
    private List<String> unableList = new ArrayList<>();//禁止使用的操作id


    public OperationMenuChildFragment(int operationType, List<SingleOperation> operations, List<String> unableList) {
        mOperationType = operationType;
        mSingleOperations = operations;
        this.unableList = unableList;
    }

    public OperationMenuChildFragment(int operationType, List<SingleOperation> operations) {
        this(operationType, operations, new ArrayList<>());
    }

    public void setOperationChangeListerner(OperationBaseDialogFragment.OperationChangeListerner operationChangeListerner) {
        mOperationChangeListerner = operationChangeListerner;
    }

    @Override
    protected void init() {

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_operation_menu_child, container, false);
    }

    protected OperationMenuChildAdapter onCreatAdapter() {
        return new OperationMenuChildAdapter();
    }

    @Override
    protected void initView(View view) {
        recyclerView = view.findViewById(R.id.recyclerview_operation_menu);
        GridLayoutManager layoutManager = RecyclerViewUtil.setGridRecycleView(recyclerView, RecyclerView.VERTICAL, COLUMN_NUM, COLUMN_SPACE, LINE_SPACE);
        layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return mOperationMenuChildAdapter.getItemViewType(position) == OperationMenuChildAdapter.VIEW_TITLE ? COLUMN_NUM : 1;
            }
        });
        mOperationMenuChildAdapter = onCreatAdapter();
        mOperationMenuChildAdapter.setUnableOperation(unableList);
        recyclerView.setAdapter(mOperationMenuChildAdapter);
        int paddingLeft = recyclerView.getPaddingLeft();
        int paddingRight = recyclerView.getPaddingRight();
        int paddingTop = recyclerView.getPaddingTop();
        int paddingBottom = recyclerView.getPaddingBottom();
        recyclerView.setPadding(paddingLeft+24,paddingTop,paddingRight,paddingBottom);

        //操作卡片点击监听，进入操作弹窗
        mOperationMenuChildAdapter.setOnItemClickListener(this);


    }

    @Override
    protected void initObserver() {

    }

    @Override
    protected void initData() {
        List<SingleOperation> singleOperations = new ArrayList<>();
        for(SingleOperation singleOperation : mSingleOperations){
            if(singleOperation.getShowMode() == ConstantModelValue.CAR_OPERATION_SHOW_TYPE_NORMAL){
                singleOperations.add(singleOperation);
            }
        }
        mOperationMenuChildAdapter.setDataList(singleOperations);
        showContentPage();
    }

    @Override
    protected void onNetWorkChanged(Boolean netWorkStatus) {

    }

    @Override
    protected void onLoginStatusChanged(boolean loginStatus) {

    }

    @Override
    protected void onVrWakeUpChanged(boolean vrStatus) {

    }

    @Override
    protected View getContentPageView() {
        return recyclerView;
    }

    @Override
    public void onItemClick(View view, int viewType, SingleOperation singleOperation, int position) {
        if (view.getTag() != null) {
            //冲突操作，不可点击
            return;
        }
        if (viewType == OperationMenuChildAdapter.VIEW_NORMAL) {
            if (singleOperation.getShowMode() == ConstantModelValue.CAR_OPERATION_SHOW_TYPE_AIOT_OFFLINE) {
                //如果设备离线
                String toastText = singleOperation.getOperationName() + "离线啦,前往连接";
                ToastManager.showSpanToast(toastText, "前往连接", new MyClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        ViewControlManager.openAiotDevicePage();
                    }
                });
            } else {
                OperationBaseDialogFragment operationBaseDialogFragment = OperationBaseViewFactory.creatOperationContainer(mOperationType, singleOperation);
                operationBaseDialogFragment.setOperationChangeListerner(mOperationChangeListerner);
                operationBaseDialogFragment.show(getChildFragmentManager(), null);
            }
        } else if (viewType == OperationMenuChildAdapter.VIEW_SALE) {
            String toastText = "您还没有" + singleOperation.getOperationName() + ",前往购买";
            ToastManager.showSpanToast(toastText, "前往购买", new MyClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    ViewControlManager.openAiotStorePage();
                }
            });
        }

    }

    @Override
    public void onConfigurationChanged (@NonNull Configuration newConfig){
        super.onConfigurationChanged(newConfig);
        if (recyclerView != null&&mOperationMenuChildAdapter!=null) {
            recyclerView.removeAllViews();
            recyclerView.removeAllViewsInLayout();
            recyclerView.setAdapter(mOperationMenuChildAdapter);
            recyclerView.getRecycledViewPool().clear();
            List<SingleOperation> singleOperations = new ArrayList<>();
            for (SingleOperation singleOperation : mSingleOperations) {
                if (singleOperation.getShowMode() == ConstantModelValue.CAR_OPERATION_SHOW_TYPE_NORMAL) {
                    singleOperations.add(singleOperation);
                }
            }
            mOperationMenuChildAdapter.setDataList(singleOperations);
        }

    }

}
