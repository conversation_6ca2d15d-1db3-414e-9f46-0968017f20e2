package com.dfl.smartscene.ccs.db.dao.scene;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


import com.dfl.smartscene.ccs.db.entity.scene.SceneStateListEntity;

import java.util.List;

import io.reactivex.Observable;

@Dao
public interface SceneStateListDao {

    @Insert
    void insertStudent(SceneStateListEntity item);

    @Delete
    void deleteStudent(SceneStateListEntity item);

    @Update
    void updateStudent(SceneStateListEntity item);

    @Query("SELECT * FROM scene_state_list")
    Observable<List<SceneStateListEntity>> queryAll();

    @Query("SELECT * FROM scene_state_list WHERE :mSceneId='' or mSceneId= :mSceneId")
    Observable<List<SceneStateListEntity>> queryBySceneId(String mSceneId);

    @Query("SELECT * FROM scene_state_list WHERE :mDeviceId='' or mSceneId= :mDeviceId")
    Observable<List<SceneStateListEntity>> queryByDeviceId(String mDeviceId);

    @Query("SELECT * FROM scene_state_list WHERE :mOperationId='' or mOperationId= :mOperationId")
    Observable<List<SceneStateListEntity>> queryByOperationId(String mOperationId);

    @Query("SELECT * FROM scene_state_list WHERE mSceneId in (:mSceneIds)")
    Observable<List<SceneStateListEntity>> queryBySceneIds(String[] mSceneIds);

}
