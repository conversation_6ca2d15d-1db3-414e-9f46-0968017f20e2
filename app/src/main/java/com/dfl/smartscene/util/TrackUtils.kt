package com.dfl.smartscene.util

import com.dfl.android.commonlib.CommonUtils
import com.dfl.android.commonlib.log.CommonLogUtils
import com.dfl.smartscene.BuildConfig
import com.dfl.smartscene.bean.customapi.DataEventSceneBean
import com.dfl.smartscene.bean.main.ScenarioBean
import com.dfl.android.common.global.GlobalConstant
import com.dfl.smartscene.ui.edit.container.SceneNewEditType
import com.dfl.smartscene.ui.edit.manager.SceneDataEventManager
import com.iauto.scenarioadapter.ScenarioInfo
import com.szlanyou.iov.eventtracking.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 *Created by 钟文祥 on 2024/3/4.
 *Describer: 埋点 辅助类
 * log打印 LyEventTrack、TrackRequest、parseL3Response
 */
object TrackUtils {

    private const val TAG = GlobalConstant.GLOBAL_TAG.plus("TrackUtils")
    private const val mBrand = "日产"
    private const val mCCSType = "LK1A"
    private const val mAppName = "智慧场景"
    private const val mPlatform = "Android"

    /**埋点 appkey*/
    private const val mAppKey = "zf8fdmt61wv6qg1s"

    private var isWidgetClickResult = false //表示是否点击卡片场景执行后有了回调， 解决点击卡片场景执行后，除本身有回调外，再有手动执行的回调
    private const val urlType = ServerConfig.PRODUCTION //商用：ServerConfig.PRODUCTION   测试：ServerConfig.TEST

    enum class PageNameType(var value: Int) {
        Community(0), My(1), MyLog(2), Help(3), Discover(4)
    }

    private val pageNameStartEventIdArr = arrayListOf<String>(
        "Scenario_Community_viewstart",
        "Scenario_My_viewstart",
        "Scenario_MyLog_viewstart",
        "Scenario_Help_viewstart",
        "Scenario_Preset_viewstart"
    )

    private val pageNameStartEventNameArr = arrayListOf<String>(
        "“社区”页面浏览开始", "“我的”页面浏览开始", "“日志”页面浏览开始", "“帮助”页面浏览开始", "“发现”页面浏览开始"
    )

    private val pageNameEndEventIdArr = arrayListOf<String>(
        "Scenario_Community_viewend",
        "Scenario_My_viewend",
        "Scenario_MyLog_viewend",
        "Scenario_Help_viewend",
        "Scenario_Preset_viewend"
    )

    private val pageNameEndEventNameArr = arrayListOf<String>(
        "“社区”页面浏览结束", "“我的”页面浏览结束", "“日志”页面浏览结束", "“帮助”页面浏览结束", "“发现”页面浏览结束"
    )

    /**初始化埋点*/
    fun initIEventTrackAPI() {
        CoroutineScope(Dispatchers.IO).launch {

            ServerConfig.setUrlType(urlType) //商用：ServerConfig.PRODUCTION   测试：ServerConfig.TEST
            val config = Config()
            config.enableLog() // 开启日志
            // appKey，appCode和brandCode由埋点管理平台分配；appKey必填；appCode和brandCode根据需求填入
            config.appKey = mAppKey
            config.appCode = mAppName
            config.brandCode = mBrand
            // 如果不需要配置远程策略，就把远程配置关掉。这个需要咨询产品
            config.isDisableRemoteStrategy = true
            // 如果不需要全埋点，则传AutoEventTrack.NONE
            config.autoTrackEventType = AutoEventTrack.NONE


            // 用户ID 可以是供应商的用户id
            config.userId = ""
            // 日产的用户ID 日产的用户id，如果userid存在安全隐患，也可统一使用uuid
            config.oemUserId = ""

            val trackApi = EventTrackApi.getInstance()
            // 初始化
            trackApi.init(CommonUtils.getApp(), config)
            // 设置一些属性 以下属性只是示例，具体根据对应的埋点需求配置，
            // Const里面的字段是方便使用预置的，实际传的埋点数据还是根据需求的来
            try {
                val jsonObject = JSONObject()
                jsonObject.put(Const.Event.DEVICE_TYPE, Const.Event.DEVICE_TYPE_CAR)
                jsonObject.put(Const.Event.APP_VERSION, BuildConfig.VERSION_NAME)
                // 车机端取Android、linux，由SDK类型决定；手机端可取Android、iOS、h5、java，php。默认是Android，没有特殊需求不用配置
                jsonObject.put(Const.Event.PLATFORM, "Android")
                // 车机系统取da，车机/手机应用取app。默认是app
                jsonObject.put(Const.Event.DATA_TYPE, "da")

                // fillValPropertiesWith 设置一些不可变的预置属性
                trackApi.fillValPropertiesWith(jsonObject)
                CommonLogUtils.logI(TAG, "埋点初始化成功")
            } catch (ex: Exception) {
                ex.printStackTrace()
                CommonLogUtils.logE(TAG, "埋点初始化失败:" + ex.message)
            }
        }
    }

    /***
     * mEventType : 事件类型
     * mEventName : 事件名称
     * mEventId : 事件id
     */
    private fun initCommonInfo(mEventType: String, mEventName: String, mEventId: String): CommonInfo {
        return CommonInfo(
            mBrand, mCCSType, mAppName, mPlatform, mEventType, mEventName, mEventId
        )
    }

    /**发送埋点*/
    private fun trackEvent(commonInfo: CommonInfo, eventDetail: JSONObject) {
        isWidgetClickResult = false
        EventTrackApi.getInstance().trackEvent(commonInfo, eventDetail)
    }

    //	/**从应用池进来*/
    //	fun appStartFromPools() {
    //		val commonInfo = initCommonInfo("启动", "在应用池内点击智慧场景", "launcher_app_Scenario_click")
    //		val eventDetail = JSONObject()
    //		eventDetail.put("app_name", mAppName)
    //		trackEvent(commonInfo, eventDetail)
    //	}
    //
    //
    //	/**从卡片进来*/
    //	fun appStartFromWidget() {
    //		val commonInfo = initCommonInfo("启动", "在widget卡片上点击智慧场景", "launcher_widget_Scenario_click")
    //		val eventDetail = JSONObject()
    //		eventDetail.put("app_name", mAppName)
    //		trackEvent(commonInfo, eventDetail)
    //	}
    //
    //	/**从状态栏进来*/
    //	fun appStartFromStatus() {
    //		val commonInfo = initCommonInfo("启动", "在状态栏上点击智慧场景", "launcher_statusbar_Scenario_click")
    //		val eventDetail = JSONObject()
    //		eventDetail.put("app_name", mAppName)
    //		trackEvent(commonInfo, eventDetail)
    //	}

    /**进入页面*/
    @JvmStatic
    fun viewStart(pageNameType: PageNameType) {
        val commonInfo = initCommonInfo(
            "浏览", pageNameStartEventNameArr[pageNameType.value], pageNameStartEventIdArr[pageNameType.value]
        )
        val eventDetail = JSONObject()
        eventDetail.put("entry_time", System.currentTimeMillis())
        trackEvent(commonInfo, eventDetail)
    }

    /**退出页面*/
    @JvmStatic
    fun viewEnd(pageNameType: PageNameType) {
        val commonInfo = initCommonInfo(
            "浏览", pageNameEndEventNameArr[pageNameType.value], pageNameEndEventIdArr[pageNameType.value]
        )
        val eventDetail = JSONObject()
        eventDetail.put("exit_time", System.currentTimeMillis())
        trackEvent(commonInfo, eventDetail)
    }


    /**点击“新建场景”*/
    fun clickBtnNewScene() {
        val commonInfo = initCommonInfo(
            "点击", "在我的场景页面点击“新建场景”", "Scenario_MyNew_click"
        )
        val eventDetail = JSONObject()
        eventDetail.put("click_time", System.currentTimeMillis())
        trackEvent(commonInfo, eventDetail)
    }

    /**保存 新建模板创建的内容*/
    fun clickSaveRecommendNewScene(
        scenario: ScenarioBean?,
        execution_switch_isVisible: Boolean,
        inquiry_switch_isVisible: Boolean,
        scenario_originalname: String
    ) {
        scenario?.let {
            clickSaveNewScene(
                scenario_id = it.scenarioInfo?.scenarioId,
                scenario_name = it.scenarioInfo?.scenarioName,
                scenario_type = it.isAutoRun,
                trigger_condition_type = SceneDataEventManager.getSkillIdFirstMenuName(
                    scenario.scenarioInfo, SceneNewEditType.TRIGGER_CONDITION
                ),
                trigger_condition_detail = SceneDataEventManager.getTriggerConditionDetail(it.scenarioInfo?.edgeCondition),
                status_condition_type = SceneDataEventManager.getSkillIdFirstMenuName(
                    scenario.scenarioInfo, SceneNewEditType.STATUS_CONDITION
                ),
                status_condition_detail = SceneDataEventManager.getStatusConditionDetail(it.scenarioInfo?.conditions),
                action_type = SceneDataEventManager.getSkillIdFirstMenuName(
                    scenario.scenarioInfo, SceneNewEditType.ACTION
                ),
                action_detail = SceneDataEventManager.getActionDetail(it.scenarioInfo?.sequence),
                execution_switch = if (execution_switch_isVisible) {
                    if (it.isAskBeforeAutoRun == true) "开" else "关"
                } else {
                    "无"
                },
                inquiry_switch = if (inquiry_switch_isVisible) {
                    if (it.isAutoRun == true) "开" else "关"
                } else {
                    "无"
                },
                scenario_originalname = scenario_originalname,
                type = 0
            )
        }
    }

    /**保存 新建场景的内容*/
    fun clickSaveNewScene(mDataEventAddSceneBean: DataEventSceneBean?, type: Int) {
        mDataEventAddSceneBean?.let {
            clickSaveNewScene(
                it.scenario_id,
                it.scenario_name,
                it.scenario_type,
                it.trigger_condition_type,
                it.trigger_condition_detail,
                it.status_condition_type,
                it.status_condition_detail,
                it.action_type,
                it.action_detail,
                it.execution_switch,
                it.inquiry_switch,
                scenario_originalname = null,
                type = type
            )
        }
    }

    /**保存 新建场景的内容*/
    private fun clickSaveNewScene(
        scenario_id: String?,
        scenario_name: String?,
        scenario_type: Boolean?,
        trigger_condition_type: String?,
        trigger_condition_detail: String?,
        status_condition_type: String?,
        status_condition_detail: String?,
        action_type: String?,
        action_detail: String?,
        execution_switch: String?,
        inquiry_switch: String?,
        scenario_originalname: String? = null,
        type: Int //
    ) {

        val eventDetail = JSONObject()
        eventDetail.put("scenario_id", scenario_id) //场景ID
        eventDetail.put("scenario_name", scenario_name) //场景名称
        eventDetail.put("scenario_type", scenario_type) //场景类型（手动执行/自动执行）
        eventDetail.put("trigger_condition_type", trigger_condition_type) //触发条件类型
        eventDetail.put("trigger_condition_detail", trigger_condition_detail) //触发条件详情
        eventDetail.put("status_condition_type", status_condition_type) //状态条件类型
        eventDetail.put("status_condition_detail", status_condition_detail) //状态条件详情
        eventDetail.put("action_type", action_type) //执行动作类型
        eventDetail.put("action_detail", action_detail) //执行动作详情
        eventDetail.put("execution_switch", execution_switch) //自动执行开关情况
        eventDetail.put("inquiry_switch", inquiry_switch) //自动执行前询问开关情况

        val commonInfo = if (scenario_originalname == null) {
            val mEventName = when (type) {
                0 -> "保存新建场景"
                1 -> "二次确认时保存新建场景"
                2 -> "保存编辑场景"
                3 -> "二次确认时保存编辑场景"
                else -> ""
            }
            val mEventId = when (type) {
                0 -> "Scenario_MyNewSave_click"
                1 -> "Scenario_MyNewReconfirmSave_click"
                2 -> "Scenario_MyEditSave_click"
                3 -> "Scenario_MyEditReconfirmSave_click"
                else -> ""
            }
            initCommonInfo("点击", mEventName, mEventId)
        } else { //用模板创建
            eventDetail.put("scenario_originalname", scenario_originalname)
            initCommonInfo("点击", "发现-用模板创建的内容", "Scenario_PresetDetailEditSave_click")
        }
        trackEvent(commonInfo, eventDetail)
    }


    /**我的场景满足条件“自动执行”*/
    fun clickMyAutomaticScene(scenario_name: String?, scenario_id: String?) {
        val commonInfo = initCommonInfo(
            "CCM判断满足条件", "我的场景满足条件“自动执行”", "Scenario_MyAutomaticScene_click"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_name", scenario_name)
        eventDetail.put("scenario_id", scenario_id)
        trackEvent(commonInfo, eventDetail)
    }

    /** 结果 ：自动执行*/
    fun resultMyAutomaticScene(scenario_id: String, result: Boolean) {
        val commonInfo = initCommonInfo(
            "结果", "自动执行", "Scenario_MyAutomaticScene_result"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_id", scenario_id)
        eventDetail.put("execution_result", if (result) "成功" else "中止")
        trackEvent(commonInfo, eventDetail)
    }


    //CCM判断满足条件，且该场景是自动执行前询问	“自动执行前询问”退出时回调触发
    fun clickMyAutomaticSceneInquiry(
        scenario_name: String?, scenario_id: String?, execution_detail: Int?
    ) {
        val commonInfo = initCommonInfo(
            "点击", "自动执行前询问执行情况", "Scenario_MyAutomaticSceneInquiry_click"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_name", scenario_name)
        eventDetail.put("scenario_id", scenario_id)
        eventDetail.put(
            "execution_detail", when (execution_detail) {
                0 -> "点击立即执行"
                1 -> "点击取消"
                2 -> "倒计时结束未点击"
                else -> ""
            }
        ) //取实际情况：点击立即执行、点击取消、倒计时结束未点击
        trackEvent(commonInfo, eventDetail)
    }


    /**点击 手动执行*/
    fun clickMyManualScene(scenario_name: String?, scenario_id: String?) {
        val commonInfo = initCommonInfo(
            "点击", "在我的场景页面点击“手动执行”", "Scenario_MyManualScene_click"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_name", scenario_name)
        eventDetail.put("scenario_id", scenario_id)
        trackEvent(commonInfo, eventDetail)
    }

    /**结果 手动执行*/
    fun resultMyManualScene(scenario_id: String, result: Boolean) {
        if (isWidgetClickResult) return
        val commonInfo = initCommonInfo(
            "结果", "手动执行的结果", "Scenario_MyManualScene_result"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_id", scenario_id)
        eventDetail.put("execution_result", if (result) "成功" else "中止")
        trackEvent(commonInfo, eventDetail)
    }

    /**点击 widget卡片上手动执行*/
    fun clickWidgetManualScene(scenario_name: String?, scenario_id: String?) {
        val commonInfo = initCommonInfo(
            "点击", "在widget卡片上点击快捷场景手动执行", "Scenario_WidgetManualScene_click"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_name", scenario_name)
        eventDetail.put("scenario_id", scenario_id)
        trackEvent(commonInfo, eventDetail)
    }

    /**结果 点击widget卡片上手动执行*/
    fun resultWidgetManualScene(scenario_id: String, result: Boolean) {
        val commonInfo = initCommonInfo(
            "结果", "widget卡片快捷场景的执行结果", "Scenario_WidgetManualScene_result"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_id", scenario_id)
        eventDetail.put("execution_result", if (result) "成功" else "中止")
        trackEvent(commonInfo, eventDetail)
        isWidgetClickResult = true
    }


    /**点击 我的页面发布场景*/
    fun clickPostScene(isFromMe: Boolean) {
        val commonInfo = if (isFromMe) {
            initCommonInfo(
                "点击", "在我的页面点击“发布场景”", "Scenario_MyUpload_click"
            )
        } else {
            initCommonInfo(
                "点击", "在社区页面点击“发布场景”", "Scenario_CommunityUpload_click"
            )
        }
        val eventDetail = JSONObject()
        eventDetail.put("click_time", System.currentTimeMillis())
        trackEvent(commonInfo, eventDetail)
    }


    /**
     * 我的-发布场景的内容
     * 社区-发布场景的内容
     */
    fun clickMePostScene(isFromMe: Boolean, bean: ScenarioBean?, scenario_L3id: String) {
        bean?.let {
            val eventDetail = JSONObject()
            val commonInfo: CommonInfo?
            if (isFromMe) {
                commonInfo = initCommonInfo(
                    "点击", "我的-发布场景的内容", "Scenario_MyUploadSave_click"
                )
                eventDetail.put("scenario_L3id", scenario_L3id) //社区ID
            } else {
                commonInfo = initCommonInfo(
                    "点击", "社区-发布场景的内容", "Scenario_CommunityUploadEdit_click"
                )
                eventDetail.put("scenario_L2id", it.scenarioInfo?.scenarioId) //l2 场景ID
                eventDetail.put("scenario_L3id", scenario_L3id) //l3 社区ID
            }

            eventDetail.put("scenario_name", it.scenarioInfo?.scenarioName) //场景名称
            eventDetail.put("scenario_introduction", it.scenarioInfo?.scenarioDesc) //场景简介
            eventDetail.put("scenario_type", it.isAutoRun) //场景类型（手动执行/自动执行）

            eventDetail.put(
                "trigger_condition_type",
                SceneDataEventManager.getSkillIdFirstMenuName(it.scenarioInfo, SceneNewEditType.TRIGGER_CONDITION)
            ) //触发条件类型
            eventDetail.put(
                "trigger_condition_detail",
                SceneDataEventManager.getTriggerConditionDetail(it.scenarioInfo?.edgeCondition)
            ) //触发条件详情
            eventDetail.put(
                "status_condition_type",
                SceneDataEventManager.getSkillIdFirstMenuName(it.scenarioInfo, SceneNewEditType.STATUS_CONDITION)
            ) //状态条件类型
            eventDetail.put(
                "status_condition_detail", SceneDataEventManager.getStatusConditionDetail(it.scenarioInfo?.conditions)
            ) //状态条件详情

            eventDetail.put(
                "action_type", SceneDataEventManager.getSkillIdFirstMenuName(it.scenarioInfo, SceneNewEditType.ACTION)
            ) //执行动作类型
            eventDetail.put("action_detail", SceneDataEventManager.getActionDetail(it.scenarioInfo?.sequence)) //执行动作详情
            //			eventDetail.put("upload_click_time", System.currentTimeMillis())//点击“发布到社区”时间
            //			eventDetail.put("upload_result", if (result) "成功" else "失败")//发布结果

            trackEvent(commonInfo, eventDetail)
        }
    }

    /**
     * 结果
     * 我的-发布场景的内容
     * 社区-发布场景的内容
     */
    fun resultClickMePostScene(isFromMe: Boolean, scenario_L3id: String, result: Boolean) {
        val commonInfo = if (isFromMe) {
            initCommonInfo("结果", "我的-发布场景到社区的结果", "Scenario_MyUpload_result")
        } else {
            initCommonInfo("结果", "社区-发布场景到社区的结果", "Scenario_CommunityUpload_result")
        }
        val eventDetail = JSONObject()
        eventDetail.put("scenario_L3id", scenario_L3id)
        eventDetail.put("upload_result", if (result) "成功" else "失败") //发布结果
        trackEvent(commonInfo, eventDetail)
    }


    /**点击 下载*/
    fun clickDownLoad(bean: ScenarioInfo?, scenario_L3id: String?) {
        bean?.let {
            val commonInfo = initCommonInfo(
                "点击", "在社区页面点击下载", "Scenario_CommunityDownload_click"
            )
            val eventDetail = JSONObject()
            eventDetail.put("scenario_L3id", scenario_L3id)
            eventDetail.put("scenario_name", it.scenarioName)
            eventDetail.put("scenario_introduction", it.scenarioDesc)
            trackEvent(commonInfo, eventDetail)
        }
    }


    /**下载 保存到我的场景*/
    fun clickDownSave(bean: ScenarioInfo?, scenario_L3id: String?) {
        bean?.let {
            val commonInfo = initCommonInfo(
                "点击", "在社区页面下载场景到我的页面", "Scenario_CommunityDownloadSave_click"
            )
            val eventDetail = JSONObject()
            eventDetail.put("scenario_L2id", it.scenarioId)
            eventDetail.put("scenario_L3id", scenario_L3id)

            eventDetail.put("scenario_name", it.scenarioName)
            eventDetail.put("scenario_introduction", it.scenarioDesc)
            eventDetail.put("scenario_type", it.autoExeFlag == 0)

            eventDetail.put(
                "trigger_condition_type",
                SceneDataEventManager.getSkillIdFirstMenuName(bean, SceneNewEditType.TRIGGER_CONDITION)
            ) //触发条件类型
            eventDetail.put(
                "trigger_condition_detail", SceneDataEventManager.getTriggerConditionDetail(it.edgeCondition)
            ) //触发条件详情
            eventDetail.put(
                "status_condition_type",
                SceneDataEventManager.getSkillIdFirstMenuName(bean, SceneNewEditType.STATUS_CONDITION)
            ) //状态条件类型
            eventDetail.put(
                "status_condition_detail", SceneDataEventManager.getStatusConditionDetail(it.conditions)
            ) //状态条件详情
            eventDetail.put(
                "action_type", SceneDataEventManager.getSkillIdFirstMenuName(bean, SceneNewEditType.ACTION)
            ) //执行动作类型
            eventDetail.put("action_detail", SceneDataEventManager.getActionDetail(it.sequence)) //执行动作详情

            //			eventDetail.put("save_click_time", System.currentTimeMillis())
            trackEvent(commonInfo, eventDetail)
        }

    }

    /**
     * 结果
     * 在社区页面下载场景到我的页面的结果
     * */
    fun resultClickDownSave(scenario_L2id: String?, scenario_L3id: String?, result: Boolean) {
        val commonInfo = initCommonInfo(
            "结果", "在社区页面下载场景到我的页面的结果", "Scenario_CommunityDownload_result"
        )
        val eventDetail = JSONObject()
        eventDetail.put("scenario_L2id", scenario_L2id)
        eventDetail.put("scenario_L3id", scenario_L3id)
        eventDetail.put("upload_result", if (result) "成功" else "失败")
        trackEvent(commonInfo, eventDetail)
    }

    /**点击 点赞*/
    fun clickLike(bean: ScenarioInfo?, isLike: Boolean, scenario_L3id: String?) {
        bean?.let {
            val commonInfo = initCommonInfo(
                "点击", "在社区页面点赞场景", "Scenario_CommunityLike_click"
            )
            val eventDetail = JSONObject()
            eventDetail.put("scenario_L3id", scenario_L3id)
            eventDetail.put("scenario_name", it.scenarioName)
            eventDetail.put("scenario_introduction", it.scenarioDesc)
            eventDetail.put("like_type", isLike)
            eventDetail.put("like_click_time", System.currentTimeMillis())
            trackEvent(commonInfo, eventDetail)
        }
    }
}