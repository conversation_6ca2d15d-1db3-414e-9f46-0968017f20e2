//package com.dfl.smartscene.util.pki
//
//import com.szlanyou.danetwork.network.DaBaseRequestParams
//
///**
// *Created by 钟文祥 on 2024/8/28.
// *Describer:
// */
//internal class PkiDownloadParams(
//    private val params: DaBaseRequestParams
//) : DaBaseRequestParams(environment = params.environment) {
//
//    override fun getAppCode(): String = params.getAppCode()
//
//    override fun getAppId(): String = params.getAppId()
//
//    override fun getAppKey(): String = params.getAppKey()
//
//    override fun getAppVersionName(): String = params.getAppVersionName()
//
//    override fun getDaId(): String = params.getDaId()
//
//    override fun getDaToken(): String = params.getDaToken()
//
//    override fun getProjectType(): String = params.getProjectType()
//
//    override fun getUserId(): String = params.getUserId()
//
//    override fun getUuid(): String = params.getUuid()
//}