package com.dfl.smartscene.util

import android.os.Handler
import android.os.Looper
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.databinding.BindingAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.lottie.LottieAnimationView
import com.dfl.smartscene.R
import com.dfl.smartscene.bean.main.MeIconBean
import com.dfl.smartscene.bean.main.MySceneBean
import com.dfl.smartscene.bean.main.SceneRecordEntity
import com.dfl.smartscene.room.SceneRecordTable
import com.dfl.smartscene.rv.adapter.IDataAdapter
import com.dfl.smartscene.rv.adapter.ISceneAdapter
import com.dfl.smartscene.soa.DataManager.timeStampFormat
import com.dfl.smartscene.util.ConditionOrActionIconUtils.getActionIcon
import com.dfl.smartscene.util.ConditionOrActionIconUtils.getStatusConditionIcon
import com.dfl.smartscene.util.ConditionOrActionIconUtils.getTriggerConditionIcon
import com.dfl.smartscene.widget.WarpLinearLayout
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import me.jessyan.autosize.utils.AutoSizeUtils
import java.util.*

/**
 * <pre>
 * author : ly-huangas
 * e-mail : <EMAIL>
 * time   : 2022/05/18
 * desc   :
 * version: 1.0
</pre> *
 */
object BindingUtils {

    @BindingAdapter("recyclerView_data")
    @JvmStatic
    fun <T> setData(recyclerView: RecyclerView, data: MutableList<T?>?) {
        if (recyclerView.adapter is IDataAdapter<*>) {
            (recyclerView.adapter as IDataAdapter<T>?)!!.fillData(data)
        }
    }

    @BindingAdapter("viewPager_data")
    @JvmStatic
    fun <T> setData(viewPager: ViewPager2, data: MutableList<T?>?) {
        if (viewPager.adapter is IDataAdapter<*>) {
            (viewPager.adapter as IDataAdapter<T>?)!!.fillData(data)
        }
    }

    @BindingAdapter("scene_lottie_setting")
    @JvmStatic
    fun setGifPlayMode(view: LottieAnimationView, loop: Boolean) {
        if (loop) {
            view.setAnimation(R.raw.scene_create_complete)
            view.playAnimation()
        } else {
            view.cancelAnimation()
            view.clearAnimation()
        }
    }


    /**
     * 我的-条件动作-图标映射
     */
    @BindingAdapter("scene_action_list")
    @JvmStatic
    fun setActionList(warpLinearLayout: WarpLinearLayout, data: MySceneBean?) {
        warpLinearLayout.removeAllViews()
        if (data == null) {
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            val iconList: MutableList<MeIconBean> = ArrayList()
            val (scenarioInfo) = data.scenario
            val detailInfo = scenarioInfo ?: return@launch
            //当前场景动作集合（注--如果场景没有动作，那么这个场景为异常数据）
            val actionList = detailInfo.sequence
            if (actionList == null || actionList.isEmpty()) {
                return@launch
            }
            //添加触发条件图标
            val triggerCondition = detailInfo.edgeCondition
            //添加状态条件图标
            val statusConditions = detailInfo.conditions
            //先根据场景提供的条件动作内容去判断对应的图标显示规则，然后按照规则去添加对应的图标
            //6种规则。1:只有动作<=9;2:条件+动作<=8;3:只有动作且>9;4:条件+动作>8且条件<=4;
            //5：条件+动作>8条件>4,动作<=4;6:条件+动作>8,条件>4,动作>4
            var state = 0 //定义一个规则变量代替对应的标签
            var mIconSize = 0 //记录添加几个图标
            //只有动作的情况
            if (triggerCondition == null && (statusConditions == null || statusConditions.isEmpty())) {
                state = if (actionList.size > 9) 3 else 1 //动作大于9为3反之为1
            } else {
                //注意如果有条件的场景必定有触发条件
                val statusSize = statusConditions?.size ?: 0
                val conditionSize = statusSize + 1
                val actionSize = actionList.size
                if (conditionSize + actionSize <= 8) {
                    state = 2
                }
                if (conditionSize + actionSize > 8) {
                    state = when {
                        conditionSize <= 4 -> {
                            4
                        }
                        actionSize <= 4 -> {
                            5
                        }
                        else -> {
                            6
                        }
                    }
                }
            }
            if (triggerCondition != null) {
                iconList.add(MeIconBean(0, getTriggerConditionIcon(triggerCondition.skillId, triggerCondition)))
                mIconSize++
            }
            if (statusConditions != null && statusConditions.isNotEmpty()) {
                for (condition in statusConditions) {
                    if (state == 6 && mIconSize == 3) { //添加省略号
                        iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                        mIconSize++
                        break
                    }
                    if (state == 5 && mIconSize == 8 - actionList.size - 1) { //添加省略号
                        iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                        mIconSize++
                        break
                    }
                    iconList.add(MeIconBean(0, getStatusConditionIcon(condition.skillId)))
                    mIconSize++
                }
            }
            if (mIconSize != 0) { //添加箭头

                iconList.add(
                    MeIconBean(
                        -2, R.drawable.scene_icon_me_action_link
                    )
                )
                mIconSize++
            }
            for (action in actionList) { //添加动作
                if ((state == 3 || state == 4 || state == 6) && mIconSize == 8) {
                    iconList.add(MeIconBean(-1, R.drawable.scene_icon_me_action_more))
                    break
                }
                val currentAction = action.action
                if (currentAction != null) {
                    iconList.add(MeIconBean(1, getActionIcon(currentAction.skillId)))
                }
                mIconSize++
            }
            Handler(Looper.getMainLooper()).post {
                val mContext = warpLinearLayout.context
                var i = 0
                while (i < iconList.size) {
                    val imageView = ImageView(mContext)
                    val v72 = AutoSizeUtils.dp2px(mContext, 72F)
                    val v48 = AutoSizeUtils.dp2px(mContext, 48F)
                    val v40 = AutoSizeUtils.dp2px(mContext, 40F)
                    imageView.setImageResource(iconList[i].icon!!)
                    if (iconList[i].type == -1) { //更多
                        imageView.layoutParams = LinearLayout.LayoutParams(v40, v72)
                    } else if (iconList[i].type == -2) { //连头
                        imageView.layoutParams = LinearLayout.LayoutParams(v48, v72)
                    } else if (iconList[i].type == 0) {
                        imageView.layoutParams = LinearLayout.LayoutParams(v72, v72)
                        imageView.setPadding(5, 5, 5, 5)
                        imageView.setBackgroundResource(R.drawable.scene_shape_bg_me_action_yuan)
                    } else if (iconList[i].type == 1) {
                        imageView.layoutParams = LinearLayout.LayoutParams(v72, v72)
                        imageView.setPadding(5, 5, 5, 5)
                        imageView.setBackgroundResource(R.drawable.scene_shape_bg_me_action_fang)
                    }
                    warpLinearLayout.addView(imageView)
                    i++
                }

            }
        }
    }


    @BindingAdapter("scene_data")
    @JvmStatic
    fun <T> setData(recyclerView: RecyclerView, data: T) {
        if (recyclerView.adapter is ISceneAdapter<*>) {
            (recyclerView.adapter as ISceneAdapter<T>?)!!.fillScene(data)
        }
    }


    @JvmStatic
    @BindingAdapter("time_stamp_data")
    fun setSceneCardDesc(textView: TextView, timeStamp: Long) {
        val format = timeStampFormat
        textView.text = format.format(Date(timeStamp))
    }

    @JvmStatic
    @BindingAdapter("scene_record_title")
    fun setSceneRecordTitle(textView: TextView, bean: SceneRecordEntity) {
        val res = textView.resources
        var title = bean.sceneName
        if (bean.startSceneResult == SceneRecordTable.VALUE_START_SCENE_RESULT_SUCCESS) {
            title = String.format(
                "%s %s", "$title  | ", res.getString(R.string.scene_text_execution_succeed)
            )
        } else if (bean.startSceneResult == SceneRecordTable.VALUE_START_SCENE_RESULT_FAIL) {
            title = String.format(
                "%s %s", "$title  | ", res.getString(R.string.scene_text_execution_fail)
            )
        }
        textView.text = title
    }

    @JvmStatic
    @BindingAdapter("scene_edit_item_icon")
    fun loadImage(imageView: AppCompatImageView, resId: Int) {
        imageView.setImageResource(resId)
    }
}