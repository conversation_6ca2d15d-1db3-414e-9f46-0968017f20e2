package com.dfl.smartscene.util

import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup

/**
 * 触摸事件拦截器工具类
 * 用于防止触摸事件穿透到下层视图
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */
object TouchEventInterceptor {
    
    /**
     * 为视图设置完全的事件拦截
 * 拦截所有触摸事件，防止穿透到下层视图
     * 
     * @param view 需要拦截事件的视图
     * @param onClickCallback 点击回调，可选
     */
    fun interceptAllTouchEvents(
        view: View, 
        onClickCallback: (() -> Unit)? = null
    ) {
        // 设置基本属性确保视图可以接收事件
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true
        
        // 设置触摸监听器，拦截所有触摸事件
        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时消费事件
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // 抬起时执行点击回调
                    onClickCallback?.invoke()
                    true
                }
                else -> {
                    // 其他事件也消费掉
                    true
                }
            }
        }
        
        // 如果是ViewGroup，还需要拦截子视图的事件分发
        if (view is ViewGroup) {
            view.setOnHierarchyChangeListener(object : ViewGroup.OnHierarchyChangeListener {
                override fun onChildViewAdded(parent: View?, child: View?) {
                    // 新增子视图时也设置事件拦截
                    child?.let { interceptAllTouchEvents(it) }
                }
                
                override fun onChildViewRemoved(parent: View?, child: View?) {
                    // 子视图移除时的处理（如果需要）
                }
            })
        }
    }
    
    /**
     * 移除事件拦截
     * 恢复视图的正常事件处理
     * 
     * @param view 需要恢复事件处理的视图
     */
    fun removeEventInterception(view: View) {
        view.isClickable = false
        view.isFocusable = false
        view.isFocusableInTouchMode = false
        view.setOnTouchListener(null)
        
        if (view is ViewGroup) {
            view.setOnHierarchyChangeListener(null)
        }
    }
    
    /**
     * 为蒙层视图设置专用的事件拦截
     * 专门用于蒙层场景，提供点击蒙层关闭的功能
     * 
     * @param maskView 蒙层视图
     * @param onMaskClickCallback 点击蒙层的回调
     */
    fun setupMaskEventInterception(
        maskView: View,
        onMaskClickCallback: () -> Unit
    ) {
        // 设置蒙层的基本属性
        maskView.isClickable = true
        maskView.isFocusable = true
        maskView.isFocusableInTouchMode = true
        
        // 设置触摸监听器
        maskView.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时就消费事件，防止穿透
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // 抬起时执行蒙层点击回调
                    onMaskClickCallback.invoke()
                    true
                }
                MotionEvent.ACTION_MOVE,
                MotionEvent.ACTION_CANCEL -> {
                    // 移动和取消事件也要消费
                    true
                }
                else -> true
            }
        }
        
        // 同时设置点击监听器作为备用
        maskView.setOnClickListener {
            onMaskClickCallback.invoke()
        }
    }
}