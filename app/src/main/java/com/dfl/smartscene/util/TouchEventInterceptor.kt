package com.dfl.smartscene.util

import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup

/**
 * 触摸事件拦截器工具类
 * 用于防止点击事件穿透，特别适用于蒙层场景
 */
class TouchEventInterceptor {

    /**
     * 拦截所有触摸事件
     * @param view 需要拦截事件的视图
     */
    fun interceptAllTouchEvents(view: View) {
        view.setOnTouchListener { _, _ ->
            // 消费所有触摸事件，防止事件传递
            true
        }
        
        // 确保视图可以接收触摸事件
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true
    }

    /**
     * 移除事件拦截
     * @param view 需要移除拦截的视图
     */
    fun removeEventInterception(view: View) {
        view.setOnTouchListener(null)
        view.setOnClickListener(null)
        view.isClickable = false
        view.isFocusable = false
        view.isFocusableInTouchMode = false
    }

    /**
     * 为蒙层设置事件拦截
     * @param maskView 蒙层视图
     * @param onMaskClick 点击蒙层时的回调
     */
    fun setupMaskEventInterception(maskView: View, onMaskClick: () -> Unit) {
        // 设置触摸监听器，消费所有触摸事件
        maskView.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时消费事件
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // 抬起时触发点击回调
                    onMaskClick.invoke()
                    true
                }
                else -> {
                    // 其他事件也消费掉
                    true
                }
            }
        }
        
        // 设置点击监听器作为备用
        maskView.setOnClickListener {
            onMaskClick.invoke()
        }
        
        // 确保蒙层可以接收事件
        maskView.isClickable = true
        maskView.isFocusable = true
        maskView.isFocusableInTouchMode = true
    }

    /**
     * 为整个视图组设置事件拦截
     * @param viewGroup 需要拦截事件的视图组
     */
    fun interceptViewGroupTouchEvents(viewGroup: ViewGroup) {
        viewGroup.setOnTouchListener { _, _ ->
            // 拦截所有触摸事件
            true
        }
        
        // 设置视图组属性
        viewGroup.isClickable = true
        viewGroup.isFocusable = true
        viewGroup.isFocusableInTouchMode = true
    }
}