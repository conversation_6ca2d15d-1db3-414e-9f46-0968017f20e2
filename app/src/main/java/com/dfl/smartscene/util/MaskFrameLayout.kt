package com.dfl.smartscene.util

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout

/**
 * 专用于蒙层的FrameLayout
 * 简单可靠的事件拦截，防止事件穿透到下层视图
 */
class MaskFrameLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var onMaskClickListener: (() -> Unit)? = null

    init {
        // 设置基本属性确保可以接收点击事件
        isClickable = true
        isFocusable = true
    }

    /**
     * 设置蒙层点击监听器
     */
    fun setOnMaskClickListener(listener: () -> Unit) {
        this.onMaskClickListener = listener
        // 直接使用OnClickListener，简单可靠
        setOnClickListener {
            onMaskClickListener?.invoke()
        }
    }

    /**
     * 拦截所有触摸事件，防止穿透到下层
     */
    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        // 拦截所有触摸事件，不让子视图接收
        return true
    }

    /**
     * 消费所有触摸事件，防止穿透
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 让父类处理点击事件（会触发OnClickListener）
        super.onTouchEvent(event)
        // 消费所有触摸事件，防止穿透到下层视图
        return true
    }
}