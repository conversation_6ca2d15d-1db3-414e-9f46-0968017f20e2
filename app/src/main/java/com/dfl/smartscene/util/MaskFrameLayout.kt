package com.dfl.smartscene.util

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout

/**
 * 专用于蒙层的FrameLayout
 * 拦截并消费所有触摸事件，防止事件穿透到下层视图
 */
class MaskFrameLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    
    private var onMaskClickListener: (() -> Unit)? = null
    
    /**
     * 设置蒙层点击监听器
     */
    fun setOnMaskClickListener(listener: () -> Unit) {
        this.onMaskClickListener = listener
        
        // 设置点击监听器
        setOnClickListener {
            onMaskClickListener?.invoke()
        }
    }
    
    /**
     * 拦截所有触摸事件
     */
    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return true // 拦截所有触摸事件，不让子视图接收
    }
    
    /**
     * 处理所有触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_UP -> {
                // 触摸抬起时触发点击回调
                onMaskClickListener?.invoke()
            }
        }
        return true // 消费所有触摸事件，防止穿透
    }
}