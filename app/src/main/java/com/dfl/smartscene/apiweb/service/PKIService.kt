package com.dfl.smartscene.apiweb.service

import com.dfl.smartscene.apiweb.api.CommunityApi
import com.dfl.smartscene.apiweb.utils.BaseService4PKI
import com.dfl.smartscene.apiweb.utils.ServiceManager
import com.dfl.smartscene.bean.apibase.BaseResponseBeanV
import com.dfl.smartscene.bean.community.request.PkiCertApplyRequest
import com.dfl.smartscene.util.pki.PkiCertApplyResponse
import io.reactivex.Observable

/**
 *Created by 钟文祥 on 2024/9/3.
 *Describer:PKI的请求Service
 */
object PKIService : BaseService4PKI() {
    /****
     * 统一请求方法
     * req: 请求类
     * apiStr：api方法名
     * classOfT：接口最终返回的解密后的业务数据的 类型
     */
    private fun <R, T> postApi(req: R, apiStr: String, classOfT: Class<T>? = null): Observable<T> {
        //1 api
        val api: CommunityApi = ServiceManager.getClientService(CommunityApi::class.java)
        //3 最终加密请求参数
        val reqV = getRequestJsonEncryption(req)
        //4 组建头
        val heardMap = getHeardMap("ly.dfnv.dntc.cert.apply", reqV)
        //5 请求api返回标准数据BaseResponseBean
        val observable: Observable<BaseResponseBeanV> = api.communityApi(reqV, heardMap)
        //6 对标准数据进行解密 得到我们想要的 请求数据
        return initObservableByBaseResponse(apiStr, observable, classOfT)
    }


    /**下载正式证书*/
    fun downloadPkiCert(userP10: String, daId: String): Observable<PkiCertApplyResponse> {
        val req = PkiCertApplyRequest(userP10 = userP10, daId = daId)
        return postApi(req, "downloadPkiCert", PkiCertApplyResponse::class.java);
    }
}