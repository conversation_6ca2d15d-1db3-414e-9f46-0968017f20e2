package com.dfl.smartscene.apiweb.service

import com.dfl.smartscene.apiweb.api.CommunityApi
import com.dfl.smartscene.apiweb.utils.BaseService
import com.dfl.smartscene.apiweb.utils.ServiceManager
import com.dfl.smartscene.bean.apibase.BaseResponseBean
import com.dfl.smartscene.bean.apibase.BaseResponseBeanV
import com.dfl.smartscene.bean.community.reponse.*
import com.dfl.smartscene.bean.community.request.*
import io.reactivex.Observable

/**
 *Created by 钟文祥 on 2023/11/13.
 *Describer:场景社区Service
 */
object CommunityService : BaseService() {

    /****
     * 统一请求方法
     * req: 请求类
     * apiStr：api方法名
     * classOfT：接口最终返回的解密后的业务数据的 类型
     */
    private fun <R, T> postApi(req: R, apiStr: String, classOfT: Class<T>? = null): Observable<T> {
        //1 api
        val api: CommunityApi = ServiceManager.getClientService(CommunityApi::class.java)
        //3 最终加密请求参数
        val reqV = getRequestJsonEncryption(req, apiStr)
        //4 组建头
        val heardMap = getHeardMap("ly.dfnv.sceneengine.community.third.${apiStr}", reqV)
        //5 请求api返回标准数据BaseResponseBean
        val observable: Observable<BaseResponseBeanV> = api.communityApi(reqV, heardMap)
        //6 对标准数据进行解密 得到我们想要的 请求数据
        return initObservableByBaseResponse(apiStr, observable, classOfT)
    }

    /**获取社区场景数据列表*/
    fun getCommunityList(
        mTabType: CommunityTabType, pageNum: Int
    ): Observable<CommunityListResponse> {
        val req = CommunityGetListRequest(pageNum)
        val apiStr = when (mTabType) {
            CommunityTabType.LastNew -> "getlast"
            CommunityTabType.Hot -> "gethot"
            CommunityTabType.Official -> "getofficial"
            CommunityTabType.Special -> "getspecial"
            else -> ""
        }
        return postApi(req, apiStr, CommunityListResponse::class.java);
    }

    /**获取社区场景轮播图*/
    fun getBannerList(): Observable<CommunityListResponse> {
        val req = CommunityGetBannerRequest()
        return postApi(req, "getbanner", CommunityListResponse::class.java);
    }


    /**获社区场景预搜索*/
    fun getCommunityPreSearch(keyword: String): Observable<CommunityPreSearch> {
        val req = CommunityPreSearchRequest(keyword)
        return postApi(req, "preSearch", CommunityPreSearch::class.java)
    }


    /**获社区场景搜索*/
    fun getCommunitySearch(keyword: String, pageNum: Int): Observable<CommunityListResponse> {
        val req = CommunitySearchRequest(keyword, pageNum)
        return postApi(req, "search", CommunityListResponse::class.java)
    }


    /**关注或者取消关注*/
    fun focusOrNoFocus(
        subscribedUserId: String, isSubscribe: Boolean
    ): Observable<BaseResponseBean> {
        val req = CommunityFocusRequest(subscribedUserId, isSubscribe)
        return postApi(req, "focusornofocus")
    }

    /**点赞或者取消点赞*/
    fun likeOrNoLike(communityScenarioId: String, isLike: Boolean): Observable<BaseResponseBean> {
        val req = CommunityLikeRequest(communityScenarioId, isLike)
        return postApi(req, "likeOrNoLike")
    }

    /**下载或者取消下载*/
    fun downloadOrNo(
        communityScenarioId: String, isDownload: Boolean
    ): Observable<BaseResponseBean> {
        val req = CommunityDownloadRequest(communityScenarioId, isDownload)
        return postApi(req, "downloadOrNo")
    }

    /**生成封面图*/
    fun genimg(): Observable<CommunityGenImg> {
        val req = CommunityGenImgRequest()
        return postApi(req, "genimg", CommunityGenImg::class.java)
    }

    /**发布场景*/
    fun upload(
//		scenarioInfo: ScenarioInfoConverter
        req: CommunityUploadRequest
    ): Observable<BaseResponseBean> {
//		val time = System.currentTimeMillis()
//		val req = CommunityUploadRequest("SQ_VEHICLE_${time}", scenarioInfo, time)
        return postApi(req, "upload")
    }

    /**
     * 获取账号信息
     * @param isOfficial 是否官方账号 1是
     * @param pageNum
     * @param visitedUserId 被访问账号
     */
    fun getCommunityUserInfo(
        isOfficial: Int, pageNum: Int, visitedUserId: String
    ): Observable<CommunityUserInfo> {
        val req = CommunityUserInfoRequest(isOfficial, pageNum, visitedUserId)
        return postApi(req, "getPersonal", CommunityUserInfo::class.java)
    }

    /**
     * 获取社区搜索最近搜索
     */
    fun getCommunityRecentSearch(): Observable<CommunityRecentSearchResponse> {
        val req = CommunityRecentSearchRequest()
        return postApi(req, "searchHistory", CommunityRecentSearchResponse::class.java)
    }

    /**
     * 清空社区搜索历史搜索记录
     */
    fun clearCommunityRecentSearch(): Observable<BaseResponseBean> {
        val req = CommunityRecentSearchRequest()
        return postApi(req, "clearsearchHistory", BaseResponseBean::class.java)
    }

    fun deleteCommunityScene(communityScenarioId: String): Observable<BaseResponseBean> {
        val req = CommunityDeleteScene(communityScenarioId)
        return postApi(req, "delete", BaseResponseBean::class.java)
    }
}