package com.dfl.smartscene.apiweb.utils;

/**
 * Created by zwx on 2018/6/5.
 * Describer: API错误信息
 */
public class ApiException extends Exception {
    public String reqURL;
    public int httpCode;
    public String message;
    /** 用来toast信息 */
    public String toastMsg;

    public ApiException(Throwable throwable, int httpCode) {
        super(throwable);
        this.httpCode = httpCode;
        this.reqURL = "";
        this.message = "";
        this.toastMsg = "";
    }

    public ApiException(Throwable throwable, String reqURL, int httpCode,
                        String message, String toastMsg) {
        super(throwable);
        this.reqURL = reqURL;
        this.httpCode = httpCode;
        this.message = message;
        this.toastMsg = toastMsg;
    }

    public String getReqURL() {
        return reqURL;
    }

    public int getHttpCode() {
        return httpCode;
    }

    public String getMessage() {
        return message;
    }

    public String getToastMsg() {
        return toastMsg;
    }

    @Override
    public String toString() {
        return "ApiException{" +
                "reqURL='" + reqURL + '\'' +
                ", httpCode=" + httpCode +
                ", message='" + message + '\'' +
                ", toastMsg='" + toastMsg + '\'' +
                '}';
    }

}


