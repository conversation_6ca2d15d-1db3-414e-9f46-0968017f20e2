package com.dfl.smartscene.apiweb.api

import com.dfl.smartscene.bean.apibase.BaseRequestV
import com.dfl.smartscene.bean.apibase.BaseResponseBeanV
import com.dfl.smartscene.bean.community.reponse.*
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.HeaderMap
import retrofit2.http.POST

/**
 *Created by 钟文祥 on 2023/11/13.
 *Describer: 场景社区api
 */
interface CommunityApi {

    /**统一调用方法*/
    @POST("v3/api")
    fun communityApi(@Body reqV: BaseRequestV, @HeaderMap map: Map<String, String>): Observable<BaseResponseBeanV>

}