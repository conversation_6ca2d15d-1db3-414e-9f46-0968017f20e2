<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_104"
    android:layout_width="match_parent">

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_library_scene_detail_condition_item_title"
        android:includeFontPadding="false"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_library_scene_detail_condition_item_desc"
        android:includeFontPadding="false"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_body"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_library_scene_detail_condition_item_icon"
        android:layout_height="@dimen/y_px_40"
        android:layout_marginRight="@dimen/x_px_10"
        android:layout_width="@dimen/x_px_40"
        android:scaleType="fitXY"
        android:src="@mipmap/icon_item_my_scene_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/textview_library_scene_detail_condition_item_desc"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:background="@color/color_divide"
        android:id="@+id/divide_item_library_scene_detail_condition"
        android:layout_height="@dimen/y_px_1"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>