<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.main.me.scenerecord.SceneRecordViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SmallDialogTheme"
            android:layout_height="1010dp"
            android:layout_gravity="center"
            android:layout_marginTop="38dp">

            <TextView
                android:id="@+id/tv_title"
                style="@style/SmallDialogTitleTheme"
                android:text="@string/scene_text_record_title"
                android:textFontWeight="500"
                android:textSize="@dimen/scene_text_size_h3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--减去日期margin_top-->
            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/sl_record"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:visibility="visible"
                app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
                app:layout_constraintTop_toBottomOf="@+id/tv_title">

                <com.dfl.android.animationlib.scrollView.OverScrollView
                    android:id="@+id/nsv_record"
                    style="@style/OverScrollViewTheme1A.WithScrollBar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_record"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:clipToPadding="false"
                            android:overScrollMode="never"
                            android:scrollbars="vertical" />

                        <TextView
                            android:id="@+id/tv_record_tips"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="60dp"
                            android:gravity="center"
                            android:text="@string/scene_text_record_display_tips"
                            android:textColor="@color/scene_color_text_3"
                            android:textSize="@dimen/scene_text_size_h5"
                            android:visibility="gone" />
                    </LinearLayout>
                </com.dfl.android.animationlib.scrollView.OverScrollView>

                <com.dfl.smartscene.widget.ClassicsFooter4All
                    android:id="@+id/classicsFooter"
                    style="@style/SmartRefreshClassicsTheme.FooterTheme"
                    android:layout_marginTop="60dp"
                    android:layout_marginBottom="24dp"
                    android:gravity="center_horizontal" />
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

            <TextView
                android:id="@+id/tv_empty_msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="202dp"
                android:drawableTop="@drawable/scene_img_me_record_empty_default"
                android:drawablePadding="16dp"
                android:gravity="center"
                android:text="@string/scene_text_common_no_data"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>