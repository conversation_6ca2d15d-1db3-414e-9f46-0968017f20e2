<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_popup_bg"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_all"
        android:layout_width="@dimen/common_width_760dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        >

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_dialog"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/text_view_default_double_dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_height_64dp"
            android:gravity="center"
            android:paddingStart="@dimen/common_width_88dp"
            android:paddingEnd="@dimen/common_width_88dp"
            android:textColor="@color/color_text_first_label"
            android:textSize="@dimen/common_height_28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--添加遮罩-->
        <com.dfl.dflcommonlibs.ui.MaskLayout
            android:id="@+id/frame_layout_button_positive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_height_32dp"
            android:layout_marginEnd="@dimen/common_width_252dp"
            android:layout_marginBottom="@dimen/common_height_40dp"
            android:clickable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_default_double_dialog_message">

            <com.dfl.dflcommonlibs.uimodeutil.UIButton
                android:id="@+id/button_default_double_positive"
                android:layout_width="@dimen/common_width_232dp"
                android:layout_height="@dimen/common_height_96dp"
                android:background="@drawable/bg_button_positive"
                android:clickable="false"
                android:text="确认"
                android:textColor="@color/colorDialogPositiveButton"
                android:textSize="@dimen/common_height_28dp" />
        </com.dfl.dflcommonlibs.ui.MaskLayout>


        <com.dfl.dflcommonlibs.ui.MaskLayout
            android:id="@+id/frame_layout_button_negative"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/common_width_40dp"
            android:layout_marginTop="@dimen/common_height_32dp"
            android:layout_marginEnd="@dimen/common_width_108dp"
            android:layout_marginBottom="@dimen/common_height_40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/frame_layout_button_positive"
            app:layout_constraintTop_toBottomOf="@id/text_view_default_double_dialog_message">

            <com.dfl.dflcommonlibs.uimodeutil.UIButton
                android:id="@+id/button_default_double_negative"
                android:layout_width="@dimen/common_width_232dp"
                android:layout_height="@dimen/common_height_96dp"
                android:background="@drawable/bg_button_negative_new"
                android:text="取消"
                android:textColor="@color/color_dialog_btn_text_cancel"
                android:textSize="@dimen/common_height_28dp" />

        </com.dfl.dflcommonlibs.ui.MaskLayout>

        <Button
            android:id="@+id/button_default_double_close"
            android:layout_width="@dimen/common_width_40dp"
            android:layout_height="@dimen/common_height_40dp"
            android:layout_marginStart="@dimen/common_height_24dp"
            android:layout_marginTop="@dimen/common_height_24dp"
            android:background="@drawable/bg_button_left_top_close"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>