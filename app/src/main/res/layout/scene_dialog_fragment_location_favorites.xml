<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SmallDialogTheme"
            android:layout_width="1520dp"
            android:layout_height="1000dp"
            android:layout_marginTop="76dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <TextView
                android:id="@+id/tv_title"
                style="@style/SmallDialogTitleTheme"
                android:text="@string/scene_text_favorites"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.android.animationlib.scrollView.FadeRecyclerView
                android:id="@+id/rv_location"
                style="@style/FadeRecyclerViewTheme3B"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="80dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/scene_shape_community_white_radius_12"
                android:maxHeight="620dp"
                android:minHeight="342dp"
                android:overScrollMode="never"
                app:layout_constraintBottom_toTopOf="@id/view_empty"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:layout_constraintVertical_chainStyle="packed" />

            <include
                android:id="@+id/view_empty"
                layout="@layout/scene_layout_comment_empty"
                android:layout_width="0dp"
                android:layout_height="214dp"
                android:layout_marginBottom="56dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_location" />

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>