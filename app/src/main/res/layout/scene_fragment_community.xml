<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/sl_update"
            style="@style/SmartRefreshLayoutTheme"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent">

            <com.scwang.smartrefresh.layout.header.ClassicsHeader
                android:id="@+id/header"
                style="@style/SmartRefreshClassicsTheme"/>

            <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                android:id="@+id/classics_footer"
                style="@style/SmartRefreshClassicsTheme.FooterTheme" />

            <!-- 直接显示RecyclerView列表，添加顶部间距确保在tab下面 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_community"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/px_64"
                android:background="@color/transparent"
                android:overScrollMode="never"
                android:clipToPadding="false"/>

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <!--父布局全屏显示，网络请求页布局根据全屏设置间距，不需要再加上marginTop-->
        <include
            layout="@layout/scene_layout_comment_req_res"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

</layout>