<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/sl_update"
            style="@style/SmartRefreshLayoutTheme"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            app:srlEnableHeaderTranslationContent="false">

            <com.dfl.smartscene.widget.ClassicsHeader4Banner
                android:id="@+id/header"
                style="@style/SmartRefreshClassicsTheme_4Banner"
                android:layout_marginBottom="620dp" />
            <!--            android:layout_marginBottom="620dp" -->
            <!--            <com.scwang.smartrefresh.layout.header.ClassicsHeader-->
            <!--                android:id="@+id/header"-->
            <!--                app:srlAccentColor="@color/scene_color_bg_1"-->
            <!--                style="@style/SmartRefreshClassicsTheme" />-->

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- 和androidx.constraintlayout.widget.ConstraintLayout不同-->
                <androidx.coordinatorlayout.widget.CoordinatorLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent">

                    <com.google.android.material.appbar.AppBarLayout
                        android:id="@+id/view_AppBarLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        app:elevation="0dp">
                        <!--    AppBarLayout已经有默认app:layout_behavior属性
                                这是一个已实现 Behavior的LinearLayout-->

                        <!-- 用来给tablayout上面占位的-->
                        <com.google.android.material.appbar.CollapsingToolbarLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_scrollFlags="scroll|exitUntilCollapsed">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingBottom="10dp">
                                <!--    app:layout_scrollFlags="scroll|enterAlwaysCollapsed"-->

                                <com.dfl.smartscene.widget.bannerview.BannerViewPager
                                    android:id="@+id/bannerView"
                                    android:layout_width="2560dp"
                                    android:layout_height="850dp"
                                    android:background="@color/transparent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <!--                                <com.dfl.smartscene.widget.bannerview.shape.MultiIndicatorView-->
                                <!--                                    android:id="@+id/indicator_view"-->
                                <!--                                    android:layout_width="300dp"-->
                                <!--                                    android:layout_height="20dp"-->
                                <!--                                    android:layout_marginBottom="40dp"-->
                                <!--                                    app:layout_constraintBottom_toBottomOf="@id/bannerView"-->
                                <!--                                    app:layout_constraintEnd_toEndOf="@id/bannerView"-->
                                <!--                                    app:layout_constraintStart_toStartOf="@id/bannerView" />-->


                                <com.dfl.android.animationlib.ScaleImageButton
                                    android:id="@+id/tv_downloads"
                                    style="@style/SecondaryBigBtnColorStyle.CardWithIconStyle"
                                    android:layout_marginStart="124dp"
                                    android:layout_marginBottom="237dp"
                                    app:backgroundBaseColor="@color/scene_color_day_btn2_night_bg2"
                                    app:imgSrc="@drawable/scene_icon_common_download_text1"
                                    app:imgSrcClicked="@drawable/scene_icon_common_download_text1"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    tools:text="100" />

                                <com.dfl.android.animationlib.ScaleImageButton
                                    android:id="@+id/tv_likes"
                                    style="@style/SecondaryBigBtnColorStyle.CardWithIconStyle"
                                    android:layout_marginStart="30dp"
                                    app:backgroundBaseColor="@color/scene_color_day_btn2_night_bg2"
                                    app:imgAutoPlay="false"
                                    app:imgIsAnimation="true"
                                    app:imgSrc="@raw/scene_likes_day"
                                    app:layout_constraintBottom_toBottomOf="@id/tv_downloads"
                                    app:layout_constraintStart_toEndOf="@id/tv_downloads"
                                    app:layout_constraintTop_toTopOf="@id/tv_downloads"
                                    tools:text="100" />

                                <TextView
                                    android:id="@+id/tv_scene_desc"
                                    android:layout_width="510dp"
                                    android:layout_height="48dp"
                                    android:layout_marginBottom="60dp"
                                    android:ellipsize="end"
                                    android:gravity="start|center_vertical"
                                    android:lines="1"
                                    android:textColor="@color/scene_color_text_3"
                                    android:textSize="@dimen/scene_text_size_h5"
                                    app:layout_constraintBottom_toTopOf="@id/tv_downloads"
                                    app:layout_constraintStart_toStartOf="@id/tv_downloads"
                                    tools:text="开启导航至公司，快乐放音频节日快快乐放音频节日快" />

                                <TextView
                                    android:id="@+id/tv_scene_name"
                                    android:layout_width="510dp"
                                    android:layout_height="86dp"
                                    android:layout_marginBottom="8dp"
                                    android:ellipsize="end"
                                    android:gravity="start|center_vertical"
                                    android:lines="1"
                                    android:textColor="@color/scene_color_text_1"
                                    android:textFontWeight="500"
                                    android:textSize="@dimen/scene_text_size_h1"
                                    app:layout_constraintBottom_toTopOf="@id/tv_scene_desc"
                                    app:layout_constraintStart_toStartOf="@id/tv_downloads"
                                    tools:text="奉旨上班最长十个字奉旨上班最长十个字" />

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <!--  Toolbar的高度与固定的高度一样-->
                            <androidx.appcompat.widget.Toolbar
                                android:layout_width="match_parent"
                                android:layout_height="276dp" />
                        </com.google.android.material.appbar.CollapsingToolbarLayout>

                        <com.dfl.android.animationlib.CustomTabLayout
                            android:id="@+id/tabLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="124dp"
                            android:layout_marginTop="20dp"
                            android:background="@color/transparent"
                            app:layout_type="2"
                            app:select_title_color="@color/scene_color_text_1"
                            app:title_bottom_margin="48dp"
                            app:unselect_title_color="@color/scene_color_text_3" />

                    </com.google.android.material.appbar.AppBarLayout>

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/vp_community"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="56dp"
                        android:background="@color/transparent"
                        android:orientation="horizontal"
                        android:overScrollMode="never"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

                </androidx.coordinatorlayout.widget.CoordinatorLayout>

                <!-- 顶部固定 Toolbar的高度与固定的高度一样-->
                <View
                    android:id="@+id/view_top_bg"
                    android:layout_width="match_parent"
                    android:layout_height="276dp"
                    android:alpha="0"
                    android:background="@color/scene_color_bg_1" />


            </RelativeLayout>
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <!--父布局全屏显示，网络请求页布局根据全屏设置间距，不需要再加上marginTop-->
        <include
            layout="@layout/scene_layout_comment_req_res"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>


</layout>