<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.dfl.smartscene.bean.main.SceneCardBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/px_584"
        android:layout_height="@dimen/px_282"
        android:background="@drawable/scene_shape_me_bg_rect">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_discover_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            app:srcCompat="@drawable/scene_img_discover_txt_bg" />

        <TextView
            android:id="@+id/tv_scene_icon"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/px_40"
            android:layout_marginEnd="25dp"
            android:layout_marginBottom="@dimen/px_16"
            android:textColor="@color/scene_color_text_normal_highlight_primary"
            android:textSize="@dimen/px_32"
            app:layout_constraintBottom_toTopOf="@+id/tv_scene_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_scene_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/px_24"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/scene_color_text_normal_highlight_primary"
            android:textSize="@dimen/px_28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tv_scene_icon"
            app:layout_constraintStart_toStartOf="@+id/tv_scene_icon" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>