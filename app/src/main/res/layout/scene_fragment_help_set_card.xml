<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/scene_shape_common_medal_bg_radius_large">

        <ImageView
            android:id="@+id/iv_set_card"
            android:layout_width="1754dp"
            android:layout_height="458dp"
            android:layout_marginLeft="123dp"
            android:layout_marginTop="96dp"
            android:background="@drawable/scene_img_common_help_set_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="96dp"
            android:text="@string/scene_text_help_set_card_desc"
            android:textColor="@color/scene_color_text_1"
            android:textSize="36dp"
            android:textStyle="normal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_set_card" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>