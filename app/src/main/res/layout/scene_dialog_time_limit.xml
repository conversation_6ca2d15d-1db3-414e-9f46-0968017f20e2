<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_dialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context="com.dfl.smartscene.ui.overlay.time.timelimit.TimeLimitDialog">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/MiddleDialogThemeP42R"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_limit_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="HardcodedText"
            tools:text="时间区间" />

        <TextView
            android:id="@+id/tv_time_condition_tips"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:text="@string/scene_text_time_condition_tip"
            android:textColor="@color/scene_mean_color_error"
            android:textSize="@dimen/scene_text_size_h5"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_limit_title" />

        <View
            android:id="@+id/view"
            style="@style/WheelViewSelectViewTheme"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="@dimen/px_41"
            app:layout_constraintBottom_toBottomOf="@+id/wv_first_hour_options"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/wv_first_hour_options" />
        <!--开始时间-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_first_hour_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_60"
            android:layout_height="@dimen/px_300"
            android:layout_marginStart="@dimen/px_116"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_time_condition_tips" />
        <!--冒号-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_52"
            android:gravity="center"
            android:text="@string/scene_text_common_colon"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintBottom_toBottomOf="@+id/wv_first_hour_options"
            app:layout_constraintEnd_toStartOf="@id/wv_first_minute_options"
            app:layout_constraintStart_toEndOf="@+id/wv_first_hour_options"
            app:layout_constraintTop_toTopOf="@id/wv_first_hour_options" />
        <!--开始分钟-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_first_minute_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_60"
            android:layout_height="@dimen/px_300"
            android:layout_marginStart="@dimen/px_326"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_time_condition_tips" />
        <!--分割线-->
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:scaleType="center"
            android:src="@drawable/scene_shape_time_limit_line"
            app:layout_constraintBottom_toBottomOf="@id/wv_first_hour_options"
            app:layout_constraintEnd_toEndOf="@id/wv_second_minute_options"
            app:layout_constraintStart_toStartOf="@id/wv_first_hour_options"
            app:layout_constraintTop_toTopOf="@id/wv_first_hour_options"
            tools:ignore="ContentDescription" />
        <!--次日-->
        <TextView
            android:id="@+id/tv_next_day"
            android:layout_width="75dp"
            android:layout_height="40dp"
            android:layout_marginStart="@dimen/px_553"
            android:background="@drawable/scene_shape_tag_tab_bg_selected_r12"
            android:gravity="center"
            android:text="@string/scene_text_condition_morrow"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h7"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/wv_first_hour_options"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/wv_first_hour_options"
            tools:text="@string/scene_text_condition_morrow" />
        <!--结束时间-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_second_hour_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_60"
            android:layout_height="@dimen/px_300"
            android:layout_marginStart="@dimen/px_640"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_time_condition_tips" />
        <!--冒号-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_52"
            android:gravity="center"
            android:text="@string/scene_text_common_colon"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintBottom_toBottomOf="@+id/wv_second_hour_options"
            app:layout_constraintEnd_toStartOf="@id/wv_second_minute_options"
            app:layout_constraintStart_toEndOf="@+id/wv_second_hour_options"
            app:layout_constraintTop_toTopOf="@id/wv_second_hour_options" />
        <!--结束分钟-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wv_second_minute_options"
            style="@style/WheelViewFiveItemStyle"
            android:layout_width="@dimen/px_60"
            android:layout_height="@dimen/px_300"
            android:layout_marginStart="@dimen/px_850"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_time_condition_tips" />

        <LinearLayout
            android:layout_marginTop="@dimen/px_22"
            android:id="@+id/include_dialog_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.dfl.android.animationlib.ScaleImageButton
                android:id="@+id/btn_primary"
                style="@style/PrimaryBigBtnColorStyle"
                android:layout_width="@dimen/px_424"
                android:layout_height="@dimen/px_76"
                app:backgroundCornerDegree="12"
                app:text="@string/scene_button_common_sure" />

            <com.dfl.android.animationlib.ScaleImageButton
                android:id="@+id/btn_normal"
                style="@style/SecondaryBigBtnColorStyle"
                android:layout_width="@dimen/px_424"
                android:layout_height="@dimen/px_76"
                app:backgroundCornerDegree="12"
                android:layout_marginStart="@dimen/px_32"
                app:text="@string/scene_text_common_cancel" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>
