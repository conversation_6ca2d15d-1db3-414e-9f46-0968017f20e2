<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="android.widget.FrameLayout">

    <RelativeLayout
        android:id="@+id/rl_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.dfl.dflcommonlibs.uimodeutil.UIEditText
            android:id="@+id/edittext_layout_custom_text_input"
            android:layout_width="@dimen/x_px_522"
            android:layout_height="@dimen/y_px_72"
            android:textColor="@color/color_text_first_label"
            android:textSize="@dimen/font_size_28"
            android:layout_centerInParent="true"
            android:textColorHint="@color/color_text_tertiary_label"
            android:paddingStart="@dimen/x_px_20"
            android:background="@drawable/bg_text_picker"
            android:layout_gravity="center"/>
    </RelativeLayout>

</merge>