<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/BigDialogTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 水平布局容器 -->
            <LinearLayout
                android:id="@+id/ll_main_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!-- 左侧垂直Tab区域 -->
                <FrameLayout
                    android:id="@+id/fl_help_tab"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <com.dfl.smartscene.widget.VerticalTabLayout
                        android:id="@+id/help_tabs"
                        android:layout_width="@dimen/px_343"
                        android:layout_height="@dimen/px_448"
                        android:layout_marginStart="@dimen/px_40"
                        android:layout_marginEnd="@dimen/px_40"
                        android:layout_marginTop="@dimen/px_68"
                        android:layout_marginBottom="@dimen/px_76"/>
                </FrameLayout>

                <!-- 右侧ViewPager区域 -->
                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/view_pager_help"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:foregroundGravity="center"
                    android:orientation="vertical" />

            </LinearLayout>

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_help_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>