<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--设置顶部间距，为居中的基础上+76（顶部栏框盖度）-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_dialog_fragment_default"
            style="@style/SmallDialogThemeP42R"
            android:paddingHorizontal="@dimen/px_64"
            android:layout_gravity="center">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginStart="36dp"
                android:layout_marginTop="36dp"
                android:contentDescription="@string/scene_text_common_close"
                android:src="@drawable/scene_icon_common_close"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/SmallDialogTitleThemeP42R"
                app:layout_constraintBottom_toTopOf="@+id/tv_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="spread_inside"
                tools:text="确定删除该场景？" />

            <View
                android:id="@+id/v_divider"
                android:layout_width="540dp"
                android:layout_height="1dp"
                android:background="#33D8D8D8"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                app:layout_constraintTop_toTopOf="parent"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h3"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/px_112"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="确定删除该场景？" />

            <TextView
                android:id="@+id/tv_sub_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:textColor="@color/scene_color_text_4"
                android:textSize="20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_content"
                tools:text="补充说明文本" />

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>