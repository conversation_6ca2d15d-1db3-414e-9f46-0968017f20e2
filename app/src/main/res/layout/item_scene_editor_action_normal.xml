<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraint_layout_item"
    android:background="@drawable/bg_scene_editor_action_default"
    android:layout_height="@dimen/y_px_167"
    android:layout_width="@dimen/x_px_320"
    android:tag="background:drawable:bg_scene_editor_action_default">

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/textview_item_scene_editor_order"
        android:includeFontPadding="false"
        android:layout_height="@dimen/y_px_60"
        android:layout_marginLeft="@dimen/x_px_24"
        android:layout_marginTop="@dimen/y_px_10"
        android:layout_width="wrap_content"
        android:maxLines="1"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_40"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/textview_item_scene_editor_title"
        android:includeFontPadding="false"
        android:layout_height="@dimen/y_px_42"
        android:layout_marginLeft="@dimen/x_px_23"
        android:layout_marginTop="@dimen/y_px_72"
        android:layout_width="@dimen/x_px_210"
        android:maxLines="1"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_body"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/textview_item_scene_editor_desc"
        android:includeFontPadding="false"
        android:layout_height="@dimen/y_px_33"
        android:layout_marginLeft="@dimen/x_px_23"
        android:layout_marginTop="@dimen/y_px_118"
        android:layout_width="@dimen/x_px_210"
        android:maxLines="1"
        android:textColor="@color/color_text_selected"
        android:textSize="@dimen/y_px_text_subbody"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imagebutton_item_scene_editor_delete"
        android:layout_height="@dimen/y_px_72"
        android:layout_width="@dimen/x_px_72"
        android:scaleType="fitXY"
        android:src="@drawable/icon_scene_editor_operation_delete"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>