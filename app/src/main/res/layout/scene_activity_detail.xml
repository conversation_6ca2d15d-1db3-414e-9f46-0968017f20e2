<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ui.main.discover.detail.SceneDetailActivity">

    <data>

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.main.discover.detail.SceneDetailViewModel" />

        <import type="android.view.View" />

        <import type="com.dfl.android.common.util.TimeUtils" />

        <import type="com.dfl.android.common.util.NumberToCNUtil" />

        <variable
            name="item"
            type="com.dfl.smartscene.bean.community.reponse.CommunitySceneInfo" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/scene_height_top_status_bar">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_back_detail"
            android:layout_width="132dp"
            android:layout_height="144dp"
            android:paddingStart="76dp"
            android:scaleType="center"
            android:src="@drawable/scene_icon_common_arrow_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="144dp"
            android:layout_marginStart="28dp"
            android:gravity="center"
            android:text="@string/scene_text_common_detail"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintStart_toEndOf="@id/btn_back_detail"
            app:layout_constraintTop_toTopOf="@id/btn_back_detail" />


        <com.dfl.android.animationlib.scrollView.OverScrollView
            style="@style/OverScrollViewTheme1A"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <LinearLayout
                android:id="@+id/csl_edit_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="@dimen/px_56">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/csl_detail_top"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tv_scene_name"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/scene_height_text_size_h3"
                        android:layout_marginStart="76dp"
                        android:text="@{vm.sceneDetailLiveData.scenarioInfo.scenarioName}"
                        android:textColor="@color/scene_color_text_1"
                        android:textFontWeight="500"
                        android:textSize="@dimen/scene_text_size_h3"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="上班模式" />

                    <!--右侧发布者-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_community_from"
                        android:layout_width="784dp"
                        android:layout_height="174dp"
                        android:layout_marginEnd="76dp"
                        android:visibility="@{item != null ? View.VISIBLE : View.GONE}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_scene_name">

                        <View
                            android:id="@+id/cl_community_from_white_view"
                            android:layout_width="0dp"
                            android:layout_height="120dp"
                            android:background="@drawable/scene_shape_community_subser_bg"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/scene_text_community_from"
                            android:textColor="@color/scene_color_text_3"
                            android:textSize="@dimen/scene_text_size_h6"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_community_upload_times"
                            android:layout_width="wrap_content"
                            android:layout_height="35dp"
                            android:text='@{TimeUtils.mills2HideCurYearString(item.uploadTimestamp)}'
                            android:textColor="@color/scene_color_text_3"
                            android:textSize="@dimen/scene_text_size_h6"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="9月11日上传" />

                        <com.dfl.smartscene.widget.CornerImageView
                            android:id="@+id/iv_community_user_heard"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_marginStart="48dp"
                            android:background="@color/transparent"
                            app:layout_constraintBottom_toBottomOf="@id/cl_community_from_white_view"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@id/cl_community_from_white_view" />
                        <!--关注按钮-->
                        <com.dfl.android.animationlib.ScaleImageButton
                            android:id="@+id/tv_community_subscribe"
                            style="@style/PrimaryBigBtnColorStyle.SubScribeBtnStyle"
                            android:layout_marginEnd="30dp"
                            app:layout_constraintBottom_toBottomOf="@id/cl_community_from_white_view"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/cl_community_from_white_view" />
                        <!--已关注按钮-->
                        <com.dfl.android.animationlib.ScaleImageButton
                            android:id="@+id/tv_community_subscribed"
                            style="@style/SecondaryBigBtnColorStyle.SubScribedBtnStyle"
                            android:layout_marginEnd="30dp"
                            android:visibility="invisible"
                            app:layout_constraintBottom_toBottomOf="@id/cl_community_from_white_view"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/cl_community_from_white_view" />
                        <!--<TextView-->
                        <!--    android:id="@+id/tv_community_subscribe"-->
                        <!--    android:layout_width="190dp"-->
                        <!--    android:layout_height="64dp"-->
                        <!--    android:layout_marginEnd="30dp"-->
                        <!--    android:background="@drawable/scene_shape_community_subscribed"-->
                        <!--    android:gravity="center"-->
                        <!--    android:text="@string/scene_text_user_center_subscribed"-->
                        <!--    android:textColor="@color/scene_color_text_1"-->
                        <!--    android:textSize="@dimen/scene_text_size_h5"-->
                        <!--    app:layout_constraintBottom_toBottomOf="@id/cl_community_from_white_view"-->
                        <!--    app:layout_constraintEnd_toEndOf="parent"-->
                        <!--    app:layout_constraintTop_toTopOf="@id/cl_community_from_white_view"-->
                        <!--    tools:ignore="SpUsage" />-->

                        <TextView
                            android:id="@+id/tv_community_user_name"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_marginStart="20dp"
                            android:ellipsize="end"
                            android:gravity="start|center_vertical"
                            android:lines="1"
                            android:text="@{item.userName}"
                            android:textColor="@color/scene_color_text_3"
                            android:textSize="@dimen/scene_text_size_h6"
                            app:layout_constraintBottom_toBottomOf="@id/cl_community_from_white_view"
                            app:layout_constraintEnd_toStartOf="@id/tv_community_subscribe"
                            app:layout_constraintStart_toEndOf="@id/iv_community_user_heard"
                            app:layout_constraintTop_toTopOf="@id/cl_community_from_white_view"
                            tools:text="智慧场景助手" />


                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <!--不再关注菜单添加elevation设置层次优先级，outlineProvider=null不显示阴影-->
                    <LinearLayout
                        android:id="@+id/view_pop"
                        android:layout_width="313dp"
                        android:layout_height="258dp"
                        android:background="@drawable/scene_shape_community_car_bg_1_r24"
                        android:elevation="1dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:outlineProvider="none"
                        android:paddingHorizontal="32dp"
                        android:paddingVertical="20dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/cl_community_from"
                        app:layout_constraintTop_toBottomOf="@id/cl_community_from">

                        <TextView
                            android:id="@+id/tv_no_longer_sub"
                            android:layout_width="match_parent"
                            android:layout_height="108dp"
                            android:gravity="center"
                            android:text="@string/scene_text_community_no_longer_subscribes"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            tools:ignore="SpUsage" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:background="@color/scene_color_divider" />

                        <TextView
                            android:id="@+id/tv_no_longer_sub_cancel"
                            android:layout_width="match_parent"
                            android:layout_height="108dp"
                            android:gravity="center"
                            android:text="@string/scene_text_common_cancel"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            tools:ignore="SpUsage" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_detail_scene_desc"
                        android:layout_width="1160dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="18dp"
                        android:layout_marginEnd="100dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="@{vm.sceneDetailLiveData.scenarioInfo.scenarioDesc}"
                        android:textColor="@color/scene_color_text_3"
                        android:textFontWeight="400"
                        android:textSize="@dimen/scene_text_size_h5"
                        app:layout_constraintBottom_toBottomOf="@id/cl_community_from"
                        app:layout_constraintStart_toStartOf="@+id/tv_scene_name"
                        app:layout_constraintTop_toBottomOf="@+id/tv_scene_name"
                        tools:text="日常导航回家路上开启音乐并打开氛围灯呼吸日常导航回家路上开启音乐并打开氛围灯呼吸日常导航回家路上开启音乐并打开氛围灯呼吸日常导航回家" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="118dp"
                        app:layout_constraintTop_toBottomOf="@+id/tv_detail_scene_desc">

                        <TextView
                            android:id="@+id/tv_detail_scene_condition_name"
                            android:layout_width="wrap_content"
                            android:layout_height="54dp"
                            android:layout_marginStart="76dp"
                            android:gravity="center"
                            android:text="@string/scene_text_edit_meet_all_conditions"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                            android:id="@+id/rv_detail_scene_condition"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="50dp"
                            android:layout_marginTop="24dp"
                            android:minHeight="120dp"
                            android:overScrollMode="never"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_detail_scene_condition_name"
                            app:spanCount="3" />

                        <TextView
                            android:id="@+id/tv_detail_scene_action_name"
                            android:layout_width="wrap_content"
                            android:layout_height="54dp"
                            android:layout_marginStart="76dp"
                            android:layout_marginTop="32dp"
                            android:gravity="center"
                            android:text="@string/scene_text_edit_scene_action"
                            android:textColor="@color/scene_color_text_1"
                            android:textSize="@dimen/scene_text_size_h4"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/rv_detail_scene_condition" />

                        <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                            android:id="@+id/rv_detail_scene_action"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="50dp"
                            android:layout_marginTop="24dp"
                            android:minHeight="320dp"
                            android:overScrollMode="never"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_detail_scene_action_name"
                            app:spanCount="7" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 使用模板和试用-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/csl_detail_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_24"
                    android:visibility="visible">

                    <com.dfl.android.animationlib.ScaleImageButton
                        android:id="@+id/btn_use_template_detail"
                        style="@style/PrimaryBigBtnColorStyle"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/btn_try_run_detail"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:text="@string/scene_button_created_using_this_template" />

                    <com.dfl.android.animationlib.ScaleImageButton
                        android:id="@+id/btn_try_run_detail"
                        style="@style/SecondaryBigBtnColorStyle"
                        android:layout_marginStart="84dp"
                        android:enabled="@{vm.tryStartSceneReadyLiveData}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/btn_use_template_detail"
                        app:layout_constraintTop_toTopOf="parent"
                        app:text="@string/scene_text_scene_detail_try" />

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/lottie_detail_try_run"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@+id/btn_try_run_detail"
                        app:layout_constraintEnd_toEndOf="@+id/btn_try_run_detail"
                        app:layout_constraintStart_toStartOf="@+id/btn_try_run_detail"
                        app:layout_constraintTop_toTopOf="@+id/btn_try_run_detail"
                        app:lottie_loop="true" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="204dp"
                    android:visibility="@{item != null ? View.VISIBLE : View.GONE,default=gone}" />

            </LinearLayout>
        </com.dfl.android.animationlib.scrollView.OverScrollView>

        <View
            android:id="@+id/view_bottom"
            android:layout_width="0dp"
            android:layout_height="204dp"
            android:background="@drawable/scene_shape_community_bottom_bg"
            android:visibility="@{item != null ? View.VISIBLE : View.GONE,default=gone}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_community_down_scene"
            style="@style/PrimaryBigBtnColorStyle.WithIconStyle"
            android:visibility="@{item != null ? View.VISIBLE : View.GONE,default=gone}"
            app:imgSrc="@drawable/scene_icon_common_download_white"
            app:imgSrcClicked="@drawable/scene_icon_common_download_white"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toStartOf="@id/btn_community_likes"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@id/view_bottom"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            app:text="@string/scene_text_community_down" />

        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_community_likes"
            style="@style/SecondaryBigBtnColorStyle.WithIconStyle"
            android:layout_marginStart="84dp"
            android:visibility="@{item != null ? View.VISIBLE : View.GONE,default=gone}"
            app:imgAutoPlay="false"
            app:imgIsAnimation="true"
            app:imgSrc="@raw/scene_likes_day"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="@id/view_bottom"
            app:layout_constraintStart_toEndOf="@id/btn_community_down_scene"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            app:text="@{item.isLike? @string/scene_text_community_likesed : @string/scene_text_community_likes}"
            app:textColor="@{item.isLike()? @color/scene_primary_color_highlight : @color/scene_color_text_1}"
            tools:text="点赞" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
