<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="#eee"
    tools:paddingBottom="20dp"
    tools:paddingTop="20dp"
    tools:parentTag="android.widget.RelativeLayout">

    <ImageView
        android:id="@+id/srl_classics_arrow"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="20dp"
        android:layout_marginRight="20dp"
        android:layout_toStartOf="@+id/srl_classics_center"
        android:layout_toLeftOf="@+id/srl_classics_center"
        android:contentDescription="@android:string/untitled" />

    <com.dfl.android.animationlib.LoadingImageView
        android:id="@+id/srl_classics_progress"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="20dp"
        android:layout_marginRight="20dp"
        android:layout_toStartOf="@+id/srl_classics_center"
        android:layout_toLeftOf="@+id/srl_classics_center"
        android:contentDescription="@android:string/untitled"
        app:lottieSrc="@raw/lk1a_48loading_light"
        app:lottieSrcNight="@raw/lk1a_48loading_dark" />

    <LinearLayout
        android:id="@+id/srl_classics_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/srl_classics_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/srl_header_pulling"
            android:textColor="#666666"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/srl_classics_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/srl_header_update"
            android:textColor="#7c7c7c"
            android:textSize="12sp" />
    </LinearLayout>

</merge>