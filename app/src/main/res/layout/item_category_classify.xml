<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y_px_104">

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_base_category_item_classify"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:ellipsize="end"
        android:gravity="left|center_vertical"
        android:maxLines="1"
        android:paddingLeft="@dimen/x_px_32"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>