<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.dfl.smartscene.bean.action.CheckListBean" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="654dp"
        android:layout_height="176dp"
        android:layout_margin="14dp"
        android:background="@drawable/scene_shape_common_bg_day_bg2_night_medal_component_r24">


        <TextView
            android:id="@+id/tv_check_name"
            android:layout_width="0dp"
            android:layout_height="54dp"
            android:layout_marginHorizontal="48dp"
            android:gravity="center_vertical"
            android:text="@{bean.content}"
            android:textColor="@{bean.isCheck?@color/scene_primary_color_highlight:@color/scene_color_text_1, default = @color/scene_color_text_1 }"
            android:textSize="@dimen/scene_text_size_h4"
            app:layout_constraintBottom_toTopOf="@id/tv_check_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="浙江省杭州市上城区" />

        <TextView
            android:id="@+id/tv_check_desc"
            android:layout_width="0dp"
            android:layout_height="42dp"
            android:layout_marginTop="2dp"
            android:text="@{bean.desc}"
            android:textColor="@{bean.isCheck?@color/scene_primary_color_highlight:@color/scene_color_text_3, default = @color/scene_color_text_3 }"
            android:textSize="@dimen/scene_text_size_h6"
            android:visibility='@{bean.desc.equals("")?View.GONE:View.VISIBLE, default = gone}'
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tv_check_name"
            app:layout_constraintStart_toStartOf="@id/tv_check_name"
            app:layout_constraintTop_toBottomOf="@id/tv_check_name"
            tools:text="宁夏回族自治区吴忠市同心路" />

        <View
            android:id="@+id/view_more"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_margin="16dp"
            android:background="@drawable/scene_icon_radio_check_more"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>