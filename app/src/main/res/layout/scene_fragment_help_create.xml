<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/px_61"
        android:layout_gravity="center"
        android:background="@drawable/scene_shape_common_medal_bg_radius_large">



        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_help_create_left_part"
            android:layout_width="@dimen/px_794"
            android:layout_height="@dimen/px_396"
            android:background="@drawable/scene_shape_help_create_part_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <!--左半部分-->
            <TextView
                android:id="@+id/tv_help_dialog_perform_subtitle1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_46"
                android:layout_marginLeft="@dimen/px_36"
                android:layout_marginTop="@dimen/px_36"
                android:text="@string/scene_text_help_create_step_title_1"
                android:textFontWeight="400"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_p42r_h3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_sequence1_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle1"
                app:layout_constraintTop_toBottomOf="@+id/tv_help_dialog_perform_subtitle1" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content1_1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:text="@string/scene_text_help_create_content1_1"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence1_left"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence1_left" />

            <View
                android:id="@+id/v_help_create_dash1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_20"
                android:background="@drawable/scene_shape_help_dash"
                app:layout_constraintEnd_toEndOf="@+id/tv_sequence1_left"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence1_left"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence1_left" />

            <TextView
                android:id="@+id/tv_sequence2_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle1"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence1_left" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content1_2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:gravity="center_vertical"
                android:text="@string/scene_text_help_create_content1_2"
                app:layout_constraintBottom_toBottomOf="@+id/tv_sequence2_left"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence2_left"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence2_left" />

            <View
                android:id="@+id/imageView4"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_20"
                android:background="@drawable/scene_shape_help_dash"
                app:layout_constraintEnd_toEndOf="@+id/tv_sequence2_left"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence2_left"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence2_left" />

            <TextView
                android:id="@+id/tv_sequence3_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle1"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence2_left" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content1_3"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:gravity="center_vertical"
                android:text="@string/scene_text_help_create_content1_3"
                app:layout_constraintBottom_toBottomOf="@+id/tv_sequence3_left"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence3_left"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence3_left"
                app:layout_constraintVertical_bias="0.0" />

            <Button
                android:layout_width="@dimen/px_209"
                android:layout_height="@dimen/px_64"
                android:layout_marginTop="@dimen/px_31"
                android:layout_marginBottom="@dimen/px_35"
                android:background="@drawable/scene_shape_help_create_unclickable_button"
                android:clickable="false"
                android:drawableStart="@drawable/scene_icon_common_download_text1"
                android:drawablePadding="@dimen/px_12"
                android:paddingStart="@dimen/px_8"
                android:paddingEnd="@dimen/px_20"
                android:stateListAnimator="@null"
                android:text="@string/scene_text_help_create_add_button"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence3_left"
                app:layout_constraintTop_toBottomOf="@+id/tv_help_dialog_create_content1_3" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--右半部分          -->
<!--          -->
<!--           -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_help_create_right_part"
            android:layout_width="@dimen/px_558"
            android:layout_height="@dimen/px_396"
            android:layout_marginLeft="@dimen/px_20"
            android:background="@drawable/scene_shape_help_create_part_bg"
            app:layout_constraintStart_toEndOf="@+id/cl_help_create_left_part"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_help_dialog_perform_subtitle2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_46"
                android:layout_marginLeft="@dimen/px_36"
                android:layout_marginTop="@dimen/px_36"
                android:text="@string/scene_text_help_create_step_title_2"
                android:textFontWeight="400"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_p42r_h3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_sequence1_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle2"
                app:layout_constraintTop_toBottomOf="@+id/tv_help_dialog_perform_subtitle2" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content2_1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:text="@string/scene_text_help_create_content2_1"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence1_right"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence1_right" />

            <View
                android:id="@+id/v_help_create_dash3"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_20"
                android:background="@drawable/scene_shape_help_dash"
                app:layout_constraintEnd_toEndOf="@+id/tv_sequence1_right"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence1_right"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence1_right" />

            <TextView
                android:id="@+id/tv_sequence2_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle2"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence1_right" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content2_2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:gravity="center_vertical"
                android:text="@string/scene_text_help_create_content2_2"
                app:layout_constraintBottom_toBottomOf="@+id/tv_sequence2_right"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence2_right"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence2_right" />

            <View
                android:id="@+id/v_help_create_dash4"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_20"
                android:background="@drawable/scene_shape_help_dash"
                app:layout_constraintEnd_toEndOf="@+id/tv_sequence2_right"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence2_right"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence2_right" />

            <TextView
                android:id="@+id/tv_sequence3_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_20"
                android:background="@drawable/scene_shape_help_create_sequence_bg"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/scene_color_text_2"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                app:layout_constraintStart_toStartOf="@+id/tv_help_dialog_perform_subtitle2"
                app:layout_constraintTop_toBottomOf="@+id/tv_sequence2_right" />

            <TextView
                android:id="@+id/tv_help_dialog_create_content2_3"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_40"
                android:layout_marginLeft="@dimen/px_24"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_p42r_h6"
                android:gravity="center_vertical"
                android:text="@string/scene_text_help_create_content2_3"
                app:layout_constraintBottom_toBottomOf="@+id/tv_sequence3_right"
                app:layout_constraintStart_toEndOf="@+id/tv_sequence3_right"
                app:layout_constraintTop_toTopOf="@+id/tv_sequence3_right"
                app:layout_constraintVertical_bias="0.0" />

            <Button
                android:layout_width="@dimen/px_166"
                android:layout_height="@dimen/px_64"
                android:layout_marginTop="@dimen/px_31"
                android:layout_marginBottom="@dimen/px_45"
                android:background="@drawable/scene_shape_help_create_unclickable_button"
                android:clickable="false"
                android:drawableStart="@drawable/scene_icon_help_plus"
                android:drawablePadding="@dimen/px_12"
                android:paddingStart="@dimen/px_8"
                android:paddingEnd="@dimen/px_20"
                android:stateListAnimator="@null"
                android:text="@string/scene_button_me_add_scene"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tv_sequence3_right"
                app:layout_constraintTop_toBottomOf="@+id/tv_help_dialog_create_content2_3" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>