<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.android.animationlib.CustomTabLayout
            android:id="@+id/tab_main"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_84"
            android:layout_marginTop="@dimen/px_76"
            android:background="@color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_type="0"
            app:select_title_color="@color/scene_color_text_1"
            app:title_bottom_margin="@dimen/px_14"
            app:unselect_title_color="@color/scene_color_text_3" />


<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="104dp"-->
<!--            android:layout_marginStart="76dp"-->
<!--            android:gravity="center"-->
<!--            android:text="@string/scene_app_name"-->
<!--            android:textColor="@color/scene_color_text_1"-->
<!--            android:textSize="64dp"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="@id/tab_main"-->
<!--            tools:ignore="SpUsage" />-->


<!--        <ImageView-->
<!--            android:id="@+id/iv_main_help"-->
<!--            android:layout_width="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_height="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_marginEnd="76dp"-->
<!--            android:layout_marginBottom="20dp"-->
<!--            android:scaleType="fitCenter"-->
<!--            android:src="@drawable/scene_icon_common_main_help"-->
<!--            app:layout_constraintBottom_toBottomOf="@id/tab_main"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="@id/tab_main" />-->

        <ImageView
            android:id="@+id/iv_main_menu"
            android:layout_width="@dimen/px_40"
            android:layout_height="@dimen/px_40"
            android:layout_marginEnd="@dimen/px_64"
            android:scaleType="fitCenter"
            android:src="@drawable/scene_img_main_menu"
            app:layout_constraintBottom_toBottomOf="@id/tab_main"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tab_main" />

        <!--社区 start-->
<!--        <ImageView-->
<!--            android:id="@+id/iv_community_user_head"-->
<!--            android:layout_width="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_height="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_marginEnd="@dimen/px_48"-->
<!--            android:background="@color/transparent"-->
<!--            android:scaleType="fitCenter"-->
<!--            android:src="@drawable/scene_icon_common_main_user"-->
<!--            android:visibility="gone"-->
<!--            app:layout_constraintBottom_toBottomOf="@id/iv_main_menu"-->
<!--            app:layout_constraintEnd_toStartOf="@id/iv_main_menu"-->
<!--            app:layout_constraintTop_toTopOf="@id/iv_main_menu" />-->


<!--        <ImageView-->
<!--            android:id="@+id/btn_community_search"-->
<!--            android:layout_width="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_height="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_marginEnd="48dp"-->
<!--            android:scaleType="fitCenter"-->
<!--            android:src="@drawable/scene_icon_common_search"-->
<!--            android:visibility="gone"-->
<!--            app:layout_constraintBottom_toBottomOf="@+id/iv_main_menu"-->
<!--            app:layout_constraintEnd_toStartOf="@id/iv_community_user_head"-->
<!--            app:layout_constraintTop_toTopOf="@+id/iv_main_menu" />-->


<!--        <com.dfl.android.animationlib.ScaleImageButton-->
<!--            android:id="@+id/btn_community_post_scene"-->
<!--            style="@style/SecondaryBigBtnColorStyle.WithIconStyle"-->
<!--            android:layout_width="289dp"-->
<!--            android:layout_height="76dp"-->
<!--            android:layout_marginEnd="48dp"-->
<!--            android:visibility="gone"-->
<!--            app:backgroundCornerDegree="16"-->
<!--            app:imgSrc="@drawable/scene_icon_community_push"-->
<!--            app:imgSrcClicked="@drawable/scene_icon_community_push"-->
<!--            app:layout_constraintBottom_toBottomOf="@id/iv_main_menu"-->
<!--            app:layout_constraintEnd_toStartOf="@id/btn_community_search"-->
<!--            app:layout_constraintTop_toTopOf="@id/iv_main_menu"-->
<!--            app:spaceWidth="20dp"-->
<!--            app:text="@string/scene_button_community_post_scene" />-->
        <!--社区 end-->

        <!-- 我的 start-->
<!--        <ImageView-->
<!--            android:id="@+id/iv_main_log"-->
<!--            android:layout_width="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_height="@dimen/scene_icon_size_normal_icon"-->
<!--            android:layout_marginEnd="48dp"-->
<!--            android:src="@drawable/scene_icon_common_main_log"-->
<!--            android:visibility="visible"-->
<!--            app:layout_constraintBottom_toBottomOf="@+id/iv_main_menu"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/iv_main_menu"-->
<!--            app:layout_constraintTop_toTopOf="@+id/iv_main_menu" />-->


        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_add_scene"
            style="@style/SecondaryBigBtnColorStyle.WithIconStyleP42R"
            android:layout_width="@dimen/px_166"
            android:layout_height="@dimen/px_48"
            android:layout_marginStart="@dimen/px_80"
            android:visibility="gone"
            app:backgroundCornerDegree="8"
            app:imgSrc="@drawable/scene_img_main_add_scene"
            app:imgSrcClicked="@drawable/scene_img_main_add_scene"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tab_main"
            app:layout_constraintBottom_toBottomOf="@id/tab_main"
            app:spaceWidth="@dimen/px_10"
            app:backgroundBaseColor="@color/scene_neutral_btn_1"
            app:textColor="@color/scene_primary_text_normal"
            app:text="@string/scene_button_me_add_scene"
            tools:ignore="RtlSymmetry" />
        <!-- 我的 end-->


        <TextView
            android:id="@+id/txt_drag_describe"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/scene_color_bg_1"
            android:gravity="center"
            android:text="@string/scene_text_me_drag_describe"
            android:textColor="@color/scene_color_text_2"
            android:textSize="@dimen/scene_text_size_h4_p42r"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tab_main"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tab_main" />

        <FrameLayout
            android:id="@+id/fl_post_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/scene_color_mask"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 添加菜单蒙层-->
        <com.dfl.smartscene.util.MaskFrameLayout
            android:id="@+id/fl_menu_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/scene_mask"
            android:visibility="gone"
            android:elevation="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>