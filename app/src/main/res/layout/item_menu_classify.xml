<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y_px_90">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_item_menu_classify"
        android:layout_width="@dimen/y_px_250"
        android:layout_height="match_parent"
        tools:ignore="MissingConstraints"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_item_menu_classify_icon"
        android:layout_width="@dimen/x_px_32"
        android:layout_height="@dimen/y_px_32"
        android:layout_marginRight="@dimen/x_px_20"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/textview_base_category_item_classify"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/textview_base_category_item_classify"
        android:layout_width="@dimen/x_px_150"
        android:layout_height="match_parent"
        android:ellipsize="end"
        android:gravity="left|center_vertical"
        android:maxLines="1"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>