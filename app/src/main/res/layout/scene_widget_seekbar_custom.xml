<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/csl_custom_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_item"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/scene_height_text_size_h5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="@+id/ll_content_custom"
            app:layout_constraintStart_toStartOf="@+id/ll_content_custom"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_left_desc_custom"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:gravity="center"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h5"
                tools:text="大于" />

            <TextView
                android:id="@+id/tv_item"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h5"
                tools:text="280" />

            <TextView
                android:id="@+id/tv_unit_custom"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingBottom="1dp"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h5"
                tools:text="km" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_content_custom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="104dp"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_item">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/scene_icon_widget_seekbar_down" />

        </LinearLayout>

        <com.dfl.common.nightmode.widget.SkinCompatSeekBar
            android:id="@+id/seekbar"
            style="@style/custom_seek_bar"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_marginHorizontal="120dp"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:max="16"
            android:progress="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_content_custom" />

        <ImageView
            android:id="@+id/iv_icon_custom"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="24dp"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="@id/seekbar"
            app:layout_constraintStart_toStartOf="@+id/seekbar"
            app:layout_constraintTop_toTopOf="@id/seekbar"
            tools:src="@drawable/scene_icon_common_search" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>