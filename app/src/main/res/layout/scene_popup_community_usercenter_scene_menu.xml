<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/csl_pop_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.dfl.smartscene.community.usercenter.UserCenterSceneMenuPopup">

    <LinearLayout
        android:id="@+id/ll_pop_menu"
        android:layout_width="313dp"
        android:layout_height="148dp"
        android:background="@drawable/scene_shape_community_bg_4_r24"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_delete_scene"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_text_size_h4"
            android:gravity="center"
            android:text="@string/scene_text_user_center_delete_scene"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h4" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
