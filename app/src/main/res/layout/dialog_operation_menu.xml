<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_common_dialog"
        android:layout_height="@dimen/y_px_dialog_height"
        android:layout_width="@dimen/x_px_dialog_width"
        android:tag="background:drawable:bg_common_dialog"
        android:paddingTop="@dimen/y_px_16"
        android:paddingLeft="@dimen/x_px_16"
        android:paddingRight="@dimen/x_px_16"
        android:paddingBottom="@dimen/y_px_16">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_height="@dimen/y_px_88"
            android:layout_width="match_parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_operation_menu_dialog_title"
                android:includeFontPadding="false"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:id="@+id/imageview_operation_menu_dialog_close"
                android:layout_height="@dimen/y_px_40"
                android:layout_marginLeft="@dimen/x_px_40"
                android:layout_width="@dimen/x_px_40"
                android:src="@drawable/icon_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                android:background="@color/color_divide"
                android:layout_height="@dimen/y_px_1"
                android:layout_marginLeft="@dimen/x_px_24"
                android:layout_marginRight="@dimen/x_px_24"
                android:layout_width="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleview_base_category_classify"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/y_px_112"
            android:layout_width="@dimen/x_px_260"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <View-->
        <!--            android:layout_width="@dimen/x_px_0"-->
        <!--            android:layout_height="@dimen/y_px_90"-->
        <!--            android:background="@drawable/shadow_halfmask"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/recycleview_base_category_classify"-->
        <!--            app:layout_constraintLeft_toLeftOf="@+id/recycleview_base_category_classify"-->
        <!--            app:layout_constraintRight_toRightOf="@+id/recycleview_base_category_classify" />-->


        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager2_base_category_content"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/y_px_112"
            android:layout_width="@dimen/x_px_740"
            app:layout_constraintLeft_toRightOf="@+id/recycleview_base_category_classify"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>