<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_hotnew_title"
        android:layout_height="@dimen/y_px_top_bar_height"
        android:layout_width="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/button_hotnew_close"
            android:layout_height="@dimen/y_px_40"
            android:layout_marginLeft="@dimen/x_px_40"
            android:layout_width="@dimen/x_px_40"
            android:src="@drawable/icon_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/btn_icon"
            android:src="@drawable/icon_flag"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginRight="@dimen/x_px_194"
            android:layout_width="@dimen/x_px_64"
            android:layout_height="@dimen/x_px_64"/>

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/btn_icon_name"
            android:text="@string/app_name"
            android:textColor="@color/color_text_tertiary_label"
            android:textSize="@dimen/font_size_34"
            android:lineHeight="@dimen/x_px_52"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginRight="@dimen/x_px_54"
            android:layout_marginTop="@dimen/y_px_14"
            android:layout_width="@dimen/x_px_136"
            android:layout_height="@dimen/x_px_52"/>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout_hotnew_title"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/x_px_300"
            android:layout_marginStart="@dimen/x_px_300"
            android:layout_marginTop="@dimen/y_px_22"
            android:layout_width="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tabBackground="@android:color/transparent"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorHeight="@dimen/y_px_4"
            app:tabIndicatorColor="@color/color_text_selected"
            app:tabMaxWidth="@dimen/x_px_600"
            app:tabMode="fixed"
            app:tabPaddingEnd="@dimen/x_px_40"
            app:tabPaddingStart="@dimen/x_px_40"
            app:tabRippleColor="@android:color/transparent"
            app:tabSelectedTextColor="@color/color_text_first_label"
            app:tabTextAppearance="@style/table_layout_text_style"
            app:tabTextColor="@color/color_text_tertiary_label"
            app:tabPaddingBottom="@dimen/y_px_20"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:background="@color/color_divide"
        android:layout_height="@dimen/y_px_1"
        android:layout_marginLeft="@dimen/x_px_24"
        android:layout_marginRight="@dimen/x_px_24"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="@+id/layout_hotnew_title"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewpager2_hotnew_fragment_content"
        android:layout_height="@dimen/y_px_0"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_hotnew_title" />

</androidx.constraintlayout.widget.ConstraintLayout>