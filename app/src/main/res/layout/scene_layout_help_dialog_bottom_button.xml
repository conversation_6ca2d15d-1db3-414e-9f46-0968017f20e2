<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/scene_height_dialog_bottom"
    android:gravity="center"
    android:orientation="horizontal">

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_primary"
        style="@style/PrimaryBigBtnColorStyle"
        android:layout_width="479dp"
        app:text="@string/scene_button_common_sure" />

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_normal"
        style="@style/SecondaryBigBtnColorStyle"
        android:layout_width="479dp"
        android:layout_marginStart="104dp"
        app:text="@string/scene_text_common_cancel" />

</LinearLayout>