<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
    android:gravity="center"
    android:orientation="horizontal">

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_primary"
        style="@style/PrimaryBigBtnColorStyleP42R"
        android:layout_width="@dimen/px_424"
        app:text="@string/scene_button_common_sure" />

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_normal"
        style="@style/SecondaryBigBtnColorStyle"
        android:layout_width="@dimen/px_424"
        android:layout_height="@dimen/px_76"
        android:layout_marginStart="@dimen/px_32"
        app:text="@string/scene_text_common_cancel" />

</LinearLayout>