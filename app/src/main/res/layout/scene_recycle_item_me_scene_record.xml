<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bean"
            type="com.dfl.smartscene.bean.main.SceneRecordBean" />
    </data>

    <!-- 一行两个item的布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/px_64"
        android:paddingEnd="@dimen/px_64"
        android:paddingBottom="@dimen/px_24">

        <!-- 第一个记录item -->
        <FrameLayout
            android:id="@+id/fl_record_first"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_120"
            android:layout_weight="1"
            android:layout_marginEnd="@dimen/px_20">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/px_48">

                <ImageView
                    android:id="@+id/iv_icon_first"
                    android:layout_width="@dimen/scene_icon_size_small_icon"
                    android:layout_height="@dimen/scene_icon_size_small_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:gravity="center_vertical"
                    tools:src="@drawable/scene_icon_community_success" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/px_368"
                    android:layout_height="@dimen/px_88"
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/px_28"
                    app:layout_constraintStart_toEndOf="@id/iv_icon_first"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <TextView
                        android:id="@+id/tv_scene_name_first"
                        android:layout_width="@dimen/px_261"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:textColor="@color/scene_color_text_1"
                        android:textSize="@dimen/scene_text_size_h4_p42r"
                        android:maxLines="1"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:ellipsize="end"
                        tools:text="快速降温" />
                    <!-- 执行状态文本 -->
                    <TextView
                        android:id="@+id/tv_status_first"
                        android:layout_width="@dimen/px_261"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_p42r_h6"
                        tools:text="执行成功" />

                    <TextView
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:id="@+id/tv_time_first"
                        time_stamp_data="@{bean.record.finishSceneTime}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_p42r_h6"
                        tools:text="10:21" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <!-- 第二个记录item -->
        <FrameLayout
            android:id="@+id/fl_record_second"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_120"
            android:layout_weight="1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/px_48">

                <ImageView
                    android:id="@+id/iv_icon_second"
                    android:layout_width="@dimen/scene_icon_size_small_icon"
                    android:layout_height="@dimen/scene_icon_size_small_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:gravity="center_vertical"
                    tools:src="@drawable/scene_icon_community_success" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/px_368"
                    android:layout_height="@dimen/px_88"
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/px_28"
                    app:layout_constraintStart_toEndOf="@id/iv_icon_second"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <TextView
                        android:id="@+id/tv_scene_name_second"
                        android:layout_width="@dimen/px_261"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:textColor="@color/scene_color_text_1"
                        android:textSize="@dimen/scene_text_size_h4_p42r"
                        android:maxLines="1"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:ellipsize="end"
                        tools:text="快速降温" />
                    <!-- 执行状态文本 -->
                    <TextView
                        android:id="@+id/tv_status_second"
                        android:layout_width="@dimen/px_261"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_p42r_h6"
                        tools:text="执行成功" />

                    <TextView
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:id="@+id/tv_time_second"
                        time_stamp_data="@{bean.record.finishSceneTime}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="start|center_vertical"
                        android:textColor="@color/scene_color_text_3"
                        android:textSize="@dimen/scene_text_size_p42r_h6"
                        tools:text="10:21" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

    </LinearLayout>
</layout>