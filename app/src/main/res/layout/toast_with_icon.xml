<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="@dimen/x_px_1488"
        android:layout_height="@dimen/y_px_612"
        android:gravity="center_horizontal">



        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y_px_448"
            android:orientation="horizontal"
            android:clickable="true"
            >

            <com.iauto.uicontrol.ImageBase
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/com_toastbg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.iauto.uicontrol.ImageBase
                android:id="@+id/image_view_toast_with_icon"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                android:layout_marginStart="@dimen/x_px_94"
                android:layout_marginTop="@dimen/y_px_47"
                android:layout_marginBottom="@dimen/y_px_47"
                app:imageString="ic48_system_warning_n"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

            </com.iauto.uicontrol.ImageBase>

            <com.iauto.uicontrol.TextBase
                android:id="@+id/text_view_toast_with_icon"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/y_px_42"
                android:layout_marginStart="@dimen/x_px_24"
                android:layout_marginEnd="@dimen/x_px_94"
                android:layout_marginTop="@dimen/y_px_47"
                android:layout_marginBottom="@dimen/y_px_47"
                android:gravity="center_vertical"
                android:text="删除成功"
                android:textColor="@color/color_text_toast_text"
                android:textSize="@dimen/font_size_28"
                app:layout_constraintStart_toEndOf="@id/image_view_toast_with_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>