<?xml version="1.0" encoding="utf-8" standalone="no"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/layout_content_top" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:orientation="vertical"
            android:layout_marginHorizontal="@dimen/x_px_32"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!--场景标题-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/imageview_scene_editor_icon"
                    android:layout_width="@dimen/x_px_48"
                    android:layout_height="@dimen/y_px_48"
                    android:layout_marginStart="@dimen/x_px_14"
                    android:scaleType="fitCenter"
                    android:src="@drawable/icon_edit_scene_64x64"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_name"
                    android:layout_width="@dimen/x_px_1000"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/x_px_16"
                    android:layout_marginTop="@dimen/y_px_22"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:maxLength="15"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/x_px_30"
                    app:layout_constraintLeft_toRightOf="@+id/imageview_scene_editor_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_describe"
                    android:layout_width="@dimen/x_px_1000"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/x_px_16"
                    android:layout_marginBottom="@dimen/y_px_22"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:ellipsize="end"
                    android:textColor="@color/color_text_secondary_label"
                    android:textSize="@dimen/x_px_26"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/imageview_scene_editor_icon" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_library_detail_collect_button"
                    android:layout_width="@dimen/x_px_280"
                    android:layout_height="@dimen/y_px_96"
                    android:layout_marginEnd="@dimen/x_px_34"
                    android:background="@drawable/bg_button_positive"
                    android:tag="background:drawable:bg_button_positive"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/layout_library_detail_execute_button">

                    <com.dfl.dflcommonlibs.uimodeutil.UITextView
                        android:id="@+id/textview_library_detail_collect_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/string_library_detail_collect_button"
                        android:textColor="@color/white"
                        android:textSize="@dimen/y_px_text_body"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                    <View
                        android:id="@+id/clickarea_library_detail_collect_button"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:contentDescription="@string/string_library_detail_collect_button"
                        android:layout_marginTop="@dimen/y_px_25"/>


                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_library_detail_execute_button"
                    android:layout_width="@dimen/x_px_280"
                    android:layout_height="@dimen/y_px_96"
                    android:layout_marginEnd="@dimen/x_px_51"
                    android:background="@drawable/bg_button_negative"
                    android:tag="background:drawable:bg_button_negative"
                    android:contentDescription="@string/string_library_detail_excute_button"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent">

                    <com.dfl.dflcommonlibs.uimodeutil.UITextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/string_library_detail_excute_button"
                        android:textColor="@color/white"
                        android:textSize="@dimen/y_px_text_body"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_title"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:background="@color/color_divide"
                    android:layout_marginTop="@dimen/y_px_124"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--语音指令-->
            <LinearLayout
                android:id="@+id/content_vr"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_vr_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_88"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_vr_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_vr"
                    android:layout_width="@dimen/x_px_1380"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="true"
                    android:layout_marginTop="@dimen/y_px_20"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textview_scene_editor_vr_title" />

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_vr"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_33"
                    android:background="@color/color_divide"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/recyclerview_scene_editor_vr" />

            </LinearLayout>

            <!--触发条件-->
            <LinearLayout
                android:id="@+id/layout_scene_editor_condition_state"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_condition_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_88"
                    android:gravity="center_vertical"
                    android:text="@string/string_scene_editor_fragment_condition_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle" />

                <include
                    android:id="@+id/item_scene_editor_condition"
                    layout="@layout/item_scene_editor_operation"
                    android:layout_width="@dimen/x_px_660"
                    android:layout_height="@dimen/y_px_80"
                    android:layout_marginTop="@dimen/y_px_20"/>

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_condition"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_32"
                    android:background="@color/color_divide"/>
            </LinearLayout>

            <!--状态条件-->
            <LinearLayout
                android:id="@+id/layout_scene_editor_state"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_state_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_88"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_state_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_state"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="true"
                    android:layout_marginTop="@dimen/y_px_28"/>

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_state"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_32"
                    android:background="@color/color_divide"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/recyclerview_scene_editor_state" />
            </LinearLayout>

            <!--生效时间-->
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_time_state_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_88"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_time_state"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_time_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="true"
                    android:layout_marginTop="@dimen/y_px_28"
                    app:layout_constraintLeft_toLeftOf="parent"/>
                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:id="@+id/divide_scene_editor_time_state"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_32"
                    android:background="@color/color_divide" />

            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/y_px_24">

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_scene_editor_action_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y_px_88"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_action_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_scene_editor_action"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/y_px_20"
                    android:nestedScrollingEnabled="true"/>
            </LinearLayout>

            <View
                android:id="@+id/view_buy_notice"
                android:contentDescription="前往购买"
                android:visibility="gone"
                android:layout_width="1px"
                android:layout_height="1px"/>

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_libary_detail_buy_notice"
                android:layout_width="match_parent"
                android:layout_height="@dimen/y_px_40"
                android:gravity="center"
                android:layout_marginBottom="@dimen/y_px_70"
                android:visibility="gone"
                android:textSize="@dimen/y_px_text_subbody"
                android:includeFontPadding="false"
                android:textColor="@color/color_text_secondary_label"
                android:ellipsize="end"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>