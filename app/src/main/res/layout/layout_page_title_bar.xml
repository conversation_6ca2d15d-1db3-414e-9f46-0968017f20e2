<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_content_top_title"
    android:layout_width="match_parent"
    android:layout_height="@dimen/y_px_top_bar_height"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/button_content_top_close"
        android:layout_width="@dimen/x_px_40"
        android:layout_height="@dimen/y_px_40"
        android:layout_marginLeft="@dimen/x_px_40"
        android:src="@drawable/icon_close"
        android:contentDescription="@string/string_close_contentDescription"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/text_view_content_top_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/y_px_1"
        android:layout_marginLeft="@dimen/x_px_24"
        android:layout_marginRight="@dimen/x_px_24"
        android:background="@color/color_divide"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>