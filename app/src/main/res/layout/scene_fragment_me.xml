<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.main.me.MeViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/sl_update"
            style="@style/SmartRefreshLayoutTheme"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent">
            <com.dfl.smartscene.widget.ClassicsHeader4All
                android:id="@+id/header"
                style="@style/SmartRefreshClassicsTheme"
                app:layout_srlSpinnerStyle="FixedBehind" />
            <com.dfl.android.animationlib.scrollView.OverScrollView
                android:id="@+id/nsv_record"
                style="@style/OverScrollViewTheme1A.WithScrollBar"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <com.dfl.android.animationlib.scrollView.FadeRecyclerView
                    android:id="@+id/rv_my_scene_list"
                    style="@style/FadeRecyclerViewTheme1A"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="50dp"
                    android:overScrollMode="never" />
            </com.dfl.android.animationlib.scrollView.OverScrollView>

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>


        <!--        添加场景按钮-->
        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/btn_add_scene"
            style="@style/PrimaryBigBtnColorStyle.WithIconStyle"
            android:layout_width="@dimen/px_272"
            android:layout_height="@dimen/px_76"
            android:layout_marginBottom="@dimen/px_90"
            app:textSize="@dimen/scene_text_size_p42r_h3"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:imgSrc="@drawable/scene_icon_me_plus"
            app:imgSrcClicked="@drawable/scene_icon_me_plus"
            app:textColor="@color/scene_primary_text_normal"
            app:backgroundCornerDegree="12"
            app:imgWidth="@dimen/px_36"
            app:imgHight="@dimen/px_36"
            android:paddingHorizontal="@dimen/px_44"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:text="@string/scene_button_me_add_scene" />


        <TextView
            android:id="@+id/tv_my_scene_list_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/px_112"
            android:drawableTop="@drawable/scene_img_me_fragment_empty"
            android:drawablePadding="@dimen/px_8"
            android:elegantTextHeight="true"
            android:gravity="center"
            android:text="@string/scene_text_add_scene_desc"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/btn_add_scene"
            app:layout_constraintEnd_toEndOf="@id/btn_add_scene"
            app:layout_constraintStart_toStartOf="@id/btn_add_scene" />
        <!--备案号放在后面防止不触发点击事件-->
        <TextView
            android:id="@+id/tv_record_my_scene_list_empty"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginBottom="@dimen/px_18"
            android:drawableEnd="@drawable/scene_icon_common_arrow_right"
            android:drawablePadding="@dimen/px_6"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:text="@string/scene_software_record_number"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_p42r_h6"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>