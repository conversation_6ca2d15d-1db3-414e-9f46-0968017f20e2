<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_edit_scene_action_name_item_dialog"
        android:layout_width="0dp"
        android:layout_height="@dimen/px_60"
        android:gravity="start|center_vertical"
        android:paddingBottom="@dimen/px_20"
        android:text="@string/scene_text_edit_scene_conditions"
        android:textColor="@color/scene_color_text_2"
        android:textSize="@dimen/scene_text_size_p42r_h6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>