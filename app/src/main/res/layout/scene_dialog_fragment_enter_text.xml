<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.overlay.apply.EnterTextDialogFragment">


    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/MiddleDialogTheme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <LinearLayout
            android:id="@+id/ll_end_address"
            android:layout_width="0dp"
            android:layout_height="96dp"
            android:layout_marginHorizontal="76dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/scene_shape_edit_address_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <ImageView
                android:id="@+id/iv_input_end"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/scene_icon_common_search"
                android:visibility="visible" />

            <com.dfl.smartscene.widget.ClearEditText
                android:id="@+id/et_edit_input"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@null"
                android:cursorVisible="true"
                android:maxLength="30"
                android:paddingStart="24dp"
                android:paddingEnd="40dp"
                android:selectAllOnFocus="true"
                android:singleLine="true"
                android:textColor="@color/scene_color_text_1"
                android:textColorHint="@color/scene_color_text_3"
                android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                android:textSize="@dimen/scene_text_size_h5" />

        </LinearLayout>


        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_action_song"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="34dp"
            android:layout_marginBottom="16dp"
            android:overScrollMode="never"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="@id/ll_end_address"
            app:layout_constraintStart_toStartOf="@id/ll_end_address"
            app:layout_constraintTop_toBottomOf="@id/ll_end_address" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>