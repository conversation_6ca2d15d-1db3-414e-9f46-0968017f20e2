<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
    android:gravity="center"
    android:orientation="horizontal">

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_primary"
        style="@style/PrimaryBigBtnColorStyleP42R"
        android:layout_width="@dimen/px_424"
        android:layout_height="@dimen/px_76"
        app:text="@string/scene_button_common_sure" />

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_warning"
        style="@style/WarningBigBtnColorStyleP42R"
        android:layout_width="@dimen/scene_width_dialog_big_button_p42r"
        android:visibility="gone"
        app:text="@string/scene_text_close_ask_before_start_scene" />

    <com.dfl.android.animationlib.ScaleImageButton
        android:id="@+id/btn_normal"
        style="@style/SecondaryBigBtnColorStyle"
        app:textSize="@dimen/scene_text_size_p42r_h3"
        android:layout_width="@dimen/px_424"
        android:layout_height="@dimen/px_76"
        android:layout_marginStart="@dimen/px_32"
        app:text="@string/scene_text_common_cancel" />

</LinearLayout>