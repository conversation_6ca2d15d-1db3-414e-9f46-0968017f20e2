<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_userinfo_desc"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h6"
        android:layout_marginBottom="5dp"
        android:gravity="center"
        android:textColor="@color/scene_color_text_3"
        android:textSize="@dimen/scene_text_size_h6"
        app:layout_constraintBottom_toBottomOf="@id/tv_userinfo_num"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="名称" />

    <TextView
        android:id="@+id/tv_userinfo_num"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h3"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="80dp"
        android:gravity="center"
        android:textColor="@color/scene_primary_color_highlight"
        android:textSize="@dimen/scene_text_size_h3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_userinfo_desc"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="数量" />

    <ImageView
        android:id="@+id/iv_number_tips"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/scene_shape_community_usercenter_number_tip"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/tv_userinfo_num"
        app:layout_constraintTop_toTopOf="@id/tv_userinfo_num" />

</androidx.constraintlayout.widget.ConstraintLayout>