<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_scene_editor_action_default"
    android:layout_height="@dimen/y_px_167"
    android:layout_width="@dimen/x_px_320"
    android:tag="background:drawable:bg_scene_editor_action_default">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:background="@mipmap/icon_add"
        android:layout_height="@dimen/y_px_32"
        android:layout_marginTop="@dimen/y_px_43"
        android:layout_width="@dimen/x_px_32"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:layout_height="@dimen/y_px_42"
        android:layout_marginBottom="@dimen/y_px_30"
        android:layout_width="wrap_content"
        android:text="添加操作"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_body"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>