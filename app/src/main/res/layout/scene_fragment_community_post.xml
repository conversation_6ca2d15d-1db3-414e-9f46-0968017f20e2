<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".community.post.CommunityPostFragment">

    <data>

        <!-- 是否在生成封面中-->
        <variable
            name="isGenImgIng"
            type="Boolean" />

        <variable
            name="scenarioInfo"
            type="com.iauto.scenarioadapter.ScenarioInfo" />


        <import type="android.view.View" />

        <import type="android.text.TextUtils" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">

        <View
            android:id="@+id/view_dis_show"
            android:layout_width="0dp"
            android:layout_height="400dp"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_content"
            android:layout_width="0dp"
            android:layout_height="1062dp"
            android:background="@drawable/scene_shape_community_select_scene_bg_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="136dp"
            android:layout_height="140dp"
            android:layout_marginTop="18dp"
            android:paddingStart="76dp"
            android:src="@drawable/scene_icon_common_arrow_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_content" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="140dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:text="@string/scene_button_community_post_scene"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_back"
            app:layout_constraintTop_toTopOf="@id/iv_back"
            tools:ignore="RtlSymmetry" />

        <com.dfl.android.animationlib.scrollView.OverScrollView
            style="@style/OverScrollViewTheme1A"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_back">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.dfl.smartscene.widget.CornerImageView
                    android:id="@+id/iv_scene_bg"
                    android:layout_width="768dp"
                    android:layout_height="308dp"
                    android:layout_marginStart="76dp"
                    android:scaleType="centerCrop"
                    app:corner_radius="@dimen/scene_radius_extra_large"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!--卡片蒙层背景-->
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/scene_shape_community_card_mask"
                    app:layout_constraintBottom_toBottomOf="@id/iv_scene_bg"
                    app:layout_constraintEnd_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg" />


                <TextView
                    android:id="@+id/tv_scene_name"
                    android:layout_width="wrap_content"
                    android:layout_height="68dp"
                    android:layout_marginHorizontal="40dp"
                    android:layout_marginTop="39dp"
                    android:background="@drawable/scene_shape_text_bg_post_scene_name_r16"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:lines="1"
                    android:maxWidth="620dp"
                    android:paddingHorizontal="20dp"
                    android:text='@={ scenarioInfo.scenarioName,default=""}'
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="@dimen/scene_text_size_h4"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintStart_toStartOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg"
                    tools:text="我旁边是个懒人" />

                <ImageView
                    android:id="@+id/tv_edit_name"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:padding="2dp"
                    android:src="@drawable/scene_icon_community_edit"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintBottom_toBottomOf="@id/tv_scene_name"
                    app:layout_constraintStart_toEndOf="@id/tv_scene_name"
                    app:layout_constraintTop_toTopOf="@id/tv_scene_name" />


                <RelativeLayout
                    android:id="@+id/rl_scene_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="26dp"
                    android:layout_marginEnd="20dp"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintStart_toStartOf="@id/tv_scene_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_scene_name">

                    <TextView
                        android:id="@+id/tv_scene_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:alpha="0.5"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxWidth="620dp"
                        android:maxLines="3"
                        android:minWidth="0dp"
                        android:minHeight="62dp"
                        android:text='@={scenarioInfo.scenarioDesc,default=""}'
                        android:textColor="@color/scene_color_text_1"
                        android:textSize="@dimen/scene_text_size_h6"
                        tools:text="场景简介场景简介场景简介场简介场景介场景简介场景简介场简介场景介场景简介场景简介场简介场景介场景简介场景简介场简介场景介场景简介场景简介场简介场景介场景简介场景简介场简介场景简介" />

                    <TextView
                        android:id="@+id/tv_scene_desc_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:alpha="0.5"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxWidth="620dp"
                        android:maxLines="3"
                        android:minWidth="0dp"
                        android:minHeight="62dp"
                        android:text="@string/scene_text_community_edit_desc"
                        android:textColor="@color/scene_color_text_1"
                        android:textSize="@dimen/scene_text_size_h6"
                        android:visibility="gone" />

                </RelativeLayout>

                <ImageView
                    android:id="@+id/tv_edit_desc"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="20dp"
                    android:padding="2dp"
                    android:src="@drawable/scene_icon_community_edit"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintBottom_toBottomOf="@id/rl_scene_desc"
                    app:layout_constraintStart_toEndOf="@id/rl_scene_desc"
                    app:layout_constraintTop_toTopOf="@id/rl_scene_desc" />


                <!--加载中-->
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/scene_shape_community_downloading_view_radius_12"
                    android:visibility="@{isGenImgIng? View.VISIBLE:View.GONE,default=gone}"
                    app:layout_constraintBottom_toBottomOf="@id/iv_scene_bg"
                    app:layout_constraintEnd_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg" />

                <com.dfl.android.animationlib.LoadingImageView
                    android:id="@+id/lottie_loading"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:layout_marginTop="72dp"
                    android:scaleType="center"
                    android:visibility="@{isGenImgIng? View.VISIBLE:View.GONE,default=gone}"
                    app:layout_constraintEnd_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg"
                    app:lottieSrc="@raw/lk1a_120loading_light"
                    app:lottieSrcNight="@raw/lk1a_120loading_dark" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="54dp"
                    android:layout_marginBottom="60dp"
                    android:gravity="center"
                    android:text="@string/scene_text_community_genimg_ing"
                    android:textColor="@color/scene_color_text_3"
                    android:textSize="@dimen/scene_text_size_h4"
                    android:visibility="@{isGenImgIng? View.VISIBLE:View.GONE,default=gone}"
                    app:layout_constraintBottom_toBottomOf="@id/iv_scene_bg"
                    app:layout_constraintEnd_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintStart_toStartOf="@id/iv_scene_bg" />

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="68dp"
                    android:src="@drawable/scene_icon_community_success"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintStart_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginStart="136dp"
                    android:gravity="center_vertical"
                    android:text="@string/scene_text_community_genimg_success"
                    android:textColor="@color/scene_color_text_3"
                    android:textSize="@dimen/scene_text_size_h5"
                    android:visibility="@{isGenImgIng? View.GONE:View.VISIBLE}"
                    app:layout_constraintStart_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg" />


                <com.dfl.android.animationlib.ScaleImageButton
                    android:id="@+id/ll_change_one"
                    style="@style/SecondaryBigBtnColorStyle"
                    android:layout_width="200dp"
                    android:layout_height="64dp"
                    android:layout_marginStart="68dp"
                    android:layout_marginTop="104dp"
                    app:backgroundCornerDegree="16"
                    app:imgHight="40dp"
                    app:imgSrc="@drawable/scene_icon_community_change_one"
                    app:imgSrcClicked="@drawable/scene_icon_community_change_one"
                    app:imgWidth="40dp"
                    app:layout_constraintStart_toEndOf="@id/iv_scene_bg"
                    app:layout_constraintTop_toTopOf="@id/iv_scene_bg"
                    app:spaceWidth="16dp"
                    app:text="@string/scene_text_community_genimg_change_one"
                    app:textSize="@dimen/scene_text_size_h5"
                    tools:ignore="RtlSymmetry" />


                <TextView
                    android:id="@+id/tv_detail_scene_condition_name"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:layout_marginHorizontal="76dp"
                    android:layout_marginTop="65dp"
                    android:gravity="start|center_vertical"
                    android:text="@string/scene_text_edit_meet_all_conditions"
                    android:textColor="@color/scene_color_text_1"
                    android:textFontWeight="400"
                    android:textSize="@dimen/scene_text_size_h4"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_scene_bg" />

                <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                    android:id="@+id/rv_detail_scene_condition"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginTop="24dp"
                    android:minHeight="120dp"
                    android:overScrollMode="never"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_detail_scene_condition_name"
                    app:spanCount="3" />

                <TextView
                    android:id="@+id/tv_detail_scene_action_name"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:layout_marginHorizontal="76dp"
                    android:layout_marginTop="60dp"
                    android:gravity="start|center_vertical"
                    android:text="@string/scene_text_edit_scene_action"
                    android:textColor="@color/scene_color_text_1"
                    android:textFontWeight="400"
                    android:textSize="@dimen/scene_text_size_h4"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rv_detail_scene_condition" />

                <com.dfl.smartscene.widget.recycleview.HeightRecycleView
                    android:id="@+id/rv_detail_scene_action"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="56dp"
                    android:minHeight="320dp"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_detail_scene_action_name"
                    app:spanCount="7" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="204dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rv_detail_scene_action" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.dfl.android.animationlib.scrollView.OverScrollView>

        <View
            android:id="@+id/view_bottom"
            android:layout_width="0dp"
            android:layout_height="240dp"
            android:background="@drawable/scene_shape_community_bottom_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/tv_post"
            style="@style/PrimaryBigBtnColorStyle"
            android:layout_width="502dp"
            app:enable="@{isGenImgIng?false:true,default=true}"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="@id/view_bottom"
            app:layout_constraintStart_toStartOf="@id/view_bottom"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            app:text="@string/scene_text_pop_menu_post"
            app:textSize="@dimen/scene_text_size_h4" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>