<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smart_refresh_mine"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

<!--        <ScrollView-->
<!--            android:id="@+id/layout_content"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:scrollbars="vertical"-->
<!--            android:scrollbarTrackVertical="@null"-->
<!--            android:scrollbarThumbVertical="@drawable/drawable_scroll_bar"-->
<!--            android:scrollbarSize="@dimen/x_px_4"-->
<!--            android:scrollbarStyle="outsideOverlay"-->
<!--            android:clipChildren="true"-->
<!--            android:paddingEnd="@dimen/x_px_38">-->

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_mine"
                android:layout_marginTop="@dimen/y_px_16"
                android:layout_marginHorizontal="@dimen/x_px_32"
                android:paddingBottom="@dimen/y_px_32"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
<!--        </ScrollView>-->
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_loading"
        android:paddingHorizontal="@dimen/x_px_44"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/home_item_loading_gif"
            android:layout_width="@dimen/x_px_160"
            android:layout_height="@dimen/y_px_160"
            layout="@layout/layout_normal_loading_anim"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/loading_tv1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/y_px_24"
            android:gravity="center"
            android:typeface="monospace"
            android:fontFamily="serif-monospace"
            android:text="正在加载..."
            android:textColor="@color/color_text_unselected"
            android:textSize="@dimen/font_size_30"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/home_item_loading_gif" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/menu_fragment_network_abnormal_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/fragment_refresh_btn"
            android:layout_width="@dimen/x_px_384"
            android:layout_height="@dimen/x_px_192"
            android:src="@drawable/bg_com_networkexception"
            android:layout_marginTop="@dimen/y_px_66"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/tv_retry_network"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y_px_44"
            android:layout_marginTop="@dimen/y_px_8"
            android:text="@string/string_no_network_notice"
            android:textColor="@color/color_text_secondary_label"
            android:textSize="@dimen/font_size_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fragment_refresh_btn" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:id="@+id/text_view_network_retry"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_54"
            android:background="@drawable/bg_button_positive_white"
            android:text="重试"
            android:textColor="@color/white"
            android:gravity="center"
            android:textSize="@dimen/font_size_26"
            android:layout_marginTop="@dimen/y_px_51"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_retry_network"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/column_result_fragment_none_result"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:visibility="gone">

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/add_scene_icon"
            android:layout_width="@dimen/x_px_383"
            android:layout_height="@dimen/y_px_192"
            android:layout_marginTop="@dimen/y_px_66"
            android:src="@drawable/icon_create_scene"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:layout_marginTop="@dimen/y_px_8"
            android:text="@string/string_create_u_new_scene"
            android:textSize="30px"
            android:textColor="@color/color_text_secondary_label"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/add_scene_icon" />

        <LinearLayout
            android:id="@+id/linear_create_scene"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_54"
            android:gravity="center_vertical"
            android:background="@drawable/bg_button_positive_white"
            android:layout_marginTop="@dimen/y_px_363"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" >

            <ImageView
                android:layout_width="@dimen/x_px_25"
                android:layout_height="@dimen/y_px_25"
                android:layout_marginLeft="@dimen/x_px_22"
                android:layout_marginRight="@dimen/x_px_5"
                android:layout_marginVertical="@dimen/y_px_12"
                android:src="@drawable/icon_add_new_scene"/>

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_create_new_scene"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26"
                android:drawablePadding="@dimen/x_px_1" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_my_scene_edit_bar"
        android:id="@+id/layout_my_scene_edit_bar"
        android:layout_height="@dimen/y_px_96"
        android:layout_width="@dimen/x_px_780"
        android:tag="background:drawable:bg_my_scene_edit_bar"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.dfl.smartscene.ccs.view.weight.CustomCheckBox
            android:id="@+id/checkbox_my_scene_all_selected"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/x_px_48"
            android:layout_width="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:button="@drawable/drawable_checkbox_state"/>

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:includeFontPadding="false"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/x_px_112"
            android:layout_width="wrap_content"
            android:text="全部"
            android:textColor="@color/color_text_first_label"
            android:textSize="@dimen/y_px_text_body"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:gravity="center"
            android:id="@+id/textview_my_scene_uncollect"
            android:includeFontPadding="false"
            android:layout_height="@dimen/y_px_42"
            android:layout_width="wrap_content"
            android:layout_marginEnd="@dimen/x_px_50"
            android:text="@string/string_library_detail_uncollect_button"
            android:textColor="@color/color_text_selected"
            android:textSize="@dimen/y_px_text_body"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/textview_my_scene_complete"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:gravity="center"
            android:id="@+id/textview_my_scene_complete"
            android:layout_width="wrap_content"
            android:includeFontPadding="false"
            android:layout_height="@dimen/y_px_42"
            android:layout_marginEnd="@dimen/x_px_40"
            android:text="@string/string_scene_editor_fragment_complete"
            android:textColor="@color/color_text_selected"
            android:textSize="@dimen/y_px_text_body"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>