<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_page_content"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:paddingTop="@dimen/y_px_16">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleview_base_category_classify"
        android:layout_height="match_parent"
        android:layout_width="@dimen/x_px_260"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewpager2_base_category_content"
        android:layout_height="match_parent"
        android:layout_width="@dimen/x_px_1192"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>