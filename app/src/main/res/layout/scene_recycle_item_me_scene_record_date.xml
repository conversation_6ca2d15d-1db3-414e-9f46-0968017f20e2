<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="bean"
            type="com.dfl.smartscene.bean.main.SceneRecordBean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/px_64"
        android:paddingEnd="@dimen/px_64"
        android:paddingBottom="@dimen/px_20">

        <TextView
            android:id="@+id/tv_date_header"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_46"
            android:gravity="center_vertical"
            android:text="@{bean.title}"
            android:textColor="@color/scene_color_text_2"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            tools:text="2025-04-21" />

    </LinearLayout>
</layout>