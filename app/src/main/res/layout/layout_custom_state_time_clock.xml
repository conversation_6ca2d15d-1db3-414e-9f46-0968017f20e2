<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_all_day_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x_px_66"
        android:layout_marginTop="@dimen/y_px_25"
        android:text="@string/string_layout_custom_state_time_clock_all_day_title"
        android:textSize="@dimen/font_size_28"
        android:textColor="@color/color_text_secondary_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.smartscene.ccs.view.weight.CustomSwitch
        android:id="@+id/switchcompat_layout_custom_state_time_clock_all_day"
        android:thumb="@drawable/selector_switch_thumb_default"
        android:track="@drawable/selector_switch_track_default"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/y_px_76"
        android:layout_marginTop="@dimen/y_px_25"
        android:layout_marginEnd="@dimen/x_px_74"
        app:layout_constraintBottom_toTopOf="@id/switchcompat_layout_custom_state_time_line"
        android:layout_marginBottom="@dimen/y_px_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:id="@+id/view_open_day_switch"
        app:layout_constraintLeft_toRightOf="@+id/textview_layout_custom_state_time_clock_all_day_title"
        app:layout_constraintTop_toTopOf="@+id/textview_layout_custom_state_time_clock_all_day_title"
        android:layout_marginLeft="20px"
        android:contentDescription="@string/string_whole_day_open"
        android:layout_width="1px"
        android:layout_height="1px"/>
    <View
        android:id="@+id/view_close_day_switch"
        app:layout_constraintLeft_toRightOf="@+id/textview_layout_custom_state_time_clock_all_day_title"
        app:layout_constraintTop_toTopOf="@+id/textview_layout_custom_state_time_clock_all_day_title"
        android:layout_marginTop="10px"
        android:layout_marginLeft="30px"
        android:contentDescription="@string/string_whole_day_close"
        android:layout_width="1px"
        android:layout_height="1px"/>

    <View
        android:id="@+id/switchcompat_layout_custom_state_time_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/y_px_1"
        android:layout_marginTop="@dimen/y_px_25"
        android:layout_marginStart="@dimen/x_px_66"
        android:layout_marginEnd="@dimen/x_px_66"
        android:background="#3E08080A"
        app:layout_constraintTop_toBottomOf="@id/textview_layout_custom_state_time_clock_all_day_title" />

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/switchcompat_layout_custom_state_time_picker_bg_1"
        android:layout_width="@dimen/x_px_406"
        android:layout_height="@dimen/y_px_86"
        android:layout_marginStart="@dimen/x_px_66"
        android:layout_marginTop="@dimen/y_px_61"
        android:background="@drawable/bg_popup_select_462_86"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/switchcompat_layout_custom_state_time_line" />

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/switchcompat_layout_custom_state_time_picker_bg_2"
        android:layout_width="@dimen/x_px_406"
        android:layout_height="@dimen/y_px_86"
        android:layout_marginEnd="@dimen/x_px_66"
        android:background="@drawable/bg_popup_select_462_86"
        app:layout_constraintBottom_toBottomOf="@id/switchcompat_layout_custom_state_time_picker_bg_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/switchcompat_layout_custom_state_time_picker_bg_1" />

    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_hour_first"
        android:layout_width="@dimen/x_px_85"
        android:layout_height="@dimen/y_px_214"
        android:layout_marginStart="@dimen/x_px_74"
        android:layout_marginTop="@dimen/y_px_25"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toStartOf="@id/switchcompat_layout_custom_state_time_picker_bg_1"
        app:layout_constraintTop_toBottomOf="@id/textview_layout_custom_state_time_clock_all_day_title" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_hour"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_hour_first"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_hour_first" />

    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_minute_first"
        android:layout_width="@dimen/x_px_85"
        android:layout_height="@dimen/y_px_214"
        android:layout_marginStart="@dimen/x_px_20"
        android:layout_marginTop="@dimen/y_px_25"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toEndOf="@id/textview_layout_custom_state_time_clock_value_unit_1"
        app:layout_constraintTop_toBottomOf="@id/textview_layout_custom_state_time_clock_all_day_title" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_minute"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_minute_first"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_minute_first"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_minute_first" />


    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_title_reach"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_reach"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_minute_first"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_minute_first" />


    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_hour_second"
        android:layout_width="@dimen/x_px_85"
        android:layout_height="@dimen/y_px_214"
        android:layout_marginStart="@dimen/x_px_148"
        android:layout_marginTop="@dimen/y_px_25"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toStartOf="@id/switchcompat_layout_custom_state_time_picker_bg_2"
        app:layout_constraintTop_toBottomOf="@id/textview_layout_custom_state_time_clock_all_day_title" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_hour"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_hour_second"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_hour_second"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_hour_second" />

    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_state_time_clock_minute_second"
        android:layout_width="@dimen/x_px_85"
        android:layout_height="@dimen/y_px_214"
        android:layout_marginStart="@dimen/x_px_20"
        android:layout_marginTop="@dimen/y_px_25"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintStart_toEndOf="@id/textview_layout_custom_state_time_clock_value_unit_3"
        app:layout_constraintTop_toBottomOf="@id/textview_layout_custom_state_time_clock_all_day_title" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_state_time_clock_value_unit_4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/string_layout_custom_state_time_clock_minute"
        android:textColor="@color/color_text_tertiary_label"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintBottom_toBottomOf="@id/numberpicker_layout_custom_state_time_clock_minute_second"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_state_time_clock_minute_second"
        app:layout_constraintTop_toTopOf="@id/numberpicker_layout_custom_state_time_clock_minute_second" />


</androidx.constraintlayout.widget.ConstraintLayout>