<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.dfl.smartscene.bean.main.MySceneBean" />

        <variable
            name="isSelected"
            type="Boolean" />

        <!--<variable-->
        <!--    name="isShowGray"-->
        <!--    type="Boolean" />-->

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="768dp"
        android:layout_height="308dp"
        android:background="@drawable/scene_shape_community_white_radius_12">

        <TextView
            android:id="@+id/tv_scene_name"
            android:layout_width="wrap_content"
            android:layout_height="54dp"
            android:layout_marginStart="48dp"
            android:layout_marginTop="32dp"
            android:text="@{bean.scenario.scenarioInfo.scenarioName}"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="场景名称" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/wll_icon_list"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginStart="48dp"
            android:layout_marginTop="102dp"
            android:layout_marginEnd="34dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_scene_name" />

        <!--        <com.dfl.smartscene.widget.WarpLinearLayout-->
        <!--            scene_action_list="@{bean}"-->
        <!--            android:layout_width="@dimen/px_504"-->
        <!--            android:layout_height="@dimen/px_60"-->
        <!--            android:layout_marginTop="19dp"-->
        <!--            android:background="@drawable/scene_shape_me_condition_action_bg"-->
        <!--            android:paddingStart="@dimen/px_22"-->
        <!--            android:paddingEnd="@dimen/px_22"-->
        <!--            app:grivate="left"-->
        <!--            app:layout_constraintStart_toStartOf="@id/tv_scene_name"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/tv_scene_name" />-->


        <ImageView
            android:id="@+id/iv_select"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginTop="33dp"
            android:layout_marginEnd="48dp"
            android:src="@{isSelected? @drawable/scene_icon_community_box_selected : @drawable/scene_icon_community_box_select,default=@drawable/scene_icon_community_box_select}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--蒙层改为不透明度50%-->
        <!--<View-->
        <!--    android:id="@+id/view_gray"-->
        <!--    android:layout_width="0dp"-->
        <!--    android:layout_height="0dp"-->
        <!--    android:background="@drawable/scene_shape_community_gray_radius_12"-->
        <!--    android:visibility="@{isShowGray? View.VISIBLE:View.GONE}"-->
        <!--    app:layout_constraintBottom_toBottomOf="parent"-->
        <!--    app:layout_constraintEnd_toEndOf="parent"-->
        <!--    app:layout_constraintStart_toStartOf="parent"-->
        <!--    app:layout_constraintTop_toTopOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>