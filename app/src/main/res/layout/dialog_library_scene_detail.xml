<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/x_px_dialog_width"
        android:layout_height="@dimen/y_px_dialog_height"
        android:background="@drawable/bg_common_dialog"
        android:tag="background:drawable:bg_common_dialog"
        android:paddingTop="@dimen/y_px_16"
        android:paddingLeft="@dimen/x_px_16">

        <include layout="@layout/layout_page_title_bar" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="@dimen/y_px_370"
            android:layout_marginTop="@dimen/x_px_88"
            android:fillViewport="true"
            android:scrollbars="none"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingLeft="@dimen/x_px_104"
                android:paddingRight="@dimen/x_px_104">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_88"
                    android:layout_marginTop="@dimen/y_px_40">

                    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                        android:id="@+id/imageview_library_scene_detail_icon"
                        android:layout_width="@dimen/x_px_88"
                        android:layout_height="@dimen/y_px_88"
                        android:scaleType="fitXY"
                        android:src="@mipmap/icon_item_my_scene_default"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dfl.dflcommonlibs.uimodeutil.UITextView
                        android:id="@+id/textview_library_scene_detail_title"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/y_px_42"
                        android:layout_marginLeft="@dimen/x_px_30"
                        android:gravity="center_vertical"
                        android:includeFontPadding="false"
                        android:text="场景标题"
                        android:textColor="@color/color_text_first_label"
                        android:textSize="@dimen/y_px_text_subtitle"
                        app:layout_constraintLeft_toRightOf="@+id/imageview_library_scene_detail_icon"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dfl.dflcommonlibs.uimodeutil.UITextView
                        android:id="@+id/textview_library_scene_detail_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/y_px_40"
                        android:layout_marginLeft="@dimen/x_px_30"
                        android:gravity="center_vertical"
                        android:includeFontPadding="false"
                        android:text="场景描述"
                        android:textColor="@color/color_text_tertiary_label"
                        android:textSize="@dimen/y_px_text_subbody"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/imageview_library_scene_detail_icon" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.dfl.dflcommonlibs.uimodeutil.UIImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_1"
                    android:layout_marginTop="@dimen/y_px_42"
                    android:background="@color/color_divide" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerview_library_scene_detail_condition_state"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:nestedScrollingEnabled="false" />

                </RelativeLayout>

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/x_px_48"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="@dimen/x_px_28"
                    android:includeFontPadding="false"
                    android:text="@string/string_scene_editor_action_title"
                    android:textColor="@color/color_text_first_label"
                    android:textSize="@dimen/y_px_text_subtitle" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_library_scene_detail_action"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/y_px_45"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:nestedScrollingEnabled="false"
                    android:paddingBottom="@dimen/y_px_45"/>

                <com.dfl.dflcommonlibs.uimodeutil.UITextView
                    android:id="@+id/textview_libary_detail_buy_notice"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/y_px_40"
                    android:gravity="center"
                    android:layout_marginBottom="@dimen/y_px_70"
                    android:visibility="gone"
                    android:textSize="@dimen/y_px_text_subbody"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_text_secondary_label"
                    android:ellipsize="end"/>
            </LinearLayout>

        </ScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_library_detail_collect_button"
            android:layout_width="@dimen/x_px_280"
            android:layout_height="@dimen/y_px_96"
            android:layout_marginLeft="@dimen/x_px_213"
            android:layout_marginBottom="@dimen/y_px_56"
            android:background="@drawable/bg_button_positive"
            android:tag="background:drawable:bg_button_positive"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:id="@+id/textview_library_detail_collect_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/string_library_detail_collect_button"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <View
                android:id="@+id/clickarea_library_detail_collect_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/y_px_25"/>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_library_detail_excute_button"
            android:layout_width="@dimen/x_px_280"
            android:layout_height="@dimen/y_px_96"
            android:layout_marginRight="@dimen/x_px_213"
            android:layout_marginBottom="@dimen/y_px_56"
            android:background="@drawable/bg_button_negative"
            android:tag="background:drawable:bg_button_negative"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.dfl.dflcommonlibs.uimodeutil.UITextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/string_library_detail_excute_button"
                android:textColor="@color/color_text_first_label"
                android:textSize="@dimen/y_px_text_body"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>