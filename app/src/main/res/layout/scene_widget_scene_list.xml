<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_widget_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:src="@drawable/scene_img_bg_widget_figure" />

    <FrameLayout
        android:id="@+id/fl_whole_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <!--    <ImageView-->
        <!--        android:id="@+id/iv_empty_msg"-->
        <!--        android:layout_width="match_parent"-->
        <!--        android:layout_height="match_parent"-->
        <!--        android:contentDescription="@null"-->
        <!--        android:src="@drawable/scene_icon_bg_widget_entrance" />-->

        <TextView
            android:id="@+id/tv_empty_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/px_24"
            android:layout_marginTop="@dimen/px_76"
            android:includeFontPadding="false"
            android:text="@string/scene_text_widget_no_data"
            android:textColor="@color/common_lib_text_color_h2"
            android:textSize="@dimen/px_24"
            android:visibility="visible" />


        <LinearLayout
            android:id="@+id/lv_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_145"
            android:layout_marginTop="@dimen/px_75"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_space_1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <FrameLayout
                android:id="@+id/fl_scene_1"
                android:layout_width="@dimen/px_120"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="@dimen/px_72"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:contentDescription="@null"
                    android:src="@drawable/scene_icon_widget_desc1" />

                <TextView
                    android:id="@+id/tv_scene1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginBottom="@dimen/px_20"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="24sp"
                    tools:text="场景一" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_space_2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <FrameLayout
                android:id="@+id/fl_scene_2"
                android:layout_width="@dimen/px_120"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="@dimen/px_72"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:contentDescription="@null"
                    android:src="@drawable/scene_icon_widget_desc3" />

                <TextView
                    android:id="@+id/tv_scene2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginBottom="@dimen/px_20"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="24sp"
                    tools:text="场景二" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_space_3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <FrameLayout
                android:id="@+id/fl_scene_3"
                android:layout_width="@dimen/px_120"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="@dimen/px_72"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:contentDescription="@null"
                    android:src="@drawable/scene_icon_widget_desc2" />

                <TextView
                    android:id="@+id/tv_scene3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginBottom="@dimen/px_20"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/scene_color_text_1"
                    android:textSize="24sp"
                    tools:text="场景三" />
            </FrameLayout>

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_execute_scene"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_52"
            android:layout_gravity="bottom"
            android:layout_marginBottom="24dp"
            android:visibility="gone">

            <Button
                android:id="@+id/btn_start_scene"
                style="@style/ConfirmButtonTheme"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_52"

                android:layout_marginStart="@dimen/px_24"
                android:text="@string/scene_button_common_execute"
                android:textSize="@dimen/px_24" />

            <Button
                android:id="@+id/btn_ignore_scene"
                style="@style/CancelButtonTheme"
                android:layout_width="@dimen/px_164"
                android:layout_height="@dimen/px_52"
                android:layout_marginStart="@dimen/px_32"
                android:text="@string/scene_button_common_ignore"
                android:textSize="@dimen/px_24" />
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_desc"
        android:layout_width="match_parent"

        android:layout_height="match_parent"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_app_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/px_26"
            android:layout_marginTop="@dimen/px_64"
            android:text="@string/scene_text_widget_app_desc"
            android:textColor="@color/common_lib_text_color_h2"
            android:textSize="@dimen/px_24"
            android:visibility="visible" />

        <Button
            android:id="@+id/btn_open_app"
            style="@style/ConfirmButtonTheme"
            android:layout_width="@dimen/px_164"
            android:layout_height="@dimen/px_52"
            android:layout_gravity="bottom|start"
            android:layout_marginStart="@dimen/px_24"
            android:layout_marginBottom="@dimen/px_16"
            android:text="@string/scene_button_widget_open_app"
            android:textSize="@dimen/px_24" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/px_26"
        android:layout_marginTop="@dimen/px_17"
        android:text="@string/scene_app_name"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/px_28" />
</FrameLayout>