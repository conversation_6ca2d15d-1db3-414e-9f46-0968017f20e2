<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:minWidth="@dimen/px_430"
    android:layout_height="@dimen/px_120"
    android:background="@drawable/scene_shape_common_bg_day_bg2_night_bg3_r12"
    android:clickable="true">

    <RadioButton
        android:id="@+id/rb_radio"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_36"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="@dimen/px_48"
        android:button="@drawable/scene_selector_me_radio_check_style"
        android:clickable="false"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/px_28"
        android:paddingEnd="0dp"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_p42r_h3"
        tools:text="打开" />

    <View
        android:id="@+id/view_more"
        android:layout_width="@dimen/px_24"
        android:layout_height="@dimen/px_24"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="@dimen/px_12"
        android:background="@drawable/scene_icon_radio_check_more"
        android:visibility="gone" />
</RelativeLayout>