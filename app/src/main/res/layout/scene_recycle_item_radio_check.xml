<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="844dp"
    android:layout_height="112dp"
    android:background="@drawable/scene_shape_common_bg_day_bg2_night_bg3_r24"
    android:clickable="true">

    <RadioButton
        android:id="@+id/rb_radio"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/scene_height_text_size_h4"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="48dp"
        android:button="@drawable/scene_selector_me_radio_check_style"
        android:clickable="false"
        android:gravity="center_vertical"
        android:paddingStart="20dp"
        android:paddingEnd="0dp"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h4"
        tools:text="打开" />

    <View
        android:id="@+id/view_more"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@drawable/scene_icon_radio_check_more"
        android:visibility="gone" />
</RelativeLayout>