<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="bean"
            type="com.dfl.smartscene.bean.action.CheckListBean" />

    </data>

    <LinearLayout
        android:id="@+id/ll_check"
        style="@style/SelectorLinearLayoutTheme"
        android:layout_marginHorizontal="15dp"
        android:layout_marginVertical="25dp"
        android:background="@{bean.isCheck ? @drawable/scene_shape_edit_check_item_checked : @drawable/scene_shape_edit_check_item_uncheck}"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_left"
            imageId="@{bean.isCheck ?  bean.selectedImgId : bean.reflexIconId }"
            android:layout_width="@dimen/scene_icon_size_normal_icon"
            android:layout_height="@dimen/scene_icon_size_normal_icon"
            android:layout_marginEnd="@dimen/scene_margin_end_selector_checkbox_icon"
            android:visibility="@{bean.reflexIconId==-1? View.GONE: View.VISIBLE }" />
        <!--新版ui被废弃，再次使用以设计稿为准-->
        <!--<TextView-->
        <!--    android:id="@+id/tv_content"-->
        <!--    style="@style/SelectorCheckTextViewTheme"-->
        <!--    android:text='@{bean.content}'-->
        <!--    android:textColor="@{bean.isCheck ? @color/scene_txt_color_in_primary : @color/scene_color_text_1}"-->
        <!--    tools:text="关闭" />-->
        <TextView
            android:id="@+id/tv_content"
            style="@style/SelectorCheckTextViewTheme"
            android:text='@{bean.content}'
            android:textColor="@color/scene_color_text_1"
            tools:text="关闭" />


    </LinearLayout>
</layout>