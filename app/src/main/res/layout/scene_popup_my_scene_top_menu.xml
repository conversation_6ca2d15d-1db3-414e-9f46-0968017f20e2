<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/csl_pop_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/scene_shape_my_pop_menu_mask_bg"
    tools:context="com.dfl.smartscene.ui.overlay.myscene.MySceneMenuTopPopup">

    <LinearLayout
        android:id="@+id/ll_menu_container"
        android:layout_width="@dimen/px_313"
        android:layout_height="@dimen/px_258"
        android:background="@drawable/scene_shape_community_card_menu_r24"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/px_32"
        android:paddingVertical="@dimen/px_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 日志菜单项 -->
        <TextView
            android:id="@+id/tv_log_menu"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_108"
            android:gravity="center"
            android:text="@string/scene_button_me_execution_log"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h5" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_2"
            android:background="@color/scene_color_divider" />

        <!-- 帮助菜单项 -->
        <TextView
            android:id="@+id/tv_help_menu"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_108"
            android:gravity="center"
            android:text="@string/scene_text_me_help_text"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h5" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>