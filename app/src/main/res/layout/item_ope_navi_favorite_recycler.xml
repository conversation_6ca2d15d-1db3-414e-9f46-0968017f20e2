<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clickable="true"
    android:layout_height="@dimen/y_px_144"
    android:layout_marginTop="@dimen/y_px_10"
    android:layout_width="match_parent">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:clickable="false"
        android:id="@+id/imageview_item_ope_navi_favorite_pos_recycler"
        android:layout_height="@dimen/y_px_48"
        android:layout_marginStart="@dimen/x_px_74"
        android:layout_width="@dimen/x_px_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:clickable="false"
        android:id="@+id/textview_item_ope_navi_favorite_pos_name"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x_px_20"
        android:layout_marginTop="@dimen/y_px_19"
        android:layout_width="wrap_content"
        android:textColor="@color/color_navi_name_text"
        android:textSize="@dimen/x_px_32"
        app:layout_constraintStart_toEndOf="@id/imageview_item_ope_navi_favorite_pos_recycler"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:clickable="false"
        android:id="@+id/textview_item_ope_navi_favorite_pos_address"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/y_px_10"
        android:layout_width="wrap_content"
        android:textColor="@color/color_navi_address_text"
        android:textSize="@dimen/x_px_28"
        app:layout_constraintStart_toStartOf="@id/textview_item_ope_navi_favorite_pos_name"
        app:layout_constraintTop_toBottomOf="@id/textview_item_ope_navi_favorite_pos_name" />

    <View
        android:background="@color/gray_text_color"
        android:layout_height="@dimen/y_px_1"
        android:layout_width="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/checkbox_item_ope_navi_favorite_pos_select_state"
        app:layout_constraintStart_toStartOf="@id/imageview_item_ope_navi_favorite_pos_recycler" />

    <com.dfl.dflcommonlibs.uimodeutil.UICheckBox
        android:background="@drawable/drawable_checkbox_state"
        android:button="@null"
        android:checked="true"
        android:clickable="false"
        android:id="@+id/checkbox_item_ope_navi_favorite_pos_select_state"
        android:layout_height="@dimen/y_px_60"
        android:layout_marginEnd="@dimen/x_px_96"
        android:layout_width="@dimen/x_px_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>