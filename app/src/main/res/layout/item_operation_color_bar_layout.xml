<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="@dimen/y_px_56"
    android:layout_width="@dimen/x_px_494">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_height="@dimen/y_px_56"
        android:layout_width="@dimen/x_px_494"
        android:clipChildren="false">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/ic_radiogroup_bg"
            android:tag="background:drawable:ic_radiogroup_bg"
            />
        <!--    <View-->
        <!--        android:background="@drawable/ic_color_bar_second_bg"-->
        <!--        android:layout_height="@dimen/y_px_36"-->
        <!--        android:layout_width="@dimen/x_px_478"-->
        <!--        app:layout_constraintBottom_toBottomOf="parent"-->
        <!--        app:layout_constraintEnd_toEndOf="parent"-->
        <!--        app:layout_constraintStart_toStartOf="parent"-->
        <!--        app:layout_constraintTop_toTopOf="parent" />-->

        <com.dfl.smartscene.ccs.view.weight.LongPressButton
            android:background="@drawable/customize_edit_btn_add_n"
            android:id="@+id/longpressbutton_item_color_bar_reduce"
            android:layout_height="@dimen/y_px_40"
            android:layout_width="@dimen/x_px_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.smartscene.ccs.view.weight.ColorSlideBar
            android:id="@+id/longpressbutton_item_color_bar_seekbar"
            android:layout_height="@dimen/y_px_36"
            android:layout_width="@dimen/x_px_426"
            android:progress="30"
            android:thumbOffset="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.smartscene.ccs.view.weight.LongPressButton
            android:background="@drawable/customize_edit_btn_add_r"
            android:id="@+id/longpressbutton_item_color_bar_add"
            android:layout_height="@dimen/x_px_40"
            android:layout_width="@dimen/y_px_40"
            android:layout_marginEnd="@dimen/x_px_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>