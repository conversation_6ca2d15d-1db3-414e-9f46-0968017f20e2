<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/px_581"
    android:layout_height="@dimen/px_100"
    android:layout_marginEnd="@dimen/px_24"
    android:background="@drawable/scene_shape_detail_condition_able_r12">

    <LinearLayout
        android:id="@+id/ll_condition_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_condition_item_icon"
            android:layout_width="@dimen/px_48"
            android:layout_height="@dimen/px_48"
            android:layout_marginStart="@dimen/px_20"
            android:scaleType="fitCenter"
            tools:src="@drawable/scene_icon_me_trigger_door_upperleft_door" />

        <TextView
            android:id="@+id/tv_condition_item_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/px_10"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:lines="2"
            android:maxLines="2"
            android:text="@string/scene_text_edit_add_condition_title"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_p42r_h3" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_condition_item_delete"
            android:layout_width="@dimen/px_42"
            android:layout_height="@dimen/px_42"
            android:layout_marginEnd="@dimen/px_29"
            app:srcCompat="@drawable/scene_icon_me_condition_delete" />
    </LinearLayout>

    <View
        android:id="@+id/v_trigger_condition_sign_item"
        android:layout_width="@dimen/px_12"
        android:layout_height="@dimen/px_12"
        android:layout_marginStart="@dimen/px_10"
        android:layout_marginTop="@dimen/px_10"
        android:background="@drawable/scene_shape_trigger_condition_sign_point"
        app:layout_constraintStart_toStartOf="@+id/ll_condition_item"
        app:layout_constraintTop_toTopOf="@+id/ll_condition_item" />
</androidx.constraintlayout.widget.ConstraintLayout>