<?xml version="1.0" encoding="utf-8"?><!--发起导航和位置合并在一个xml-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/px_308">

    <ImageView
        android:id="@+id/view_empty_img"
        android:layout_width="@dimen/px_156"
        android:layout_height="@dimen/px_116"
        android:layout_marginTop="@dimen/px_68"
        android:scaleType="centerCrop"
        android:src="@drawable/scene_img_common_nodata"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="@dimen/px_46"
        android:layout_marginTop="@dimen/px_8"
        android:gravity="center"
        android:text="@string/scene_text_location_favorites_dialog_empty_view2"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_p42r_h3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_empty_img" />
</androidx.constraintlayout.widget.ConstraintLayout>