<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogThemeP42R"
        android:layout_height="@dimen/px_490"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_weather_title"
            style="@style/SmallDialogTitleThemeP42R"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题" />

        <TextView
            android:id="@+id/tv_single_wheel_title_desc"
            style="@style/SmallDialogSubTitleThemeP42R"
            android:layout_height="@dimen/px_36"
            android:textSize="@dimen/scene_text_size_h7"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_weather_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="=====标题描述=====" />

        <!--            WheelView选中位置白带样式-->
        <View
            style="@style/WheelViewSelectViewTheme"
            android:layout_width="@dimen/px_616"
            app:layout_constraintBottom_toBottomOf="@+id/wheel_weather"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/wheel_weather" />

        <!--3d滚轮设置高度不起作用会根据设置到itemHeight重绘,例如itemHeight=96,整体高度为244,手动设置间距-->
        <com.dfl.smartscene.widget.wheel.WheelView
            android:id="@+id/wheel_weather"
            style="@style/WheelViewFiveItemStyle"
            android:layout_marginHorizontal="64dp"
            app:layout_constraintBottom_toTopOf="@id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_weather_title" />

        <TextView
            android:id="@+id/tv_single_wheel_left_desc"
            style="@style/WheelViewTextTheme"
            android:layout_marginStart="@dimen/px_256"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            android:text="@string/scene_text_common_more_than"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/wheel_weather"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/wheel_weather" />

        <include
            android:layout_marginTop="@dimen/px_28"
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom_p42r"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>