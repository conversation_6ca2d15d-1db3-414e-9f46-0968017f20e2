<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_view"
            style="@style/SmallDialogTheme"
            android:layout_height="776dp"
            android:layout_marginTop="76dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                style="@style/SmallDialogTitleTheme"
                android:text="@string/scene_text_action_initiate_navigation"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_desc"
                style="@style/SmallDialogSubTitleTheme"
                android:text="@string/scene_text_start_navi_title_desc2"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_select_location"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/scene_height_text_size_h5"
                android:layout_marginStart="76dp"
                android:layout_marginTop="246dp"
                android:gravity="center"
                android:text="@string/scene_text_location_select"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_h5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_select_distance"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/scene_height_text_size_h5"
                android:layout_marginTop="165dp"
                android:gravity="center"
                android:text="@string/scene_text_range_radius"
                android:textColor="@color/scene_color_text_3"
                android:textSize="@dimen/scene_text_size_h5"
                app:layout_constraintStart_toStartOf="@id/tv_select_location"
                app:layout_constraintTop_toBottomOf="@+id/tv_select_location" />

            <CheckedTextView
                android:id="@+id/ctv_go_collect_txt"
                android:layout_width="327dp"
                android:layout_height="112dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="66dp"
                android:background="@drawable/scene_layer_common_bg_jump_radio"
                android:drawableStart="@drawable/scene_selector_me_radio_check_style"
                android:drawablePadding="24dp"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:paddingHorizontal="48dp"
                android:text="@string/scene_text_favorites"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4"
                app:layout_constraintStart_toEndOf="@+id/tv_select_location"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />

            <CheckedTextView
                android:id="@+id/ctv_go_some_where_txt"
                android:layout_width="327dp"
                android:layout_height="112dp"
                android:layout_marginStart="30dp"
                android:background="@drawable/scene_layer_common_bg_jump_radio"
                android:drawableStart="@drawable/scene_selector_me_radio_check_style"
                android:drawablePadding="24dp"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:paddingHorizontal="48dp"
                android:text="@string/scene_text_select_points"
                android:textColor="@color/scene_color_text_1"
                android:textSize="@dimen/scene_text_size_h4"
                app:layout_constraintStart_toEndOf="@id/ctv_go_collect_txt"
                app:layout_constraintTop_toTopOf="@id/ctv_go_collect_txt" />
            <!--宽度=ui宽度加上两边间距240，marginStart=ui的marginStart-120-->
            <com.dfl.smartscene.widget.seekbar.CustomSeekBar
                android:id="@+id/sb_distance"
                android:layout_width="900dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="116dp"
                android:layout_marginTop="40dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ctv_go_collect_txt" />

            <include
                android:id="@+id/include_dialog_bottom"
                layout="@layout/scene_layout_dialog_bottom_button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scene_height_dialog_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>