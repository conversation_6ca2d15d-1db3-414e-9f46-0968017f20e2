<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/px_120">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_edit_skill_icon_item"
        android:layout_width="@dimen/scene_icon_size_me_icon"
        android:layout_height="@dimen/scene_icon_size_me_icon"
        android:scaleType="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/tv_ellipsis_item"
        android:layout_width="@dimen/scene_icon_size_me_icon"
        android:layout_height="@dimen/scene_icon_size_me_icon"
        android:scaleType="centerInside"
        android:src="@drawable/scene_icon_common_card_more"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>