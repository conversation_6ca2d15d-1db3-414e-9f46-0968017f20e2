<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_add_action"
        android:layout_width="@dimen/px_278"
        android:layout_height="@dimen/px_162"
        android:background="@drawable/scene_shape_detail_condition_able_r12">

        <!--这个不能删，代码问题，必须有-->
        <ImageView
            android:id="@+id/iv_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="@dimen/px_278"
            android:layout_height="@dimen/px_162"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_add_action"
            android:layout_width="@dimen/px_48"
            android:layout_height="@dimen/px_48"
            android:layout_marginTop="@dimen/px_33"
            android:scaleType="centerCrop"
            android:src="@drawable/scene_icon_me_action_add"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_add_action_desc_item"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_81"
            android:layout_marginTop="@dimen/px_6"
            android:gravity="center"
            android:textColor="@color/scene_color_text_3"
            android:textSize="@dimen/scene_text_size_p42r_h3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_add_action"
            tools:text="添加动作" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>