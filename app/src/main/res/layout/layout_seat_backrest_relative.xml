<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_seat_backrest"
    android:clipChildren="false"
    android:layout_width="@dimen/x_px_400"
    android:layout_height="@dimen/y_px_400">

    <!--Driver-->
    <RelativeLayout
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:visibility="invisible"
        android:id="@+id/seat_driver_backrest_layout">

        <!-- Driver seat image -->
        <com.iauto.uicontrol.ImageBase
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:imageString="@drawable/bg_set_waistdragleft">
        </com.iauto.uicontrol.ImageBase>

        <!-- Driver, line  -->
        <!-- Driver, line front/rear -->
        <com.iauto.uicontrol.ImageBase
            android:layout_marginLeft="@dimen/x_px_57"
            android:layout_marginTop="@dimen/y_px_225"
            android:layout_width="@dimen/x_px_205"
            android:layout_height="@dimen/y_px_15"
            app:imageString="@drawable/bg_set_adjustmentlinerleft05">
        </com.iauto.uicontrol.ImageBase>
        <!-- Driver, line up/down -->
        <com.iauto.uicontrol.ImageBase
            android:layout_marginLeft="@dimen/x_px_130"
            android:layout_marginTop="@dimen/y_px_170"
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_98"
            app:imageString="@drawable/bg_set_adjustmentlinerleft06">
        </com.iauto.uicontrol.ImageBase>

        <!-- Driver, Animation part -->
        <com.iauto.uicontrol.ImageBase
            android:id="@+id/image_driver_seat_backrest_part"
            android:layout_marginLeft="@dimen/x_px_108"
            android:layout_marginTop="@dimen/y_px_198"
            android:layout_width="@dimen/x_px_100"
            android:layout_height="@dimen/y_px_100"
            app:imageString="@drawable/bg_set_waistdraglightleft">
        </com.iauto.uicontrol.ImageBase>

        <!-- Driver, button -->
        <!-- Driver, button that backrest move to front -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_driver_seat_backrest_front"
            android:layout_marginLeft="@dimen/x_px_250"
            android:layout_marginTop="@dimen/y_px_202"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo4">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Driver, button that backrest move to rear -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_driver_seat_backrest_rear"
            android:layout_marginLeft="@dimen/x_px_neg_4"
            android:layout_marginTop="@dimen/y_px_188"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo3">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Driver, button that backrest move to up -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_driver_seat_backrest_up"
            android:layout_marginLeft="@dimen/x_px_96"
            android:layout_marginTop="@dimen/y_px_113"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo1">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Driver, button that backrest move to down -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_driver_seat_backrest_down"
            android:layout_marginLeft="@dimen/x_px_135"
            android:layout_marginTop="@dimen/y_px_255"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo2">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
    </RelativeLayout>

    <!-- Copilot -->
    <RelativeLayout
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:visibility="invisible"
        android:id="@+id/seat_copilot_backrest_layout">

        <!-- Copilot seat image -->
        <com.iauto.uicontrol.ImageBase
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:imageString="@drawable/bg_set_waistdragright">
        </com.iauto.uicontrol.ImageBase>

        <!-- Copilot, line  -->
        <!-- Copilot, line front/rear -->
        <com.iauto.uicontrol.ImageBase
            android:layout_marginLeft="@dimen/x_px_138"
            android:layout_marginTop="@dimen/y_px_225"
            android:layout_width="@dimen/x_px_205"
            android:layout_height="@dimen/y_px_15"
            app:imageString="@drawable/bg_set_adjustmentlinerright05">
        </com.iauto.uicontrol.ImageBase>
        <!-- Copilot, line up/down -->
        <com.iauto.uicontrol.ImageBase
            android:layout_marginLeft="@dimen/x_px_228"
            android:layout_marginTop="@dimen/y_px_170"
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_98"
            app:imageString="@drawable/bg_set_adjustmentlinerright06">
        </com.iauto.uicontrol.ImageBase>

        <!-- Copilot, Animation part -->
        <com.iauto.uicontrol.ImageBase
            android:id="@+id/image_copilot_seat_backrest_part"
            android:layout_marginLeft="@dimen/x_px_192"
            android:layout_marginTop="@dimen/y_px_198"
            android:layout_width="@dimen/x_px_100"
            android:layout_height="@dimen/y_px_100"
            app:imageString="@drawable/bg_set_waistdraglightright">
        </com.iauto.uicontrol.ImageBase>

        <!-- Copilot, button -->
        <!-- Copilot, button that backrest move to front -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_copilot_seat_backrest_front"
            android:layout_marginLeft="@dimen/x_px_78"
            android:layout_marginTop="@dimen/y_px_202"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo8">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Copilot, button that backrest move to rear -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_copilot_seat_backrest_rear"
            android:layout_marginLeft="@dimen/x_px_332"
            android:layout_marginTop="@dimen/y_px_188"
            android:layout_marginRight="@dimen/x_px_neg_4"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo7">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Copilot, button that backrest move to up -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_copilot_seat_backrest_up"
            android:layout_marginLeft="@dimen/x_px_232"
            android:layout_marginTop="@dimen/y_px_113"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo6">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
        <!-- Copilot, button that backrest move to down -->
        <com.iauto.uicontrol.ButtonBase
            android:id="@+id/button_copilot_seat_backrest_down"
            android:layout_marginLeft="@dimen/x_px_193"
            android:layout_marginTop="@dimen/y_px_255"
            android:layout_width="@dimen/x_px_72"
            android:layout_height="@dimen/y_px_72"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginLeft="@dimen/x_px_12"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_width="@dimen/x_px_48"
                android:layout_height="@dimen/y_px_48"
                app:hasAlphaMask="true"
                app:imageString="@drawable/btn_set_arrowyaotuo5">
            </com.iauto.uicontrol.ImageBase>
        </com.iauto.uicontrol.ButtonBase>
    </RelativeLayout>
</RelativeLayout>