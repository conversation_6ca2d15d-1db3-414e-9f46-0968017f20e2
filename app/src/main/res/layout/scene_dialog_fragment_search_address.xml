<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    tools:context="com.dfl.smartscene.ui.overlay.apply.SearchAddressDialogFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_height="wrap_content"
        android:layout_marginTop="124dp"
        android:minHeight="398dp"
        android:paddingBottom="58dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="发起导航" />

        <ImageView
            android:id="@+id/iv_search_address"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginStart="50dp"
            android:src="@drawable/scene_icon_common_close"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title" />

        <!--        途经点-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_mid_address"
            android:layout_width="0dp"
            android:layout_height="128dp"
            android:layout_marginHorizontal="64dp"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_wayside_address_close"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginStart="48dp"
                android:src="@drawable/scene_icon_common_clear_text1"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginEnd="48dp"
                android:background="@drawable/scene_shape_edit_address_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="48dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_wayside_address_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/scene_icon_me_action_navigation_gowhere_end" />

                <com.dfl.smartscene.widget.ClearEditText
                    android:id="@+id/et_edit_input_mid"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:cursorVisible="true"
                    android:hint="@string/scene_edit_input_hint_wayside_address"
                    android:maxLength="30"
                    android:paddingStart="20dp"
                    android:paddingEnd="48dp"
                    android:selectAllOnFocus="true"
                    android:singleLine="true"
                    android:textColor="@color/scene_color_text_1"
                    android:textColorHint="@color/scene_color_text_3"
                    android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                    android:textSize="@dimen/scene_text_size_h5" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        目标地-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_end_address"
            android:layout_width="0dp"
            android:layout_height="128dp"
            android:layout_marginHorizontal="64dp"
            android:layout_marginTop="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_mid_address">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_close_end_address"
                android:layout_width="@dimen/scene_icon_size_normal_icon"
                android:layout_height="@dimen/scene_icon_size_normal_icon"
                android:layout_marginStart="48dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ll_end_address"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/scene_icon_common_clear_text1" />

            <LinearLayout
                android:id="@+id/ll_end_address"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/scene_shape_edit_address_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="48dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_close_end_address"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_input_end"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/scene_icon_me_action_navigation_gowhere_end"
                    android:visibility="visible" />

                <com.dfl.smartscene.widget.ClearEditText
                    android:id="@+id/et_edit_input_end"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:cursorVisible="true"
                    android:hint="@string/scene_edit_input_hint_destination_address"
                    android:maxLength="30"
                    android:paddingStart="24dp"
                    android:paddingEnd="40dp"
                    android:singleLine="true"
                    android:textColor="@color/scene_color_text_1"
                    android:textColorHint="@color/scene_color_text_3"
                    android:textCursorDrawable="@drawable/scene_shape_edit_cursor_bg"
                    android:textSize="@dimen/scene_text_size_h5" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_mid_address"
            android:layout_width="0dp"
            android:layout_height="128dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/scene_shape_edit_address_bg"
            android:drawableStart="@drawable/scene_icon_me_action_navigation_gowhere_wayside"
            android:drawablePadding="22dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="48dp"
            android:text="@string/scene_edit_action_add_wayside_address"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h5"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/cl_end_address"
            app:layout_constraintStart_toStartOf="@id/cl_end_address"
            app:layout_constraintTop_toBottomOf="@id/cl_end_address" />

        <!--        <include-->
        <!--            android:id="@+id/include_dialog_bottom"-->
        <!--            layout="@layout/scene_layout_dialog_bottom_button"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="@dimen/scene_height_dialog_bottom"-->
        <!--            android:layout_marginTop="14dp"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/tv_mid_address" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sl_search_address"
        style="@style/ScrollBarStyle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="460dp"
        android:background="@color/scene_color_day_bg3_night_bg1"
        android:fillViewport="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.dfl.smartscene.widget.recycleview.HeightRecycleView
            android:id="@+id/rv_search_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>