<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <data>

    </data>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/text_view_toast_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/y_px_136"
            android:layout_gravity="center_vertical"
            android:minWidth="@dimen/x_px_440"
            android:background="@drawable/com_toastbg"
            android:gravity="center"
            android:textColor="@color/color_text_first_label"
            android:textSize="@dimen/font_size_28"/>

    </RelativeLayout>
</layout>