<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="298dp"
    android:layout_height="260dp"
    android:layout_marginHorizontal="26dp">

    <!--卡片背景-->
    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <View
        android:id="@+id/v_edit_action_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/scene_shape_detail_condition_able_r24" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_action_item_serial_no"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_marginStart="26dp"
            android:layout_marginTop="27dp"
            android:gravity="center"
            android:textColor="@color/scene_color_text_4"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="1" />


        <TextView
            android:id="@+id/tv_action_item_name"
            android:layout_width="0dp"
            android:layout_height="49dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="14dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:maxLines="1"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_action_item_serial_no"
            tools:ignore="SpUsage"
            tools:text="大灯打招呼灯语" />

        <TextView
            android:id="@+id/tv_action_item_detail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="98dp"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:maxLength="20"
            android:maxLines="2"
            android:textColor="@color/scene_color_text_4"
            android:textSize="@dimen/scene_text_size_h6"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_action_item_name"
            tools:ignore="SpUsage"
            tools:text="自定义自定义自定义自定义自定义自定义" />

        <!--    颜色块放在中间就行了-->
        <View
            android:id="@+id/v_ambient_light_bg"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@color/scene_mean_color_success"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_action_item_detail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_action_item_detail" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_action_item_remove"
            android:layout_width="@dimen/px_48"
            android:layout_height="@dimen/px_48"
            android:layout_marginTop="27dp"
            android:layout_marginEnd="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/scene_icon_me_condition_delete" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>