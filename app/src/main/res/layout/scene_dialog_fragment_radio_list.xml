<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SmallDialogTheme"
        android:layout_marginTop="76dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_check_list_title"
            style="@style/SmallDialogTitleTheme"
            app:layout_constraintBottom_toTopOf="@+id/rv_check_list"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="spread_inside"
            tools:text="挡位" />

        <TextView
            android:id="@+id/tv_desc"
            style="@style/SmallDialogSubTitleTheme"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_check_list_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="----标题描述----" />

        <TextView
            android:id="@+id/tv_all_select"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/scene_height_text_size_h4"
            android:layout_marginEnd="64dp"
            android:text="@string/scene_text_common_all_select"
            android:textColor="@color/scene_primary_color_highlight"
            android:textSize="@dimen/scene_text_size_h4"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tv_check_list_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_check_list_title"
            tools:text="全选" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_check_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="16dp"
            app:layout_constraintBottom_toTopOf="@+id/include_dialog_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_check_list_title" />

        <include
            android:id="@+id/include_dialog_bottom"
            layout="@layout/scene_layout_dialog_bottom_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/scene_height_dialog_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_check_list" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>