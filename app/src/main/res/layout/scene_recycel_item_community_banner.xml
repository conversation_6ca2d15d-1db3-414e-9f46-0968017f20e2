<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--    banner 需要设置match_parent，否则会报错-->
    <!--    android:layout_width="1015dp"-->
    <!--    android:layout_height="282dp"-->

    <com.dfl.smartscene.widget.CornerImageView
        android:id="@+id/iv_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_left_mix"
        android:layout_width="640dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/transparent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_right_mix"
        android:layout_width="640dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/transparent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--    以下是点赞下载信息相关-->
    <!--    <com.dfl.android.animationlib.ScaleImageButton-->
    <!--        android:id="@+id/tv_downloads"-->
    <!--        style="@style/SecondaryBigBtnColorStyle"-->
    <!--        android:layout_width="180dp"-->
    <!--        android:layout_height="60dp"-->
    <!--        android:layout_marginStart="76dp"-->
    <!--        android:layout_marginBottom="119dp"-->
    <!--        android:visibility="gone"-->
    <!--        app:backgroundBaseColor="#E0EBEDF0"-->
    <!--        app:backgroundBaseColorClicked="#FFC7C9CC"-->
    <!--        app:backgroundCornerDegree="10"-->
    <!--        app:imgHight="36dp"-->
    <!--        app:imgSrc="@drawable/scene_icon_community_card_down_banner"-->
    <!--        app:imgSrcClicked="@drawable/scene_icon_community_card_down_banner"-->
    <!--        app:imgWidth="36dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:spaceWidth="20dp"-->
    <!--        app:textColor="#1D1D1E"-->
    <!--        app:textColorClicked="#1D1D1E"-->
    <!--        app:textDisableColor="#2E1D1D1E"-->
    <!--        app:textSize="@dimen/scene_text_size_h6"-->
    <!--        tools:text="100" />-->


    <!--    <com.dfl.android.animationlib.ScaleImageButton-->
    <!--        android:id="@+id/tv_likes"-->
    <!--        style="@style/SecondaryBigBtnColorStyle"-->
    <!--        android:layout_width="180dp"-->
    <!--        android:layout_height="60dp"-->
    <!--        android:layout_marginStart="30dp"-->
    <!--        android:visibility="gone"-->
    <!--        app:backgroundBaseColor="#E0EBEDF0"-->
    <!--        app:backgroundBaseColorClicked="#FFC7C9CC"-->
    <!--        app:backgroundCornerDegree="10"-->
    <!--        app:imgAutoPlay="false"-->
    <!--        app:imgHight="36dp"-->
    <!--        app:imgIsAnimation="true"-->
    <!--        app:imgSrc="@raw/scene_likes_banner"-->
    <!--        app:imgWidth="36dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/tv_downloads"-->
    <!--        app:layout_constraintStart_toEndOf="@id/tv_downloads"-->
    <!--        app:layout_constraintTop_toTopOf="@id/tv_downloads"-->
    <!--        app:spaceWidth="20dp"-->
    <!--        app:textColor="#1D1D1E"-->
    <!--        app:textColorClicked="#1D1D1E"-->
    <!--        app:textDisableColor="#2E1D1D1E"-->
    <!--        app:textSize="@dimen/scene_text_size_h6"-->
    <!--        tools:text="100" />-->


    <!--    <TextView-->
    <!--        android:id="@+id/tv_scene_desc"-->
    <!--        android:layout_width="510dp"-->
    <!--        android:layout_height="48dp"-->
    <!--        android:layout_marginBottom="60dp"-->
    <!--        android:ellipsize="end"-->
    <!--        android:gravity="start|center_vertical"-->
    <!--        android:lines="1"-->
    <!--        android:textColor="#A31D1D1E"-->
    <!--        android:textSize="@dimen/scene_text_size_h5"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/tv_downloads"-->
    <!--        app:layout_constraintStart_toStartOf="@id/tv_downloads"-->
    <!--        tools:text="开启导航至公司，快乐放音频节日快快乐放音频节日快" />-->


    <!--    <TextView-->
    <!--        android:id="@+id/tv_scene_name"-->
    <!--        android:layout_width="510dp"-->
    <!--        android:layout_height="86dp"-->
    <!--        android:layout_marginBottom="8dp"-->
    <!--        android:ellipsize="end"-->
    <!--        android:gravity="start|center_vertical"-->
    <!--        android:lines="1"-->
    <!--        android:textColor="#FF1D1D1E"-->
    <!--        android:textSize="@dimen/scene_text_size_h1"-->
    <!--        android:textStyle="bold"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/tv_scene_desc"-->
    <!--        app:layout_constraintStart_toStartOf="@id/tv_downloads"-->
    <!--        tools:text="奉旨上班最长十个字奉旨上班最长十个字" />-->

    <!-- 以下是用户关注相关代码-->
    <!--    &lt;!&ndash; 毛玻璃&ndash;&gt;-->
    <!--    <net.center.blurview.ShapeBlurView-->
    <!--        android:id="@+id/view_bottom"-->
    <!--        android:layout_width="196dp"-->
    <!--        android:layout_height="234dp"-->
    <!--        android:layout_marginEnd="@dimen/px_32"-->
    <!--        android:visibility="gone"-->
    <!--        app:blur_corner_radius="12dp"-->
    <!--        app:blur_mode="rectangle"-->
    <!--        app:blur_overlay_color="@color/scene_community_bg_banner_right_color"-->
    <!--        app:blur_radius="5dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent" />-->
    <!--    -->
    <!--    <com.dfl.smartscene.widget.CornerImageView-->
    <!--        android:id="@+id/iv_user_heard"-->
    <!--        android:layout_width="54dp"-->
    <!--        android:layout_height="54dp"-->
    <!--        android:layout_marginTop="@dimen/px_40"-->
    <!--        android:background="@color/transparent"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/view_bottom"-->
    <!--        app:layout_constraintStart_toStartOf="@id/view_bottom"-->
    <!--        app:layout_constraintTop_toTopOf="@id/view_bottom" />-->
    <!--    -->
    <!--    <TextView-->
    <!--        android:id="@+id/tv_user_name"-->
    <!--        android:layout_width="@dimen/px_0"-->
    <!--        android:layout_height="@dimen/px_30"-->
    <!--        android:layout_marginTop="@dimen/px_16"-->
    <!--        android:gravity="center"-->
    <!--        android:textColor="@color/scene_community_text_banner_title_color"-->
    <!--        android:textSize="@dimen/px_22"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/view_bottom"-->
    <!--        app:layout_constraintStart_toStartOf="@id/view_bottom"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/iv_user_heard"-->
    <!--        tools:text="飞车高手" />-->

    <!--    <Button-->
    <!--        android:id="@+id/btn_subscribe"-->
    <!--        android:layout_width="@dimen/px_74"-->
    <!--        android:layout_height="@dimen/px_34"-->
    <!--        android:layout_marginTop="@dimen/px_24"-->
    <!--        android:background="@drawable/scene_shape_community_banner_subscribe"-->
    <!--        android:text="关注"-->
    <!--        android:textColor="@color/scene_primary_color"-->
    <!--        android:textSize="@dimen/px_18"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/view_bottom"-->
    <!--        app:layout_constraintStart_toStartOf="@id/view_bottom"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/tv_user_name" />-->
    <!--闪烁-->
    <com.dfl.android.animationlib.LoadingImageView
        android:id="@+id/me_loading_view"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:scaleType="centerCrop"
        android:visibility="gone"
        android:layout_marginBottom="249dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:lottieSrc="@raw/lk1a_120loading_light"
        app:lottieSrcNight="@raw/lk1a_120loading_dark" />
</androidx.constraintlayout.widget.ConstraintLayout>