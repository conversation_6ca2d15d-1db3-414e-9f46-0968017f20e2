<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/sl_update"
        style="@style/SmartRefreshLayoutTheme"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">


        <com.dfl.android.animationlib.scrollView.FadeRecyclerView
            android:id="@+id/rv_community"
            style="@style/FadeRecyclerViewTheme1A"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:paddingHorizontal="20dp" />

        <com.dfl.smartscene.widget.ClassicsFooter4All
            android:id="@+id/classicsFooter"
            style="@style/SmartRefreshClassicsTheme.FooterTheme" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</layout>