<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="horizontal"
    android:layout_marginVertical="@dimen/x_px_26">

    <ImageView
        android:id="@+id/header_progress"
        android:src="@drawable/icon_refresh_loading"
        android:layout_gravity="center_vertical"
        android:layout_width="@dimen/x_px_32"
        android:layout_height="@dimen/x_px_32"/>

    <ImageView
        android:id="@+id/header_icon"
        android:layout_gravity="center_vertical"
        android:src="@drawable/icon_refresh_down"
        android:layout_width="@dimen/x_px_32"
        android:layout_height="@dimen/x_px_32"/>

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:id="@+id/header_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/x_px_16"
        android:textColor="@color/color_text_loading_fresh"
        android:textSize="@dimen/font_size_34"/>
</LinearLayout>
