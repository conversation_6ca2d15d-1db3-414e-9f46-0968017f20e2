<?xml version="1.0" encoding="utf-8" standalone="no"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_item_aiot_sale"
    android:layout_height="@dimen/y_px_240"
    android:layout_width="@dimen/x_px_324"
    android:tag="background:drawable:bg_item_aiot_sale">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:id="@+id/imageview_item_operation_aiot_sale_pic"
        android:layout_height="@dimen/y_px_158"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dfl.dflcommonlibs.uimodeutil.UITextView
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:id="@+id/textview_item_aiot_sale_name"
        android:layout_height="@dimen/y_px_48"
        android:layout_marginBottom="@dimen/y_px_17"
        android:layout_width="match_parent"
        android:maxLines="1"
        android:paddingLeft="@dimen/x_px_18"
        android:textColor="@color/color_text_first_label"
        android:textSize="@dimen/y_px_text_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>