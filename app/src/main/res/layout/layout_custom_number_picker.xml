<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.iauto.uicontrol.ImageBase
        android:id="@+id/numberpicker_layout_custom_number_compare_condition_time_bg_1"
        android:layout_width="@dimen/x_px_546"
        android:layout_height="@dimen/y_px_86"
        android:background="@drawable/number_picaker_system_popup_selectbg_s"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <com.dfl.smartscene.ccs.view.weight.CustomNumberPicker
        android:id="@+id/numberpicker_layout_custom_number_picker_value"
        android:layout_width="@dimen/x_px_100"
        android:layout_height="@dimen/y_px_200"
        app:layout_constraintStart_toStartOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_1"
        app:layout_constraintEnd_toEndOf="@id/numberpicker_layout_custom_number_compare_condition_time_bg_1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:descendantFocusability="blocksDescendants" />

    <com.iauto.uicontrol.TextBase
        android:id="@+id/textview_layout_custom_number_picker_unit"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:layout_constraintStart_toEndOf="@id/numberpicker_layout_custom_number_picker_value"
        android:textSize="@dimen/font_size_28"
        android:gravity="center"
        android:textColor="@color/color_text_selected" />

</merge>