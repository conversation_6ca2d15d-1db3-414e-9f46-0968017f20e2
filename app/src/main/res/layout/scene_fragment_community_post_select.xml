<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/view_dis_show"
            android:layout_width="0dp"
            android:layout_height="400dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_content"
            android:layout_width="0dp"
            android:layout_height="1062dp"
            android:background="@drawable/scene_shape_community_select_scene_bg_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="136dp"
            android:layout_height="140dp"
            android:layout_marginTop="18dp"
            android:paddingStart="76dp"
            android:src="@drawable/scene_icon_common_arrow_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_content"
            tools:ignore="RtlSymmetry" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="140dp"
            android:gravity="start|center_vertical"
            android:paddingStart="24dp"
            android:text="@string/scene_text_community_select"
            android:textColor="@color/scene_color_text_1"
            android:textSize="@dimen/scene_text_size_h3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_back"
            app:layout_constraintTop_toTopOf="@id/iv_back" />

        <com.dfl.android.animationlib.scrollView.OverScrollView
            style="@style/OverScrollViewTheme1A.WithScrollBar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:overScrollMode="never"
            android:paddingHorizontal="76dp"
            android:paddingBottom="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_back">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_select"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never" />
        </com.dfl.android.animationlib.scrollView.OverScrollView>


        <View
            android:id="@+id/view_bottom"
            android:layout_width="0dp"
            android:layout_height="204dp"
            android:background="@drawable/scene_shape_community_bottom_bg"
            android:clickable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.dfl.android.animationlib.ScaleImageButton
            android:id="@+id/tv_next"
            style="@style/PrimaryBigBtnColorStyle"
            android:layout_width="502dp"
            android:clickable="true"
            app:backgroundDisableColor="@color/scene_color_bg_disable_community_next"
            app:enable="false"
            app:layout_constraintBottom_toBottomOf="@id/view_bottom"
            app:layout_constraintEnd_toEndOf="@id/view_bottom"
            app:layout_constraintStart_toStartOf="@id/view_bottom"
            app:layout_constraintTop_toTopOf="@id/view_bottom"
            app:text="@string/scene_text_community_next_step"
            app:textDisableColor="@color/scene_color_text_disable_community_next" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>