<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/px_398"
    android:layout_height="@dimen/px_80"
    android:layout_marginEnd="@dimen/px_20"
    android:layout_marginBottom="@dimen/px_20"
    android:background="@drawable/scene_shape_condition_fragment_item_r12"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_condition_reflex_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/px_28"
        android:scaleType="center"
        tools:src="@drawable/scene_icon_time_system_clock" />

    <TextView
        android:id="@+id/tv_condition_reflex_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/px_13"
        android:gravity="center_vertical"
        android:textColor="@color/scene_color_text_1"
        android:textSize="@dimen/scene_text_size_h7"
        tools:text="时间点" />
</LinearLayout>
