<?xml version="1.0" encoding="utf-8" standalone="no"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:orientation="vertical">

    <com.dfl.dflcommonlibs.uimodeutil.UIImageView
        android:alpha="0.8"
        android:background="@drawable/drawable_scene_bg_popup_bg"
        android:layout_height="match_parent"
        android:layout_width="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/bg_dialog"
        android:layout_gravity="center"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/common_width_760dp"
        android:tag="background:drawable:bg_dialog">

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:gravity="center"
            android:id="@+id/text_view_default_double_dialog_title"
            android:layout_height="@dimen/common_height_48dp"
            android:layout_marginEnd="@dimen/common_width_88dp"
            android:layout_marginStart="@dimen/common_width_88dp"
            android:layout_marginTop="@dimen/common_height_48dp"
            android:layout_width="match_parent"
            android:lines="1"
            android:textColor="@color/color_text_first_label"
            android:textSize="@dimen/common_width_32dp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dfl.dflcommonlibs.uimodeutil.UITextView
            android:gravity="center"
            android:id="@+id/text_view_default_double_dialog_message"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_height_24dp"
            android:layout_width="@dimen/common_width_544dp"
            android:maxHeight="@dimen/common_height_270dp"
            android:textColor="@color/colorDialogText"
            android:textSize="@dimen/common_width_28dp"
            app:layout_constraintLeft_toLeftOf="@id/text_view_default_double_dialog_title"
            app:layout_constraintRight_toRightOf="@id/text_view_default_double_dialog_title"
            app:layout_constraintTop_toBottomOf="@id/text_view_default_double_dialog_title" />

        <View
            android:layout_height="@dimen/common_height_168dp"
            android:layout_width="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_default_double_dialog_message" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/common_height_40dp"
            android:layout_marginTop="@dimen/common_height_32dp"
            app:layout_constraintTop_toBottomOf="@id/text_view_default_double_dialog_message">

            <!--添加遮罩-->
            <com.dfl.dflcommonlibs.ui.MaskLayout
                android:clickable="true"
                android:id="@+id/frame_layout_button_positive"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent">

                <com.dfl.dflcommonlibs.uimodeutil.UIButton
                    android:background="@drawable/bg_button_positive"
                    android:clickable="false"
                    android:id="@+id/button_default_double_positive"
                    android:layout_height="@dimen/common_height_96dp"
                    android:layout_width="@dimen/common_width_232dp"
                    android:text="确认"
                    android:textColor="@color/colorDialogPositiveButton"
                    android:textSize="@dimen/common_height_28dp" />
            </com.dfl.dflcommonlibs.ui.MaskLayout>


            <com.dfl.dflcommonlibs.ui.MaskLayout
                android:id="@+id/frame_layout_button_negative"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/common_height_40dp"
                android:layout_width="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/frame_layout_button_positive">

                <com.dfl.dflcommonlibs.uimodeutil.UIButton
                    android:background="@drawable/bg_button_negative_new"
                    android:id="@+id/button_default_double_negative"
                    android:layout_height="@dimen/common_height_96dp"
                    android:layout_width="@dimen/common_width_232dp"
                    android:text="取消"
                    android:textColor="@color/color_dialog_btn_text_cancel"
                    android:textSize="@dimen/common_height_28dp" />

            </com.dfl.dflcommonlibs.ui.MaskLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>