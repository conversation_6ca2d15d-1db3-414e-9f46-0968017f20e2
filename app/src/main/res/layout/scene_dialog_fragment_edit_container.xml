<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.dfl.smartscene.ui.edit.container.SceneEditContainerViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/BigDialogTheme"
            android:layout_gravity="center_horizontal">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="148dp"
                android:gravity="center"
                android:text="@{vm.titleName}"
                android:textColor="@color/scene_color_text_1"
                android:textFontWeight="500"
                android:textSize="@dimen/scene_text_size_h3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="标题" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_close"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginStart="50dp"
                android:layout_marginTop="42dp"
                android:contentDescription="@string/scene_text_common_close"
                android:src="@drawable/scene_icon_common_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--左边一级菜单-->
            <com.dfl.android.animationlib.scrollView.OverScrollView
                android:id="@+id/rv_edit_item_list"
                style="@style/OverScrollViewTheme1A"
                android:layout_width="358dp"
                android:layout_height="0dp"
                android:layout_marginVertical="16dp"
                android:layout_marginStart="32dp"
                android:orientation="vertical"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title">

                <com.dfl.android.animationlib.TabSwitchView
                    android:id="@+id/tab_first_menu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:itemWidth="358dp"
                    android:background="@color/transparent"
                    app:iconHeight="48dp"
                    app:iconMarginStart="44dp"
                    app:iconWidth="48dp"
                    app:isHorizontal="false"
                    app:itemGap="36dp"
                    app:itemHeight="124dp"
                    app:itemSelectedBg="@drawable/scene_shape_white_bg"
                    app:itemTotality="6"
                    app:textColor="@color/nissan_option_bt_normal_text"
                    app:textColorSelected="@color/nissan_option_bt_clicked_text"
                    app:textMarginStart="120dp"
                    app:textSize="@dimen/scene_text_size_h3" />
            </com.dfl.android.animationlib.scrollView.OverScrollView>


            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp_fragment_container"
                viewPager_data="@{vm.categoryList}"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="106dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/rv_edit_item_list"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>