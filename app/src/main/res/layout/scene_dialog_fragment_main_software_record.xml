<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SmallDialogTheme"
            android:layout_height="782dp"
            android:layout_gravity="center"
            android:layout_marginTop="38dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_148"
                android:gravity="center"
                android:text="@string/scene_software_record_info"
                android:textColor="@color/scene_color_text_1"
                android:textFontWeight="500"
                android:textSize="@dimen/px_40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_help_record_close"
                android:layout_width="@dimen/px_64"
                android:layout_height="@dimen/px_64"
                android:layout_marginStart="@dimen/px_48"
                android:contentDescription="@string/scene_text_common_close"
                android:src="@drawable/scene_icon_common_close"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_title" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="640dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title">

                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:src="@drawable/scene_img_me_beian_qrcode_software_record"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginTop="40dp"
                    android:text="@string/scene_software_record_tip"
                    android:textColor="@color/scene_color_text_2"
                    android:textSize="32dp"
                    app:layout_constraintEnd_toEndOf="@+id/imageView"
                    app:layout_constraintStart_toStartOf="@+id/imageView"
                    app:layout_constraintTop_toBottomOf="@+id/imageView" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="42dp"
                    android:layout_marginTop="40dp"
                    android:text="@string/scene_software_record_number_pop"
                    android:textColor="@color/scene_color_text_3"
                    android:textSize="28dp"
                    app:layout_constraintEnd_toEndOf="@+id/imageView"
                    app:layout_constraintStart_toStartOf="@+id/imageView"
                    app:layout_constraintTop_toBottomOf="@+id/textView" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="42dp"
                    android:text="@string/scene_software_record_website"
                    android:textColor="@color/scene_color_text_3"
                    android:textSize="28dp"
                    app:layout_constraintEnd_toEndOf="@+id/imageView"
                    app:layout_constraintStart_toStartOf="@+id/imageView"
                    app:layout_constraintTop_toBottomOf="@+id/textView2" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>