<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="156dp"
    android:height="116dp"
    android:viewportWidth="156"
    android:viewportHeight="116">
  <path
      android:pathData="M93.05,6.84C96.88,6.84 100.09,9.46 101,13H108.1C114.15,13 119.05,17.9 119.05,23.95V97.84C119.05,103.89 114.15,108.79 108.1,108.79H46.53C40.48,108.79 35.58,103.89 35.58,97.84V23.95C35.58,17.9 40.48,13 46.53,13H53.63C54.54,9.46 57.75,6.84 61.58,6.84H93.05Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.45"/>
  <path
      android:pathData="M91.68,12.32C94.71,12.32 97.16,14.77 97.16,17.79L97.16,91C97.16,94.03 94.71,96.48 91.68,96.48L62.95,96.48C59.92,96.48 57.47,94.03 57.47,91L57.47,17.79C57.47,14.77 59.92,12.32 62.95,12.32L91.68,12.32Z"
      android:fillColor="#F5A161"/>
  <path
      android:pathData="M41.05,26.69C41.05,22.15 44.73,18.48 49.27,18.48L105.37,18.48C109.9,18.48 113.58,22.15 113.58,26.69L113.58,95.11C113.58,99.64 109.9,103.32 105.37,103.32L49.27,103.32C44.73,103.32 41.05,99.64 41.05,95.11L41.05,26.69Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M41.05,26.69C41.05,22.15 44.73,18.48 49.27,18.48L105.37,18.48C109.9,18.48 113.58,22.15 113.58,26.69L113.58,95.11C113.58,99.64 109.9,103.32 105.37,103.32L49.27,103.32C44.73,103.32 41.05,99.64 41.05,95.11L41.05,26.69Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="77.32"
          android:startY="22.83"
          android:endX="77.32"
          android:endY="103.32"
          android:type="linear">
        <item android:offset="0" android:color="#FFEFAD71"/>
        <item android:offset="1" android:color="#FFF9CDB2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M49.27,18.82L105.37,18.82C109.72,18.82 113.24,22.34 113.24,26.69L113.24,95.11C113.24,99.45 109.72,102.98 105.37,102.98L49.27,102.98C44.92,102.98 41.4,99.45 41.4,95.11L41.4,26.69L41.41,26.28C41.61,22.26 44.84,19.03 48.86,18.83L49.27,18.82Z"
      android:strokeAlpha="0.6"
      android:strokeWidth="0.684211"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="77.32"
          android:startY="18.48"
          android:endX="77.32"
          android:endY="103.32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M62.41,41.29C62.41,40.53 63.02,39.92 63.77,39.92L102.78,39.92C103.53,39.92 104.14,40.53 104.14,41.29V42.87C104.14,43.63 103.53,44.24 102.78,44.24H63.77C63.02,44.24 62.41,43.63 62.41,42.87V41.29Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M62.44,59.22C62.44,58.46 63.05,57.85 63.81,57.85L90.1,57.85C90.86,57.85 91.47,58.46 91.47,59.22V60.8C91.47,61.56 90.86,62.17 90.1,62.17H63.81C63.05,62.17 62.44,61.56 62.44,60.8V59.22Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M62.44,77.16C62.44,76.4 63.05,75.79 63.81,75.79L81.56,75.79C82.32,75.79 82.93,76.4 82.93,77.16V78.74C82.93,79.49 82.32,80.11 81.56,80.11H63.81C63.05,80.11 62.44,79.49 62.44,78.74V77.16Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.4"/>
  <path
      android:pathData="M50.48,41.29C50.48,40.53 51.1,39.92 51.85,39.92L53.43,39.92C54.19,39.92 54.8,40.53 54.8,41.29V42.87C54.8,43.63 54.19,44.24 53.43,44.24H51.85C51.1,44.24 50.48,43.63 50.48,42.87V41.29Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M50.48,59.22C50.48,58.46 51.1,57.85 51.85,57.85L53.43,57.85C54.19,57.85 54.8,58.46 54.8,59.22V60.8C54.8,61.56 54.19,62.17 53.43,62.17H51.85C51.1,62.17 50.48,61.56 50.48,60.8V59.22Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M50.48,77.16C50.48,76.4 51.1,75.79 51.85,75.79L53.43,75.79C54.19,75.79 54.8,76.4 54.8,77.16V78.74C54.8,79.49 54.19,80.11 53.43,80.11H51.85C51.1,80.11 50.48,79.49 50.48,78.74V77.16Z"
      android:fillColor="#ffffff"
      android:fillAlpha="0.4"/>
</vector>
