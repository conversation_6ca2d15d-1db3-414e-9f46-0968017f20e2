<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!--    标准shape-->
    <!-- "rectangle" | "oval" | "line" | "ring" 矩形（rectangle）、椭圆形(oval)、线性形状(line)、环形(ring)-->
    <!--solid 填充颜色-->
    <solid android:color="@color/transparent" />
    <!-- 渐变色   https://blog.csdn.net/g984160547/article/details/131397307-->
    <!--    <gradient-->
    <!--        android:angle="0"-->
    <!--        android:endColor="#00D1BEA5"-->
    <!--        android:startColor="#FFEEE0D1"-->
    <!--        android:type="linear" />-->
    <!-- corners 圆角-->
    <corners android:radius="16dp" />
    <!--    <corners-->
    <!--        android:bottomLeftRadius="14dp"-->
    <!--        android:bottomRightRadius="0dp"-->
    <!--        android:topLeftRadius="14dp"-->
    <!--        android:topRightRadius="0dp" />-->
    <!--stroke 边框-->
    <!--    <stroke-->
    <!--        android:width="3dp"-->
    <!--        android:color="@color/scene_primary_color" />-->
</shape>