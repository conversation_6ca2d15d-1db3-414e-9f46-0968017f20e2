<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <!-- "rectangle" | "oval" | "line" | "ring" 矩形（rectangle）、椭圆形(oval)、线性形状(line)、环形(ring)-->
            <!--solid 填充颜色-->
            <solid android:color="@color/scene_color_bg_2" /><!-- corners 圆角-->
            <corners android:radius="@dimen/scene_radius_large" />
        </shape>
    </item>
    <item
        android:gravity="right|top"
        android:right="16dp"
        android:top="16dp">
        <vector
            android:width="27dp"
            android:height="27dp"
            android:viewportWidth="27"
            android:viewportHeight="27">
            <path
                android:fillColor="#FFD4D6D9"
                android:pathData="M20.172,25.172C22.691,27.691 27,25.907 27,22.343V6C27,2.686 24.314,0 21,0H4.657C1.093,0 -0.691,4.309 1.828,6.828L20.172,25.172Z" />
        </vector>

    </item>
</layer-list>
