<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="36dp"
    android:height="36dp"
    android:viewportWidth="36"
    android:viewportHeight="36">
    <path
        android:fillType="evenOdd"
        android:pathData="M21.644,15.087H29.208C30.556,15.087 31.518,16.394 31.118,17.681L27.8,28.35C27.263,30.075 25.667,31.25 23.861,31.25H12.82C11.955,31.25 11.253,30.549 11.253,29.684V16.187C11.253,15.245 11.524,14.322 12.034,13.53L16.969,5.86C17.314,5.324 17.908,5 18.546,5H21.207C22.375,5 23.258,6.056 23.053,7.205L21.644,15.087ZM6.625,14.75L8.458,14.746C9.126,14.745 9.668,15.286 9.668,15.954V30.276C9.668,30.814 9.232,31.25 8.694,31.25H8.224C6.917,31.25 5.809,30.288 5.625,28.994L4.027,17.749C3.802,16.168 5.028,14.753 6.625,14.75Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="17.605"
                android:endY="31.25"
                android:startX="17.605"
                android:startY="5"
                android:type="linear">
                <item
                    android:color="#FFF9AB6B"
                    android:offset="0" />
                <item
                    android:color="#FFF87A51"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
