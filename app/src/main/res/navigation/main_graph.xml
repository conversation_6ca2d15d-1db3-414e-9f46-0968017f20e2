<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_graph"
    app:startDestination="@id/mainFragment">

    <fragment
        android:id="@+id/mainFragment"
        android:name="com.dfl.smartscene.ccs.view.fragment.MainFragment"
        android:label="MainFragment" >
        <action
            android:id="@+id/action_mainFragment_to_sceneEditorFragment"
            app:destination="@id/sceneEditorFragment" />
        <action
            android:id="@+id/action_mainFragment_to_sceneDetailFragment"
            app:destination="@id/sceneDetailFragment"/>
        <action
            android:id="@+id/action_mainFragment_to_libraryChildFragment"
            app:destination="@id/libraryChildFragment" />
        <action
            android:id="@+id/action_mainFragment_to_surpriseEggListFragment"
            app:destination="@id/surpriseEggListFragment" />
        <action
            android:id="@+id/action_mainFragment_to_relieveStressPatternFragment"
            app:destination="@id/relieveStressPatternFragment" />
        <action
            android:id="@+id/action_mainFragment_to_sceneSmallViewFragment"
            app:destination="@id/sceneSmallViewFragment" />
        <action
            android:id="@+id/action_mainFragment_to_libraryCardChildFragment"
            app:destination="@id/libraryCardChildFragment" />
    </fragment>
    <fragment
        android:id="@+id/sceneDetailFragment"
        android:name="com.dfl.smartscene.ccs.view.fragment.SceneDetailFragment"
        android:label="SceneDetailFragment" >

    </fragment>
    <fragment
        android:id="@+id/sceneEditorFragment"
        android:name="com.dfl.smartscene.ccs.fragment.SceneEditorFragment"
        android:label="SceneEditorFragment" />
    <fragment
        android:id="@+id/libraryChildFragment"
        android:name="com.dfl.smartscene.ccs.view.fragment.LibraryChildFragment"
        android:label="LibraryChildFragment" >
        <action
            android:id="@+id/action_libraryChildFragment_to_sceneDetailFragment"
            app:destination="@id/sceneDetailFragment"/>
    </fragment>
    <fragment
        android:id="@+id/surpriseEggListFragment"
        android:name="com.dfl.smartscene.ccs.view.surpriseegg.SurpriseEggListFragment"
        android:label="SurpriseEggListFragment" />
    <fragment
        android:id="@+id/relieveStressPatternFragment"
        android:name="com.dfl.smartscene.ccs.view.relievestress.RelieveStressPatternFragment"
        android:label="RelieveStressPatternFragment" />
    <fragment
        android:id="@+id/sceneSmallViewFragment"
        android:name="com.dfl.smartscene.ccs.view.fragment.SceneSmallViewFragment"
        android:label="SceneSmallViewFragment" />
        <action
            android:id="@+id/action_global_sceneSmallViewFragment"
            app:destination="@id/sceneSmallViewFragment"/>

    <fragment
        android:id="@+id/libraryCardChildFragment"
        android:name="com.dfl.smartscene.ccs.view.fragment.LibraryCardChildFragment"
        android:label="LibraryCardChildFragment" >
        <action
            android:id="@+id/action_libraryCardChildFragment_to_surpriseEggListFragment"
            app:destination="@id/surpriseEggListFragment" />
        <action
            android:id="@+id/action_libraryCardChildFragment_to_relieveStressPatternFragment"
            app:destination="@id/relieveStressPatternFragment" />
    </fragment>
</navigation>