<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- RecycleView通用 -->
    <dimen name="height_common_dividers">1dp</dimen>

    <dimen name="width_popup_my_scene">130dp</dimen>
    <dimen name="height_popup_my_scene">120dp</dimen>
    <dimen name="height_input_scene">96dp</dimen>
    <dimen name="width_input_edit">1101dp</dimen>
    <dimen name="height_input_edit">64dp</dimen>
    <dimen name="width_height_input_clear">46dp</dimen>


    <dimen name="padding_start_input">36dp</dimen>


    <dimen name="margin_input_cancel">48dp</dimen>
    <dimen name="margin_input_ok">24dp</dimen>

    <dimen name="text_size_edit_hint">28sp</dimen>
    <dimen name="text_size_input_cancel">36sp</dimen>

    <dimen name="widget_margin">0dp</dimen>
    <dimen name="text_size_common_content">24sp</dimen>
    <dimen name="px_0.5">0.5dp</dimen>
    <dimen name="px_0">0dp</dimen>
    <dimen name="px_1">1dp</dimen>
    <dimen name="px_2">2dp</dimen>
    <dimen name="px_3">3dp</dimen>
    <dimen name="px_4">4dp</dimen>
    <dimen name="px_5">5dp</dimen>
    <dimen name="px_6">6dp</dimen>
    <dimen name="px_8">8dp</dimen>
    <dimen name="px_10">10dp</dimen>
    <dimen name="px_12">12dp</dimen>
    <dimen name="px_13">13dp</dimen>
    <dimen name="px_14">14dp</dimen>
    <dimen name="px_16">16dp</dimen>
    <dimen name="px_17">17dp</dimen>
    <dimen name="px_18">18dp</dimen>
    <dimen name="px_19">19dp</dimen>
    <dimen name="px_20">20dp</dimen>
    <dimen name="px_22">22dp</dimen>
    <dimen name="px_24">24dp</dimen>
    <dimen name="px_25">25dp</dimen>
    <dimen name="px_26">26dp</dimen>
    <dimen name="px_28">28dp</dimen>
    <dimen name="px_30">30dp</dimen>
    <dimen name="px_32">32dp</dimen>
    <dimen name="px_33">33dp</dimen>
    <dimen name="px_34">34dp</dimen>
    <dimen name="px_35">35dp</dimen>
    <dimen name="px_36">36dp</dimen>
    <dimen name="px_37">37dp</dimen>
    <dimen name="px_38">38dp</dimen>
    <dimen name="px_40">40dp</dimen>
    <dimen name="px_41">41dp</dimen>
    <dimen name="px_42">42dp</dimen>
    <dimen name="px_43">43dp</dimen>
    <dimen name="px_44">44dp</dimen>
    <dimen name="px_45">45dp</dimen>
    <dimen name="px_46">46dp</dimen>
    <dimen name="px_48">48dp</dimen>
    <dimen name="px_50">50dp</dimen>
    <dimen name="px_52">52dp</dimen>
    <dimen name="px_55">55dp</dimen>
    <dimen name="px_56">56dp</dimen>
    <dimen name="px_58">58dp</dimen>
    <dimen name="px_60">60dp</dimen>
    <dimen name="px_62">62dp</dimen>
    <dimen name="px_63">63dp</dimen>
    <dimen name="px_64">64dp</dimen>
    <dimen name="px_66">66dp</dimen>
    <dimen name="px_68">68dp</dimen>
    <dimen name="px_70">70dp</dimen>
    <dimen name="px_72">72dp</dimen>
    <dimen name="px_74">74dp</dimen>
    <dimen name="px_75">75dp</dimen>
    <dimen name="px_76">76dp</dimen>
    <dimen name="px_78">78dp</dimen>
    <dimen name="px_79">79dp</dimen>
    <dimen name="px_80">80dp</dimen>
    <dimen name="px_82">82dp</dimen>
    <dimen name="px_83">83dp</dimen>
    <dimen name="px_84">84dp</dimen>
    <dimen name="px_86">86dp</dimen>
    <dimen name="px_88">88dp</dimen>
    <dimen name="px_90">90dp</dimen>
    <dimen name="px_92">92dp</dimen>
    <dimen name="px_94">94dp</dimen>
    <dimen name="px_95">95dp</dimen>
    <dimen name="px_96">96dp</dimen>
    <dimen name="px_97">97dp</dimen>
    <dimen name="px_98">98dp</dimen>
    <dimen name="px_100">100dp</dimen>
    <dimen name="px_104">104dp</dimen>
    <dimen name="px_108">108dp</dimen>
    <dimen name="px_110">110dp</dimen>
    <dimen name="px_112">112dp</dimen>
    <dimen name="px_114">114dp</dimen>
    <dimen name="px_116">116dp</dimen>
    <dimen name="px_118">118dp</dimen>
    <dimen name="px_120">120dp</dimen>
    <dimen name="px_123">123dp</dimen>
    <dimen name="px_124">124dp</dimen>
    <dimen name="px_126">126dp</dimen>
    <dimen name="px_128">128dp</dimen>
    <dimen name="px_132">132dp</dimen>
    <dimen name="px_134">134dp</dimen>
    <dimen name="px_136">136dp</dimen>
    <dimen name="px_140">140dp</dimen>
    <dimen name="px_142">142dp</dimen>
    <dimen name="px_144">144dp</dimen>
    <dimen name="px_145">145dp</dimen>
    <dimen name="px_148">148dp</dimen>
    <dimen name="px_152">152dp</dimen>
    <dimen name="px_156">156dp</dimen>
    <dimen name="px_160">160dp</dimen>
    <dimen name="px_164">164dp</dimen>
    <dimen name="px_166">166dp</dimen>
    <dimen name="px_168">168dp</dimen>
    <dimen name="px_176">176dp</dimen>
    <dimen name="px_178">178dp</dimen>
    <dimen name="px_180">180dp</dimen>
    <dimen name="px_184">184dp</dimen>
    <dimen name="px_188">188dp</dimen>
    <dimen name="px_190">190dp</dimen>
    <dimen name="px_192">192dp</dimen>
    <dimen name="px_198">198dp</dimen>
    <dimen name="px_200">200dp</dimen>
    <dimen name="px_202">202dp</dimen>
    <dimen name="px_203">203dp</dimen>
    <dimen name="px_208">208dp</dimen>
    <dimen name="px_218">218dp</dimen>
    <dimen name="px_220">220dp</dimen>
    <dimen name="px_222">222dp</dimen>
    <dimen name="px_223">223dp</dimen>
    <dimen name="px_224">224dp</dimen>
    <dimen name="px_230">230dp</dimen>
    <dimen name="px_232">232dp</dimen>
    <dimen name="px_236">236dp</dimen>
    <dimen name="px_237">237dp</dimen>
    <dimen name="px_238">238dp</dimen>
    <dimen name="px_240">240dp</dimen>
    <dimen name="px_242">242dp</dimen>
    <dimen name="px_245">245dp</dimen>
    <dimen name="px_250">250dp</dimen>
    <dimen name="px_252">252dp</dimen>
    <dimen name="px_256">256dp</dimen>
    <dimen name="px_258">258dp</dimen>
    <dimen name="px_260">260dp</dimen>
    <dimen name="px_264">264dp</dimen>
    <dimen name="px_268">268dp</dimen>
    <dimen name="px_272">272dp</dimen>
    <dimen name="px_276">276dp</dimen>
    <dimen name="px_280">280dp</dimen>
    <dimen name="px_282">282dp</dimen>
    <dimen name="px_286">286dp</dimen>
    <dimen name="px_288">288dp</dimen>
    <dimen name="px_292">292dp</dimen>
    <dimen name="px_293">293dp</dimen>
    <dimen name="px_294">294dp</dimen>
    <dimen name="px_300">300dp</dimen>
    <dimen name="px_306">306dp</dimen>
    <dimen name="px_312">312dp</dimen>
    <dimen name="px_313">313dp</dimen>
    <dimen name="px_316">316dp</dimen>
    <dimen name="px_318">318dp</dimen>
    <dimen name="px_320">320dp</dimen>
    <dimen name="px_324">@dimen/scene_height_wheel_view</dimen>
    <dimen name="px_332">332dp</dimen>
    <dimen name="px_334">334dp</dimen>
    <dimen name="px_338_r">338dp</dimen>
    //?  690dp
    <dimen name="px_338">@dimen/scene_width_edit_condition_rv_item</dimen>
    <dimen name="px_340">340dp</dimen>
    <dimen name="px_343">343dp</dimen>
    <dimen name="px_344">344dp</dimen>
    <dimen name="px_354">354dp</dimen>
    <dimen name="px_360">360dp</dimen>
    <dimen name="px_363">363dp</dimen>
    <dimen name="px_364">364dp</dimen>
    <dimen name="px_368">368dp</dimen>
    <dimen name="px_380">380dp</dimen>
    <dimen name="px_400">400dp</dimen>
    <dimen name="px_412">412dp</dimen>
    <dimen name="px_416">416dp</dimen>
    <dimen name="px_421">421dp</dimen>
    <dimen name="px_428">428dp</dimen>
    <dimen name="px_440">440dp</dimen>
    <dimen name="px_444">444dp</dimen>
    <dimen name="px_446">446dp</dimen>
    <dimen name="px_448">448dp</dimen>
    <dimen name="px_450">450dp</dimen>
    <dimen name="px_456">456dp</dimen>
    <dimen name="px_483">483dp</dimen>
    <dimen name="px_484">484dp</dimen>
    <dimen name="px_486">486dp</dimen>
    <dimen name="px_498">498dp</dimen>
    <dimen name="px_504">504dp</dimen>
    <dimen name="px_520">520dp</dimen>
    <dimen name="px_536">536dp</dimen>
    <dimen name="px_540">540dp</dimen>
    <dimen name="px_544">544dp</dimen>
    <dimen name="px_554">554dp</dimen>
    <dimen name="px_560">560dp</dimen>
    <dimen name="px_568">568dp</dimen>
    <dimen name="px_576">576dp</dimen>
    <dimen name="px_584">584dp</dimen>
    <dimen name="px_588">588dp</dimen>
    <dimen name="px_630">630dp</dimen>
    <dimen name="px_645">645dp</dimen>
    <dimen name="px_656">656dp</dimen>
    <dimen name="px_664">664dp</dimen>
    <dimen name="px_680">680dp</dimen>
    <dimen name="px_682">682dp</dimen>
    <dimen name="px_688">688dp</dimen>
    <dimen name="px_704">704dp</dimen>
    <dimen name="px_712">712dp</dimen>
    <dimen name="px_724">724dp</dimen>
    <dimen name="px_752">752dp</dimen>
    <dimen name="px_788">788dp</dimen>
    <dimen name="px_796">796dp</dimen>
    <dimen name="px_800">800dp</dimen>
    <dimen name="px_810">810dp</dimen>
    <dimen name="px_830">830dp</dimen>
    <dimen name="px_848">848dp</dimen>
    <dimen name="px_880">880dp</dimen>
    <dimen name="px_1096">1096dp</dimen>
    <dimen name="px_1184">1184dp</dimen>
    <dimen name="px_1200">1200dp</dimen>
    <dimen name="px_1404">1404dp</dimen>
    <dimen name="px_1508">1508dp</dimen>
    <dimen name="px_1538">1538dp</dimen>
    <dimen name="px_1645">1645dp</dimen>
    <dimen name="px_1816">1816dp</dimen>
    <dimen name="px_768">768dp</dimen>
    <dimen name="px_376">376dp</dimen>
    <dimen name="px_419">419dp</dimen>
    <dimen name="px_216">216dp</dimen>
    <dimen name="px_106">106dp</dimen>


    <!--编辑动作条件三级弹框 check item的字体大小-->
    <dimen name="scene_dialog_check_item_content">@dimen/scene_text_size_h6</dimen>
    <dimen name="scene_dialog_check_item_content2">@dimen/scene_text_size_h4</dimen>
    <!--编辑动作条件三级弹框,CommonSwitchDialog的标题margin top-->
    <dimen name="scene_margin_top_third_dialog_title">35dp</dimen>
    <!--编辑动作条件三级弹框 标题字体高度-->
    <dimen name="scene_height_edit_condition_title">44dp</dimen>
    <!--编辑动作条件三级弹框 标题下注释距离标题的margin top-->
    <dimen name="scene_margin_top_dialog_title_comment">33dp</dimen>


    <!--添加动作或条件弹框右侧rv子项icon大小,距离顶部标题间距,距离左侧tab间距-->
    <dimen name="scene_margin_top_condition_icon">24dp</dimen>
    <dimen name="scene_margin_start_condition_icon">32dp</dimen>
    <!--添加动作右侧rv卡片icon与动作名称间距-->
    <dimen name="scene_margin_top_tv_action_reflex_name">16dp</dimen>

    <!--添加编辑条件弹框右侧rv item的字体与icon的间距-->
    <dimen name="scene_margin_start_edit_condition_rv_item_tv">12dp</dimen>
    <dimen name="scene_width_edit_condition_rv_item">690dp</dimen>
    <dimen name="scene_height_edit_condition_rv_item">112dp</dimen>

    <dimen name="scene_height_wheel_view">324dp</dimen>


    <!--账号页-->
    <dimen name="scene_row_gap_user_center_upload">32dp</dimen>
    <dimen name="scene_column_gap_user_center_upload">48dp</dimen>
    <dimen name="scene_gap_user_center_userinfo">150dp</dimen>

    <!--社区搜索-->
    <!--搜索框背景大小-->
    <dimen name="scene_width_community_search_bg">2160dp</dimen>
    <dimen name="scene_height_community_search_bg">96dp</dimen>
    <!--社区首页用户头像宽度-->
    <dimen name="scene_width_community_tab_user_head">48dp</dimen>

    <!--字体规范大小,行高为字号大小的1.5倍-->
    <!--H1用于整个页面的标题-->
    <dimen name="scene_text_size_h1">64sp</dimen>
    <dimen name="scene_height_text_size_h1">96dp</dimen>
    <!--H2超大标题(空调弹窗标题使用)-->
    <dimen name="scene_text_size_h2">48sp</dimen>
    <dimen name="scene_height_text_size_h2">72dp</dimen>
    <!--H3一级标题-->
    <dimen name="scene_text_size_h3">40sp</dimen>
    <dimen name="scene_height_text_size_h3">60dp</dimen>
    <!--H4正文本/按钮-->
    <dimen name="scene_text_size_h4">36sp</dimen>
    <dimen name="scene_text_size_h4_p42r">34sp</dimen>
    <dimen name="scene_height_text_size_h4">54dp</dimen>
    <!--H5副文本-->
    <dimen name="scene_text_size_h5">32sp</dimen>
    <dimen name="scene_height_text_size_h5">48dp</dimen>
    <!--h6不太重要的注释文本，请谨慎使用-->
    <dimen name="scene_text_size_h6">28sp</dimen>
    <dimen name="scene_height_text_size_h6">42dp</dimen>

    <!--h7不太重要的注释文本，请谨慎使用-->
    <dimen name="scene_text_size_h7">24sp</dimen>
    <!--p42r按钮文本 22sp -->
    <dimen name="scene_text_size_h8">22sp</dimen>

    <!--圆角规范-->
    <dimen name="scene_radius_small">16dp</dimen>
    <dimen name="scene_radius_medium">20dp</dimen>
    <dimen name="scene_radius_large">24dp</dimen>
    <dimen name="scene_radius_extra_large">30dp</dimen>

    <!--弹框相关规范-->
    <!--弹框底部内容高度-->
    <dimen name="scene_width_small_dialog">880dp</dimen>
    <dimen name="scene_height_dialog_bottom">168dp</dimen>

    <!--中弹框 时间区间-->
    <dimen name="scene_width_middle_dialog">1488dp</dimen>
    <dimen name="scene_height_middle_dialog">848dp</dimen>

    <!--大弹框-->
    <dimen name="scene_width_big_dialog">2000dp</dimen>
    <dimen name="scene_height_big_dialog">1108dp</dimen>

    <!--按钮相关规范-->
    <dimen name="scene_radius_smaller_button">10dp</dimen>
    <dimen name="scene_radius_small_button">20dp</dimen>
    <dimen name="scene_radius_big_button">42dp</dimen>
    <!--大按钮 带与不带icon-->
    <dimen name="scene_height_big_button">92dp</dimen>
    <dimen name="scene_height_big_button_p42r">48dp</dimen>
    <!--大按钮不带icon最小宽度 超过则保持两侧间距-->
    <dimen name="scene_width_big_button_without_icon">357dp</dimen>
    <dimen name="scene_width_big_button_without_icon_p42r">166dp</dimen>

    <!--对话框底部大按钮的宽度-->
    <dimen name="scene_width_dialog_big_button">390dp</dimen>

    <!--中按钮 带与不带icon-->
    <dimen name="scene_height_middle_button">72dp</dimen>
    <dimen name="scene_width_middle_button_without_icon">229dp</dimen>
    <dimen name="scene_width_middle_button_with_icon">293dp</dimen>
    <!--小按钮不带icon-->
    <dimen name="scene_width_small_button">151dp</dimen>
    <dimen name="scene_height_small_button">56dp</dimen>
    <dimen name="scene_margin_small_button">40dp</dimen>

    <!--选择器相关规范-->
    <!--分段选择器中按钮的宽度-->
    <dimen name="scene_width_selector_button">232dp</dimen>
    <!--选择框圆角-->
    <dimen name="scene_radius_selector_checkbox">30dp</dimen>
    <dimen name="scene_height_selector_checkbox">96dp</dimen>
    <!--选择框宽度-->
    <!--用在单选或多个选项-->
    <dimen name="scene_width_big_selector_checkbox">420dp</dimen>
    <!--用在三个选项-->
    <dimen name="scene_width_selector_checkbox">324dp</dimen>
    <!--车辆到达的选择框-->
    <dimen name="scene_width_location_selector_checkbox">340dp</dimen>
    <dimen name="scene_height_location_selector_checkbox">92dp</dimen>
    <!--选择框之间间距-->
    <dimen name="scene_margin_selector_checkbox">30dp</dimen>
    <!--选择框内icon与字体间距-->
    <dimen name="scene_margin_end_selector_checkbox_icon">16dp</dimen>
    <dimen name="scene_min_width_selector_checkbox">200dp</dimen>
    <dimen name="scene_width_selector_checkbox_middle">240dp</dimen>
    <dimen name="scene_width_selector_checkbox_big">260dp</dimen>
    <dimen name="scene_width_selector_checkbox_bigger">420dp</dimen>

    <!--标签相关规范-->
    <!--标签高度,圆形标签宽高-->
    <dimen name="scene_size_tag">72dp</dimen>
    <dimen name="scene_radius_tag">36dp</dimen>
    <!--标签之间距离,标签与内容之间间距-->
    <dimen name="scene_padding_tag">44dp</dimen>

    <!--规范中icon相关大小-->
    <!--注释图标-->
    <dimen name="scene_icon_size_small_icon">40dp</dimen>
    <!--默认尺寸 按钮图标-->
    <dimen name="scene_icon_size_normal_icon">48dp</dimen>
    <!--搜索框&搜索列表尺寸-->
    <dimen name="scene_icon_size_big_icon">56dp</dimen>
    <!--tab栏icon大小-->
    <dimen name="scene_icon_size_tab_icon">64dp</dimen>
    <!--缺省图大小-->
    <dimen name="scene_width_default_icon">228dp</dimen>
    <dimen name="scene_height_default_icon">168dp</dimen>
    <!--添加动作或条件右侧item的icon大小-->
    <dimen name="scene_icon_size_condition_icon">72dp</dimen>
    <!--添加动作或条件左侧tab栏icon大小-->
    <dimen name="scene_icon_size_left_tab_icon">36dp</dimen>

    <!--社区卡片相关-->
    <dimen name="scene_height_community_card_bottom">98dp</dimen>
    <dimen name="scene_radius_community_card">24dp</dimen>
    <dimen name="scene_width_community_card">768dp</dimen>
    <dimen name="scene_height_community_card">308dp</dimen>
    <dimen name="scene_width_community_card_content">664dp</dimen>

    <!--indicatorSeekBarDialog突出左侧icon较小-->
    <dimen name="scene_icon_size_indicator_dialog_left">36dp</dimen>

    <!--WheelView选中宽度-->
    <dimen name="scene_width_wheel_view_selected">740dp</dimen>
    <!--WheelView选高度高度-->
    <dimen name="scene_height_wheel_view_selected">100dp</dimen>
    <!--WheelView背景框高度-->
    <dimen name="scene_height_wheel_view_background">104dp</dimen>

    <!--我的页面图标大小-->
    <dimen name="scene_icon_size_me_icon">72dp</dimen>

    <!--顶部状态栏高度-->
    <dimen name="scene_height_top_status_bar">104dp</dimen>
    <!-- 桌面左右滑动事件间距 -->
    <dimen name="gesture_spacing">100dp</dimen>
</resources>