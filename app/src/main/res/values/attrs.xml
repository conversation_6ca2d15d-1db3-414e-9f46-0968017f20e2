<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="WarpLinearLayout">
        <attr name="grivate" format="enum"><!--对齐方式 !-->
            <enum name="right" value="0" />
            <enum name="left" value="1" />
            <enum name="center" value="2" />
        </attr>
        <attr name="horizontal_Space" format="dimension" />
        <attr name="vertical_Space" format="dimension" />
        <attr name="isFull" format="boolean" />
    </declare-styleable>
    <declare-styleable name="LoopView">
        <attr name="awv_textSize" format="integer" />
        <attr name="awv_center_textSize" format="integer" />
        <attr name="awv_third_textSize" format="dimension" />
        <attr name="awv_lineSpace" format="float" />
        <attr name="awv_centerTextColor" format="integer" />
        <attr name="awv_outerTextColor" format="integer" />
        <attr name="awv_dividerTextColor" format="integer" />
        <attr name="awv_itemsVisibleCount" format="integer" />
        <attr name="awv_isLoop" format="boolean" />
        <attr name="awv_isCurve" format="boolean" />
        <attr name="awv_initialPosition" format="integer" />
        <attr name="awv_scaleX" format="float" />
    </declare-styleable>
    <declare-styleable name="WheelView">
        <attr name="wheelview_gravity">
            <enum name="center" value="17" />
            <enum name="left" value="3" />
            <enum name="right" value="5" />
        </attr>
        <attr name="wheelview_itemWidth" format="dimension" />
        <attr name="wheelview_itemHeight" format="dimension" />
        <attr name="wheelview_centerTextSize" format="dimension" />
        <attr name="wheelview_secondTextSize" format="dimension" />
        <attr name="wheelview_thirdTextSize" format="dimension" />
        <attr name="wheelview_textLabelSize" format="dimension" />
        <attr name="wheelview_textLabelGap" format="dimension" />
        <attr name="wheelview_textColorOut" format="color|reference" />
        <attr name="wheelview_textColorCenter" format="color|reference" />
        <attr name="wheelview_centerBackgroundColor" format="color|reference" />
        <attr name="wheelview_dividerColor" format="color|reference" />
        <attr name="wheelview_dividerWidth" format="dimension" />
        <attr name="wheelview_lineSpacingMultiplier" format="float" />
        <attr name="wheelview_maskId" format="reference" />
        <attr name="wheelview_isEnableCurve" format="boolean" />
        <attr name="wheelview_isLoop" format="boolean" />
        <attr name="wheelview_isCenterLabel" format="boolean" />
    </declare-styleable>

    <!-- 正则表达式编辑框 -->
    <declare-styleable name="RegexEditText">
        <!-- 正则输入限制 -->
        <attr name="inputRegex" format="string" />
        <!-- 常用正则类型 -->
        <attr name="regexType">
            <!-- 手机号（只能以 1 开头）-->
            <enum name="mobile" value="0x01" />
            <!-- 中文（普通的中文字符）-->
            <enum name="chinese" value="0x02" />
            <!-- 英文（大写和小写的英文）-->
            <enum name="english" value="0x03" />
            <!-- 数字（只允许输入纯数字）-->
            <enum name="number" value="0x04" />
            <!-- 整数（非 0 开头的数字）-->
            <enum name="count" value="0x05" />
            <!-- 用户名（中文、英文、数字）-->
            <enum name="name" value="0x06" />
            <!-- 非空格字符 -->
            <enum name="nonnull" value="0x07" />
        </attr>
    </declare-styleable>

    <declare-styleable name="AppWidgetAttrs">
        <attr name="appWidgetPadding" format="dimension" />
        <attr name="appWidgetInnerRadius" format="dimension" />
        <attr name="appWidgetRadius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ScaleSeekBar">
        <attr name="maxNum" format="integer" />
        <attr name="percent" format="integer" />
        <attr name="ticksNum" format="integer" />
        <attr name="ticksTextNum" format="integer" />
        <attr name="startNum" format="integer" />
        <attr name="minNum" format="integer" />
        <attr name="numMultiple" format="integer" />
        <attr name="ticksUnit" format="string" />
    </declare-styleable>

    <declare-styleable name="ColorSeekBar">
        <attr name="colorSeekBarColorSeeds" format="reference" />
        <attr name="colorSeekBarBarHeight" format="dimension" />
        <attr name="colorSeekBarProgress" format="integer" />
        <attr name="colorSeekBarRadius" format="dimension" />
        <attr name="colorSeekBarMaxProgress" format="integer" />
        <attr name="colorSeekBarVertical" format="boolean" />
        <attr name="colorSeekBarShowThumb" format="boolean" />
        <attr name="colorSeekBarBorderColor" format="color" />
        <attr name="colorSeekBarBorderSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="AirConditionerSeekBar">
        <!--竖线宽度-->
        <attr name="asb_line_space_width" format="dimension|reference" />
        <!--进度条高度-->
        <attr name="asb_seekbar_height" format="dimension|reference" />
        <!--圆角弧度-->
        <attr name="asb_round_radius_size" format="dimension|reference" />
        <!--方块值单位-->
        <attr name="asb_default_block_unit" format="string" />
        <!--最大方块数目-->
        <attr name="asb_max_block_num" format="integer" />
        <!--限定最小方块数目-->
        <attr name="asb_min_block_num" format="integer" />
        <!--设置默认方块值-->
        <attr name="asb_default_block_num" format="integer" />
        <!--设置左边的图标-->
        <attr name="asb_left_icon_src" format="reference" />
        <!--设置右边的图标-->
        <attr name="asb_right_icon_src" format="reference" />
    </declare-styleable>
    <declare-styleable name="CustomSeekBar">
        <attr name="name" format="string" />
        <attr name="max" format="integer" />
        <attr name="defaultv" format="integer" />
        <attr name="icon" format="reference" />
    </declare-styleable>

    <declare-styleable name="SkinBackgroundHelper">
        <attr name="android:background" />
    </declare-styleable>

    <declare-styleable name="SkinCompatImageView">
        <attr name="android:src" />
        <attr name="srcCompat" />
        <attr name="android:tint" format="color" />
        <attr name="tint" format="color" />
    </declare-styleable>

    <declare-styleable name="SkinCompatTextHelper">
        <attr name="android:drawableLeft" />
        <attr name="android:drawableTop" />
        <attr name="android:drawableRight" />
        <attr name="android:drawableBottom" />
        <attr name="android:drawableStart" />
        <attr name="android:drawableEnd" />
        <attr name="android:textAppearance" />
    </declare-styleable>

    <declare-styleable name="SkinTextAppearance">
        <attr name="android:textSize" />
        <attr name="android:textColor" />
        <attr name="android:textColorHint" />
        <attr name="android:textStyle" />
        <attr name="android:typeface" />
        <attr name="textAllCaps" />
        <attr name="android:shadowColor" />
        <attr name="android:shadowDy" />
        <attr name="android:shadowDx" />
        <attr name="android:shadowRadius" />
    </declare-styleable>
    <declare-styleable name="SkinCompatProgressBar">
        <attr name="android:indeterminateDrawable" />
        <attr name="android:progressDrawable" />
    </declare-styleable>

    <!-- 流布局-->
    <declare-styleable name="FlowLayout">
        <!--支持折叠 -->
        <attr name="flow_fold" format="boolean" />
        <!--折叠时的行数（支持折叠情况下） -->
        <attr name="flow_foldLines" format="integer" />
        <!--平均分配 -->
        <attr name="flow_equally" format="boolean" />
        <!--平局分配一行的数量 -->
        <attr name="flow_equally_count" format="integer" />
        <!--水平间距 -->
        <attr name="flow_horizontalSpacing" format="dimension" />
        <!--竖直间距 -->
        <attr name="flow_verticalSpacing" format="dimension" />
        <!--左右对齐 -->
        <attr name="flow_gravity">
            <enum name="LEFT" value="0" />
            <enum name="RIGHT" value="1" />
        </attr>
    </declare-styleable>

    <!--圆角图片-->
    <declare-styleable name="CornerImageView">
        <!--设置圆角-->
        <attr name="corner_radius" format="dimension" />
    </declare-styleable>
    <!--车控空调风量调节seekbar控件-->
    <declare-styleable name="IndicatorSeekBar">
        <!--seekBar-->
        <attr name="isb_max" format="float" /><!-- the max value of seekBar to seek, default 100-->
        <attr name="isb_min" format="float" /><!-- the min value of seekBar to seek, default 0 -->
        <attr name="isb_progress" format="float" /><!-- the current progress value of seekBar, default 0-->
        <attr name="isb_progress_value_float" format="boolean" /><!--set the value of seekBar to float type, default false-->
        <attr name="isb_seek_smoothly" format="boolean" /><!--true to seek smoothly when has ticks, default false-->
        <attr name="isb_r2l" format="boolean" /><!--compat app local change,like arabic local, default false-->
        <attr name="isb_ticks_count" format="integer" /><!--seekBar's ticks count, default zero-->
        <attr name="isb_user_seekable" format="boolean" /><!--prevent user from seeking,only can be change thumb location by setProgress(), default false-->
        <attr name="isb_clear_default_padding" format="boolean" /><!-- set seekBar's leftPadding&rightPadding to zero, default false, default padding is 16dp-->
        <attr name="isb_only_thumb_draggable" format="boolean" /><!--user change the thumb's location by touching thumb/touching track,true for touching track to seek. false for touching thumb; default false-->
        <attr name="isb_first_show_seekbar" format="boolean" /><!--Text or SeekBar first-->
        <attr name="isb_is_no_sliding" format="boolean" />
        <!--indicator-->
        <attr name="isb_show_indicator"><!-- the type of indicator, default rectangle_rounded_corner/0.-->
            <enum name="none" value="0" />
            <enum name="circular_bubble" value="1" />
            <enum name="rounded_rectangle" value="2" />
            <enum name="rectangle" value="3" />
            <enum name="custom" value="4" /><!--choose custom type that you can set the custom indicator layout you want.-->
        </attr>
        <attr name="isb_indicator_color" format="color|reference" /><!-- indicator's color, default #FF4081-->
        <attr name="isb_indicator_text_color" format="color|reference" /><!-- indicator's text color, default #FF4081 -->
        <attr name="isb_indicator_text_size" format="dimension|reference" /><!-- indicator's text size, default 14sp-->
        <attr name="isb_indicator_content_layout" format="reference" /><!-- when you set indicator type to custom , you can set ths indicator layout you want-->
        <attr name="isb_indicator_top_content_layout" format="reference" /> <!--set the indicator's top view you want, not impact arrow , effect on indicator type : rectangle or rectangle_rounded_corner-->
        <!--track-->
        <attr name="isb_track_background_size" format="dimension|reference" /><!-- set indicatorSeekBar's track background bar size, default 2dp-->
        <attr name="isb_track_background_color" format="color|reference" /><!-- set indicatorSeekBar's track background bar color, default #D7D7D7-->
        <attr name="isb_track_progress_size" format="dimension|reference" /><!-- set indicatorSeekBar's track progress bar size, default 2dp-->
        <attr name="isb_track_progress_color" format="color|reference" /><!-- set indicatorSeekBar's track progress bar color, default #FF4081-->
        <attr name="isb_track_rounded_corners" format="boolean" /><!-- set the track's both ends' shape to round-->
        <!--thumb text-->
        <attr name="isb_thumb_text_color" format="color|reference" /><!--set thumb's color, default #FF4081-->
        <attr name="isb_show_thumb_text" format="boolean" /><!--show thumb text or not, default false-->
        <!--thumb-->
        <attr name="isb_thumb_size" format="dimension|reference" /><!--set thumb's size, default 14dp, thumb size will be limited in 30dp by the THUMB_MAX_WIDTH-->
        <attr name="isb_thumb_color" format="color|reference" /><!--set thumb's color, default #FF4081-->
        <attr name="isb_thumb_drawable" format="reference" /><!--set custom thumb's drawable you want,thumb size will be limited in 30dp by the THUMB_MAX_WIDTH, if drawable less than 30dp ,will show in intrinsic size -->
        <attr name="isb_thumb_adjust_auto" format="boolean" /><!--set the thumb move to the closed tick after touched up, default true-->
        <attr name="isb_thumb_hide" format="boolean" /><!--set the thumb visibility-->
        <attr name="isb_thumb_touch_size" format="float" />
        <!--tickMarks-->
        <attr name="isb_tick_marks_color" format="color|reference" /><!--set tick's color, default #FF4081-->
        <attr name="isb_tick_marks_size" format="dimension|reference" /><!--set the tick width, default 10dp,custom drawable will be limited in 30dp, if less than 30dp ,will show in intrinsic size-->
        <attr name="isb_tick_marks_drawable" format="reference" /><!--set custom tick's drawable you want, custom drawable will be limited in 30dp, if less than 30dp ,will show in intrinsic size-->
        <attr name="isb_tick_marks_ends_hide" format="boolean" /><!--hide 2 ticks on the seekBar's both ends, default false-->
        <attr name="isb_tick_marks_swept_hide" format="boolean" /><!--hide the ticks on the seekBar's thumb left, default false-->
        <attr name="isb_show_tick_marks_type"><!--select the tick shape type, default not show： NONE/0-->
            <enum name="none" value="0" />
            <enum name="oval" value="1" />
            <enum name="square" value="2" />
            <enum name="divider" value="3" /> <!--show tickMarks shape as vertical line , line'size is 1 dp.-->
        </attr>
        <!--tickTexts-->
        <attr name="isb_show_tick_texts" format="boolean" /><!--show the text below tick or not, default false-->
        <attr name="isb_tick_texts_color" format="reference|color" /><!--set texts' color, default #FF4081-->
        <attr name="isb_tick_texts_size" format="dimension|reference" /><!--set the text size of tick below text, default 13sp-->
        <attr name="isb_tick_texts_array" format="reference" /><!--set the custom texts below tick to replace default progress text, default null-->
        <attr name="isb_tick_texts_typeface"><!--select the typeface for tick texts/thumb text, default normal-->
            <enum name="normal" value="0" />
            <enum name="monospace" value="1" />
            <enum name="sans" value="2" />
            <enum name="serif" value="3" />
        </attr>
    </declare-styleable>


    <declare-styleable name="BannerViewPager">
        <!--页面切换时间间隔-->
        <attr name="bvp_interval" format="integer" />
        <attr name="bvp_scroll_duration" format="integer" />
        <attr name="bvp_can_loop" format="boolean" />
        <attr name="bvp_auto_play" format="boolean" />
        <attr name="bvp_indicator_checked_color" format="color" />
        <attr name="bvp_indicator_normal_color" format="color" />
        <attr name="bvp_indicator_radius" format="dimension" />
        <attr name="bvp_round_corner" format="dimension" />
        <attr name="bvp_page_margin" format="dimension" />
        <attr name="bvp_reveal_width" format="dimension" />
        <attr name="bvp_indicator_style" format="enum">
            <enum name="circle" value="0" />
            <enum name="dash" value="2" />
            <enum name="round_rect" value="4" />
        </attr>
        <attr name="bvp_indicator_slide_mode" format="enum">
            <enum name="normal" value="0" />
            <enum name="smooth" value="2" />
            <enum name="worm" value="3" />
            <enum name="scale" value="4" />
            <enum name="color" value="5" />
        </attr>
        <attr name="bvp_indicator_gravity" format="enum">
            <enum name="center" value="0" />
            <enum name="start" value="2" />
            <enum name="end" value="4" />
        </attr>
        <attr name="bvp_page_style" format="enum">
            <enum name="normal" value="0" />
            <enum name="multi_page" value="2" />
            <enum name="multi_page_overlap" value="4" />
            <enum name="multi_page_scale" value="8" />
        </attr>
        <attr name="bvp_indicator_visibility" format="enum">
            <enum name="visible" value="0" />
            <enum name="invisible" value="4" />
            <enum name="gone" value="8" />/>
        </attr>

    </declare-styleable>
    <declare-styleable name="FadeRecyclerView">
        <!-- 渐消类型 0实底背景渐消 1无背景纯文本类-->
        <attr name="recyclerFadeType" format="integer" />
        <!-- 渐消遮罩的长度类型 0为1% 1为3%-->
        <attr name="recyclerFadeLengthType" format="integer" />
    </declare-styleable>
    <!--leakcanary release版本正常运行-->
    <bool name="leak_canary_allow_in_non_debuggable_build">true</bool>
</resources>