<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ccs5.0 -->
    <color name="colorPrimary">#6200EE</color>
    <color name="colorPrimaryDark">#3700B3</color>
    <color name="colorAccent">#03DAC5</color>
    <color name="ColorId_black_n">#FF000000</color>
    <color name="ColorId_gold_n">#FFF7CDC5</color>
    <color name="ColorId_screen_white_n">#FFFFFFFF</color>
    <color name="ColorId_error_white_n">#FFDCE3F0</color>
    <color name="ColorId_blue_s">#FF2E7CFF</color>
    <color name="ColorId_time">#FFB4BDCC</color>

    <color name="color_custom_number_picker_text_n">#CC2F3238</color>
    <color name="color_custom_number_picker_text_s">#FFC96E60</color>
    <color name="color_text_route_preference">#000000</color>
    <color name="color_text_selected_enable">#66C96E60</color>
    <color name="color_tab_selected">#C96E60</color>
    <color name="color_tab_unselected">#CCB4BDCC</color>
    <color name="color_text_unselected_enable">#6632363E</color>
    <color name="color_navi_name_text">#000000</color>
    <color name="color_navi_address_text">#CC2F3238</color>


    <color name="color_adjust_step_check_0">#B3000000</color>
    <color name="color_adjust_step_check_1">#CC000000</color>
    <color name="color_adjust_step_check_2">#E6000000</color>
    <color name="color_adjust_step_check_3">#FF000000</color>


    <color name="color_adjust_step_start" >#B58D8A</color>
    <color name="color_adjust_step_end" >#CC9E93</color>
    <color name="color_adjust_step_interval" >#FFFFFF</color>

    <color name="color_text_indicator_alpha">#66C96E60</color>
    <color name="color_text_indicator">#C96E60</color>
    <color name="color_mark_indicator_unselect">#40FFFFFF</color>
    <color name="color_mark_indicator_select">#B3FFFFFF</color>
    <color name="color_text_first_label">#FF000000</color>
    <color name="color_text_secondary_label">#32363E</color>
    <color name="color_text_tertiary_label">#CC2F3238</color>
    <color name="color_text_forth_label">#FF707174</color>
    <color name="color_divide">#2609090B</color>

    <color name="color_video_time_picker_bg">#161A27</color>
    <color name="color_text_secondary_label_white">#DCE3F0</color>


    <color name="color_tab_indicator">#C96E60</color>
    <color name="color_login_edit_text">#FFFFFFFF</color>
    <color name="color_login_hint">#FFB4BDCC</color>

    <color name="color_library_ad_bg">#F5F9FFFA</color>

    <!-- 弹窗“取消”按钮文本颜色值 -->
    <color name="color_dialog_btn_text_cancel">#FF647398</color>
    <color name="color_library_scene_desc_text">#D9E1EB</color>

    <color name="color_text_loading_fresh">#CC2F3238</color>

    // 共通定义
    <color name="color_text_title">#FF000000</color>
    <color name="color_text_explain">#FF32363E</color>
    <color name="color_text_explain_spec">#FFC96E60</color>
    <color name="color_text_button">#FF32363E</color>
    <color name="color_text_text">#FFFFFFFF</color>
    <color name="color_text_list_title">#CC2F3238</color>
    <color name="color_text_list_explain">#FF32363E</color>
    <color name="color_text_list_del_text">#FF000000</color>
    <color name="color_text_list_del_button">#FFC96E60</color>
    <color name="color_text_popup_button">#FFFFFFFF</color>
    <color name="color_text_popup_text">#FF000000</color>
    <color name="color_text_toast_text">#FF000000</color>

    <color name="color_text_tertiary_label_gray">#662F3238</color>
    <!-- 20% 33, 30% 4D, 40% 66, 50% 80, 80% CC ,90% E6 -->
    <!--通用颜色-->
    <color name="transparent">#00000000</color>
    <color name="app_background">@color/scene_color_bg_1</color>
    <!--导航栏颜色-->
    <color name="scene_bg_navigation">#E0EFF1F4</color>

    <!--颜色规范-->
    <!--品牌色normal-->
    <color name="scene_primary_color">#FFF5A161</color>
    <color name="scene_primary_color_unable">#80F5A161</color>

    <!--按钮背景色-->
    <color name="scene_neutral_btn_1">#393E49</color>
    <color name="scene_neutral_btn_2">#D4D6D9</color>
    <!--按钮文本色-->
    <color name="scene_primary_text_normal">#FFFFFF</color>
    <!--背景色-->
    <color name="scene_neutral_bg_1">#E1E3E5</color>
    <color name="scene_neutral_bg_4">#FFFFFF</color>
    <!--蒙层颜色-->
    <color name="scene_mask">#8024272F</color>
    <color name="scene_neutral_mask">#331D1D1E</color>

    <!--品牌色highlight -->
    <color name="scene_primary_color_highlight">#FFDF7727</color>
    <color name="scene_primary_color_highlight_unable">#80DF7727</color>
    <!--辅助颜色 次于主色的辅助颜色，用于页面主色过多时缓释视觉效果，暂时主要用于次按钮颜色-->
    <color name="scene_secondary_color">#FF4D6AFF</color>
    <!--语义颜色 用于提示、危险提示、成功提示等语义设计场景，也用作仪表指示灯的图标设计颜色-->
    <!--提示或警告-->
    <color name="scene_mean_color_warning">#FFFF8F1C</color>
    <color name="scene_mean_color_remind">#FFFFDE00</color>
    <!--危险提示或错误-->
    <color name="scene_mean_color_error">#FFFF453A</color>
    <color name="scene_mean_color_error_unable">#80FF453A</color>
    <!--成功-->
    <color name="scene_mean_color_success">#FF27BA84</color>
    <!--中性颜色 用于不同的文字层级应用、系统图标颜色、背景色、分割线、卡片等场景设计-->
    <color name="scene_color_text_1">#FF1D1D1E</color>
    <color name="scene_color_text_2">#B81D1D1E</color>
    <color name="scene_color_text_3">#A31D1D1E</color>
    <color name="scene_color_text_4">#7A1D1D1E</color>
    <color name="scene_color_text_5">#5B1D1D1E</color>
    <color name="scene_color_text_normal_highlight_primary">#FFFFFFFF</color>
    <color name="scene_color_text_disable_primary">#59FFFFFF</color>
    <color name="scene_color_text_disable_secondary">#2E1D1D1E</color>
    <color name="scene_color_bg_1">#FFE1E3E5</color>
    <!-- 88%-->
    <color name="scene_color_bg_2">#E0EBEDF0</color>
    <color name="scene_color_bg_3">#FFDFE2E5</color>
    <color name="scene_color_bg_4">#FFFFFFFF</color>
    <!--遮罩层颜色，下载场景背景-->
    <color name="scene_color_mask">#8024272F</color>
    <!--主要按钮-->
    <color name="scene_color_btn_1">#FF393E49</color>
    <!--次要按钮-->
    <color name="scene_color_btn_2">#FFD4D6D9</color>
    <!--滚动条颜色-->
    <color name="scene_color_scrollbar">#1F393E49</color>
    <!--对话框背景色-->
    <color name="scene_color_medal_bg">#FFE1E3E5</color>
    <!--分割线-->
    <color name="scene_color_divider">#14000000</color>

    <!--ui特殊深浅色不一致颜色变量-->
    <color name="scene_color_day_bg2_night_bg3">#E0EBEDF0</color>
    <color name="scene_color_day_bg2_night_btn2">#E0EBEDF0</color>
    <color name="scene_color_day_bg3_night_bg1">#FFDFE2E5</color>
    <color name="scene_color_day_btn2_night_bg2">#FFD4D6D9</color>
    <color name="scene_color_day_bg2_night_medal_component">#E0EBEDF0</color>

    <!--我的模块相关颜色-->
    <!--我的更多菜单背景蒙层-->
    <color name="scene_me_card_popup_menu_mask">#4DE1E3E5</color>
    
    <!--我的 卡片背景-->
    <color name="scene_color_card_bg_1">#FFFFFFFF</color>
    <!--我的卡片底部分割线-->
    <color name="scene_color_me_card_line">#E0EBEDF0</color>
    <!--状态-时间周期-次日tag-->
    <color name="scene_color_tag_tab_bg_selected">#1FF5A161</color>
    <!--seekBar已滑动的进度条颜色-->
    <color name="scene_color_progress_bg_f_default">#FFFFFFFF</color>
    <!--左边tab选中色-->
    <color name="scene_color_tab_selected">#FFFFFFFF</color>
    <!--卡片图标的背景色 55%-->
    <color name="scene_color_bg_icon">#8CEBEDF0</color>
    <!--帮助indicator-->
    <color name="scene_help_indicator_night_normal">#FFBEBFC1</color>
    <!--滚轮wheel蒙层渐变用到的颜色-->
    <color name="scene_color_wheel_mask_day_1">#00E1E3E5</color>
    <color name="scene_color_wheel_mask_day_2">#FFE1E3E5</color>

    <!--社区场景模块-->
    <!--社区场景卡片的蒙层-->
    <color name="scene_community_mask">#00000000</color>
    <!--社区轮播图的蒙层-->
    <color name="scene_community_banner_mask">#00000000</color>
    <!--社区封面图生成背景-->
    <color name="scene_community_downloading_view">#E0EBEDF0</color>
    <!--轮播图面包碎 通常颜色-->
    <color name="scene_community_banner_indicator_normal">#FFBEBFC1</color>
    <!--轮播图面包碎 选中颜色-->
    <color name="scene_community_banner_indicator_select">#FFF5A161</color>
    <!--    社区选择场景页的背景色-->
    <color name="scene_color_bg_select_scene">#FFE1E3E5</color>
    <!-- 发布时场景名称的背景色   -->
    <color name="scene_color_post_scene_name_bg">#1A1D1D1E</color>
    <!-- 社区刷新控件的颜色-->
    <color name="scene_color_banner_header_bg_1">#FFFFFFFF</color>
    <!-- 社区页面图标的颜色-->
    <color name="scene_color_community_icon">#FFFFFFFF</color>
    <!-- 动作-推荐的图标颜色-->
    <color name="scene_color_action_recommend_adapter_icon">#FFFFA038</color>
    <!--    选择场景 的 下一步按钮 不可用时背景色 白黑模式同色-->
    <color name="scene_color_bg_disable_community_next">#80D4D6D9</color>
    <!--    选择场景 的 下一步按钮 不可用时字体色 白黑模式同色-->
    <color name="scene_color_text_disable_community_next">#2E1D1D1E</color>
    <!--    选择场景 的 下一步按钮 蒙层 开始 -->
    <color name="scene_color_mask_community_next_start">#00EDEFF2</color>
    <!--    选择场景 的 下一步按钮 蒙层 结束 -->
    <color name="scene_color_mask_community_next_end">#FFEDEFF2</color>
    <!--    下载场景-编辑场景的试用按钮 背景色-->
    <color name="scene_color_bg_btn_try2">#FFE1E3E5</color>
    <!--    主要按钮不可用是文字颜色-->
    <color name="scene_color_text_second_bt_disable">#59FFFFFF</color>
    <!--    搜索地址列表的图标-->
    <color name="scene_colo_icon_search">#A31D1D1E</color>

    <!--    删除图标的颜色-->
    <color name="scene_color_icon_del">#FFA0A9BB</color>


</resources>