<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--通用颜色-->
    <!--导航栏颜色-->
    <color name="scene_bg_navigation">#E01A1B20</color>

    <!--颜色规范-->
    <!--品牌色normal-->
    <color name="scene_primary_color">#FFD68751</color>



    <!--按钮色-->
    <color name="scene_neutral_btn_1">#D2D3D6</color>
    <!--按钮文本色-->
    <color name="scene_primary_text_normal">#1D1D1E</color>
    <!--背景色-->
    <color name="scene_neutral_bg_4">#4D525C</color>
    <!--蒙层颜色-->
    <color name="scene_mask">#000000B8</color>



    <!--品牌色highlight -->
    <color name="scene_primary_color_highlight">#FFEB8A50</color>
    <!--语义颜色 用于提示、危险提示、成功提示等语义设计场景，也用作仪表指示灯的图标设计颜色-->
    <color name="scene_mean_color_remind">#FFD9BD06</color>
    <!--危险提示或错误-->
    <color name="scene_mean_color_error">#FFB93831</color>
    <color name="scene_mean_color_error_unable">#80B93831</color>
    <!--中性颜色 用于不同的文字层级应用、系统图标颜色、背景色、分割线、卡片等场景设计-->
    <color name="scene_color_text_1">#F5D8DCE0</color>
    <color name="scene_color_text_2">#B8D8DCE0</color>
    <color name="scene_color_text_3">#7AD8DCE0</color>
    <color name="scene_color_text_4">#5CD8DCE0</color>
    <color name="scene_color_text_disable_secondary">#1FD8DCE0</color>
    <color name="scene_color_text_normal_highlight_primary">#F5FFFFFF</color>
    <color name="scene_color_bg_1">#FF0C0C0D</color>
    <color name="scene_color_bg_2">#E51A1C1F</color>
    <color name="scene_color_bg_3">#145B5F66</color>
    <color name="scene_color_bg_4">#FF4D525C</color>
    <!--遮罩层颜色-->
    <color name="scene_color_mask">#B3000000</color>
    <!--主要按钮-->
    <color name="scene_color_btn_1">#FFD2D3D6</color>
    <!--次要按钮-->
    <color name="scene_color_btn_2">#215B5F66</color>
    <!--滚动条颜色-->
    <color name="scene_color_scrollbar">#FF1A1C1F</color>
    <!--对话框背景色-->
    <color name="scene_color_medal_bg">#FF1D1F24</color>
    <!--分割线-->
    <color name="scene_color_divider">#14FFFFFF</color>

    <!--ui特殊深浅色不一致颜色变量-->
    <color name="scene_color_day_bg2_night_bg3">#145B5F66</color>
    <color name="scene_color_day_bg2_night_btn2">#215B5F66</color>
    <color name="scene_color_day_bg3_night_bg1">#FF0C0C0D</color>
    <color name="scene_color_day_btn2_night_bg2">#E51A1C1F</color>
    <color name="scene_color_day_bg2_night_medal_component">#145B5F66</color>

    <!--我的 模块-->
    <!--我的更多菜单背景蒙层-->
    <color name="scene_me_card_popup_menu_mask">#E51A1C1F</color>
    <!--我的 卡片背景-->
    <color name="scene_color_card_bg_1">#FF1D1F24</color>
    <!--我的卡片底部分割线-->
    <color name="scene_color_me_card_line">#1AD2D3D6</color>
    <!--卡片图标的背景色 13%-->
    <color name="scene_color_bg_icon">#215B5F66</color>

    <!--帮助indicator-->
    <color name="scene_help_indicator_night_normal">#46484D</color>

    <!--左边tab选中色-->
    <color name="scene_color_tab_selected">@color/scene_color_bg_3</color>

    <!--滚轮wheel蒙层渐变用到的颜色-->
    <color name="scene_color_wheel_mask_day_1">#001D1F24</color>
    <color name="scene_color_wheel_mask_day_2">#FF1D1F24</color>
    <!--状态-时间周期-次日tag-->
    <color name="scene_color_tag_tab_bg_selected">#14F19055</color>
    <!--seekBar已滑动的进度条颜色-->
    <color name="scene_color_progress_bg_f_default">#FF636975</color>

    <!--社区 场景-->
    <!--社区场景卡片的蒙层-->
    <color name="scene_community_mask">#C0000000</color>
    <!--社区轮播图的蒙层-->
    <color name="scene_community_banner_mask">#80000000</color>
    <!--社区封面图生成背景-->
    <color name="scene_community_downloading_view">#E01D1F24</color>
    <!--轮播图面包碎 通常颜色-->
    <color name="scene_community_banner_indicator_normal">#4DBEBFC1</color>
    <!--轮播图面包碎 选中颜色-->
    <color name="scene_community_banner_indicator_select">#FFD68751</color>
    <!--    社区选择场景页的背景色-->
    <color name="scene_color_bg_select_scene">@color/scene_color_bg_1</color>

    <!--    选择场景 的 下一步按钮 蒙层 开始 -->
    <color name="scene_color_mask_community_next_start">#00000000</color>
    <!--    选择场景 的 下一步按钮 蒙层 结束 -->
    <color name="scene_color_mask_community_next_end">#FF000000</color>
    <!--    下载场景-编辑场景的试用按钮 背景色-->
    <color name="scene_color_bg_btn_try2">#E51A1C1F</color>
    <!--    主要按钮不可用是文字颜色-->
    <color name="scene_color_text_second_bt_disable">#B31D1D1E</color>
    <!--    搜索地址列表的图标-->
    <color name="scene_colo_icon_search">#FF787A80</color>
</resources>