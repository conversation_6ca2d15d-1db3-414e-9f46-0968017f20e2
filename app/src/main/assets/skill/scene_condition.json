[{"category": "周期", "desc": "#year#年#month#月#day#日", "input": [{"name": "year", "type": "uint32", "desc": "range:[1000,3000] 限制范围[当前年，当前年+100]", "method": "range", "value": "1000_3000", "valueMap": {}}, {"name": "month", "type": "uint32", "desc": "range:[1,12]", "method": "range", "value": "1_12", "valueMap": {}}, {"name": "day", "type": "uint32", "desc": "range:[1,31]", "method": "range", "value": "1_31", "valueMap": {}}], "permission": "USER", "skillId": "0xFF00", "skillValue": "condition.common.Date", "type": "Status Condition", "config": "1"}, {"category": "周期", "desc": "每周#day#", "input": [{"name": "day", "type": "uint32", "desc": "range:[1,127] 每天：127 周一：1 ，周二：2 ，周三：4 ，周四：8，周五：16，周六：32，周日：64 多选相加，如周一二三四，15 （1+2+4+8=15）", "method": "range", "value": "1_127", "valueMap": {}}], "permission": "USER", "skillId": "0xFF02", "skillValue": "condition.common.OnceEveryWeek", "type": "Status Condition", "config": "1"}, {"category": "周期", "desc": "法定工作日", "input": [], "permission": "USER", "skillId": "0xFF03", "skillValue": "condition.common.Workday", "type": "Status Condition", "config": "1"}, {"category": "周期", "desc": "法定节假日", "input": [], "permission": "USER", "skillId": "0xFF04", "skillValue": "condition.common.Holiday", "type": "Status Condition", "config": "1"}, {"category": "时间区间", "desc": "#start_hour#:#start_minute#~#stop_hour#:#stop_minute#", "input": [{"name": "start_hour", "type": "uint32", "desc": "range:[0,23]", "method": "range", "value": "0_23", "valueMap": {}}, {"name": "start_minute", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}, {"name": "start_second", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}, {"name": "stop_hour", "type": "uint32", "desc": "range:[0,23]", "method": "range", "value": "0_23", "valueMap": {}}, {"name": "stop_minute", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}, {"name": "stop_second", "type": "uint32", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}], "permission": "USER", "skillId": "0xFF05", "skillValue": "condition.common.TimeRanges", "type": "Status Condition", "config": "1"}, {"category": "车辆离开", "desc": "范围半径#distance#m", "input": [{"name": "distance", "type": "int", "desc": "range:[100,5000]", "method": "range", "value": "100_5000", "valueMap": {}}, {"name": "latitude", "type": "double", "desc": "searchConditionAddress", "method": "third<PERSON><PERSON>y", "value": "0.0_90.0", "valueMap": {}}, {"name": "longitude", "type": "double", "desc": "range:[0.0,180.0]", "method": "<PERSON><PERSON><PERSON><PERSON>", "value": "0.0_180.0", "valueMap": {}}], "permission": "USER", "skillId": "0x2", "skillValue": "condition.navi.DistanceToPoiStatusGreater", "type": "Status Condition", "config": "1", "params": [{"index": 0, "arg": "特定点名称（搜索默认选中第一个，distance填入InputArg）"}]}, {"category": "车辆到达", "desc": "范围半径#distance#m", "input": [{"name": "distance", "type": "int", "desc": "range:[100,5000]", "method": "range", "value": "100_5000", "valueMap": {}}, {"name": "latitude", "type": "double", "desc": "searchConditionAddress", "method": "third<PERSON><PERSON>y", "value": "0.0_90.0", "valueMap": {}}, {"name": "longitude", "type": "double", "desc": "range:[0.0,180.0]", "method": "<PERSON><PERSON><PERSON><PERSON>", "value": "0.0_180.0", "valueMap": {}}], "permission": "USER", "skillId": "0x3", "skillValue": "condition.navi.DistanceToPoiStatusLess", "type": "Status Condition", "config": "1", "params": [{"index": 0, "arg": "特定点名称（搜索默认选中第一个，distance填入InputArg）"}]}, {"category": "导航预计到达距离", "desc": "小于#distance#m", "input": [{"name": "distance", "type": "double", "desc": "range:[500,10000]", "method": "range", "value": "500_10000", "valueMap": {}}], "permission": "USER", "skillId": "0x5f", "skillValue": "condition.navi.ArrivalDistanceLess", "type": "Status Condition", "config": "1"}, {"category": "导航预计到达时间", "desc": "小于#h#时#min#分", "input": [{"name": "h", "type": "int", "desc": "range:[0,6]", "method": "range", "value": "0_6", "valueMap": {}}, {"name": "min", "type": "int", "desc": "range:[0,59]", "method": "range", "value": "0_59", "valueMap": {}}], "permission": "USER", "skillId": "0x61", "skillValue": "condition.navi.ArrivalTimeLess", "type": "Status Condition", "config": "1"}, {"category": "天气预报", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum: 晴天:1,阴天 : 2,雨天 : 3,雪天 : 4,雾天 : 5,雾霾 : 6,沙尘 : 7,其他 : 8", "method": "enum", "value": "1_2_3_4_5_6_7_8", "valueMap": {"1": "晴天", "2": "阴天", "3": "雨天", "4": "雪天", "5": "雾天", "6": "雾霾", "7": "沙尘", "8": "其他"}}], "permission": "USER", "skillId": "0x6b", "skillValue": "condition.weather.WeatherStatusEqual", "type": "Status Condition", "config": "1"}, {"category": "空气质量", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:差 : 0,良 : 1,优 : 2", "method": "enum", "value": "0_1_2", "valueMap": {"0": "差", "1": "良", "2": "优"}}], "permission": "USER", "skillId": "0x6f", "skillValue": "condition.weather.AirQualityEqual", "type": "Status Condition", "config": "1"}, {"category": "儿童检测", "desc": "#position# #scStatus#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "scStatus", "type": "uint8", "desc": "enum:有儿童:1,无儿童:0", "method": "enum", "value": "1_0", "valueMap": {"1": "有儿童", "0": "无儿童"}}], "permission": "USER", "skillId": "0x77", "skillValue": "condition.oms.SeatExistChildEqual", "type": "Status Condition", "config": "1"}, {"category": "乘客分布检测", "desc": "#position# #scStatus#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "scStatus", "type": "uint8", "desc": "enum: 无人:0,有人:1", "method": "enum", "value": "0_1", "valueMap": {"0": "无人", "1": "有人"}}], "permission": "USER", "skillId": "0x78", "skillValue": "condition.oms.SeatExistPassengerEqual", "type": "Status Condition", "config": "1"}, {"category": "乘客性别检测", "desc": "#position# #scGender#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1,后排左:2,后排中:3,后排右:4,三排左:5,三排中:6,三排右:7", "method": "enum", "value": "0_1_2_3_4_5_6_7", "valueMap": {"0": "主驾", "1": "副驾", "2": "后排左", "3": "后排中", "4": "后排右", "5": "三排左", "6": "三排中", "7": "三排右"}}, {"name": "sc<PERSON><PERSON>", "type": "uint8", "desc": "enum: 男:0,女:1", "method": "enum", "value": "0_1", "valueMap": {"0": "男", "1": "女"}}], "permission": "USER", "skillId": "0x79", "skillValue": "condition.oms.PassengerGenderEqual", "type": "Status Condition", "config": "1"}, {"category": "乘客行为检测", "desc": "#position# #scB<PERSON><PERSON>or#", "input": [{"name": "position", "type": "uint8", "desc": "enum:主驾:0,副驾:1", "method": "enum", "value": "0_1", "valueMap": {"0": "主驾", "1": "副驾"}}, {"name": "sc<PERSON><PERSON><PERSON><PERSON>", "type": "uint8", "desc": "enum: 打电话:0", "method": "enum", "value": "0", "valueMap": {"0": "打电话"}}], "permission": "USER", "skillId": "0x7A", "skillValue": "condition.oms.PassengerBehaviorEqual", "type": "Status Condition", "config": "1"}, {"category": "车内湿度", "desc": "大于#Humidityvalue#%", "input": [{"name": "Humidityvalue", "type": "double", "desc": "range:[0,100]", "method": "range", "value": "0_100", "valueMap": {}}], "permission": "USER", "skillId": "0x14a", "skillValue": "condition.env.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ue<PERSON><PERSON>", "type": "Status Condition", "config": "1"}, {"category": "车内湿度", "desc": "小于#Humidityvalue#%", "input": [{"name": "Humidityvalue", "type": "double", "desc": "range:[0,100]", "method": "range", "value": "0_100", "valueMap": {}}], "permission": "USER", "skillId": "0x14b", "skillValue": "condition.env.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Status Condition", "config": "1"}, {"category": "车外天气", "desc": "#RainSizeSts#", "input": [{"name": "RainSizeSts", "type": "int", "desc": "enum:无雨 : 0,毛毛雨 : 1,小雨 : 2,中雨 : 3,大雨 : 4", "method": "enum", "value": "0_1_2_3_4", "valueMap": {"0": "无雨", "1": "毛毛雨", "2": "小雨", "3": "中雨", "4": "大雨"}}], "permission": "USER", "skillId": "0x14e", "skillValue": "condition.env.RainfallEqual", "type": "Status Condition", "config": "1"}, {"category": "车外天气", "desc": "#RainSizeSts#", "input": [{"name": "RainSizeSts", "type": "int", "desc": "enum:无雨 : 0,毛毛雨 : 1,小雨 : 2,中雨 : 3,大雨 : 4", "method": "enum", "value": "0_1_2_3_4", "valueMap": {"0": "无雨", "1": "毛毛雨", "2": "小雨", "3": "中雨", "4": "大雨"}}], "permission": "USER", "skillId": "0x14f", "skillValue": "condition.env.RainfallNotEqual", "type": "Status Condition", "config": "1"}, {"category": "近光灯", "desc": "#lampSts#", "input": [{"name": "lampSts", "type": "int", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x20a", "skillValue": "condition.light.DippedBeamStatus", "type": "Status Condition", "config": "1"}, {"category": "远光灯", "desc": "#lampSts#", "input": [{"name": "lampSts", "type": "int", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x20d", "skillValue": "condition.light.HighBeamStatus", "type": "Status Condition", "config": "1"}, {"category": "后雾灯", "desc": "#lampSts#", "input": [{"name": "lampSts", "type": "int", "desc": "enum:打开 : 1", "method": "enum", "value": "1", "valueMap": {"1": "打开"}}], "permission": "USER", "skillId": "0x211", "skillValue": "condition.light.RearFogLightStatus", "type": "Status Condition", "config": "1"}, {"category": "主驾车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,全关:10", "method": "enum", "value": "0_10", "valueMap": {"0": "全开", "10": "全关"}}], "permission": "USER", "skillId": "0x307", "skillValue": "condition.window.LFWindowPositionEqual", "type": "Status Condition", "config": "1"}, {"category": "副驾车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,全关:10", "method": "enum", "value": "0_10", "valueMap": {"0": "全开", "10": "全关"}}], "permission": "USER", "skillId": "0x30d", "skillValue": "condition.window.RFWindowPositionEqual", "type": "Status Condition", "config": "1"}, {"category": "左后车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,全关:10", "method": "enum", "value": "0_10", "valueMap": {"0": "全开", "10": "全关"}}], "permission": "USER", "skillId": "0x313", "skillValue": "condition.window.LRWindowPositionEqual", "type": "Status Condition", "config": "1"}, {"category": "右后车窗", "desc": "#level#", "input": [{"name": "level", "type": "int", "desc": "enum:全开:0,全关:10", "method": "enum", "value": "0_10", "valueMap": {"0": "全开", "10": "全关"}}], "permission": "USER", "skillId": "0x319", "skillValue": "condition.window.RRWindowPositionEqual", "type": "Status Condition", "config": "1"}, {"category": "主驾乘坐状态", "desc": "#isOccupied#", "input": [{"name": "isOccupied", "type": "int", "desc": "enum:未就座:0,已就座:1", "method": "enum", "value": "0_1", "valueMap": {"0": "未就座", "1": "已就座"}}], "permission": "USER", "skillId": "0x413", "skillValue": "condition.seat.LFSeatOccupStatus", "type": "Status Condition", "config": "1"}, {"category": "副驾乘坐状态", "desc": "#isOccupied#", "input": [{"name": "isOccupied", "type": "int", "desc": "enum:未就座:0,已就座:1", "method": "enum", "value": "0_1", "valueMap": {"0": "未就座", "1": "已就座"}}], "permission": "USER", "skillId": "0x415", "skillValue": "condition.seat.RFSeatOccupStatus", "type": "Status Condition", "config": "1"}, {"category": "主驾安全带", "desc": "#beltState#", "input": [{"name": "beltState", "type": "int", "desc": "enum:已系上:0,已解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "已系上", "2": "已解开"}}], "permission": "USER", "skillId": "0x41d", "skillValue": "condition.seat.LFSafetyBeltStatus", "type": "Status Condition", "config": "1"}, {"category": "副驾安全带", "desc": "#beltState#", "input": [{"name": "beltState", "type": "int", "desc": "enum:已系上:0,已解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "已系上", "2": "已解开"}}], "permission": "USER", "skillId": "0x41e", "skillValue": "condition.seat.RFSafetyBeltStatus", "type": "Status Condition", "config": "1"}, {"category": "后排左安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x43b", "skillValue": "condition.seat.BackLFSafetyBeltStatus", "type": "Status Condition", "config": "1"}, {"category": "后排右安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x43c", "skillValue": "condition.seat.BackRFSafetyBeltStatus", "type": "Status Condition", "config": "1"}, {"category": "后排中安全带", "desc": "#status#", "input": [{"name": "status", "type": "uint8", "desc": "enum:系上:0,解开:2", "method": "enum", "value": "0_2", "valueMap": {"0": "系上", "2": "解开"}}], "permission": "USER", "skillId": "0x43d", "skillValue": "condition.seat.BackCenterSafetyBeltStatus", "type": "Status Condition", "config": "1"}, {"category": "胎压报警", "desc": "左前车轮", "input": [{"name": "tirePressureLowStatus", "type": "int", "desc": "enum:正常 : 0,低压 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "正常", "1": "低压"}}], "permission": "USER", "skillId": "0x519", "skillValue": "condition.tire.LFTirePressureLowWarningStatus", "type": "Status Condition", "config": "1"}, {"category": "胎压报警", "desc": "左后车轮", "input": [{"name": "tirePressureLowStatus", "type": "int", "desc": "enum:正常 : 0,低压 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "正常", "1": "低压"}}], "permission": "USER", "skillId": "0x51a", "skillValue": "condition.tire.LRTirePressureLowWarningStatus", "type": "Status Condition", "config": "1"}, {"category": "胎压报警", "desc": "右前车轮", "input": [{"name": "tirePressureLowStatus", "type": "int", "desc": "enum:正常 : 0,低压 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "正常", "1": "低压"}}], "permission": "USER", "skillId": "0x51b", "skillValue": "condition.tire.RFTirePressureLowWarningStatus", "type": "Status Condition", "config": "1"}, {"category": "胎压报警", "desc": "右后车轮", "input": [{"name": "tirePressureLowStatus", "type": "int", "desc": "enum:正常 : 0,低压 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "正常", "1": "低压"}}], "permission": "USER", "skillId": "0x51c", "skillValue": "condition.tire.RRTirePressureLowWarningStatus", "type": "Status Condition", "config": "1"}, {"category": "全车门锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x602", "skillValue": "condition.lock.CarLockStatus", "type": "Status Condition", "config": "1"}, {"category": "行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭:0,打开:1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x60a", "skillValue": "condition.lock.BackDoorStatus", "type": "Status Condition", "config": "5"}, {"category": "电动行李厢", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2,停止: 0x3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "关闭", "2": "打开", "3": "停止"}}], "permission": "USER", "skillId": "0x60d", "skillValue": "condition.lock.PBDStatusEqual", "type": "Status Condition", "config": "3", "specials": {"PK1B": {"category": "电动尾门", "desc": "%d", "input": [{"name": "status", "type": "int", "desc": "enum:关闭 : 0x1,打开 : 0x2,停止: 0x3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "关闭", "2": "打开", "3": "停止"}}], "permission": "USER", "skillId": "0x60d", "skillValue": "condition.lock.PBDStatusEqual", "type": "Status Condition", "config": "3"}}}, {"category": "左后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x61e", "skillValue": "condition.lock.LeftChildLockStatusEqual", "type": "Status Condition", "config": "1"}, {"category": "右后车门童锁", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum:解锁 : 0,上锁 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "解锁", "1": "上锁"}}], "permission": "USER", "skillId": "0x621", "skillValue": "condition.lock.RightChildLockStatusEqual", "type": "Status Condition", "config": "1"}, {"category": "蓝牙", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x715", "skillValue": "condition.setting.BluetoothSwitchStatus", "type": "Status Condition", "config": "1"}, {"category": "蓝牙", "desc": "#connectionStatus#", "input": [{"name": "connectionStatus", "type": "int", "desc": "enum:未连接 : 0,已连接 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "未连接", "1": "已连接"}}], "permission": "USER", "skillId": "0x716", "skillValue": "condition.setting.BluetoothConnectionStatus", "type": "Status Condition", "config": "1"}, {"category": "热点", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x717", "skillValue": "condition.setting.HotspotSwitchStatus", "type": "Status Condition", "config": "1"}, {"category": "热点", "desc": "#connectionStatus#", "input": [{"name": "connectionStatus", "type": "int", "desc": "enum:未连接 : 0,已连接 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "未连接", "1": "已连接"}}], "permission": "USER", "skillId": "0x718", "skillValue": "condition.setting.HotspotConnectionStatus", "type": "Status Condition", "config": "1"}, {"category": "WLAN", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x719", "skillValue": "condition.setting.WLANSwitchStatus", "type": "Status Condition", "config": "1"}, {"category": "WLAN", "desc": "#connectionStatus#", "input": [{"name": "connectionStatus", "type": "int", "desc": "enum:未连接 : 0,已连接 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "未连接", "1": "已连接"}}], "permission": "USER", "skillId": "0x71a", "skillValue": "condition.setting.WLANConnectionStatus", "type": "Status Condition", "config": "1"}, {"category": "移动网络", "desc": "#switchStatus#", "input": [{"name": "switchStatus", "type": "int", "desc": "enum:关闭 : 0,打开 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "关闭", "1": "打开"}}], "permission": "USER", "skillId": "0x71b", "skillValue": "condition.setting.MobileNetworkSwitchStatus", "type": "Status Condition", "config": "1"}, {"category": "空气质量", "desc": "#airQuality#", "input": [{"name": "airQuality", "type": "int", "desc": "enum:优 : 1,良 : 2, 差:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "优", "2": "良", "3": "差"}}], "permission": "USER", "skillId": "0xb06", "skillValue": "condition.aiot.AirQualityInCarEqual", "type": "Status Condition", "config": "0"}, {"category": "空气质量", "desc": "#airQuality#", "input": [{"name": "airQuality", "type": "int", "desc": "enum:优 : 1,良 : 2, 差:3", "method": "enum", "value": "1_2_3", "valueMap": {"1": "优", "2": "良", "3": "差"}}], "permission": "USER", "skillId": "0xb07", "skillValue": "condition.aiot.AirQualityInCarNotEqual", "type": "Status Condition", "config": "0"}, {"category": "乘坐状态", "desc": "#status#", "input": [{"name": "status", "type": "int", "desc": "enum: 空座: 0, 落座 : 1", "method": "enum", "value": "0_1", "valueMap": {"0": "空座", "1": "落座"}}], "permission": "USER", "skillId": "0xb1d", "skillValue": "condition.aiot.ChildSeatStatus", "type": "Status Condition", "config": "0"}]