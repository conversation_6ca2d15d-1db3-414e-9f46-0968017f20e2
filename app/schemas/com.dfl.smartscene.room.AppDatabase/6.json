{"formatVersion": 1, "database": {"version": 6, "identityHash": "135156333b802ccb0cd3dd6174e0eabb", "entities": [{"tableName": "scene_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sr_id` INTEGER, `scenario_id` TEXT NOT NULL, `scene_name` TEXT NOT NULL, `finish_scene_time` INTEGER NOT NULL, `start_scene_result` INTEGER NOT NULL, PRIMARY KEY(`sr_id`))", "fields": [{"fieldPath": "srId", "columnName": "sr_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "scenarioGroupId", "columnName": "scenario_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "scene<PERSON><PERSON>", "columnName": "scene_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "finishSceneTime", "columnName": "finish_scene_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startSceneResult", "columnName": "start_scene_result", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["sr_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "CommunityListResponse", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`autoId_CommunityListResponse` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `tabId` INTEGER NOT NULL, `pageIndex` INTEGER NOT NULL, `pageSize` INTEGER NOT NULL, `pages` INTEGER NOT NULL, `records` INTEGER NOT NULL, `rows` TEXT)", "fields": [{"fieldPath": "autoId_CommunityListResponse", "columnName": "autoId_CommunityListResponse", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tabId", "columnName": "tabId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pageIndex", "columnName": "pageIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pageSize", "columnName": "pageSize", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pages", "columnName": "pages", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "records", "columnName": "records", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rows", "columnName": "rows", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["autoId_CommunityListResponse"]}, "indices": [], "foreignKeys": []}, {"tableName": "recent_search", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`recentSearchList` TEXT, `id` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "recentSearchList", "columnName": "recentSearchList", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "CommunityUserInfo", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userAvatar` TEXT, `userSubscribe` INTEGER NOT NULL, `likeNum` INTEGER NOT NULL, `userFanNum` INTEGER NOT NULL, `uuid` TEXT NOT NULL, `userName` TEXT NOT NULL, `userSubScribeNum` INTEGER NOT NULL, `userUploadNum` INTEGER NOT NULL, `sceneList` TEXT NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "userAvatar", "columnName": "userAvatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userSubscribe", "columnName": "userSubscribe", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likeNum", "columnName": "likeNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userFanNum", "columnName": "userFanNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userSubScribeNum", "columnName": "userSubScribeNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userUploadNum", "columnName": "userUploadNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sceneList", "columnName": "sceneList", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '135156333b802ccb0cd3dd6174e0eabb')"]}}