{"formatVersion": 1, "database": {"version": 1, "identityHash": "e9490133de62f99ada097f33d6801f9b", "entities": [{"tableName": "scene_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sr_id` INTEGER, `scenario_id` TEXT NOT NULL, `scene_name` TEXT NOT NULL, `finish_scene_time` INTEGER NOT NULL, `start_scene_result` INTEGER NOT NULL, PRIMARY KEY(`sr_id`))", "fields": [{"fieldPath": "srId", "columnName": "sr_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "scenarioGroupId", "columnName": "scenario_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "scene<PERSON><PERSON>", "columnName": "scene_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "finishSceneTime", "columnName": "finish_scene_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startSceneResult", "columnName": "start_scene_result", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["sr_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e9490133de62f99ada097f33d6801f9b')"]}}